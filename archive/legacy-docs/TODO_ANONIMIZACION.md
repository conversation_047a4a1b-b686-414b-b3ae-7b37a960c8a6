# TODO: Sistema de Anonimización Híbrida

## 🎯 Plan de Trabajo [2025-01-18] - Sistema de Anonimización de Imágenes

### Análisis del Problema

- **Problema identificado**: Las imágenes con rostros identificables no se anonimizan antes del procesamiento
- **Archivos afectados**:
  - [ ] utils/secure-image-upload.ts (nuevo)
  - [ ] stores/ai-analysis-store.ts
  - [ ] app/service/hooks/usePhotoAnalysis.ts
  - [ ] stores/client-history-store.ts
  - [ ] supabase/functions/anonymize-and-store/index.ts (nuevo)
  - [ ] supabase/functions/salonier-assistant/index.ts
  - [ ] supabase/migrations/005_anonymization_buckets.sql (nuevo)
- **Impacto estimado**: ~1000 líneas nuevas/modificadas
- **Riesgos identificados**: Cambio crítico en flujo de imágenes, requiere testing exhaustivo

### Tareas a Realizar

#### Fase 1: Infraestructura de Storage

- [✅] Crear migración SQL para bucket originals-for-anonymization
- [✅] Configurar políticas RLS para el nuevo bucket
- [✅] Implementar política de ciclo de vida (24h)
- [✅] Verificar permisos de buckets existentes

#### Fase 2: Edge Function de Anonimización

- [✅] Crear Edge Function anonymize-and-store
- [✅] Implementar detección de rostros (placeholder MVP)
- [✅] Aplicar blur/pixelado a rostros detectados
- [✅] Optimizar imágenes (200-400KB)
- [✅] Lógica de subida a client-photos
- [✅] Eliminación de original
- [✅] Manejo robusto de errores

#### Fase 3: Cliente - Flujo de Subida Segura

- [✅] Crear utils/secure-image-upload.ts
- [✅] Implementar uploadToTemporaryBucket()
- [✅] Implementar anonymizeAndStore()
- [✅] Manejo de errores y reintentos
- [✅] Feedback visual al usuario

#### Fase 4: Refactorización de Stores

- [✅] Modificar ai-analysis-store.ts para URLs
- [✅] Actualizar performImageAnalysis()
- [✅] Mantener compatibilidad temporal con base64
- [✅] Actualizar client-history-store.ts (documentado)

#### Fase 5: Actualización de Edge Function Principal

- [✅] Modificar salonier-assistant para URLs
- [✅] Mantener retrocompatibilidad
- [✅] Actualizar validaciones
- [ ] Testing con ambos formatos

#### Fase 6: Hooks y Componentes

- [✅] Actualizar usePhotoAnalysis.ts
- [✅] Modificar flujo de captura
- [ ] Actualizar componentes de galería
- [ ] Ajustar feedback visual

#### Fase 7: API de Borrado

- [✅] Implementar deletePhoto en utils/secure-image-upload.ts
- [ ] Agregar botón de eliminación en UI
- [ ] Confirmación antes de borrar
- [ ] Logging de auditoría

### Validaciones

- [ ] Tests de anonimización funcionan
- [ ] Imágenes se eliminan de bucket temporal
- [ ] URLs públicas funcionan con IA
- [ ] Sin regresiones en flujo existente
- [ ] Cumplimiento GDPR verificado

### Sección de Revisión

- **Cambios realizados**:
  - Creada migración SQL para bucket `originals-for-anonymization` con políticas RLS
  - Implementada Edge Function `anonymize-and-store` con detección y blur de rostros
  - Creada utilidad `secure-image-upload.ts` para manejo completo del flujo
  - Actualizado `ai-analysis-store.ts` para soportar URLs públicas y base64
  - Modificada Edge Function `salonier-assistant` para aceptar ambos formatos
  - Integrado flujo en `usePhotoAnalysis.ts` con subida segura
  - Documentación completa en `ANONYMIZATION_FLOW.md`

- **Problemas encontrados**:
  - Detección de rostros usando placeholder MVP (requiere librería real)
  - Canvas API en Deno puede requerir ajustes para producción
  - Necesidad de mantener compatibilidad temporal con base64

- **Lecciones aprendidas**:
  - La anonimización debe ser inmediata y automática
  - Fail-safes son críticos para cumplimiento GDPR
  - Compatibilidad hacia atrás es esencial durante migración
  - Documentación clara acelera adopción del nuevo flujo

- **Próximos pasos**:
  - Implementar detección real de rostros con TensorFlow.js
  - Migrar componentes de galería al nuevo flujo
  - Agregar UI para eliminación de imágenes
  - Testing exhaustivo en dispositivos reales
  - Configurar función scheduled para limpieza automática

---

## Estado: EN PROGRESO 🔄

Iniciado: 2025-01-18
Última actualización: 2025-01-18
