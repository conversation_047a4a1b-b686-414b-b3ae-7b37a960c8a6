# Guía de Prueba - Consistencia de Sesiones

## Objetivo

Verificar que el número de sesiones estimadas se muestra consistentemente en todas las pantallas.

## Caso de Prueba: 4.5 a 9 (como en tu ejemplo)

### 1. En Diagnóstico Capilar

- Asegúrate de indicar nivel 4.5
- Marca el estado como "Teñido" o "Colored" (tiene color artificial)

### 2. En Resultado Deseado

- Selecciona nivel objetivo 9
- **Verificar**: Debe mostrar:
  - Diferencia de niveles: 4.5 niveles
  - Sesiones estimadas: **3 sesiones** (calculado como: 4.5 niveles con color = requiere decapado + decoloración)
  - Warning: "Color no levanta color. Se requiere decapado previo"

### 3. En Formulación

- **Verificar**: El tip "Aclarado Intenso" debe mostrar:
  - "Diferencia de 4.5 niveles"
  - "Considera hacer en **3 sesiones** para mantener integridad capilar"
  - NO debe decir "2 sesiones" como antes

### 4. Verificaciones adicionales

- Los warnings de colorimetría deben aparecer en FormulaTips
- Si hay proceso de decapado requerido, debe mencionarse
- El volumen de oxidante debe ser el correcto según el proceso

## Otros casos para probar:

### Caso: Oscurecer de 9 a 5

- Debe recomendar oxidante de **10 volúmenes** (no 20)
- Debe mencionar pre-pigmentación (oscurecer 4 niveles)
- Sesiones: 1 (es un proceso de depósito)

### Caso: Natural 6 a 8

- Cabello natural puede aclarar 3 niveles con tinte
- Debe ser viable en 1 sesión
- Oxidante: 20-30 volúmenes

### Caso: Teñido 6 a 8

- Debe indicar que requiere decapado primero
- Sesiones estimadas: 2 (decapado + coloración)
- Warning sobre "color no levanta color"

## Resultado esperado:

✅ El número de sesiones debe ser el MISMO en todas las pantallas
✅ Los warnings de colorimetría deben ser consistentes
✅ No más hardcoding de "2 sesiones" para cualquier diferencia >= 3
