# Casos de Prueba - Principios de Colorimetría

## Caso 1: Color no levanta color

**Situación**: Cliente con nivel 5 teñido hace meses, quiere alcanzar nivel 7

**Comportamiento esperado con v40**:

- El sistema debe detectar que el cabello tiene color artificial (state: "Teñido")
- Debe incluir un paso de DECAPADO antes de la coloración
- Warnings debe incluir: "Color no levanta color. Se requiere decapado previo"
- La fórmula debe incluir primero el proceso de eliminación de color

## Caso 2: Oscurecer de rubio a oscuro

**Situación**: Cliente rubia que quiere ir a castaño oscuro

**Comportamiento esperado con v40**:

- El sistema debe detectar que es un proceso de oscurecimiento (más de 3 niveles)
- Debe usar oxidante de 10 volúmenes, NO 20 volúmenes
- Si la diferencia es mayor a 3 niveles, debe incluir PRE-PIGMENTACIÓN
- Warnings debe incluir: "Pre-pigmentación requerida al oscurecer X niveles"

## Cómo probar:

1. **En el diagnóstico capilar**:
   - Para Caso 1: Asegurarse de indicar "Teñido" o "Colored" en estado
   - Para Caso 2: Indicar nivel alto (8-9) en diagnóstico actual

2. **En color deseado**:
   - Para Caso 1: Seleccionar nivel 7 o superior
   - Para Caso 2: Seleccionar nivel 3-4

3. **Verificar en la fórmula generada**:
   - Revisar sección de warnings
   - Verificar que los pasos incluyan procesos preparatorios
   - Confirmar volumen de oxidante correcto

## Resultado esperado:

- Fórmulas técnicamente correctas
- Procesos seguros que respetan los principios de colorimetría
- Sin resultados inesperados o daño al cabello
