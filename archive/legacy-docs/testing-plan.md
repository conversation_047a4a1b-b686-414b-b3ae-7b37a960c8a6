# Plan Exhaustivo de Testing - Chat Assistant Salonier

**Fecha**: 2025-01-04  
**Objetivo**: Verificar que el chat asistente funciona perfectamente antes de producción

---

## 📋 Resumen Ejecutivo

### Sistema Analizado

- **Chat Store**: Gestión de conversaciones y mensajes con Zustand
- **Edge Function**: chat-assistant (análisis de IA + GPT-4 Vision)
- **UI Components**: ChatGPTInterface, StreamingMessage, SmartSuggestions
- **Funcionalidades**: Conversaciones, análisis de imágenes, streaming, persistencia

### Flujos Críticos Identificados

1. **Creación y gestión de conversaciones**
2. **Envío y recepción de mensajes**
3. **Análisis de imágenes con IA**
4. **Sistema de streaming de respuestas**
5. **Persistencia offline y sincronización**
6. **Manejo de errores y reconexión**

---

## 🚀 FASE 1: Configuración del Entorno de Testing

### 1.1 Instalación de Dependencias de Testing

```bash
# Instalar herramientas de testing
npm install --save-dev jest @testing-library/react-native @testing-library/jest-native react-test-renderer
npm install --save-dev jest-expo @types/jest
npm install --save-dev msw faker @testing-library/react-hooks
npm install --save-dev supertest # Para testing de Edge Functions
```

### 1.2 Configuración Jest

```javascript
// jest.config.js
module.exports = {
  preset: 'jest-expo',
  setupFilesAfterEnv: ['@testing-library/jest-native/extend-expect', './src/__tests__/setup.ts'],
  transformIgnorePatterns: [
    'node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)',
  ],
  collectCoverageFrom: [
    'stores/**/*.{ts,tsx}',
    'components/**/*.{ts,tsx}',
    'utils/**/*.{ts,tsx}',
    '!**/*.d.ts',
    '!**/*.stories.tsx',
    '!**/index.ts',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 85,
      lines: 85,
      statements: 85,
    },
  },
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  testTimeout: 10000,
};
```

### 1.3 Setup de Testing

```typescript
// src/__tests__/setup.ts
import 'react-native-gesture-handler/jestSetup';
import mockAsyncStorage from '@react-native-async-storage/async-storage/jest/async-storage-mock';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => mockAsyncStorage);

// Mock Supabase
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getUser: jest.fn(),
      signInWithPassword: jest.fn(),
    },
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      single: jest.fn(),
    })),
    functions: {
      invoke: jest.fn(),
    },
    storage: {
      from: jest.fn(() => ({
        upload: jest.fn(),
        createSignedUrl: jest.fn(),
      })),
    },
  },
}));

// Mock react-native modules
jest.mock('react-native-reanimated', () => require('react-native-reanimated/mock'));
jest.mock('expo-image-picker', () => ({
  launchImageLibraryAsync: jest.fn(),
  launchCameraAsync: jest.fn(),
  requestMediaLibraryPermissionsAsync: jest.fn(),
  requestCameraPermissionsAsync: jest.fn(),
}));

// Global test utilities
global.__DEV__ = true;
```

---

## 🚀 FASE 2: Tests del Chat Store (Crítico)

### 2.1 Test de Gestión de Conversaciones

```typescript
// stores/__tests__/chat-store.test.ts
import { renderHook, act, waitFor } from '@testing-library/react-hooks';
import { useChatStore } from '../chat-store';
import { supabase } from '@/lib/supabase';

// Mock data
const mockConversation = {
  id: 'conv-1',
  salonId: 'salon-1',
  userId: 'user-1',
  title: 'Test Conversation',
  status: 'active',
  createdAt: new Date(),
  updatedAt: new Date(),
};

const mockMessage = {
  id: 'msg-1',
  conversationId: 'conv-1',
  role: 'user',
  content: 'Test message',
  createdAt: new Date(),
};

describe('ChatStore - Conversaciones', () => {
  beforeEach(() => {
    // Reset store
    useChatStore.setState({
      conversations: [],
      messages: {},
      activeConversationId: null,
      isLoading: false,
      error: null,
    });
    jest.clearAllMocks();
  });

  describe('loadConversations', () => {
    it('debe cargar conversaciones correctamente', async () => {
      // Mock successful response
      (supabase.from as jest.Mock).mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: [mockConversation],
          error: null,
        }),
      });

      const { result } = renderHook(() => useChatStore());

      await act(async () => {
        await result.current.loadConversations();
      });

      expect(result.current.conversations).toHaveLength(1);
      expect(result.current.conversations[0].id).toBe('conv-1');
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
    });

    it('debe manejar errores de carga', async () => {
      const mockError = new Error('Database error');

      (supabase.from as jest.Mock).mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: null,
          error: mockError,
        }),
      });

      const { result } = renderHook(() => useChatStore());

      await act(async () => {
        await result.current.loadConversations();
      });

      expect(result.current.conversations).toHaveLength(0);
      expect(result.current.error).toEqual(mockError);
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('createConversation', () => {
    it('debe crear nueva conversación', async () => {
      // Mock auth user
      (supabase.auth.getUser as jest.Mock).mockResolvedValue({
        data: { user: { id: 'user-1', salonId: 'salon-1' } },
      });

      (supabase.from as jest.Mock).mockReturnValue({
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: mockConversation,
          error: null,
        }),
      });

      const { result } = renderHook(() => useChatStore());

      let newConv;
      await act(async () => {
        newConv = await result.current.createConversation({
          title: 'Test Conversation',
        });
      });

      expect(newConv).toBeDefined();
      expect(result.current.conversations).toHaveLength(1);
      expect(result.current.activeConversationId).toBe('conv-1');
    });

    it('debe manejar error de creación', async () => {
      const mockError = new Error('Create failed');

      (supabase.from as jest.Mock).mockReturnValue({
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: null,
          error: mockError,
        }),
      });

      const { result } = renderHook(() => useChatStore());

      let newConv;
      await act(async () => {
        newConv = await result.current.createConversation({
          title: 'Test Conversation',
        });
      });

      expect(newConv).toBeNull();
      expect(result.current.error).toEqual(mockError);
    });
  });
});
```

### 2.2 Test de Mensajes y Streaming

```typescript
// stores/__tests__/chat-store-messages.test.ts
import { renderHook, act, waitFor } from '@testing-library/react-hooks';
import { useChatStore } from '../chat-store';
import { supabase } from '@/lib/supabase';

describe('ChatStore - Mensajes', () => {
  beforeEach(() => {
    useChatStore.setState({
      conversations: [mockConversation],
      messages: {},
      activeConversationId: 'conv-1',
      isLoading: false,
      error: null,
      streamingMessage: null,
    });
    jest.clearAllMocks();
  });

  describe('sendMessage', () => {
    it('debe enviar mensaje correctamente', async () => {
      // Mock successful Edge Function response
      (supabase.functions.invoke as jest.Mock).mockResolvedValue({
        data: {
          success: true,
          content: 'Respuesta de la IA',
          usage: {
            promptTokens: 50,
            completionTokens: 100,
            totalTokens: 150,
            cost: 0.001,
          },
        },
        error: null,
      });

      const { result } = renderHook(() => useChatStore());

      await act(async () => {
        await result.current.sendMessage('Hola asistente', 'conv-1');
      });

      const messages = result.current.messages['conv-1'];
      expect(messages).toHaveLength(2); // User + Assistant
      expect(messages[0].content).toBe('Hola asistente');
      expect(messages[0].role).toBe('user');
      expect(messages[1].role).toBe('assistant');
      expect(result.current.isSending).toBe(false);
    });

    it('debe manejar mensajes con imágenes', async () => {
      const mockAttachment = {
        type: 'image' as const,
        url: 'data:image/jpeg;base64,mockdata',
        mimeType: 'image/jpeg',
      };

      (supabase.functions.invoke as jest.Mock).mockResolvedValue({
        data: {
          success: true,
          content: 'Veo un cabello rubio nivel 7...',
          usage: { promptTokens: 100, completionTokens: 200 },
        },
        error: null,
      });

      const { result } = renderHook(() => useChatStore());

      await act(async () => {
        await result.current.sendMessage('Analiza esta imagen', 'conv-1', [mockAttachment]);
      });

      const messages = result.current.messages['conv-1'];
      const userMessage = messages.find(m => m.role === 'user');

      expect(userMessage?.hasAttachments).toBe(true);
      expect(userMessage?.attachments?.[0].type).toBe('image');
      expect(messages.find(m => m.role === 'assistant')?.content).toContain('cabello');
    });

    it('debe manejar errores de Edge Function', async () => {
      const mockError = new Error('AI service unavailable');

      (supabase.functions.invoke as jest.Mock).mockResolvedValue({
        data: null,
        error: mockError,
      });

      const { result } = renderHook(() => useChatStore());

      await act(async () => {
        await result.current.sendMessage('Test message', 'conv-1');
      });

      expect(result.current.error).toEqual(mockError);
      expect(result.current.isSending).toBe(false);
      expect(result.current.pendingSyncMessages).toHaveLength(1);
    });
  });

  describe('Streaming', () => {
    it('debe iniciar streaming correctamente', async () => {
      const { result } = renderHook(() => useChatStore());

      act(() => {
        result.current.startStreaming('conv-1', 'msg-streaming');
      });

      expect(result.current.streamingMessage).toEqual({
        conversationId: 'conv-1',
        messageId: 'msg-streaming',
        content: '',
        isComplete: false,
        currentIndex: 0,
      });
      expect(result.current.typingStatus).toBe('thinking');
    });

    it('debe actualizar contenido durante streaming', async () => {
      const { result } = renderHook(() => useChatStore());

      act(() => {
        result.current.startStreaming('conv-1', 'msg-streaming');
        result.current.updateStreamingContent('Hola, soy el asistente');
      });

      expect(result.current.streamingMessage?.content).toBe('Hola, soy el asistente');
      expect(result.current.typingStatus).toBe('writing');
    });

    it('debe completar streaming correctamente', async () => {
      const { result } = renderHook(() => useChatStore());

      act(() => {
        result.current.startStreaming('conv-1', 'msg-streaming');
        result.current.updateStreamingContent('Mensaje completo');
        result.current.completeStreaming();
      });

      expect(result.current.streamingMessage).toBeNull();
      expect(result.current.typingStatus).toBe('idle');

      const messages = result.current.messages['conv-1'];
      expect(messages).toHaveLength(1);
      expect(messages[0].content).toBe('Mensaje completo');
    });
  });
});
```

---

## 🚀 FASE 3: Tests de Edge Function

### 3.1 Test de Chat Assistant Edge Function

```typescript
// supabase/functions/__tests__/chat-assistant.test.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { corsHeaders } from '../_shared/cors.ts';

// Mock environment variables
Deno.env.set('OPENAI_API_KEY', 'test-key');
Deno.env.set('SUPABASE_SERVICE_ROLE_KEY', 'test-service-key');

describe('Chat Assistant Edge Function', () => {
  const baseUrl = 'http://localhost:54321/functions/v1/chat-assistant';

  beforeEach(() => {
    // Reset any global state
  });

  describe('POST /chat-assistant', () => {
    it('debe responder consulta simple correctamente', async () => {
      const requestBody = {
        conversationId: 'test-conv-1',
        message: '¿Cómo corrijo un color naranja?',
        salonId: 'salon-1',
        userId: 'user-1',
        attachments: [],
      };

      const response = await fetch(baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer test-key',
        },
        body: JSON.stringify(requestBody),
      });

      expect(response.status).toBe(200);

      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.content).toBeDefined();
      expect(data.content.length).toBeGreaterThan(50);
      expect(data.usage?.totalTokens).toBeGreaterThan(0);

      // Verificar que la respuesta es relevante
      expect(data.content.toLowerCase()).toMatch(/(naranja|tono|corrección|matiz)/);
    });

    it('debe analizar imagen de cabello', async () => {
      const mockImageBase64 = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAAAAAAAD...'; // Mock base64

      const requestBody = {
        conversationId: 'test-conv-2',
        message: 'Analiza este cabello actual',
        salonId: 'salon-1',
        userId: 'user-1',
        attachments: [
          {
            type: 'image',
            url: mockImageBase64,
            mimeType: 'image/jpeg',
          },
        ],
      };

      const response = await fetch(baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer test-key',
        },
        body: JSON.stringify(requestBody),
      });

      expect(response.status).toBe(200);

      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.content).toBeDefined();

      // Verificar que la respuesta incluye análisis de imagen
      const content = data.content.toLowerCase();
      expect(content).toMatch(/(imagen|foto|observo|veo|nivel|cabello|color)/);
      expect(data.usage?.totalTokens).toBeGreaterThan(100); // Vision API usa más tokens
    });

    it('debe manejar errores de OpenAI', async () => {
      // Mock request que causa error en OpenAI
      const requestBody = {
        conversationId: 'test-conv-error',
        message: '', // Mensaje vacío debería causar error
        salonId: 'salon-1',
        userId: 'user-1',
        attachments: [],
      };

      const response = await fetch(baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer invalid-key', // Key inválida
        },
        body: JSON.stringify(requestBody),
      });

      expect(response.status).toBe(500);

      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBeDefined();
    });

    it('debe validar parámetros requeridos', async () => {
      const invalidRequestBody = {
        // Falta conversationId
        message: 'Test message',
        salonId: 'salon-1',
        // Falta userId
        attachments: [],
      };

      const response = await fetch(baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer test-key',
        },
        body: JSON.stringify(invalidRequestBody),
      });

      expect(response.status).toBe(400);

      const data = await response.json();
      expect(data.error).toMatch(/(required|missing|invalid)/i);
    });

    it('debe respetar límites de rate limiting', async () => {
      const requests = [];

      // Hacer múltiples requests rápidos
      for (let i = 0; i < 10; i++) {
        requests.push(
          fetch(baseUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: 'Bearer test-key',
            },
            body: JSON.stringify({
              conversationId: `test-conv-${i}`,
              message: `Test message ${i}`,
              salonId: 'salon-1',
              userId: 'user-1',
              attachments: [],
            }),
          })
        );
      }

      const responses = await Promise.all(requests);

      // Al menos algunas requests deberían ser exitosas
      const successfulResponses = responses.filter(r => r.status === 200);
      expect(successfulResponses.length).toBeGreaterThan(0);

      // Si hay rate limiting, algunos deberían devolver 429
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      // No es estrictamente necesario que haya rate limiting en test
    });
  });

  describe('OPTIONS /chat-assistant', () => {
    it('debe manejar preflight CORS', async () => {
      const response = await fetch(baseUrl, {
        method: 'OPTIONS',
        headers: {
          Origin: 'http://localhost:3000',
          'Access-Control-Request-Method': 'POST',
          'Access-Control-Request-Headers': 'Content-Type, Authorization',
        },
      });

      expect(response.status).toBe(200);
      expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
      expect(response.headers.get('Access-Control-Allow-Methods')).toContain('POST');
    });
  });
});
```

---

## 🚀 FASE 4: Tests de Componentes UI

### 4.1 Test de ChatGPTInterface

```typescript
// components/chat/__tests__/ChatGPTInterface.test.tsx
import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import ChatGPTInterface from '../ChatGPTInterface';
import { useChatStore } from '@/stores/chat-store';
import { useAuthStore } from '@/stores/auth-store';

// Mock stores
jest.mock('@/stores/chat-store');
jest.mock('@/stores/auth-store');

const mockChatStore = {
  conversations: [],
  messages: {},
  activeConversationId: null,
  isSending: false,
  isLoading: false,
  error: null,
  streamingMessage: null,
  typingStatus: 'idle',
  sendMessage: jest.fn(),
  loadMessages: jest.fn(),
  loadConversations: jest.fn(),
  setActiveConversation: jest.fn(),
  createConversation: jest.fn(),
};

const mockAuthStore = {
  user: {
    id: 'user-1',
    salonId: 'salon-1',
    name: 'Test User',
  },
};

describe('ChatGPTInterface', () => {
  beforeEach(() => {
    (useChatStore as jest.Mock).mockReturnValue(mockChatStore);
    (useAuthStore as jest.Mock).mockReturnValue(mockAuthStore);
    jest.clearAllMocks();
  });

  const renderComponent = (props = {}) => {
    return render(
      <NavigationContainer>
        <ChatGPTInterface {...props} />
      </NavigationContainer>
    );
  };

  describe('Renderizado inicial', () => {
    it('debe mostrar welcome screen cuando no hay conversaciones', () => {
      const { getByText } = renderComponent();

      expect(getByText('Salonier Assistant')).toBeTruthy();
      expect(getByText('¿En qué puedo ayudarte hoy?')).toBeTruthy();
    });

    it('debe mostrar input de mensaje', () => {
      const { getByPlaceholderText } = renderComponent();

      expect(getByPlaceholderText('Pregúntame lo que necesites...')).toBeTruthy();
    });

    it('debe mostrar botones de acción', () => {
      const { getByTestId } = renderComponent();

      // Camera button should be present
      const cameraButton = getByTestId('camera-button');
      expect(cameraButton).toBeTruthy();
    });
  });

  describe('Envío de mensajes', () => {
    it('debe enviar mensaje cuando se presiona botón send', async () => {
      const { getByPlaceholderText, getByTestId } = renderComponent();

      const input = getByPlaceholderText('Pregúntame lo que necesites...');
      const sendButton = getByTestId('send-button');

      fireEvent.changeText(input, 'Hola asistente');
      fireEvent.press(sendButton);

      expect(mockChatStore.sendMessage).toHaveBeenCalledWith(
        'Hola asistente',
        null, // No active conversation
        undefined // No attachments
      );
    });

    it('debe limpiar input después de enviar', async () => {
      const { getByPlaceholderText, getByTestId } = renderComponent();

      const input = getByPlaceholderText('Pregúntame lo que necesites...');
      const sendButton = getByTestId('send-button');

      fireEvent.changeText(input, 'Test message');
      fireEvent.press(sendButton);

      await waitFor(() => {
        expect(input.props.value).toBe('');
      });
    });

    it('debe deshabilitar envío cuando está enviando', () => {
      (useChatStore as jest.Mock).mockReturnValue({
        ...mockChatStore,
        isSending: true,
      });

      const { getByTestId } = renderComponent();
      const sendButton = getByTestId('send-button');

      expect(sendButton.props.disabled).toBe(true);
    });
  });

  describe('Manejo de imágenes', () => {
    it('debe abrir selector de imágenes al presionar botón cámara', () => {
      const { getByTestId } = renderComponent();
      const cameraButton = getByTestId('camera-button');

      fireEvent.press(cameraButton);

      // Verify that image picker options are shown
      // This would depend on your ActionSheet implementation
    });

    it('debe mostrar loading mientras procesa imagen', async () => {
      // Mock image processing
      const mockImagePicker = require('expo-image-picker');
      mockImagePicker.launchImageLibraryAsync.mockResolvedValue({
        canceled: false,
        assets: [{ uri: 'mock-image-uri' }],
      });

      const { getByTestId } = renderComponent();
      const cameraButton = getByTestId('camera-button');

      fireEvent.press(cameraButton);

      // Simulate selecting gallery option
      // Implementation depends on your ActionSheet
    });
  });

  describe('Streaming de respuestas', () => {
    it('debe mostrar mensaje streaming mientras la IA responde', () => {
      const mockStreamingMessage = {
        conversationId: 'conv-1',
        messageId: 'msg-1',
        content: 'Estoy analizando tu consulta...',
        isComplete: false,
      };

      (useChatStore as jest.Mock).mockReturnValue({
        ...mockChatStore,
        activeConversationId: 'conv-1',
        streamingMessage: mockStreamingMessage,
        messages: {
          'conv-1': [
            {
              id: 'user-1',
              role: 'user',
              content: 'Test question',
              createdAt: new Date(),
            },
          ],
        },
      });

      const { getByText } = renderComponent();

      expect(getByText('Estoy analizando tu consulta...')).toBeTruthy();
    });

    it('debe mostrar typing indicator cuando está procesando', () => {
      (useChatStore as jest.Mock).mockReturnValue({
        ...mockChatStore,
        isSending: true,
        typingStatus: 'thinking',
      });

      const { getByTestId } = renderComponent();

      expect(getByTestId('typing-indicator')).toBeTruthy();
    });
  });

  describe('Gestión de errores', () => {
    it('debe mostrar error cuando falla el envío', () => {
      const mockError = new Error('Network error');
      (useChatStore as jest.Mock).mockReturnValue({
        ...mockChatStore,
        error: mockError,
      });

      const { getByText } = renderComponent();

      expect(getByText(/error/i)).toBeTruthy();
    });

    it('debe permitir reintentar después de error', async () => {
      const mockError = new Error('Network error');
      (useChatStore as jest.Mock).mockReturnValue({
        ...mockChatStore,
        error: mockError,
      });

      const { getByText } = renderComponent();
      const retryButton = getByText(/reintentar/i);

      fireEvent.press(retryButton);

      expect(mockChatStore.clearError).toHaveBeenCalled();
    });
  });
});
```

### 4.2 Test de StreamingMessage

```typescript
// components/chat/__tests__/StreamingMessage.test.tsx
import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import StreamingMessage from '../StreamingMessage';

describe('StreamingMessage', () => {
  const defaultProps = {
    content: 'This is a test message from the AI assistant.',
    isStreaming: false,
    isUser: false,
  };

  it('debe renderizar contenido estático correctamente', () => {
    const { getByText } = render(<StreamingMessage {...defaultProps} />);

    expect(getByText('This is a test message from the AI assistant.')).toBeTruthy();
  });

  it('debe mostrar efecto streaming cuando isStreaming=true', async () => {
    const longContent = 'This is a very long message that should be streamed character by character to simulate the typing effect of an AI assistant responding in real time.';

    const { getByText } = render(
      <StreamingMessage
        content={longContent}
        isStreaming={true}
        isUser={false}
      />
    );

    // Should start with empty or partial content
    await waitFor(() => {
      const element = getByText(/This is a/);
      expect(element).toBeTruthy();
    }, { timeout: 100 });

    // Should eventually show full content
    await waitFor(() => {
      const element = getByText(longContent);
      expect(element).toBeTruthy();
    }, { timeout: 3000 });
  });

  it('debe mostrar botón "Ver más" para contenido largo', () => {
    const longContent = 'A'.repeat(300); // Content longer than maxPreviewLength

    const { getByText } = render(
      <StreamingMessage
        content={longContent}
        maxPreviewLength={200}
        isUser={false}
      />
    );

    expect(getByText('Ver más detalles')).toBeTruthy();
  });

  it('debe expandir contenido al presionar "Ver más"', () => {
    const longContent = 'A'.repeat(300);

    const { getByText, queryByText } = render(
      <StreamingMessage
        content={longContent}
        maxPreviewLength={200}
        isUser={false}
      />
    );

    const expandButton = getByText('Ver más detalles');
    fireEvent.press(expandButton);

    expect(queryByText('Ver menos')).toBeTruthy();
  });

  it('debe mostrar indicador de streaming', () => {
    const { getByTestId } = render(
      <StreamingMessage
        content="Streaming content..."
        isStreaming={true}
        isUser={false}
      />
    );

    expect(getByTestId('streaming-indicator')).toBeTruthy();
  });

  it('debe manejar mensajes de usuario sin streaming', () => {
    const { getByText, queryByTestId } = render(
      <StreamingMessage
        content="User message"
        isStreaming={false}
        isUser={true}
      />
    );

    expect(getByText('User message')).toBeTruthy();
    expect(queryByTestId('streaming-indicator')).toBeNull();
  });

  it('debe renderizar markdown correctamente', () => {
    const markdownContent = '**Bold text** and *italic text* with `code`';

    const { getByText } = render(
      <StreamingMessage
        content={markdownContent}
        isUser={false}
      />
    );

    // Markdown should be rendered (exact implementation depends on markdown library)
    expect(getByText(/Bold text/)).toBeTruthy();
  });
});
```

---

## 🚀 FASE 5: Tests de Integración

### 5.1 Test de Flujo Completo de Chat

```typescript
// __tests__/integration/chat-flow.test.tsx
import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import ChatGPTInterface from '@/components/chat/ChatGPTInterface';
import { useChatStore } from '@/stores/chat-store';
import { supabase } from '@/lib/supabase';

// Mock responses for different scenarios
const mockResponses = {
  simpleQuery: {
    success: true,
    content: 'Para corregir un color naranja, necesitas aplicar un matizador ceniza...',
    usage: { promptTokens: 50, completionTokens: 100, totalTokens: 150, cost: 0.001 },
  },
  imageAnalysis: {
    success: true,
    content: 'Observo un cabello con nivel natural 6, con reflejos dorados. El cabello presenta...',
    usage: { promptTokens: 150, completionTokens: 200, totalTokens: 350, cost: 0.002 },
  },
};

describe('Chat Flow Integration', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock successful database operations
    (supabase.from as jest.Mock).mockImplementation((table) => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({ data: {}, error: null }),
    }));
  });

  it('debe completar flujo de consulta simple', async () => {
    // Mock Edge Function response
    (supabase.functions.invoke as jest.Mock).mockResolvedValue({
      data: mockResponses.simpleQuery,
      error: null,
    });

    const { getByPlaceholderText, getByTestId, getByText } = render(
      <NavigationContainer>
        <ChatGPTInterface />
      </NavigationContainer>
    );

    // 1. Usuario escribe mensaje
    const input = getByPlaceholderText('Pregúntame lo que necesites...');
    fireEvent.changeText(input, '¿Cómo corrijo un color naranja?');

    // 2. Usuario envía mensaje
    const sendButton = getByTestId('send-button');
    fireEvent.press(sendButton);

    // 3. Verificar que se muestra loading/typing
    await waitFor(() => {
      expect(getByTestId('typing-indicator')).toBeTruthy();
    });

    // 4. Verificar respuesta de la IA
    await waitFor(() => {
      expect(getByText(/matizador ceniza/)).toBeTruthy();
    }, { timeout: 5000 });

    // 5. Verificar que se creó conversación
    expect(supabase.from).toHaveBeenCalledWith('chat_conversations');

    // 6. Verificar que se guardaron mensajes
    expect(supabase.functions.invoke).toHaveBeenCalledWith('chat-assistant', {
      body: expect.objectContaining({
        message: '¿Cómo corrijo un color naranja?',
        attachments: [],
      }),
    });
  });

  it('debe completar flujo de análisis de imagen', async () => {
    // Mock image picker
    const mockImagePicker = require('expo-image-picker');
    mockImagePicker.launchImageLibraryAsync.mockResolvedValue({
      canceled: false,
      assets: [{ uri: 'mock://image.jpg', fileSize: 1024000 }],
    });

    // Mock image compression
    jest.mock('@/utils/image-processor', () => ({
      ImageProcessor: {
        compressForUpload: jest.fn().mockResolvedValue('mock-base64-data'),
      },
    }));

    // Mock Edge Function response
    (supabase.functions.invoke as jest.Mock).mockResolvedValue({
      data: mockResponses.imageAnalysis,
      error: null,
    });

    const { getByTestId, getByText } = render(
      <NavigationContainer>
        <ChatGPTInterface />
      </NavigationContainer>
    );

    // 1. Usuario presiona botón de cámara
    const cameraButton = getByTestId('camera-button');
    fireEvent.press(cameraButton);

    // 2. Simular selección de imagen (esto dependería de tu implementación de ActionSheet)
    // For now, we'll directly test the image processing logic

    // 3. Verificar que se procesa imagen
    await waitFor(() => {
      expect(mockImagePicker.launchImageLibraryAsync).toHaveBeenCalled();
    });

    // 4. Verificar respuesta con análisis de imagen
    await waitFor(() => {
      expect(getByText(/nivel natural 6/)).toBeTruthy();
    }, { timeout: 10000 });

    // 5. Verificar que se envió imagen a Edge Function
    expect(supabase.functions.invoke).toHaveBeenCalledWith('chat-assistant', {
      body: expect.objectContaining({
        attachments: expect.arrayContaining([
          expect.objectContaining({
            type: 'image',
            mimeType: 'image/jpeg',
          }),
        ]),
      }),
    });
  });

  it('debe manejar errores de red correctamente', async () => {
    // Mock network error
    (supabase.functions.invoke as jest.Mock).mockRejectedValue(
      new Error('Network request failed')
    );

    const { getByPlaceholderText, getByTestId, getByText } = render(
      <NavigationContainer>
        <ChatGPTInterface />
      </NavigationContainer>
    );

    // 1. Usuario envía mensaje
    const input = getByPlaceholderText('Pregúntame lo que necesites...');
    fireEvent.changeText(input, 'Test message');

    const sendButton = getByTestId('send-button');
    fireEvent.press(sendButton);

    // 2. Verificar que se muestra error
    await waitFor(() => {
      expect(getByText(/error/i)).toBeTruthy();
    });

    // 3. Verificar que mensaje se guarda para sync posterior
    const store = useChatStore.getState();
    expect(store.pendingSyncMessages.length).toBeGreaterThan(0);
  });

  it('debe sincronizar mensajes offline cuando se recupera conexión', async () => {
    const store = useChatStore.getState();

    // Simular mensaje offline
    const offlineMessage = {
      id: 'offline-1',
      conversationId: 'conv-1',
      role: 'user' as const,
      content: 'Offline message',
      createdAt: new Date(),
      synced: false,
      localId: 'local-1',
    };

    // Agregar mensaje pendiente de sync
    useChatStore.setState({
      pendingSyncMessages: [offlineMessage],
    });

    // Mock successful sync
    (supabase.functions.invoke as jest.Mock).mockResolvedValue({
      data: mockResponses.simpleQuery,
      error: null,
    });

    // Ejecutar sync
    await store.syncPendingMessages();

    // Verificar que mensaje se sincronizó
    expect(supabase.functions.invoke).toHaveBeenCalled();
    expect(store.pendingSyncMessages).toHaveLength(0);
  });
});
```

---

## 🚀 FASE 6: Tests de Performance

### 6.1 Test de Rendimiento del Chat

```typescript
// __tests__/performance/chat-performance.test.tsx
import React from 'react';
import { render } from '@testing-library/react-native';
import ChatGPTInterface from '@/components/chat/ChatGPTInterface';
import { performance } from 'perf_hooks';

describe('Chat Performance Tests', () => {
  it('debe renderizar rápidamente con muchas conversaciones', () => {
    // Create large dataset
    const manyConversations = Array.from({ length: 100 }, (_, i) => ({
      id: `conv-${i}`,
      title: `Conversation ${i}`,
      salonId: 'salon-1',
      userId: 'user-1',
      status: 'active' as const,
      createdAt: new Date(),
      updatedAt: new Date(),
      messageCount: Math.floor(Math.random() * 50),
      lastMessage: `Last message in conversation ${i}`,
    }));

    const mockStore = {
      conversations: manyConversations,
      messages: {},
      activeConversationId: null,
      isLoading: false,
      isSending: false,
      error: null,
      streamingMessage: null,
      typingStatus: 'idle' as const,
      loadConversations: jest.fn(),
      loadMessages: jest.fn(),
      sendMessage: jest.fn(),
      setActiveConversation: jest.fn(),
      createConversation: jest.fn(),
    };

    jest.mocked(require('@/stores/chat-store').useChatStore).mockReturnValue(mockStore);

    const startTime = performance.now();

    render(<ChatGPTInterface />);

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    // Should render in less than 100ms even with 100 conversations
    expect(renderTime).toBeLessThan(100);
  });

  it('debe manejar streaming rápido sin lag', async () => {
    const longContent = 'A'.repeat(1000); // Very long message

    const { rerender } = render(
      <StreamingMessage
        content=""
        isStreaming={true}
        isUser={false}
      />
    );

    const startTime = performance.now();

    // Simulate rapid streaming updates
    for (let i = 0; i < longContent.length; i += 10) {
      rerender(
        <StreamingMessage
          content={longContent.slice(0, i)}
          isStreaming={true}
          isUser={false}
        />
      );
    }

    const endTime = performance.now();
    const streamingTime = endTime - startTime;

    // Should handle rapid updates without significant performance impact
    expect(streamingTime).toBeLessThan(200);
  });

  it('debe mantener 60fps durante animaciones', () => {
    // This test would require integration with React Native's performance monitoring
    // For now, we'll test that animations don't block the main thread

    const animationDuration = 1000; // 1 second
    const expectedFrames = 60; // 60fps

    // Mock frame counting would go here
    // In a real test, you'd integrate with React Native's performance APIs

    expect(true).toBe(true); // Placeholder
  });
});
```

---

## 🚀 FASE 7: Tests E2E con Detox

### 7.1 Configuración Detox

```javascript
// .detoxrc.js
module.exports = {
  testRunner: {
    args: {
      $0: 'jest',
      config: 'e2e/jest.config.js',
    },
    jest: {
      setupFilesAfterEnv: ['./e2e/init.js'],
    },
  },
  apps: {
    'ios.debug': {
      type: 'ios.app',
      binaryPath: 'ios/build/Build/Products/Debug-iphonesimulator/SalonierApp.app',
      build:
        'xcodebuild -workspace ios/SalonierApp.xcworkspace -scheme SalonierApp -configuration Debug -sdk iphonesimulator -derivedDataPath ios/build',
    },
    'android.debug': {
      type: 'android.apk',
      binaryPath: 'android/app/build/outputs/apk/debug/app-debug.apk',
      build: 'cd android && ./gradlew assembleDebug assembleAndroidTest -DtestBuildType=debug',
    },
  },
  devices: {
    simulator: {
      type: 'ios.simulator',
      device: {
        type: 'iPhone 15 Pro',
      },
    },
    emulator: {
      type: 'android.emulator',
      device: {
        avdName: 'Pixel_3a_API_30_x86',
      },
    },
  },
  configurations: {
    'ios.sim.debug': {
      device: 'simulator',
      app: 'ios.debug',
    },
    'android.emu.debug': {
      device: 'emulator',
      app: 'android.debug',
    },
  },
};
```

### 7.2 Test E2E del Chat Assistant

```typescript
// e2e/chat-assistant.e2e.ts
describe('Chat Assistant E2E', () => {
  beforeAll(async () => {
    await device.launchApp();
  });

  beforeEach(async () => {
    await device.reloadReactNative();
  });

  it('debe completar flujo completo de consulta simple', async () => {
    // 1. Navegar al chat assistant
    await element(by.id('assistant-tab')).tap();

    // 2. Verificar welcome screen
    await expect(element(by.text('Salonier Assistant'))).toBeVisible();

    // 3. Escribir consulta
    await element(by.id('message-input')).typeText('¿Cómo corrijo un color naranja?');

    // 4. Enviar mensaje
    await element(by.id('send-button')).tap();

    // 5. Verificar typing indicator
    await expect(element(by.id('typing-indicator'))).toBeVisible();

    // 6. Esperar respuesta (máximo 30 segundos para IA)
    await waitFor(element(by.text('matizador')))
      .toBeVisible()
      .withTimeout(30000);

    // 7. Verificar que se creó conversación
    await expect(element(by.id('conversation-item-0'))).toBeVisible();
  });

  it('debe manejar análisis de imagen completo', async () => {
    // 1. Ir al chat
    await element(by.id('assistant-tab')).tap();

    // 2. Presionar botón cámara
    await element(by.id('camera-button')).tap();

    // 3. Seleccionar galería
    await element(by.text('🖼️ Galería - Color Actual')).tap();

    // 4. Seleccionar imagen de prueba (simulado)
    // En un test real, esto interactuaría con el sistema de archivos
    await device.selectPhoto('test-hair-image.jpg');

    // 5. Verificar que se procesa imagen
    await expect(element(by.text('🔍 Analizando imagen...'))).toBeVisible();

    // 6. Esperar análisis de IA
    await waitFor(element(by.text('observo')))
      .toBeVisible()
      .withTimeout(45000);

    // 7. Verificar que la respuesta incluye análisis detallado
    await expect(element(by.text('nivel'))).toBeVisible();
    await expect(element(by.text('cabello'))).toBeVisible();
  });

  it('debe mostrar streaming de respuestas', async () => {
    // 1. Ir al chat
    await element(by.id('assistant-tab')).tap();

    // 2. Hacer pregunta larga que genere respuesta larga
    await element(by.id('message-input')).typeText(
      'Explícame paso a paso cómo hacer un balayage perfecto desde cero'
    );

    // 3. Enviar mensaje
    await element(by.id('send-button')).tap();

    // 4. Verificar estados de typing
    await expect(element(by.id('typing-indicator'))).toBeVisible();

    // 5. Verificar que aparece texto gradualmente
    await waitFor(element(by.text('balayage')))
      .toBeVisible()
      .withTimeout(10000);

    // 6. Verificar streaming completo
    await waitFor(element(by.text('coloración')))
      .toBeVisible()
      .withTimeout(30000);
  });

  it('debe manejar errores de conexión', async () => {
    // 1. Desactivar conexión
    await device.setNetworkConditions({
      offline: true,
    });

    // 2. Ir al chat
    await element(by.id('assistant-tab')).tap();

    // 3. Intentar enviar mensaje
    await element(by.id('message-input')).typeText('Test offline message');
    await element(by.id('send-button')).tap();

    // 4. Verificar error
    await expect(element(by.text('Error de conexión'))).toBeVisible();

    // 5. Reactivar conexión
    await device.setNetworkConditions({
      offline: false,
    });

    // 6. Verificar retry automático
    await waitFor(element(by.text('conectado')))
      .toBeVisible()
      .withTimeout(10000);
  });

  it('debe navegar entre conversaciones', async () => {
    // 1. Crear primera conversación
    await element(by.id('assistant-tab')).tap();
    await element(by.id('message-input')).typeText('Primera consulta');
    await element(by.id('send-button')).tap();

    // 2. Esperar respuesta
    await waitFor(element(by.id('message-assistant-0')))
      .toBeVisible()
      .withTimeout(30000);

    // 3. Crear segunda conversación
    await element(by.id('new-conversation-button')).tap();
    await element(by.id('message-input')).typeText('Segunda consulta');
    await element(by.id('send-button')).tap();

    // 4. Verificar que hay 2 conversaciones
    await expect(element(by.id('conversation-item-0'))).toBeVisible();
    await expect(element(by.id('conversation-item-1'))).toBeVisible();

    // 5. Cambiar a primera conversación
    await element(by.id('conversation-item-0')).tap();

    // 6. Verificar contenido correcto
    await expect(element(by.text('Primera consulta'))).toBeVisible();
  });
});
```

---

## 🚀 FASE 8: Scripts de Automatización

### 8.1 Scripts de Testing

```bash
#!/bin/bash
# scripts/run-tests.sh

echo "🚀 Ejecutando Suite Completa de Testing - Salonier Chat Assistant"
echo "=================================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Función para logs
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

# Variables
FAILED_TESTS=0
TOTAL_TESTS=0

# Función para ejecutar tests y capturar resultados
run_test_suite() {
    local suite_name=$1
    local command=$2

    log "Ejecutando $suite_name..."

    if eval $command; then
        log "✅ $suite_name - PASSED"
    else
        error "❌ $suite_name - FAILED"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi

    TOTAL_TESTS=$((TOTAL_TESTS + 1))
}

# 1. Linting y Type Checking
log "FASE 1: Análisis Estático"
run_test_suite "ESLint" "npx eslint stores/ components/ --ext .ts,.tsx"
run_test_suite "TypeScript" "npx tsc --noEmit"

# 2. Unit Tests
log "FASE 2: Tests Unitarios"
run_test_suite "Chat Store Tests" "npm test stores/__tests__/chat-store.test.ts"
run_test_suite "Message Store Tests" "npm test stores/__tests__/chat-store-messages.test.ts"
run_test_suite "Streaming Message Tests" "npm test components/chat/__tests__/StreamingMessage.test.tsx"

# 3. Integration Tests
log "FASE 3: Tests de Integración"
run_test_suite "Chat Flow Integration" "npm test __tests__/integration/chat-flow.test.tsx"
run_test_suite "UI Component Integration" "npm test components/chat/__tests__/ChatGPTInterface.test.tsx"

# 4. Performance Tests
log "FASE 4: Tests de Performance"
run_test_suite "Chat Performance" "npm test __tests__/performance/chat-performance.test.tsx"

# 5. Coverage Report
log "FASE 5: Generando Reporte de Coverage"
run_test_suite "Coverage Report" "npm test -- --coverage --watchAll=false"

# 6. E2E Tests (opcional, requiere simulador)
if [[ "$1" == "--e2e" ]]; then
    log "FASE 6: Tests E2E"
    run_test_suite "E2E Chat Flow" "detox test --configuration ios.sim.debug"
fi

# Resumen final
echo ""
echo "=================================================================="
log "RESUMEN DE TESTING"
echo "=================================================================="

if [ $FAILED_TESTS -eq 0 ]; then
    log "🎉 TODOS LOS TESTS PASARON ($TOTAL_TESTS/$TOTAL_TESTS)"
    log "✅ Chat Assistant listo para producción"
    exit 0
else
    error "❌ $FAILED_TESTS/$TOTAL_TESTS tests fallaron"
    error "🚨 Revisar y corregir antes de desplegar"
    exit 1
fi
```

### 8.2 Script de Testing Continuo

```bash
#!/bin/bash
# scripts/watch-tests.sh

echo "🔄 Iniciando Testing Continuo - Salonier Chat Assistant"

# Watch mode para desarrollo
npm test -- --watch --coverage --verbose

# Alternativamente, watch específico para chat
# npm test -- --watch --testPathPattern="chat" --coverage
```

### 8.3 Script de Validación Pre-Commit

```bash
#!/bin/bash
# scripts/pre-commit-tests.sh

echo "🔍 Validación Pre-Commit - Chat Assistant"

# Quick tests críticos antes de commit
npm test stores/__tests__/chat-store.test.ts --watchAll=false
npm test components/chat/__tests__/ChatGPTInterface.test.tsx --watchAll=false

if [ $? -eq 0 ]; then
    echo "✅ Tests críticos pasaron - Commit permitido"
    exit 0
else
    echo "❌ Tests críticos fallaron - Commit bloqueado"
    exit 1
fi
```

---

## 🚀 FASE 9: Checklist de Validación

### 9.1 Checklist Pre-Producción

```markdown
# Checklist de Testing - Chat Assistant Salonier

## ✅ Tests Unitarios (Obligatorio)

- [ ] ChatStore - loadConversations()
- [ ] ChatStore - createConversation()
- [ ] ChatStore - sendMessage()
- [ ] ChatStore - streaming functions
- [ ] ChatStore - error handling
- [ ] StreamingMessage - rendering
- [ ] StreamingMessage - animation
- [ ] SmartSuggestions - contextuales

## ✅ Tests de Integración (Obligatorio)

- [ ] Flujo completo: mensaje → IA → respuesta
- [ ] Flujo imágenes: cámara → análisis → respuesta
- [ ] Manejo de errores de red
- [ ] Sincronización offline
- [ ] Navegación entre conversaciones

## ✅ Tests de Edge Function (Crítico)

- [ ] Respuesta a consultas simples
- [ ] Análisis de imágenes GPT-4 Vision
- [ ] Manejo de errores OpenAI
- [ ] Validación de parámetros
- [ ] Rate limiting
- [ ] CORS headers

## ✅ Tests de Performance (Recomendado)

- [ ] Renderizado con 100+ conversaciones < 100ms
- [ ] Streaming sin lag < 200ms
- [ ] Animaciones 60fps
- [ ] Memory leaks check

## ✅ Tests E2E (Opcional)

- [ ] Flujo completo iOS
- [ ] Flujo completo Android
- [ ] Análisis imagen real
- [ ] Navegación conversaciones
- [ ] Error recovery

## ✅ Métricas de Coverage (Obligatorio)

- [ ] Statements: >85%
- [ ] Branches: >80%
- [ ] Functions: >85%
- [ ] Lines: >85%

## ✅ Validaciones Funcionales (Crítico)

- [ ] IA responde en <30 segundos
- [ ] Imágenes se analizan correctamente
- [ ] Streaming funciona sin cortes
- [ ] Conversaciones se persisten
- [ ] Errores se manejan gracefully
- [ ] UI responsive en todas las pantallas

## ✅ Validaciones de Seguridad (Obligatorio)

- [ ] No hay API keys en logs
- [ ] Imágenes se anonimizan
- [ ] RLS funciona correctamente
- [ ] CORS configurado apropiadamente
- [ ] Rate limiting implementado

## ✅ Validaciones de UX (Recomendado)

- [ ] Loading states visibles
- [ ] Error messages claros
- [ ] Navegación intuitiva
- [ ] Accesibilidad básica
- [ ] Performance percibido bueno
```

### 9.2 Criterios de Aceptación

```typescript
// criteria/acceptance-tests.ts

export const ACCEPTANCE_CRITERIA = {
  performance: {
    maxResponseTime: 30000, // 30 segundos máximo para IA
    maxRenderTime: 100, // 100ms máximo para renderizar
    maxMemoryUsage: 512, // 512MB máximo
  },

  reliability: {
    minUptime: 99.5, // 99.5% uptime mínimo
    maxErrorRate: 1, // <1% error rate
    recoveryTime: 5000, // 5s máximo para recuperarse
  },

  usability: {
    maxTapsToSend: 2, // Máximo 2 taps para enviar mensaje
    maxTapsToImage: 1, // Máximo 1 tap para imagen + contexto
    minContrastRatio: 4.5, // WCAG AA compliance
  },

  security: {
    requiresAuth: true,
    requiresRLS: true,
    requiresImageAnonymization: true,
    maxLogLevel: 'info', // No debug logs en producción
  },
};
```

---

## 🚀 FASE 10: Ejecución del Plan

### 10.1 Cronograma de Implementación

```
Semana 1: Setup y Tests Unitarios
├── Día 1-2: Configuración Jest + dependencias
├── Día 3-4: Tests Chat Store
└── Día 5: Tests componentes UI

Semana 2: Tests de Integración y Edge Function
├── Día 1-2: Tests flujo completo chat
├── Día 3-4: Tests Edge Function
└── Día 5: Tests manejo de errores

Semana 3: Performance y E2E
├── Día 1-2: Tests de performance
├── Día 3-4: Setup Detox + E2E básicos
└── Día 5: Optimizaciones y fixes

Semana 4: Validación y Documentación
├── Día 1-2: Ejecutar suite completa
├── Día 3: Fixes de tests fallidos
├── Día 4: Coverage + métricas
└── Día 5: Documentación final
```

### 10.2 Comandos de Ejecución

```bash
# Instalación inicial
npm install --save-dev jest @testing-library/react-native @testing-library/jest-native react-test-renderer jest-expo

# Ejecutar tests específicos
npm test chat-store # Solo chat store
npm test chat-interface # Solo UI
npm test integration # Solo integración
npm test performance # Solo performance

# Suite completa
npm run test:complete # Ejecuta todo
npm run test:ci # Para CI/CD
npm run test:coverage # Con coverage

# E2E (requiere setup)
npm run test:e2e:ios
npm run test:e2e:android
```

---

## 📊 Métricas de Éxito

### Objetivos Cuantitativos

- **Coverage**: >85% en todas las métricas
- **Performance**: <100ms renderizado, <30s respuesta IA
- **Reliability**: <1% error rate, >99.5% uptime
- **Tests**: 100% tests críticos passing

### Objetivos Cualitativos

- **Confianza**: Desarrolladores seguros de desplegar
- **Mantenibilidad**: Tests fáciles de entender y modificar
- **Documentación**: Testing plan completo y actualizado
- **Automatización**: Pipeline CI/CD con tests automáticos

---

## 🔚 Conclusión

Este plan exhaustivo de testing garantiza que el Chat Assistant de Salonier funcione perfectamente en producción. La implementación sistemática de estos tests proporcionará:

1. **Confianza total** en la funcionalidad crítica
2. **Detección temprana** de regresiones
3. **Performance óptimo** en todas las condiciones
4. **Experiencia de usuario** consistente y fluida
5. **Mantenimiento fácil** del código a largo plazo

El plan está diseñado para ser **ejecutable inmediatamente** y proporcionar **valor desde el primer día**. Cada test está justificado por un flujo crítico del usuario y contribuye directamente a la calidad del producto final.
