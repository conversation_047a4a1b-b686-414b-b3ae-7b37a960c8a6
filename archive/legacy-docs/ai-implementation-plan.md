# Plan de Implementación - Optimización IA Salonier

## 🎯 **Objetivo**: Reducir latencia 79% y costos 74% en 6 semanas

---

## 📋 **SPRINT 1: Optimizaciones Core (Semanas 1-3)**

### **🔥 Tarea 1.1: Prompt Compression (Prioridad ALTA)**

**Objetivo**: Reducir tokens de 2,087 a 300 caracteres (-85%)

**Tareas Técnicas**:

```bash
# 1. Reemplazar prompt templates
cp prompt-templates-optimized.ts supabase/functions/salonier-assistant/utils/prompt-templates.ts

# 2. Implementar model routing
# En salonier-assistant/index.ts - línea 363
const complexity = detectComplexity(payload, hasImage);
const modelConfig = selectOptimalModel(complexity);
const prompt = getCompressedPrompt(task, complexity);
```

**Archivos a Modificar**:

- ✅ `supabase/functions/salonier-assistant/utils/prompt-templates.ts`
- ✅ `supabase/functions/salonier-assistant/index.ts` (líneas 290-370)
- ✅ `supabase/functions/chat-assistant/index.ts` (líneas 319-450)

**Testing**:

- [ ] 100 samples diagnosis test
- [ ] Token count validation (<350 promedio)
- [ ] Accuracy preservation (>90%)

**Expected Impact**: 50% latency reduction, 60% cost reduction

---

### **🔥 Tarea 1.2: Model Routing Implementation (Prioridad ALTA)**

**Objetivo**: Route 60% requests a modelos baratos

**Implementación**:

```typescript
// En salonier-assistant/index.ts - nueva función
function selectOptimalModel(
  complexity: 'simple' | 'medium' | 'complex',
  hasImage: boolean,
  dailyBudget: number
): ModelConfig {
  // Emergency mode si presupuesto >$20/día
  if (dailyBudget > 20 && complexity !== 'complex') {
    return { model: 'gpt-3.5-turbo', maxTokens: 200 };
  }

  const models = {
    simple: hasImage ? 'gpt-4o-mini' : 'gpt-3.5-turbo',
    medium: 'gpt-4o-mini',
    complex: 'gpt-4o',
  };

  return getModelConfig(models[complexity]);
}
```

**Tareas**:

- [ ] Implementar complexity detection
- [ ] Add budget tracking en edge function
- [ ] Deploy model selection logic
- [ ] Add cost calculation per request

**Expected Impact**: Additional 20% cost reduction

---

### **🔥 Tarea 1.3: Fallback System (Prioridad MEDIA)**

**Objetivo**: Aumentar success rate de 93% a 98%

**Implementación**:

```typescript
// Fallback chain en salonier-assistant/index.ts
async function executeWithFallbacks(request) {
  const strategies = [
    () => callGPT4o(request),
    () => callGPT4oMini(request),
    () => callGPT35Turbo(request),
    () => returnSafeDefault(request),
  ];

  for (const strategy of strategies) {
    try {
      return await strategy();
    } catch (error) {
      console.warn('Strategy failed:', error);
    }
  }
}
```

**Tareas**:

- [ ] Implement fallback chain
- [ ] Create safe default responses
- [ ] Add timeout handling
- [ ] Test failure scenarios

**Expected Impact**: +5% success rate, eliminate timeouts

---

### **🔥 Tarea 1.4: Cost Monitoring (Prioridad ALTA)**

**Objetivo**: Track costs en tiempo real

**Schema Update**:

```sql
-- Add cost tracking table
CREATE TABLE ai_cost_tracking (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  salon_id UUID NOT NULL,
  request_type TEXT NOT NULL,
  model_used TEXT NOT NULL,
  tokens_used INTEGER NOT NULL,
  cost_usd DECIMAL(10,6) NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Edge Function Updates**:

```typescript
// En salonier-assistant/index.ts después de línea 598
await supabase.from('ai_cost_tracking').insert({
  salon_id: salonId,
  request_type: task,
  model_used: 'gpt-4o',
  tokens_used: totalTokens,
  cost_usd: costUsd,
});

// Daily budget check
const todayCost = await getDailyCost(salonId);
if (todayCost > 15) {
  // $15 daily limit
  console.warn('Daily budget exceeded:', todayCost);
}
```

**Tareas**:

- [ ] Deploy cost tracking schema
- [ ] Update edge functions con cost logging
- [ ] Add budget alerts
- [ ] Create cost dashboard

**Expected Impact**: Budget control, cost visibility

---

## 📋 **SPRINT 2: Advanced Optimizations (Semanas 4-6)**

### **🚀 Tarea 2.1: Intelligent Caching (Prioridad ALTA)**

**Objetivo**: 40% cache hit rate para reducir llamadas

**Schema Update**:

```sql
-- Usar código del ai-cache-system.ts
-- Deploy cache table con indexes optimizados
```

**Cache Integration**:

```typescript
// En salonier-assistant/index.ts - línea 97 update
const cacheKey = generateIntelligentCacheKey(task, payload);
const cached = await aiCache.get(task, payload.imageUrl, payload, salonId);

if (cached) {
  return new Response(
    JSON.stringify({
      success: true,
      data: cached,
      cached: true,
    })
  );
}

// Después del procesamiento - línea 605
await aiCache.set(task, payload.imageUrl, payload, salonId, result, {
  model: 'gpt-4o',
  tokens_used: totalTokens,
  cost_usd: costUsd,
  confidence: result.overallConfidence || 85,
});
```

**Tareas**:

- [ ] Deploy cache table schema
- [ ] Integrate cache en salonier-assistant
- [ ] Implement intelligent key generation
- [ ] Add cache metrics tracking
- [ ] Test cache hit/miss scenarios

**Expected Impact**: 2.1s latency reduction on cache hits, $0.038 savings per hit

---

### **🚀 Tarea 2.2: Request Batching (Prioridad MEDIA)**

**Objetivo**: Batch requests para mejor throughput

**Implementation**:

```typescript
// New middleware en salonier-assistant/index.ts
class RequestBatcher {
  private queue: PendingRequest[] = [];

  async add(request): Promise<Response> {
    if (this.queue.length >= 3 || request.priority === 'high') {
      return this.processBatch();
    }

    // Wait for batch timeout
    setTimeout(() => this.processBatch(), 500);
  }

  private async processBatch() {
    const batch = this.queue.splice(0, 3);
    return Promise.all(batch.map(processRequest));
  }
}
```

**Tareas**:

- [ ] Implement request batching
- [ ] Add priority handling
- [ ] Test batch processing
- [ ] Monitor batch efficiency

**Expected Impact**: 15% throughput improvement, better resource usage

---

### **🚀 Tarea 2.3: Structured Output & Validation (Prioridad ALTA)**

**Objetivo**: 98% JSON parse success rate

**OpenAI Updates**:

```typescript
// En salonier-assistant/index.ts - línea 405 update
const requestBody = {
  model: modelConfig.model,
  messages: messageContent,
  max_tokens: modelConfig.maxTokens,
  temperature: modelConfig.temperature,
  response_format: {
    type: 'json_object',
    schema: getJsonSchema(task), // New function
  },
};

// Add Zod validation
import { z } from 'zod';

const DiagnosisSchema = z.object({
  hairThickness: z.enum(['Fine', 'Medium', 'Thick']),
  hairDensity: z.enum(['Low', 'Medium', 'High']),
  averageLevel: z.number().min(1).max(10),
  // ... resto del schema
});

const result = DiagnosisSchema.parse(JSON.parse(aiResponse));
```

**Tareas**:

- [ ] Add Zod schemas for all response types
- [ ] Update prompts con JSON schema
- [ ] Implement validation layer
- [ ] Add retry con schema correction

**Expected Impact**: 98% parse success, -87% retry rate

---

### **🚀 Tarea 2.4: Performance Monitoring (Prioridad MEDIA)**

**Objetivo**: Real-time metrics dashboard

**Metrics Collection**:

```typescript
// New table for metrics
CREATE TABLE ai_performance_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  salon_id UUID NOT NULL,
  task_type TEXT NOT NULL,
  latency_ms INTEGER NOT NULL,
  tokens_used INTEGER NOT NULL,
  cost_usd DECIMAL(10,6) NOT NULL,
  success BOOLEAN NOT NULL,
  cached BOOLEAN DEFAULT FALSE,
  model_used TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

// Edge function instrumentation
const startTime = Date.now();
// ... process request
const latency = Date.now() - startTime;

await supabase.from('ai_performance_metrics').insert({
  salon_id: salonId,
  task_type: task,
  latency_ms: latency,
  tokens_used: totalTokens,
  cost_usd: costUsd,
  success: true,
  cached: !!cachedResult,
  model_used: modelConfig.model
});
```

**Dashboard Features**:

- Real-time P95 latency
- Cost per hour/day tracking
- Cache hit rate trends
- Success rate monitoring
- Model usage distribution

**Tareas**:

- [ ] Deploy metrics schema
- [ ] Add instrumentation to edge functions
- [ ] Create Grafana dashboard
- [ ] Setup alerting rules

**Expected Impact**: Visibility, proactive issue detection

---

## 🧪 **TESTING & VALIDATION**

### **Phase 1: Unit Testing (Semana 2)**

```bash
# Test prompt compression
npm test -- prompt-compression.test.ts

# Test model routing
npm test -- model-routing.test.ts

# Test fallback system
npm test -- fallback-system.test.ts

# Test cost calculation
npm test -- cost-tracking.test.ts
```

### **Phase 2: Integration Testing (Semana 4)**

```bash
# Cache integration tests
npm test -- cache-system.test.ts

# End-to-end latency tests
npm test -- e2e-performance.test.ts

# Load testing
npx artillery run load-test.yml
```

### **Phase 3: Production Validation (Semana 6)**

- [ ] A/B test con 10% traffic
- [ ] Monitor key metrics 48h
- [ ] Gradual rollout a 100%
- [ ] Performance benchmarking

---

## 📊 **SUCCESS CRITERIA**

### **Sprint 1 Targets**

- ✅ P95 Latency: <6s (50% reduction)
- ✅ Cost per request: <$0.040 (60% reduction)
- ✅ Success rate: >95%
- ✅ Token usage: <500 average

### **Sprint 2 Targets**

- ✅ P95 Latency: <3s (79% total reduction)
- ✅ Cost per request: <$0.025 (74% total reduction)
- ✅ Cache hit rate: >40%
- ✅ Success rate: >98%

### **Final Validation**

- ✅ Load test: 1000 concurrent users
- ✅ Accuracy test: >95% on 500 samples
- ✅ Cost test: <$15 daily budget
- ✅ User satisfaction: +30% improvement

---

## 🚨 **RISK MITIGATION**

### **Technical Risks**

- **Cache misses**: Implement intelligent key generation
- **Model accuracy**: Extensive testing before deployment
- **Infrastructure load**: Gradual rollout with monitoring
- **Data consistency**: Strong validation schemas

### **Business Risks**

- **User experience**: A/B testing for gradual rollout
- **Cost overruns**: Hard budget limits with alerts
- **Service downtime**: Robust fallback systems
- **Accuracy regression**: Continuous quality monitoring

### **Rollback Plan**

```bash
# Emergency rollback commands
git checkout main
git revert <optimization-commit>
npm run deploy:edge-functions
npm run deploy:database:rollback
```

---

## 📈 **EXPECTED OUTCOMES**

### **Week 6 Final State**

```
🎯 P95 Latency: 2.8s (-79% from 11.2s)
💰 Cost per request: $0.025 (-74% from $0.095)
📊 Cache hit rate: 42%
✅ Success rate: 98%
🎨 Accuracy: 95%
📉 Support tickets: -60%
💎 User satisfaction: +40%
💵 Monthly savings: $375 ($4,500/año)
```

**ROI Timeline**: Payback inmediato (ahorro primer mes > costo desarrollo)
