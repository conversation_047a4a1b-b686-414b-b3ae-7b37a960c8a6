/**
 * Enhanced Chemical Validation System for Salonier
 * Ensures safe and accurate color formulations
 * Prevents dangerous chemical combinations
 */

interface HairState {
  level: number; // 1-10
  state: 'natural' | 'colored' | 'bleached' | 'damaged';
  porosity: 'low' | 'medium' | 'high';
  elasticity: 'poor' | 'good' | 'excellent';
  previousProcesses: string[];
  grayPercentage: number;
  metalDetected?: boolean;
  hennaDetected?: boolean;
}

interface ColorGoal {
  targetLevel: number;
  targetTone: string;
  technique: string;
  urgency: 'normal' | 'fast' | 'gentle';
}

interface ValidationResult {
  isViable: boolean;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  warnings: string[];
  recommendations: string[];
  requiredTests: string[];
  estimatedSessions: number;
  processingTime: number;
  aftercareRequired: string[];
}

interface UnderlyingPigment {
  level: number;
  dominantPigment: 'red' | 'orange' | 'yellow' | 'pale yellow';
  intensity: 'strong' | 'medium' | 'weak';
}

export class ChemicalValidator {
  /**
   * Main validation function
   */
  static validate(hairState: HairState, colorGoal: ColorGoal): ValidationResult {
    const validation: ValidationResult = {
      isViable: true,
      riskLevel: 'low',
      warnings: [],
      recommendations: [],
      requiredTests: [],
      estimatedSessions: 1,
      processingTime: 35,
      aftercareRequired: [],
    };

    // Critical safety checks
    this.checkMetallicSalts(hairState, validation);
    this.checkHennaCompatibility(hairState, validation);
    this.checkHairIntegrity(hairState, validation);

    // Colorimetry rules
    this.validateColorLaws(hairState, colorGoal, validation);
    this.checkUnderlyingPigments(hairState, colorGoal, validation);
    this.validateTechnique(hairState, colorGoal, validation);

    // Process optimization
    this.calculateSessions(hairState, colorGoal, validation);
    this.recommendPreparation(hairState, colorGoal, validation);
    this.addAftercareInstructions(hairState, colorGoal, validation);

    return validation;
  }

  /**
   * Critical safety checks
   */
  private static checkMetallicSalts(hairState: HairState, validation: ValidationResult) {
    if (hairState.metalDetected) {
      validation.riskLevel = 'critical';
      validation.isViable = false;
      validation.warnings.push(
        '⚠️ PELIGRO: Sales metálicas detectadas. NO aplicar química sin decapar completamente.'
      );
      validation.requiredTests.push('Prueba de sales metálicas');
      validation.recommendations.push('Decapado profesional requerido antes de cualquier proceso');
    }

    // Check for signs in process history
    const metalRiskKeywords = [
      'tinte de farmacia',
      'coloración casera',
      'henna metálica',
      'progressive dye',
    ];
    if (
      hairState.previousProcesses.some(process =>
        metalRiskKeywords.some(keyword => process.toLowerCase().includes(keyword))
      )
    ) {
      validation.riskLevel = 'high';
      validation.warnings.push('Riesgo de sales metálicas por tratamientos previos');
      validation.requiredTests.push('Test de incompatibilidad química');
    }
  }

  private static checkHennaCompatibility(hairState: HairState, validation: ValidationResult) {
    if (hairState.hennaDetected) {
      validation.riskLevel = 'high';
      validation.warnings.push('Henna detectada - solo se puede oscurecer o mantener color');
      validation.recommendations.push('Evitar decoloración - riesgo de rotura capilar');

      // Henna can only be darkened or covered
      if (hairState.level > 6) {
        // If henna lightened the hair somehow
        validation.warnings.push('Posible henna metálica - MÁXIMA PRECAUCIÓN');
        validation.requiredTests.push('Prueba de mechón obligatoria');
      }
    }
  }

  private static checkHairIntegrity(hairState: HairState, validation: ValidationResult) {
    if (hairState.elasticity === 'poor') {
      validation.riskLevel = 'high';
      validation.warnings.push('Elasticidad capilar comprometida - riesgo de rotura');
      validation.recommendations.push('Tratamiento reconstructor antes del servicio');
      validation.aftercareRequired.push('Protocolo de reconstrucción capilar');
    }

    if (hairState.porosity === 'high' && hairState.state === 'damaged') {
      validation.riskLevel = 'medium';
      validation.warnings.push('Alta porosidad - puede absorber color de forma irregular');
      validation.recommendations.push('Pre-pigmentación requerida para colores oscuros');
      validation.processingTime *= 0.8; // Reduce processing time
    }
  }

  /**
   * Fundamental colorimetry laws
   */
  private static validateColorLaws(
    hairState: HairState,
    colorGoal: ColorGoal,
    validation: ValidationResult
  ) {
    const levelDifference = colorGoal.targetLevel - hairState.level;

    // Law 1: Color cannot lift color
    if (hairState.state === 'colored' && levelDifference > 0) {
      validation.riskLevel = 'high';
      validation.warnings.push('🚨 COLOR NO LEVANTA COLOR: Requiere decapado antes de aclarar');
      validation.estimatedSessions = Math.max(validation.estimatedSessions, 2);
      validation.recommendations.push('Decapado + decoloración en sesiones separadas');

      if (levelDifference > 2) {
        validation.recommendations.push(
          'Proceso gradual - mínimo 3 sesiones para resultado óptimo'
        );
      }
    }

    // Law 2: Maximum lift per session
    if (levelDifference > 4 && hairState.state === 'natural') {
      validation.warnings.push('Aclarado >4 niveles - múltiples sesiones recomendadas');
      validation.estimatedSessions = Math.ceil(levelDifference / 4);
    }

    // Law 3: Pre-pigmentation for darkening
    if (levelDifference < -3 && (hairState.state === 'bleached' || hairState.porosity === 'high')) {
      validation.recommendations.push('PRE-PIGMENTACIÓN OBLIGATORIA: Evita tonos verdes/cenizos');
      validation.processingTime += 20; // Add pre-pigmentation time
    }

    // Law 4: Developer volume rules
    this.recommendDeveloperVolume(levelDifference, hairState, validation);
  }

  private static recommendDeveloperVolume(
    levelDifference: number,
    hairState: HairState,
    validation: ValidationResult
  ) {
    let recommendedVolume: number;
    let reasoning: string;

    if (levelDifference <= 0) {
      recommendedVolume = 10;
      reasoning = 'Solo depósito - 10 vol suficiente';
    } else if (levelDifference <= 1) {
      recommendedVolume = 20;
      reasoning = 'Aclarado leve - 20 vol estándar';
    } else if (levelDifference <= 2) {
      recommendedVolume = 30;
      reasoning = 'Aclarado moderado - 30 vol recomendado';
    } else {
      recommendedVolume = 40;
      reasoning = 'Aclarado intenso - 40 vol (máximo)';

      if (hairState.state === 'damaged') {
        validation.warnings.push('Cabello dañado + oxidante 40 vol = RIESGO ALTO');
        validation.recommendations.push('Considerar proceso gradual con menores volúmenes');
      }
    }

    validation.recommendations.push(`Oxidante: ${recommendedVolume} vol - ${reasoning}`);
  }

  /**
   * Underlying pigment analysis
   */
  private static checkUnderlyingPigments(
    hairState: HairState,
    colorGoal: ColorGoal,
    validation: ValidationResult
  ) {
    const underlyingPigment = this.getUnderlyingPigment(hairState.level);

    // Warn about unwanted reflects
    if (colorGoal.targetLevel > hairState.level) {
      validation.warnings.push(
        `Pigmento subyacente nivel ${hairState.level}: ${underlyingPigment.dominantPigment.toUpperCase()}`
      );

      const neutralizationNeeded = this.getNeutralizationStrategy(
        underlyingPigment,
        colorGoal.targetTone
      );

      if (neutralizationNeeded) {
        validation.recommendations.push(neutralizationNeeded);
      }
    }

    // Special gray coverage considerations
    if (hairState.grayPercentage > 30) {
      validation.recommendations.push(
        `Cobertura de canas ${hairState.grayPercentage}% - fórmula especial requerida`
      );

      if (hairState.grayPercentage > 70) {
        validation.warnings.push('Canas abundantes - considerar pre-pigmentación');
        validation.processingTime += 10; // Extra time for gray coverage
      }
    }
  }

  private static getUnderlyingPigment(level: number): UnderlyingPigment {
    const pigmentMap: Record<number, UnderlyingPigment> = {
      1: { level: 1, dominantPigment: 'red', intensity: 'strong' },
      2: { level: 2, dominantPigment: 'red', intensity: 'strong' },
      3: { level: 3, dominantPigment: 'red', intensity: 'medium' },
      4: { level: 4, dominantPigment: 'red', intensity: 'medium' },
      5: { level: 5, dominantPigment: 'orange', intensity: 'strong' },
      6: { level: 6, dominantPigment: 'orange', intensity: 'medium' },
      7: { level: 7, dominantPigment: 'yellow', intensity: 'strong' },
      8: { level: 8, dominantPigment: 'yellow', intensity: 'medium' },
      9: { level: 9, dominantPigment: 'pale yellow', intensity: 'weak' },
      10: { level: 10, dominantPigment: 'pale yellow', intensity: 'weak' },
    };

    return pigmentMap[level] || pigmentMap[5];
  }

  private static getNeutralizationStrategy(
    underlyingPigment: UnderlyingPigment,
    targetTone: string
  ): string | null {
    const neutralizationMap: Record<string, string> = {
      red: 'Usar base verde/ceniza para neutralizar',
      orange: 'Usar base azul/ceniza para neutralizar',
      yellow: 'Usar base violeta para neutralizar',
      'pale yellow': 'Matizador violeta post-servicio',
    };

    // Only recommend neutralization if target is natural/ash
    if (
      targetTone.toLowerCase().includes('ceniza') ||
      targetTone.toLowerCase().includes('natural') ||
      targetTone.toLowerCase().includes('ash')
    ) {
      return neutralizationMap[underlyingPigment.dominantPigment];
    }

    return null;
  }

  /**
   * Technique-specific validation
   */
  private static validateTechnique(
    hairState: HairState,
    colorGoal: ColorGoal,
    validation: ValidationResult
  ) {
    const technique = colorGoal.technique.toLowerCase();

    switch (technique) {
      case 'balayage':
        if (hairState.level < 4) {
          validation.warnings.push('Cabello muy oscuro - balayage requerirá pre-aclarado');
          validation.processingTime += 30;
        }
        validation.recommendations.push('Usar decolorante en crema para control');
        break;

      case 'highlights':
        if (hairState.state === 'colored') {
          validation.warnings.push('Mechas sobre cabello teñido - riesgo de bandas');
          validation.recommendations.push('Decapado selectivo en secciones');
        }
        break;

      case 'ombre':
        validation.recommendations.push('Degradado requiere técnica de difuminado experta');
        if (hairState.level > 6) {
          validation.warnings.push('Cabello claro - ombré sutil recomendado');
        }
        break;

      case 'color_correction':
        validation.riskLevel = 'high';
        validation.estimatedSessions = Math.max(validation.estimatedSessions, 2);
        validation.requiredTests.push('Prueba de mechón obligatoria');
        validation.recommendations.push('Corrección gradual - paciencia esencial');
        break;
    }
  }

  /**
   * Session planning
   */
  private static calculateSessions(
    hairState: HairState,
    colorGoal: ColorGoal,
    validation: ValidationResult
  ) {
    const levelDifference = Math.abs(colorGoal.targetLevel - hairState.level);

    // Base sessions on level difference and hair state
    let sessions = 1;

    if (hairState.state === 'colored' && colorGoal.targetLevel > hairState.level) {
      sessions += 1; // Color removal session
    }

    if (levelDifference > 4) {
      sessions = Math.max(sessions, Math.ceil(levelDifference / 4));
    }

    if (hairState.elasticity === 'poor' || validation.riskLevel === 'high') {
      sessions += 1; // Extra prep session
    }

    validation.estimatedSessions = Math.max(validation.estimatedSessions, sessions);

    // Session breakdown
    if (validation.estimatedSessions > 1) {
      const sessionPlan = this.createSessionPlan(
        hairState,
        colorGoal,
        validation.estimatedSessions
      );
      validation.recommendations.push(`Plan de sesiones: ${sessionPlan}`);
    }
  }

  private static createSessionPlan(
    hairState: HairState,
    colorGoal: ColorGoal,
    sessions: number
  ): string {
    const plans: string[] = [];

    if (sessions === 2) {
      if (hairState.state === 'colored' && colorGoal.targetLevel > hairState.level) {
        plans.push('Sesión 1: Decapado/Decoloración', 'Sesión 2: Color final + tratamiento');
      } else {
        plans.push('Sesión 1: Aclarado inicial', 'Sesión 2: Tono final');
      }
    } else if (sessions >= 3) {
      plans.push('Sesión 1: Preparación/Decapado');
      plans.push('Sesión 2: Aclarado gradual');
      plans.push(`Sesión ${sessions}: Color final`);
    }

    return plans.join(' → ');
  }

  /**
   * Preparation recommendations
   */
  private static recommendPreparation(
    hairState: HairState,
    colorGoal: ColorGoal,
    validation: ValidationResult
  ) {
    // Pre-service treatments
    if (hairState.porosity === 'high') {
      validation.recommendations.push('Tratamiento ecualizador de porosidad 24h antes');
    }

    if (hairState.elasticity === 'poor') {
      validation.recommendations.push('Protocolo reconstructor 1 semana antes');
    }

    // Day-of preparation
    validation.recommendations.push('No lavar cabello 24-48h antes del servicio');

    if (colorGoal.targetLevel > hairState.level + 3) {
      validation.recommendations.push('Corte sanear puntas antes del proceso químico');
    }
  }

  /**
   * Aftercare instructions
   */
  private static addAftercareInstructions(
    hairState: HairState,
    colorGoal: ColorGoal,
    validation: ValidationResult
  ) {
    // Universal aftercare
    validation.aftercareRequired.push('Champú sin sulfatos primera semana');
    validation.aftercareRequired.push('Mascarilla hidratante 2x por semana');

    // Specific to process
    const levelDifference = Math.abs(colorGoal.targetLevel - hairState.level);

    if (levelDifference > 2) {
      validation.aftercareRequired.push('Tratamiento reparador profesional en 2 semanas');
    }

    if (colorGoal.targetTone.includes('rubio') || colorGoal.targetLevel > 7) {
      validation.aftercareRequired.push('Matizador violeta 1x por semana');
    }

    if (validation.riskLevel === 'high') {
      validation.aftercareRequired.push('Revisión profesional en 1 semana');
      validation.aftercareRequired.push('Protocolo intensivo de hidratación');
    }
  }

  /**
   * Quick safety check for immediate decisions
   */
  static quickSafetyCheck(
    hairState: HairState,
    colorGoal: ColorGoal
  ): {
    safe: boolean;
    immediateRisks: string[];
    proceedWithCaution: boolean;
  } {
    const risks: string[] = [];
    let safe = true;
    let proceedWithCaution = false;

    // Critical risks
    if (hairState.metalDetected) {
      safe = false;
      risks.push('SALES METÁLICAS - NO PROCEDER');
    }

    if (hairState.elasticity === 'poor' && Math.abs(colorGoal.targetLevel - hairState.level) > 2) {
      safe = false;
      risks.push('CABELLO FRÁGIL - Riesgo de rotura');
    }

    // Caution needed
    if (hairState.state === 'colored' && colorGoal.targetLevel > hairState.level) {
      proceedWithCaution = true;
      risks.push('Color sobre color - decapado necesario');
    }

    if (hairState.hennaDetected && colorGoal.targetLevel > hairState.level) {
      proceedWithCaution = true;
      risks.push('Henna presente - solo oscurecer recomendado');
    }

    return { safe, immediateRisks: risks, proceedWithCaution };
  }
}

// Export utility functions
export const ColorimetryUtils = {
  /**
   * Calculate processing time based on multiple factors
   */
  calculateProcessingTime(
    hairState: HairState,
    colorGoal: ColorGoal,
    developerVolume: number
  ): number {
    let baseTime = 35; // minutes

    // Adjust for level difference
    const levelDiff = Math.abs(colorGoal.targetLevel - hairState.level);
    baseTime += levelDiff * 5;

    // Adjust for developer volume
    if (developerVolume >= 40) baseTime += 10;
    if (developerVolume <= 10) baseTime -= 10;

    // Adjust for hair condition
    if (hairState.porosity === 'high') baseTime *= 0.8;
    if (hairState.porosity === 'low') baseTime *= 1.2;

    // Gray coverage
    if (hairState.grayPercentage > 50) baseTime += 15;

    return Math.round(baseTime);
  },

  /**
   * Get recommended color formula ratio
   */
  getColorRatio(hairState: HairState, colorGoal: ColorGoal): string {
    const levelDiff = colorGoal.targetLevel - hairState.level;

    if (levelDiff <= 0) return '1:1'; // Deposit only
    if (levelDiff <= 2) return '1:1.5'; // Standard lift
    if (levelDiff <= 4) return '1:2'; // High lift

    return 'Decoloración requerida'; // Cannot lift with color
  },

  /**
   * Check if toner is needed
   */
  needsToner(underlyingPigment: string, targetTone: string): boolean {
    const ashTones = ['ceniza', 'ash', 'natural'];
    const hasUnwantedWarmth = ['orange', 'yellow', 'red'].includes(underlyingPigment);
    const wantsAshTone = ashTones.some(tone => targetTone.toLowerCase().includes(tone));

    return hasUnwantedWarmth && wantsAshTone;
  },
};
