/**
 * AI Cache System for Salonier - Intelligent Caching Strategy
 * Target: 40% cache hit rate, 24h TTL, smart invalidation
 */

import { logger } from './utils/logger';
import { J<PERSON> } from './types/database';

// Type definitions for AI context objects
interface DiagnoseImageContext {
  [key: string]: never; // Empty context for image diagnosis
}

interface AnalyzeDesiredLookContext {
  currentLevel?: number;
  targetLevel?: number;
  [key: string]: unknown;
}

interface GenerateFormulaContext {
  brand?: string;
  line?: string;
  diagnosis?: {
    averageLevel?: number;
    [key: string]: unknown;
  };
  desiredResult?: {
    detectedLevel?: number;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

type AITaskContext =
  | DiagnoseImageContext
  | AnalyzeDesiredLookContext
  | GenerateFormulaContext
  | Record<string, unknown>;

// AI Processing result types
interface AIProcessingMetadata {
  model: string;
  tokens_used: number;
  cost_usd: number;
  confidence: number;
  complexity?: 'simple' | 'medium' | 'complex';
  image_quality?: 'high' | 'medium' | 'low';
}

interface AIProcessingResult {
  result: Json;
  metadata: AIProcessingMetadata;
}

// Supabase client type (simplified)
interface SupabaseClient {
  from: (table: string) => {
    select: (columns: string) => SupabaseQueryBuilder;
    upsert: (entry: CacheEntry) => Promise<{ error: any | null }>;
    update: (data: Partial<CacheEntry>) => SupabaseQueryBuilder;
    delete: () => SupabaseQueryBuilder;
  };
}

interface SupabaseQueryBuilder {
  eq: (column: string, value: string) => SupabaseQueryBuilder;
  gt: (column: string, value: string) => SupabaseQueryBuilder;
  gte: (column: string, value: number) => SupabaseQueryBuilder;
  lt: (column: string, value: string) => SupabaseQueryBuilder;
  lte: (column: string, value: number) => SupabaseQueryBuilder;
  single: () => Promise<{ data: CacheEntry | null; error: any | null }>;
  order: (column: string, options?: { ascending: boolean }) => SupabaseQueryBuilder;
  limit: (count: number) => Promise<{ data: CacheEntry[] | null; error: any | null }>;
}

// Cached AI Request processor function type
type AIRequestProcessor = (request: {
  task: 'diagnose_image' | 'analyze_desired_look' | 'generate_formula';
  imageData?: string;
  context?: AITaskContext;
  salonId: string;
}) => Promise<AIProcessingResult>;

interface CacheEntry {
  key: string;
  salon_id: string;
  analysis_type: 'diagnose_image' | 'analyze_desired_look' | 'generate_formula';
  input_hash: string;
  result: Json;
  metadata: AIProcessingMetadata;
  created_at: string;
  expires_at: string;
  hit_count: number;
  last_accessed: string;
}

interface CacheStats {
  hitRate: number;
  totalRequests: number;
  cacheHits: number;
  cacheMisses: number;
  averageCostSavings: number;
  storageSize: number;
}

export class AICache {
  private supabase: SupabaseClient;
  private localCache: Map<string, CacheEntry> = new Map();
  private stats = {
    requests: 0,
    hits: 0,
    misses: 0,
    savings: 0,
  };

  constructor(supabaseClient: SupabaseClient) {
    this.supabase = supabaseClient;
  }

  /**
   * Generate intelligent cache key based on input similarity
   */
  async generateKey(
    task: string,
    imageData: string | null,
    context: AITaskContext | null,
    salonId: string
  ): Promise<string> {
    const crypto = await import('crypto');

    // For image-based tasks, use perceptual hashing (simplified)
    let imageHash = '';
    if (imageData) {
      // Use first 200 chars for quick comparison + last 100 for uniqueness
      const preview = imageData.substring(0, 200) + imageData.slice(-100);
      imageHash = crypto.createHash('md5').update(preview).digest('hex').substring(0, 16);
    }

    // Context normalization for better cache hits
    const normalizedContext = this.normalizeContext(context, task);
    const contextHash = crypto
      .createHash('md5')
      .update(JSON.stringify(normalizedContext))
      .digest('hex')
      .substring(0, 12);

    return `${task}_${salonId}_${imageHash}_${contextHash}`;
  }

  /**
   * Normalize context to improve cache hit rate
   */
  private normalizeContext(context: AITaskContext | null, task: string): Record<string, unknown> {
    if (!context) return {};

    const normalized: Record<string, unknown> = {};

    switch (task) {
      case 'diagnose_image':
        // Only cache based on image, ignore other context
        return {};

      case 'analyze_desired_look':
        // Cache based on current level ranges (group similar levels)
        const lookContext = context as AnalyzeDesiredLookContext;
        if (lookContext.currentLevel) {
          normalized.levelGroup = Math.floor(lookContext.currentLevel / 2) * 2; // Group in 2s: 0-1, 2-3, 4-5, etc.
        }
        break;

      case 'generate_formula':
        // Cache based on significant formula parameters
        const formulaContext = context as GenerateFormulaContext;
        normalized.brand = formulaContext.brand;
        normalized.line = formulaContext.line;
        if (formulaContext.diagnosis?.averageLevel) {
          normalized.currentLevel = Math.floor(formulaContext.diagnosis.averageLevel);
        }
        if (formulaContext.desiredResult?.detectedLevel) {
          normalized.targetLevel = Math.floor(formulaContext.desiredResult.detectedLevel);
        }
        break;
    }

    return normalized;
  }

  /**
   * Get from cache with smart matching
   */
  async get(
    task: string,
    imageData: string | null,
    context: AITaskContext | null,
    salonId: string
  ): Promise<Json | null> {
    this.stats.requests++;

    const key = await this.generateKey(task, imageData, context, salonId);

    try {
      // Try local cache first (for this session)
      if (this.localCache.has(key)) {
        const entry = this.localCache.get(key)!;
        if (new Date(entry.expires_at) > new Date()) {
          this.stats.hits++;
          this.updateHitCount(key, entry);
          return entry.result;
        } else {
          this.localCache.delete(key);
        }
      }

      // Query database cache
      const { data, error } = await this.supabase
        .from('ai_analysis_cache')
        .select('*')
        .eq('key', key)
        .eq('salon_id', salonId)
        .gt('expires_at', new Date().toISOString())
        .single();

      if (error && error.code !== 'PGRST116') {
        logger.warn('Cache query error', 'AICacheSystem', error);
        this.stats.misses++;
        return null;
      }

      if (data) {
        this.stats.hits++;
        this.stats.savings += data.metadata.cost_usd;

        // Store in local cache
        this.localCache.set(key, data);

        // Update hit count
        this.updateHitCount(key, data);

        // console.log(`Cache HIT: ${task} (saved $${data.metadata.cost_usd.toFixed(4)})`);
        return data.result;
      }

      this.stats.misses++;
      return null;
    } catch (error) {
      logger.error('Cache get error', 'AICacheSystem', error);
      this.stats.misses++;
      return null;
    }
  }

  /**
   * Store in cache with intelligent TTL
   */
  async set(
    task: string,
    imageData: string | null,
    context: AITaskContext | null,
    salonId: string,
    result: Json,
    metadata: {
      model: string;
      tokens_used: number;
      cost_usd: number;
      confidence: number;
      complexity?: 'simple' | 'medium' | 'complex';
    }
  ): Promise<void> {
    const key = await this.generateKey(task, imageData, context, salonId);
    const now = new Date();

    // Dynamic TTL based on task type and confidence
    const baseTTL = this.calculateTTL(task, metadata.confidence);
    const expiresAt = new Date(now.getTime() + baseTTL);

    const entry: CacheEntry = {
      key,
      salon_id: salonId,
      analysis_type: task as 'diagnose_image' | 'analyze_desired_look' | 'generate_formula',
      input_hash: key,
      result,
      metadata: {
        ...metadata,
        complexity: metadata.complexity || 'medium',
        image_quality: this.detectImageQuality(imageData),
      },
      created_at: now.toISOString(),
      expires_at: expiresAt.toISOString(),
      hit_count: 0,
      last_accessed: now.toISOString(),
    };

    try {
      // Store in database
      const { error } = await this.supabase.from('ai_analysis_cache').upsert(entry);

      if (error) {
        logger.error('Cache store error', 'AICacheSystem', error);
        return;
      }

      // Store in local cache
      this.localCache.set(key, entry);

      // console.log(
      //   `Cache STORE: ${task} (TTL: ${baseTTL / 1000 / 60} min, confidence: ${metadata.confidence}%)`
      // );
    } catch (error) {
      logger.error('Cache set error', 'AICacheSystem', error);
    }
  }

  /**
   * Calculate dynamic TTL based on confidence and task type
   */
  private calculateTTL(task: string, confidence: number): number {
    const baseTTLs = {
      diagnose_image: 24 * 60 * 60 * 1000, // 24 hours - hair doesn't change quickly
      analyze_desired_look: 12 * 60 * 60 * 1000, // 12 hours - reference images stable
      generate_formula: 6 * 60 * 60 * 1000, // 6 hours - formulas may need updates
    };

    const baseTTL = baseTTLs[task as keyof typeof baseTTLs] || baseTTLs.diagnose_image;

    // Higher confidence = longer cache
    const confidenceMultiplier = Math.max(0.5, Math.min(1.5, confidence / 100));

    return Math.floor(baseTTL * confidenceMultiplier);
  }

  /**
   * Detect image quality for cache optimization
   */
  private detectImageQuality(imageData: string | null): 'high' | 'medium' | 'low' {
    if (!imageData) return 'medium';

    // Simple heuristic based on data size
    const sizeKB = (imageData.length * 3) / 4 / 1024;

    if (sizeKB > 500) return 'high';
    if (sizeKB > 150) return 'medium';
    return 'low';
  }

  /**
   * Update hit count for analytics
   */
  private async updateHitCount(key: string, entry: CacheEntry) {
    try {
      await this.supabase
        .from('ai_analysis_cache')
        .update({
          hit_count: (entry.hit_count || 0) + 1,
          last_accessed: new Date().toISOString(),
        })
        .eq('key', key);
    } catch {
      // Silent fail - not critical
    }
  }

  /**
   * Find similar cached results for better hit rate
   */
  async findSimilar(
    task: string,
    context: AITaskContext | null,
    salonId: string,
    threshold = 0.8
  ): Promise<CacheEntry[]> {
    try {
      const { data, error } = await this.supabase
        .from('ai_analysis_cache')
        .select('result, metadata')
        .eq('salon_id', salonId)
        .eq('analysis_type', task)
        .gt('expires_at', new Date().toISOString())
        .gte('metadata->>confidence', Math.floor(threshold * 100))
        .order('hit_count', { ascending: false })
        .limit(5);

      if (error) {
        logger.warn('Similar cache query error', 'AICacheSystem', error);
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Find similar error', 'AICacheSystem', error);
      return [];
    }
  }

  /**
   * Cache cleanup and optimization
   */
  async cleanup(): Promise<void> {
    try {
      // Remove expired entries
      await this.supabase
        .from('ai_analysis_cache')
        .delete()
        .lt('expires_at', new Date().toISOString());

      // Note: Complex delete operations should be handled via stored procedures
      // This is a simplified version - in production, use a database function

      // console.log('Cache cleanup completed');
    } catch (error) {
      logger.error('Cache cleanup error', 'AICacheSystem', error);
    }
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const hitRate = this.stats.requests > 0 ? this.stats.hits / this.stats.requests : 0;

    return {
      hitRate,
      totalRequests: this.stats.requests,
      cacheHits: this.stats.hits,
      cacheMisses: this.stats.misses,
      averageCostSavings: this.stats.savings,
      storageSize: this.localCache.size,
    };
  }

  /**
   * Invalidate cache for specific salon (useful for settings changes)
   */
  async invalidateSalon(salonId: string): Promise<void> {
    try {
      await this.supabase.from('ai_analysis_cache').delete().eq('salon_id', salonId);

      // Clear local cache for this salon
      const keysToDelete: string[] = [];
      this.localCache.forEach((entry, key) => {
        if (entry.salon_id === salonId) {
          keysToDelete.push(key);
        }
      });
      keysToDelete.forEach(key => this.localCache.delete(key));

      // console.log(`Cache invalidated for salon: ${salonId}`);
    } catch (error) {
      logger.error('Cache invalidation error', 'AICacheSystem', error);
    }
  }

  /**
   * Preload cache with common patterns
   */
  async preloadCommonPatterns(salonId: string): Promise<void> {
    try {
      // Get most hit cache entries for this salon
      const { data } = await this.supabase
        .from('ai_analysis_cache')
        .select('*')
        .eq('salon_id', salonId)
        .gte('hit_count', 3)
        .gt('expires_at', new Date().toISOString())
        .order('hit_count', { ascending: false })
        .limit(20);

      if (data) {
        data.forEach((entry: CacheEntry) => {
          this.localCache.set(entry.key, entry);
        });
        // console.log(`Preloaded ${data.length} cache entries for salon: ${salonId}`);
      }
    } catch (error) {
      logger.error('Cache preload error', 'AICacheSystem', error);
    }
  }
}

/**
 * Cache-aware AI request wrapper
 */
export class CachedAIRequest {
  private cache: AICache;

  constructor(cache: AICache) {
    this.cache = cache;
  }

  async execute(request: {
    task: 'diagnose_image' | 'analyze_desired_look' | 'generate_formula';
    imageData?: string;
    context?: AITaskContext;
    salonId: string;
    processor: AIRequestProcessor;
  }): Promise<Json & { fromCache: boolean }> {
    // Try cache first
    const cached = await this.cache.get(
      request.task,
      request.imageData || null,
      request.context || null,
      request.salonId
    );

    if (cached) {
      return { ...(cached as object), fromCache: true } as Json & { fromCache: boolean };
    }

    // Process with AI
    const { result, metadata } = await request.processor(request);

    // Store in cache
    await this.cache.set(
      request.task,
      request.imageData || null,
      request.context || null,
      request.salonId,
      result,
      metadata
    );

    return { ...(result as object), fromCache: false } as Json & { fromCache: boolean };
  }
}

// Database migration for cache table
export const CACHE_TABLE_SCHEMA = `
CREATE TABLE IF NOT EXISTS ai_analysis_cache (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  key TEXT NOT NULL UNIQUE,
  salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
  analysis_type TEXT NOT NULL CHECK (analysis_type IN ('diagnose_image', 'analyze_desired_look', 'generate_formula')),
  input_hash TEXT NOT NULL,
  result JSONB NOT NULL,
  metadata JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  expires_at TIMESTAMPTZ NOT NULL,
  hit_count INTEGER NOT NULL DEFAULT 0,
  last_accessed TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_ai_cache_key ON ai_analysis_cache(key);
CREATE INDEX IF NOT EXISTS idx_ai_cache_salon_id ON ai_analysis_cache(salon_id);
CREATE INDEX IF NOT EXISTS idx_ai_cache_expires ON ai_analysis_cache(expires_at);
CREATE INDEX IF NOT EXISTS idx_ai_cache_type ON ai_analysis_cache(analysis_type);
CREATE INDEX IF NOT EXISTS idx_ai_cache_hit_count ON ai_analysis_cache(hit_count DESC);

-- RLS policies
ALTER TABLE ai_analysis_cache ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Cache isolation by salon" ON ai_analysis_cache
  FOR ALL USING (salon_id IN (
    SELECT salon_id FROM profiles WHERE id = auth.uid()
    UNION
    SELECT salon_id FROM team_members WHERE user_id = auth.uid()
  ));
`;
