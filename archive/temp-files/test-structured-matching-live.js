/**
 * Test del Sistema de Matching Estructurado - Live Data
 *
 * Este script prueba el algoritmo de matching con productos reales del inventario
 */

// Simulamos productos de una fórmula generada por IA
const aiFormulaProducts = [
  {
    productId: 'temp-1',
    productName: 'Tinte Wella Illumina 7/81',
    brand: 'Wella',
    line: 'Illumina',
    type: 'color',
    shade: '7/81',
    quantity: 30,
    unit: 'ml',
  },
  {
    productId: 'temp-2',
    productName: "Oxidante L'Oreal INOA 20 vol",
    brand: "L'Oreal",
    line: 'INOA',
    type: 'developer',
    shade: '20 vol',
    quantity: 60,
    unit: 'ml',
  },
  {
    productId: 'temp-3',
    productName: 'Tinte Salerm Vison 8',
    brand: 'Salerm',
    line: 'Vison',
    type: 'color',
    shade: '8',
    quantity: 25,
    unit: 'ml',
  },
];

// Productos reales del inventario (basado en la consulta anterior)
const inventoryProducts = [
  {
    id: 'fa1bdb89-0f48-457d-9f7b-12bcb2138c06',
    brand: 'Wella',
    name: 'Tinte Wella Illumina 7/81',
    line: 'Illumina',
    type: 'color',
    shade: '7/81',
    display_name: 'color Wella Illumina 7/81',
    stock_ml: 180,
  },
  {
    id: 'ccb33ffd-28f9-4a4b-adcd-2beac129993a',
    brand: "L'Oreal",
    name: "Oxidante L'Oreal INOA 20 vol",
    line: 'INOA',
    type: 'developer',
    shade: '20 vol',
    display_name: "developer L'Oreal INOA 20 vol",
    stock_ml: 2000,
  },
  {
    id: 'fb1d14d6-84ec-4b88-8426-bd6ca55adeb1',
    brand: 'Salerm',
    name: 'Tinte Salerm Vison 7',
    line: 'Vison',
    type: 'color',
    shade: '7',
    display_name: 'color Salerm Vison 7',
    stock_ml: 150,
  },
  {
    id: '465e9c4d-4fbb-457b-a59b-bbbc7042819b',
    brand: 'Salerm',
    name: 'Tinte Salerm Vison 8/3',
    line: 'Vison',
    type: 'color',
    shade: '8/3',
    display_name: 'color Salerm Vison 8/3',
    stock_ml: 225,
  },
];

/**
 * Mapeo de tipos para compatibilidad
 */
const typeMapping = {
  Tinte: 'color',
  Oxidante: 'developer',
  Decolorante: 'bleach',
  Tratamiento: 'treatment',
};

/**
 * Algoritmo de matching estructurado
 */
function findStructuredMatch(formulaProduct, inventory) {
  console.log(`\n🔍 Buscando match para: ${formulaProduct.productName}`);
  console.log(
    `   Brand: ${formulaProduct.brand}, Line: ${formulaProduct.line}, Type: ${formulaProduct.type}, Shade: ${formulaProduct.shade}`
  );

  const matches = [];

  for (const product of inventory) {
    let score = 0;
    const details = [];

    // 1. Brand match (peso: 30%)
    if (
      product.brand &&
      formulaProduct.brand &&
      product.brand.toLowerCase().trim() === formulaProduct.brand.toLowerCase().trim()
    ) {
      score += 30;
      details.push('✅ Brand match');
    } else {
      details.push('❌ Brand mismatch');
      continue; // Brand es crítico, skip si no coincide
    }

    // 2. Type match (peso: 25%)
    const mappedType = typeMapping[formulaProduct.type] || formulaProduct.type.toLowerCase();
    if (product.type && product.type.toLowerCase() === mappedType.toLowerCase()) {
      score += 25;
      details.push('✅ Type match');
    } else {
      details.push('❌ Type mismatch');
      continue; // Type es crítico
    }

    // 3. Shade match (peso: 25%)
    if (
      product.shade &&
      formulaProduct.shade &&
      product.shade.toLowerCase().trim() === formulaProduct.shade.toLowerCase().trim()
    ) {
      score += 25;
      details.push('✅ Shade exact match');
    } else if (product.shade && formulaProduct.shade) {
      // Partial shade match (números similares)
      const formulaNum = formulaProduct.shade.match(/(\d+)/)?.[1];
      const productNum = product.shade.match(/(\d+)/)?.[1];
      if (formulaNum && productNum && Math.abs(parseInt(formulaNum) - parseInt(productNum)) <= 1) {
        score += 15;
        details.push('🟡 Shade partial match');
      } else {
        details.push('❌ Shade mismatch');
      }
    }

    // 4. Line match (peso: 20%, opcional)
    if (
      product.line &&
      formulaProduct.line &&
      product.line.toLowerCase().trim() === formulaProduct.line.toLowerCase().trim()
    ) {
      score += 20;
      details.push('✅ Line match');
    }

    // 5. Stock availability check
    const hasStock = parseFloat(product.stock_ml) >= formulaProduct.quantity;
    if (hasStock) {
      details.push('✅ Stock sufficient');
    } else {
      details.push('⚠️ Stock insufficient');
      score *= 0.5; // Penalizar falta de stock
    }

    if (score > 0) {
      matches.push({
        product,
        score,
        details,
        hasStock,
        stockNeeded: formulaProduct.quantity,
        stockAvailable: parseFloat(product.stock_ml),
      });
    }
  }

  // Ordenar por score descendente
  matches.sort((a, b) => b.score - a.score);

  return matches;
}

/**
 * Test principal
 */
function runMatchingTest() {
  console.log('🚀 INICIANDO TEST DE MATCHING ESTRUCTURADO\n');
  console.log('=========================================\n');

  const results = {
    totalProducts: aiFormulaProducts.length,
    found: 0,
    foundWithStock: 0,
    notFound: 0,
    details: [],
  };

  for (const formulaProduct of aiFormulaProducts) {
    const matches = findStructuredMatch(formulaProduct, inventoryProducts);

    if (matches.length > 0) {
      results.found++;
      const bestMatch = matches[0];

      console.log(`\n✅ MATCH ENCONTRADO (Score: ${bestMatch.score}/100)`);
      console.log(`   Producto: ${bestMatch.product.display_name}`);
      console.log(`   ID: ${bestMatch.product.id}`);
      console.log(`   Stock: ${bestMatch.stockAvailable}ml (necesita: ${bestMatch.stockNeeded}ml)`);
      console.log(`   Detalles: ${bestMatch.details.join(', ')}`);

      if (bestMatch.hasStock) {
        results.foundWithStock++;
      }

      results.details.push({
        formula: formulaProduct.productName,
        match: bestMatch.product.display_name,
        score: bestMatch.score,
        hasStock: bestMatch.hasStock,
      });
    } else {
      results.notFound++;
      console.log(`\n❌ NO MATCH FOUND`);
      console.log(`   Producto: ${formulaProduct.productName}`);

      results.details.push({
        formula: formulaProduct.productName,
        match: null,
        score: 0,
        hasStock: false,
      });
    }
  }

  console.log('\n=========================================');
  console.log('📊 RESUMEN DE RESULTADOS:');
  console.log('=========================================');
  console.log(`Total productos en fórmula: ${results.totalProducts}`);
  console.log(
    `Productos encontrados: ${results.found}/${results.totalProducts} (${Math.round((results.found / results.totalProducts) * 100)}%)`
  );
  console.log(
    `Con stock suficiente: ${results.foundWithStock}/${results.totalProducts} (${Math.round((results.foundWithStock / results.totalProducts) * 100)}%)`
  );
  console.log(
    `No encontrados: ${results.notFound}/${results.totalProducts} (${Math.round((results.notFound / results.totalProducts) * 100)}%)`
  );

  if (results.found === results.totalProducts) {
    console.log('\n🎉 ¡ÉXITO TOTAL! Todos los productos fueron encontrados');
  } else if (results.found > 0) {
    console.log('\n🟡 ÉXITO PARCIAL - algunos productos encontrados');
  } else {
    console.log('\n❌ FALLO - ningún producto encontrado');
  }

  return results;
}

// Ejecutar el test
const testResults = runMatchingTest();

// Export para uso en Node.js si es necesario
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runMatchingTest, testResults };
}
