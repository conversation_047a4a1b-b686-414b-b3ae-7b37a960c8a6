/**
 * Mock Data Factories for Testing
 * Generates realistic test data for Salonier components
 */

export interface MockUser {
  id: string;
  email: string;
  name: string;
  isOwner: boolean;
  salonId: string;
  licenseNumber?: string;
  yearsExperience?: string;
  specializations?: string[];
  phone?: string;
}

export interface MockClient {
  id: string;
  name: string;
  email: string;
  phone?: string;
  birthdate?: string;
  preferences?: {
    allergies?: string[];
    preferred_brands?: string[];
  };
  vip: boolean;
  created_at: string;
  updated_at: string;
}

export interface MockService {
  id: string;
  clientId: string;
  formulaId: string;
  status: 'draft' | 'in_progress' | 'completed' | 'cancelled';
  created_at: string;
  completed_at?: string;
}

/**
 * User Factory
 */
export const createMockUser = (overrides?: Partial<MockUser>): MockUser => ({
  id: `user-${Math.random().toString(36).substr(2, 9)}`,
  email: `colorista${Math.floor(Math.random() * 1000)}@salonier.app`,
  name: getRandomName(),
  isOwner: Math.random() > 0.7,
  salonId: `salon-${Math.random().toString(36).substr(2, 9)}`,
  licenseNumber: `COL${Math.floor(Math.random() * 10000)}`,
  yearsExperience: getRandomExperience(),
  specializations: getRandomSpecializations(),
  phone: `+34 ${Math.floor(Math.random() * 900000000) + 600000000}`,
  ...overrides,
});

/**
 * Client Factory
 */
export const createMockClient = (overrides?: Partial<MockClient>): MockClient => ({
  id: `client-${Math.random().toString(36).substr(2, 9)}`,
  name: getRandomClientName(),
  email: `cliente${Math.floor(Math.random() * 1000)}@email.com`,
  phone: `+34 ${Math.floor(Math.random() * 900000000) + 600000000}`,
  birthdate: getRandomBirthdate(),
  preferences: {
    allergies: getRandomAllergies(),
    preferred_brands: getRandomPreferredBrands(),
  },
  vip: Math.random() > 0.8,
  created_at: getRandomPastDate(),
  updated_at: getRandomRecentDate(),
  ...overrides,
});

/**
 * Service Factory
 */
export const createMockService = (overrides?: Partial<MockService>): MockService => ({
  id: `service-${Math.random().toString(36).substr(2, 9)}`,
  clientId: `client-${Math.random().toString(36).substr(2, 9)}`,
  formulaId: `formula-${Math.random().toString(36).substr(2, 9)}`,
  status: getRandomServiceStatus(),
  created_at: getRandomPastDate(),
  completed_at: Math.random() > 0.5 ? getRandomRecentDate() : undefined,
  ...overrides,
});

/**
 * Create multiple users
 */
export const createMockUsers = (count = 5): MockUser[] => {
  return Array.from({ length: count }, () => createMockUser());
};

/**
 * Create multiple clients
 */
export const createMockClients = (count = 10): MockClient[] => {
  return Array.from({ length: count }, () => createMockClient());
};

/**
 * Create multiple services
 */
export const createMockServices = (count = 15): MockService[] => {
  return Array.from({ length: count }, () => createMockService());
};

// Helper functions
function getRandomName(): string {
  const names = [
    'María García',
    'Carmen López',
    'Ana Martín',
    'Isabel Ruiz',
    'Pilar Hernández',
    'Josefa Díaz',
    'Julia Moreno',
    'Rosa Muñoz',
    'Teresa Álvarez',
    'Laura Romero',
  ];
  return names[Math.floor(Math.random() * names.length)];
}

function getRandomClientName(): string {
  const names = [
    'Sandra Pérez',
    'Cristina Torres',
    'Marta Jiménez',
    'Patricia Navarro',
    'Silvia Serrano',
    'Raquel Blanco',
    'Beatriz Suárez',
    'Mónica Ramos',
    'Eva Gil',
    'Nuria Castro',
  ];
  return names[Math.floor(Math.random() * names.length)];
}

function getRandomExperience(): string {
  const experiences = ['0-2 años', '3-5 años', '5-10 años', '10+ años'];
  return experiences[Math.floor(Math.random() * experiences.length)];
}

function getRandomSpecializations(): string[] {
  const specs = ['coloración', 'mechas', 'balayage', 'corte', 'peinado', 'tratamientos'];
  const count = Math.floor(Math.random() * 3) + 1;
  return specs.sort(() => 0.5 - Math.random()).slice(0, count);
}

function getRandomAllergies(): string[] {
  const allergies = ['PPD', 'Amoníaco', 'Parabenos', 'Sulfatos'];
  if (Math.random() > 0.7) {
    const count = Math.floor(Math.random() * 2) + 1;
    return allergies.sort(() => 0.5 - Math.random()).slice(0, count);
  }
  return [];
}

function getRandomPreferredBrands(): string[] {
  const brands = ['Wella', "L'Oréal", 'Matrix', 'Schwarzkopf', 'Revlon'];
  const count = Math.floor(Math.random() * 2) + 1;
  return brands.sort(() => 0.5 - Math.random()).slice(0, count);
}

function getRandomBirthdate(): string {
  const start = new Date(1960, 0, 1);
  const end = new Date(2005, 0, 1);
  const date = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
  return date.toISOString().split('T')[0];
}

function getRandomPastDate(): string {
  const start = new Date(2023, 0, 1);
  const end = new Date();
  const date = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
  return date.toISOString();
}

function getRandomRecentDate(): string {
  const start = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
  const end = new Date();
  const date = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
  return date.toISOString();
}

function getRandomServiceStatus(): 'draft' | 'in_progress' | 'completed' | 'cancelled' {
  const statuses: ('draft' | 'in_progress' | 'completed' | 'cancelled')[] = [
    'draft',
    'in_progress',
    'completed',
    'cancelled',
  ];
  return statuses[Math.floor(Math.random() * statuses.length)];
}

/**
 * AI Response Mocks
 */
export const createMockAIResponse = (overrides?: any) => ({
  result: {
    formula: 'Wella Koleston 7/0 + 6% vol. (1:1)',
    products: [
      { name: 'Wella Koleston 7/0', quantity: '60g' },
      { name: 'Wella Welloxon 6%', quantity: '60ml' },
    ],
    reasoning: 'Para cubrir canas y aportar brillo natural',
    confidence: 0.95,
  },
  model: 'gpt-4o',
  cached: false,
  cost: 0.025,
  latency: 2800,
  confidence: 0.95,
  ...overrides,
});

/**
 * Error Mocks
 */
export const createMockError = (type?: string) => {
  const errors = {
    network: new Error('fetch failed: network error'),
    timeout: new Error('Request timeout after 30s'),
    auth: new Error('401 unauthorized'),
    validation: new Error('No se pudo identificar el producto'),
    ai: new Error('AI model failed to generate formula'),
    unknown: new Error('Something unexpected happened'),
  };

  const errorType = (type as keyof typeof errors) || 'unknown';
  return errors[errorType] || errors.unknown;
};

/**
 * Session Mocks
 */
export const createMockSession = (overrides?: any) => ({
  user: {
    id: 'mock-user-id',
    email: '<EMAIL>',
    user_metadata: {
      full_name: 'Test User',
    },
  },
  access_token: 'mock-access-token',
  refresh_token: 'mock-refresh-token',
  expires_at: Date.now() + 3600000,
  ...overrides,
});

/**
 * Supabase Response Mocks
 */
export const createMockSupabaseResponse = (data?: any, error?: any) => ({
  data,
  error,
});

/**
 * Test Utilities
 */
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const mockAsyncFunction = <T>(returnValue: T, delay = 100) => {
  return jest
    .fn()
    .mockImplementation(
      () => new Promise(resolve => setTimeout(() => resolve(returnValue), delay))
    );
};

export const mockRejectedAsyncFunction = (error: Error, delay = 100) => {
  return jest
    .fn()
    .mockImplementation(() => new Promise((_, reject) => setTimeout(() => reject(error), delay)));
};
