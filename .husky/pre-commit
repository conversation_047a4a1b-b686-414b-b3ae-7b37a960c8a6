#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Ejecutar linting y formato
npm run lint:fix

# Ejecutar tests críticos (solo los que están funcionando)
npm test -- --testPathPatterns="viability-analyzer|ai-analysis-store|auth-store-simple|useServiceFlow|EnhancedLoadingStates" --passWithNoTests --watchAll=false

# Verificar que no hay console.log en el código
if grep -r "console\." src/ app/ stores/ utils/ --exclude-dir=node_modules --exclude-dir=.expo --exclude="*.test.*" --exclude="*.spec.*"; then
  echo "❌ Found console.log statements in code. Please remove them before committing."
  exit 1
fi

echo "✅ Pre-commit checks passed!"