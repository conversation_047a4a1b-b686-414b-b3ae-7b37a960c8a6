# OpenAI API - Guía de Referencia para Salonier

**Última actualización**: 2025-01-17  
**Versión de la librería**: openai@4.x

Esta documentación proporciona una guía completa para usar la API de OpenAI en el proyecto Salonier, con ejemplos específicos para análisis capilar y generación de fórmulas.

## 📋 Tabla de Contenidos

1. [Instalación y Configuración](#instalación-y-configuración)
2. [Modelos Disponibles](#modelos-disponibles)
3. [Chat Completions API](#chat-completions-api)
4. [Vision API - Análisis de Imágenes](#vision-api---análisis-de-imágenes)
5. [Embeddings API](#embeddings-api)
6. [Images API](#images-api)
7. [Audio APIs](#audio-apis)
8. [Assistants API](#assistants-api)
9. [Fine-tuning](#fine-tuning)
10. [Manejo de Errores](#manejo-de-errores)
11. [Ejemplos Prácticos para Salonier](#ejemplos-prácticos-para-salonier)
12. [Mejores Prácticas](#mejores-prácticas)

---

## 🚀 Instalación y Configuración

### Instalación

```bash
npm install openai
# o
yarn add openai
# o
bun add openai
```

### Configuración Básica

```typescript
import OpenAI from 'openai';

const client = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY, // Por defecto busca esta variable
});
```

### Configuración con Azure

```typescript
import { AzureOpenAI } from 'openai';

const client = new AzureOpenAI({
  apiKey: process.env.AZURE_OPENAI_API_KEY,
  endpoint: process.env.AZURE_OPENAI_ENDPOINT,
  apiVersion: '2024-02-01',
});
```

### Seguridad

- **NUNCA** hardcodees API keys en el código
- Usa variables de entorno (`.env.local`)
- En React Native/Expo, usa `expo-constants` o `react-native-config`
- Considera usar un backend/edge function para llamadas sensibles

---

## 🤖 Modelos Disponibles

### Modelos de Chat

| Modelo          | Descripción                     | Contexto    | Uso Recomendado             |
| --------------- | ------------------------------- | ----------- | --------------------------- |
| `gpt-4o`        | Modelo más avanzado, multimodal | 128K tokens | Análisis complejos, visión  |
| `gpt-4o-mini`   | Versión optimizada de GPT-4o    | 128K tokens | Tareas rápidas, menor costo |
| `gpt-4-turbo`   | GPT-4 con mejoras de velocidad  | 128K tokens | Balance costo/calidad       |
| `gpt-3.5-turbo` | Modelo rápido y económico       | 16K tokens  | Tareas simples              |
| `o1-pro`        | Razonamiento avanzado           | Variable    | Problemas complejos         |
| `o3-mini`       | Modelo experimental             | Variable    | Testing/desarrollo          |

### Modelos Especializados

- **Vision**: `gpt-4o`, `gpt-4o-mini` (soporte nativo para imágenes)
- **Embeddings**: `text-embedding-3-small`, `text-embedding-3-large`
- **Audio**: `whisper-1` (STT), `tts-1`, `tts-1-hd` (TTS)
- **Images**: `dall-e-2`, `dall-e-3`

---

## 💬 Chat Completions API

### Uso Básico

```typescript
const completion = await client.chat.completions.create({
  model: 'gpt-4o',
  messages: [
    { role: 'system', content: 'Eres un experto colorista capilar.' },
    { role: 'user', content: '¿Cómo neutralizo tonos naranjas?' },
  ],
  temperature: 0.7,
  max_tokens: 500,
});

console.log(completion.choices[0].message.content);
```

### Streaming de Respuestas

```typescript
const stream = await client.chat.completions.create({
  model: 'gpt-4o',
  messages: [{ role: 'user', content: 'Explica el proceso de decoloración' }],
  stream: true,
});

for await (const chunk of stream) {
  process.stdout.write(chunk.choices[0]?.delta?.content || '');
}
```

### Structured Output (JSON)

```typescript
const completion = await client.chat.completions.create({
  model: 'gpt-4o',
  messages: [{ role: 'user', content: 'Genera una fórmula para rubio ceniza' }],
  response_format: { type: 'json_object' },
});

// La respuesta será un JSON válido
const formula = JSON.parse(completion.choices[0].message.content);
```

### Con Funciones (Function Calling)

```typescript
const runner = await client.chat.completions.runTools({
  model: 'gpt-4o',
  messages: [{ role: 'user', content: 'Calcula el oxidante necesario' }],
  tools: [
    {
      type: 'function',
      function: {
        name: 'calculatePeroxide',
        description: 'Calcula volumen de oxidante',
        parameters: {
          type: 'object',
          properties: {
            grams: { type: 'number' },
            ratio: { type: 'string' },
          },
        },
      },
    },
  ],
});
```

---

## 🖼️ Vision API - Análisis de Imágenes

### Análisis Básico de Imagen

```typescript
const response = await client.chat.completions.create({
  model: 'gpt-4o',
  messages: [
    {
      role: 'user',
      content: [
        { type: 'text', text: 'Analiza el estado de este cabello' },
        {
          type: 'image_url',
          image_url: {
            url: 'https://example.com/hair-photo.jpg',
            detail: 'high', // "low", "high", o "auto"
          },
        },
      ],
    },
  ],
  max_tokens: 1000,
});
```

### Análisis con Imagen Base64

```typescript
const base64Image = 'data:image/jpeg;base64,/9j/4AAQSkZJRg...';

const response = await client.chat.completions.create({
  model: 'gpt-4o',
  messages: [
    {
      role: 'user',
      content: [
        { type: 'text', text: 'Identifica el tono actual del cabello' },
        {
          type: 'image_url',
          image_url: { url: base64Image },
        },
      ],
    },
  ],
});
```

### Múltiples Imágenes

```typescript
const response = await client.chat.completions.create({
  model: 'gpt-4o',
  messages: [
    {
      role: 'user',
      content: [
        { type: 'text', text: 'Compara estas dos fotos: antes y después' },
        { type: 'image_url', image_url: { url: beforeImageUrl } },
        { type: 'image_url', image_url: { url: afterImageUrl } },
      ],
    },
  ],
});
```

### Consideraciones para Vision

- **Tamaños**: Las imágenes se redimensionan automáticamente
- **Formatos**: JPEG, PNG, GIF (primer frame), WebP
- **Límites**: Máximo 20MB por imagen
- **Detail**:
  - `low`: 512x512px, más económico
  - `high`: hasta 2048x2048px, más tokens
  - `auto`: el modelo decide

---

## 🔢 Embeddings API

### Generar Embeddings

```typescript
const embedding = await client.embeddings.create({
  model: 'text-embedding-3-small',
  input: 'Cabello rubio ceniza nivel 8',
  encoding_format: 'float', // o "base64"
});

console.log(embedding.data[0].embedding); // Vector de números
```

### Embeddings para Múltiples Textos

```typescript
const embeddings = await client.embeddings.create({
  model: 'text-embedding-3-large',
  input: ['Tinte permanente', 'Semi-permanente', 'Demipermanente'],
});

// embeddings.data contiene un embedding por cada input
```

### Casos de Uso

- Búsqueda semántica de productos
- Encontrar fórmulas similares
- Clasificación de tipos de cabello
- Recomendaciones personalizadas

---

## 🎨 Images API

### Generar Imágenes

```typescript
const response = await client.images.generate({
  model: 'dall-e-3',
  prompt: 'Cabello ondulado color caramelo con reflejos dorados',
  n: 1,
  size: '1024x1024',
  quality: 'standard', // o "hd"
  style: 'natural', // o "vivid"
});

console.log(response.data[0].url);
```

### Editar Imágenes

```typescript
const response = await client.images.edit({
  model: 'dall-e-2',
  image: fs.createReadStream('hair.png'),
  mask: fs.createReadStream('mask.png'),
  prompt: 'Añade mechas rubias',
  n: 1,
  size: '512x512',
});
```

### Variaciones de Imagen

```typescript
const response = await client.images.createVariation({
  model: 'dall-e-2',
  image: fs.createReadStream('hairstyle.png'),
  n: 3,
  size: '256x256',
});
```

---

## 🎤 Audio APIs

### Speech to Text (Transcripción)

```typescript
const transcription = await client.audio.transcriptions.create({
  file: fs.createReadStream('consulta-cliente.mp3'),
  model: 'whisper-1',
  language: 'es', // Español
  response_format: 'json', // o "text", "srt", "verbose_json", "vtt"
});

console.log(transcription.text);
```

### Text to Speech

```typescript
const mp3 = await client.audio.speech.create({
  model: 'tts-1',
  voice: 'nova', // alloy, echo, fable, onyx, nova, shimmer
  input: 'Aplica el tinte de medios a puntas',
  speed: 1.0, // 0.25 a 4.0
});

const buffer = Buffer.from(await mp3.arrayBuffer());
await fs.promises.writeFile('instrucciones.mp3', buffer);
```

### Traducción de Audio

```typescript
const translation = await client.audio.translations.create({
  file: fs.createReadStream('french-consultation.mp3'),
  model: 'whisper-1',
});

// Siempre traduce al inglés
console.log(translation.text);
```

---

## 🤝 Assistants API

### Crear un Asistente

```typescript
const assistant = await client.beta.assistants.create({
  name: 'Colorista Experto',
  instructions: 'Eres un colorista profesional que ayuda con fórmulas',
  model: 'gpt-4o',
  tools: [{ type: 'code_interpreter' }, { type: 'file_search' }],
});
```

### Crear Thread y Ejecutar

```typescript
// Crear thread
const thread = await client.beta.threads.create();

// Añadir mensaje
await client.beta.threads.messages.create(thread.id, {
  role: 'user',
  content: 'Necesito una fórmula para cubrir canas al 50%',
});

// Ejecutar asistente
const run = await client.beta.threads.runs.createAndPoll(thread.id, {
  assistant_id: assistant.id,
});

// Obtener respuesta
if (run.status === 'completed') {
  const messages = await client.beta.threads.messages.list(run.thread_id);
  console.log(messages.data[0].content);
}
```

---

## 🎯 Fine-tuning

### Preparar Dataset

```typescript
// Formato JSONL para fine-tuning
const trainingData = [
  {
    messages: [
      { role: 'system', content: 'Eres un experto en coloración' },
      { role: 'user', content: 'Cabello nivel 5, quiero llegar a 8' },
      { role: 'assistant', content: 'Para subir 3 tonos...' },
    ],
  },
  // Mínimo 10 ejemplos
];
```

### Crear Fine-tune

```typescript
// 1. Subir archivo
const file = await client.files.create({
  file: fs.createReadStream('training.jsonl'),
  purpose: 'fine-tune',
});

// 2. Crear fine-tuning job
const fineTune = await client.fineTuning.jobs.create({
  training_file: file.id,
  model: 'gpt-3.5-turbo',
  hyperparameters: {
    n_epochs: 3,
  },
});

// 3. Monitorear progreso
const job = await client.fineTuning.jobs.retrieve(fineTune.id);
console.log(job.status);
```

---

## ⚠️ Manejo de Errores

### Tipos de Errores

```typescript
import {
  APIError,
  AuthenticationError,
  RateLimitError,
  APIConnectionError
} from 'openai';

try {
  const response = await client.chat.completions.create({...});
} catch (error) {
  if (error instanceof RateLimitError) {
    console.log('Límite de rate alcanzado:', error.message);
    // Implementar retry con backoff
  } else if (error instanceof AuthenticationError) {
    console.log('Error de autenticación:', error.message);
  } else if (error instanceof APIConnectionError) {
    console.log('Error de conexión:', error.message);
  } else {
    console.log('Error desconocido:', error);
  }
}
```

### Retry con Exponential Backoff

```typescript
async function callWithRetry(fn: () => Promise<any>, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (error instanceof RateLimitError && i < maxRetries - 1) {
        const delay = Math.pow(2, i) * 1000; // 1s, 2s, 4s
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }
      throw error;
    }
  }
}
```

---

## 💡 Ejemplos Prácticos para Salonier

### Análisis de Foto Capilar

```typescript
async function analyzeHairPhoto(imageBase64: string) {
  const response = await client.chat.completions.create({
    model: 'gpt-4o',
    messages: [
      {
        role: 'system',
        content: `Eres un experto colorista. Analiza la foto y proporciona:
          1. Nivel de tono actual (1-10)
          2. Reflejo predominante
          3. Porcentaje de canas
          4. Estado del cabello
          5. Recomendaciones`,
      },
      {
        role: 'user',
        content: [
          { type: 'text', text: 'Analiza este cabello:' },
          { type: 'image_url', image_url: { url: imageBase64 } },
        ],
      },
    ],
    response_format: { type: 'json_object' },
    max_tokens: 1000,
  });

  return JSON.parse(response.choices[0].message.content);
}
```

### Generación de Fórmula con Contexto

```typescript
async function generateFormula(diagnosis: any, desiredResult: any) {
  const response = await client.chat.completions.create({
    model: 'gpt-4o',
    messages: [
      {
        role: 'system',
        content: `Genera fórmulas de coloración profesionales.
          Usa productos reales del mercado.
          Incluye proporciones exactas y tiempos.`,
      },
      {
        role: 'user',
        content: `
          Estado actual: ${JSON.stringify(diagnosis)}
          Resultado deseado: ${JSON.stringify(desiredResult)}
          
          Genera una fórmula detallada con:
          - Productos específicos
          - Proporciones (1:1, 1:1.5, etc)
          - Volumen de oxidante
          - Tiempo de exposición
          - Técnica de aplicación
        `,
      },
    ],
    temperature: 0.3, // Más determinístico para fórmulas
    response_format: { type: 'json_object' },
  });

  return JSON.parse(response.choices[0].message.content);
}
```

### Optimización con GPT-4o-mini

```typescript
// Para tareas más simples, usa gpt-4o-mini para reducir costos
async function validateFormula(formula: any) {
  const response = await client.chat.completions.create({
    model: 'gpt-4o-mini',
    messages: [
      {
        role: 'system',
        content: 'Valida fórmulas de coloración',
      },
      {
        role: 'user',
        content: `¿Es válida esta fórmula? ${JSON.stringify(formula)}`,
      },
    ],
    max_tokens: 200,
  });

  return response.choices[0].message.content;
}
```

### Análisis Comparativo (Antes/Después)

```typescript
async function compareResults(beforeImage: string, afterImage: string) {
  const response = await client.chat.completions.create({
    model: 'gpt-4o',
    messages: [
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: 'Compara estas fotos y evalúa el resultado del tratamiento',
          },
          {
            type: 'image_url',
            image_url: { url: beforeImage, detail: 'high' },
          },
          {
            type: 'image_url',
            image_url: { url: afterImage, detail: 'high' },
          },
        ],
      },
    ],
    response_format: { type: 'json_object' },
  });

  return JSON.parse(response.choices[0].message.content);
}
```

---

## 📚 Mejores Prácticas

### 1. Optimización de Costos

```typescript
// Usa el modelo apropiado para cada tarea
const models = {
  complex: 'gpt-4o', // Análisis de imagen, fórmulas complejas
  standard: 'gpt-4o-mini', // Validaciones, tareas simples
  simple: 'gpt-3.5-turbo', // Respuestas rápidas, chat básico
};
```

### 2. Gestión de Contexto

```typescript
// Mantén el contexto relevante y conciso
const systemPrompt = `
Rol: Colorista experto
Contexto: ${salonInfo}
Productos disponibles: ${inventory}
Restricciones: ${clientAllergies}
`;
```

### 3. Cacheo de Respuestas

```typescript
import { LRUCache } from 'lru-cache';

const cache = new LRUCache<string, any>({
  max: 100,
  ttl: 1000 * 60 * 60, // 1 hora
});

async function getCachedResponse(key: string, generator: () => Promise<any>) {
  const cached = cache.get(key);
  if (cached) return cached;

  const result = await generator();
  cache.set(key, result);
  return result;
}
```

### 4. Validación de Respuestas

```typescript
import { z } from 'zod';

const FormulaSchema = z.object({
  products: z.array(
    z.object({
      name: z.string(),
      amount: z.number(),
      unit: z.enum(['g', 'ml']),
    })
  ),
  developer: z.object({
    volume: z.number(),
    ratio: z.string(),
  }),
  processingTime: z.number(),
  technique: z.string(),
});

// Validar respuesta de IA
const formula = FormulaSchema.parse(aiResponse);
```

### 5. Monitoreo y Logs

```typescript
async function trackAPIUsage(model: string, tokens: number) {
  // Registra uso para análisis de costos
  await analytics.track('openai_api_call', {
    model,
    tokens,
    cost: calculateCost(model, tokens),
    timestamp: new Date().toISOString(),
  });
}
```

### 6. Límites y Quotas

| Modelo        | TPM (Tokens/min) | RPM (Requests/min) |
| ------------- | ---------------- | ------------------ |
| GPT-4o        | 30,000           | 500                |
| GPT-4o-mini   | 200,000          | 2,000              |
| GPT-3.5-turbo | 200,000          | 3,500              |

### 7. Seguridad

```typescript
// Sanitiza inputs del usuario
function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Prevenir inyección
    .trim()
    .slice(0, 1000); // Limitar longitud
}

// No incluyas información sensible en prompts
const safePrompt = `Analiza cabello del cliente ${clientId}`; // No uses nombres reales
```

---

## 🔗 Recursos Adicionales

- [Documentación Oficial OpenAI](https://platform.openai.com/docs)
- [Pricing Calculator](https://openai.com/pricing)
- [API Reference](https://platform.openai.com/docs/api-reference)
- [Playground](https://platform.openai.com/playground)
- [Token Counter](https://platform.openai.com/tokenizer)

---

## 📞 Soporte

Para problemas específicos del proyecto Salonier:

- Revisa `/docs/TROUBLESHOOTING.md`
- Consulta los logs de Edge Functions
- Verifica la configuración en Supabase

Para problemas con la API de OpenAI:

- [Status Page](https://status.openai.com/)
- [Developer Forum](https://community.openai.com/)
- [Support](https://help.openai.com/)
