# Sistema de Nomenclatura Unificada - Plan de Implementación

## 📋 Resumen Ejecutivo

Este documento detalla la implementación del sistema de nomenclatura unificada para garantizar consistencia entre el inventario y las formulaciones generadas por IA.

## 🎯 Objetivo

Crear un sistema que garantice que los nombres de productos sean consistentes en toda la aplicación, adaptándose a configuraciones regionales y niveles de control de inventario.

## 📊 Estado Actual del Proyecto

- **Fecha inicio**: 2025-01-23
- **Duración estimada**: 3 semanas
- **Prioridad**: Alta

## 🔄 Progreso por Fases

### FASE 1: Base de Datos (100% completado) ✅

- [x] Crear backup de tabla products
- [x] Crear migración para normalizar tipos
- [x] Agregar columna computed display_name
- [x] Mejorar estructura de product_mappings
- [x] Crear índices para búsqueda

### FASE 2: UI de Inventario (100% completado) ✅

- [x] Rediseñar pantalla nuevo producto
  - Vista previa del nombre generado
  - Detección inteligente de duplicados con similitud
  - Integración con ProductNamingService
- [x] Actualizar pantalla editar producto
  - Uso de campos estructurados
  - Generación consistente de nombres
- [x] Mejorar lista de inventario
  - Muestra displayName en lugar de name
  - Visualización de línea y tono cuando disponibles

### FASE 3: Sistema de Matching (100% completado) ✅

- [x] Mejorar algoritmo de matching en InventoryConsumptionService
  - Integración con ProductNamingService
  - Doble método de scoring (normalización + naming)
  - Soporte para búsqueda estructurada
- [x] Actualizar MaterialsSummaryCard
  - Muestra productos matcheados con confianza
  - Indicadores visuales según tipo de match
  - Diferencia entre exact/partial/fuzzy/none
- [x] Crear ProductMatchConfirmation component
  - Modal mejorado para confirmar mappings
  - Guarda asociaciones para uso futuro
  - Búsqueda integrada de productos

### FASE 4: Adaptación Regional (0% completado)

- [ ] Display name regional
- [ ] Búsqueda multi-idioma

### FASE 5: Sistema de Aprendizaje (0% completado)

- [ ] Analytics de mappings
- [ ] Auto-mejora con confianza

## 📝 Notas de Implementación

- Mantener compatibilidad con servicios históricos
- Feature flag para rollback fácil
- Testing exhaustivo antes de producción

## 🚀 Trabajo Completado (23 Enero 2025)

### FASE 1: Base de Datos ✅

1. **Migración SQL creada** (`20250123000000_unified_product_naming.sql`):
   - Normaliza tipos de productos a español
   - Añade columna computed `display_name`
   - Mejora tabla `product_mappings`
   - Crea índices para búsqueda optimizada

2. **ProductNamingService** (`services/productNamingService.ts`):
   - Genera nombres consistentes basados en tipo, marca, línea y tono
   - Soporta configuración regional
   - Calcula similitud entre productos (algoritmo Levenshtein)
   - Parser para extraer componentes de nombres de IA

### FASE 2: UI de Inventario ✅

1. **Pantalla Nuevo Producto** mejorada:
   - Vista previa en tiempo real del nombre generado
   - Detección inteligente de duplicados (>80% similitud)
   - Campos estructurados: marca, línea, tipo, tono
   - Integración completa con ProductNamingService

2. **Pantalla Editar Producto** actualizada:
   - Carga correcta de campos estructurados
   - Generación consistente de nombres
   - Validación de duplicados excluyendo el producto actual

3. **Lista de Inventario** mejorada:
   - Muestra `displayName` en lugar de `name`
   - Visualiza línea y tono cuando están disponibles
   - Mejor organización visual de la información

### FASE 3: Sistema de Matching Inteligente ✅

1. **InventoryConsumptionService mejorado**:
   - Integración dual con ProductNormalizationService y ProductNamingService
   - Búsqueda estructurada por brand, line, type, shade
   - Fallback automático a búsqueda por nombre
   - Scoring combinado para mejor precisión

2. **MaterialsSummaryCard actualizado**:
   - Visualización de productos matcheados con % de confianza
   - Indicadores visuales: ✓ exact, ⚠ partial, ⚠ fuzzy
   - Muestra nombre del producto del inventario asociado
   - Estados claros: "En stock", "Falta", "No encontrado"

3. **ProductMatchConfirmation component**:
   - Modal especializado para confirmar asociaciones
   - Guarda mappings en product_mappings para uso futuro
   - Búsqueda integrada con vista previa de productos
   - Información clara sobre el sistema de aprendizaje

## 🎯 Próximos Pasos

### FASE 4: Adaptación Regional

- Implementar display names según configuración regional
- Soporte para búsqueda multi-idioma
- Mapeo de términos regionales (tinte/color, oxidante/developer)

### FASE 5: Sistema de Aprendizaje

- Dashboard de analytics para mappings más usados
- Auto-sugerencia basada en confirmaciones previas
- Mejora continua de algoritmos de matching
