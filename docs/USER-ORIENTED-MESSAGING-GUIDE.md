# Guía de Mensajes Orientados al Usuario - Salonier

## 🎯 Principios Fundamentales

### 1. **Dirigirse al Usuario Final**
- Todos los mensajes deben estar dirigidos al estilista profesional, no al desarrollador
- Usar lenguaje profesional pero accesible
- Evitar terminología técnica de desarrollo

### 2. **Generar Confianza**
- Los mensajes deben transmitir seguridad y profesionalismo
- Evitar palabras como "pendiente", "validación", "experimental"
- Enfocarse en los beneficios para el usuario

### 3. **Ser Específico y Útil**
- Proporcionar información accionable
- Explicar qué significa cada estado para el trabajo del estilista
- Ofrecer próximos pasos claros

## ❌ Mensajes Problemáticos Corregidos

### Dashboard - Sistema de Feedback

**ANTES (Problemático):**
```
"Aún no hay feedback registrado. Las estadísticas aparecerán cuando los estilistas evalúen las fórmulas."
```

**DESPUÉS (Correcto):**
```
"Comienza a crear fórmulas. Aquí verás las estadísticas de tus servicios y el rendimiento de tus fórmulas."
```

**Por qué es mejor:** Se enfoca en la acción del usuario y explica el beneficio.

### Formulación - Nueva Fórmula

**ANTES (Problemático):**
```
"🔍 NUEVA FÓRMULA: Esta fórmula fue generada por IA y está pendiente de validación. Sus resultados ayudarán a mejorar futuras recomendaciones."
```

**DESPUÉS (Correcto):**
```
"✨ FÓRMULA PERSONALIZADA: Esta fórmula ha sido calculada específicamente para este diagnóstico capilar. Sigue las instrucciones paso a paso para obtener los mejores resultados."
```

**Por qué es mejor:** Genera confianza, enfatiza la personalización y da instrucciones claras.

### Captura de Fotos - Calidad

**ANTES (Confuso):**
```
"Retomar"
```

**DESPUÉS (Claro):**
```
"Mejorar"
```

**Por qué es mejor:** Indica que la foto puede mejorarse sin sonar como un error.

### Dashboard - Estado del Sistema

**ANTES (Técnico):**
```
"Sistema Optimizado"
"Cache bajo objetivo"
```

**DESPUÉS (Usuario-Friendly):**
```
"Sistema Funcionando Correctamente"
"Optimizando rendimiento"
```

**Por qué es mejor:** Usa lenguaje que el usuario entiende y no genera preocupación.

## ✅ Lineamientos para Nuevos Mensajes

### Estados de Carga
- ❌ "Procesando datos..."
- ✅ "Analizando tu diagnóstico..."

### Errores
- ❌ "Error 500: Fallo en el servidor"
- ✅ "No pudimos procesar tu solicitud. Inténtalo de nuevo."

### Éxito
- ❌ "Operación completada exitosamente"
- ✅ "¡Fórmula lista! Revisa los detalles abajo."

### Estados Vacíos
- ❌ "No hay datos disponibles"
- ✅ "Comienza creando tu primer servicio"

## 🎨 Tono y Estilo

### Características del Tono Ideal:
1. **Profesional pero Amigable**
2. **Confiado y Seguro**
3. **Orientado a la Acción**
4. **Específico al Contexto**

### Palabras a Evitar:
- "Validación", "Pendiente", "Experimental"
- "Desarrollador", "Sistema", "Cache"
- "Error", "Fallo", "Problema"
- "Datos", "Procesando", "Algoritmo"

### Palabras a Usar:
- "Personalizada", "Calculada", "Optimizada"
- "Profesional", "Precisa", "Confiable"
- "Lista", "Completa", "Exitosa"
- "Análisis", "Diagnóstico", "Recomendación"

## 🔄 Proceso de Revisión

### Antes de Implementar un Mensaje:
1. **¿Está dirigido al estilista?** ✓
2. **¿Genera confianza?** ✓
3. **¿Es accionable?** ✓
4. **¿Evita jerga técnica?** ✓
5. **¿Explica el beneficio?** ✓

### Ejemplos de Aplicación:

**Contexto: Análisis de IA completado**
- ❌ "Análisis de IA completado con 85% de confianza"
- ✅ "Diagnóstico capilar completo. Revisa las recomendaciones personalizadas."

**Contexto: Fórmula guardada**
- ❌ "Datos guardados en la base de datos"
- ✅ "Fórmula guardada en tu historial de servicios."

**Contexto: Sin conexión**
- ❌ "Error de conectividad de red"
- ✅ "Trabajando sin conexión. Tus cambios se sincronizarán automáticamente."

## 📱 Implementación

### Archivos Clave a Revisar:
- `app/components/FeedbackStats.tsx`
- `app/(tabs)/index.tsx`
- `supabase/functions/salonier-assistant/index.ts`
- `components/PhotoGallery.tsx`
- `components/dashboard/AIMetricsDashboard.tsx`

### Próximos Pasos:
1. Revisar todos los componentes de UI para mensajes problemáticos
2. Actualizar textos de error y estados de carga
3. Revisar notificaciones push y emails
4. Actualizar documentación de usuario

---

**Fecha de creación:** 2025-01-25  
**Última actualización:** 2025-01-25  
**Responsable:** Equipo de Desarrollo Salonier
