# Guía de Testing para Funcionalidad de IA

## Casos de Prueba para Análisis de Productos

### 1. Productos Válidos (Deberían Funcionar)

**Productos de Tinte:**

- `<PERSON>a Koleston 8/0`
- `L'Oréal Majirel 7.1`
- `<PERSON><PERSON><PERSON><PERSON><PERSON> Igora 9-1`
- `Matrix SoColor 6N`

**Productos de Oxidante:**

- `Oxidante 20 vol`
- `Wella Welloxon 30 vol`
- `L'Oréal Oxidante 40 vol`

**Productos de Decolorante:**

- `Well<PERSON> Blondor`
- `<PERSON><PERSON><PERSON><PERSON><PERSON> Igora Vario`
- `L'Oréal Platifiz`

### 2. Productos Inválidos (Deberían Mostrar Error de Validación)

**Códigos no reconocibles:**

- `SV001`
- `PROD123`
- `ABC-XYZ`

**Productos no de belleza:**

- `Coca Cola`
- `iPhone 15`
- `Notebook Dell`

### 3. Casos de Error de Red

**Para simular errores de red:**

- Desconectar WiFi/datos móviles
- Debería mostrar: "Error de conexión" con opción de retry

### 4. Casos de Timeout

**Para simular timeout:**

- Llamadas muy lentas (simulación manual)
- Debería mostrar: "Timeout" con opción de retry

## Comportamiento Esperado

### ✅ Productos Válidos

- **Logging**: Muestra todos los logs detallados
- **Respuesta**: Campos se pre-rellenan automáticamente
- **Feedback**: Alert "✅ Análisis Completado"
- **Tiempo**: Respuesta en 2-5 segundos

### ❌ Productos Inválidos

- **Logging**: Muestra error categorizado como 'validation'
- **Respuesta**: Mensaje específico "Producto no reconocido"
- **Feedback**: Alert con título "Producto no reconocido"
- **Retry**: No debe mostrar opción de retry

### 🌐 Errores de Red

- **Logging**: Muestra error categorizado como 'network'
- **Respuesta**: Mensaje sobre conexión
- **Feedback**: Alert con título "Error de conexión"
- **Retry**: Debe intentar automáticamente 2 veces

### ⏱️ Errores de Timeout

- **Logging**: Muestra error categorizado como 'timeout'
- **Respuesta**: Mensaje sobre tiempo de espera
- **Feedback**: Alert con título "Timeout"
- **Retry**: Debe intentar automáticamente 2 veces

## Logging Esperado

### Llamada Exitosa

```
🚀 Starting AI analysis for: Wella Koleston 8/0
📱 User authenticated: true [user-id]
🔍 Calling salonier-assistant Edge Function...
🔄 Attempt 1/3 calling salonier-assistant
🌐 Raw HTTP Status: 200
🌐 Raw HTTP Body: {"success":true,"data":{...}}
✅ AI Response: {"brand":"Wella","productType":"Tinte",...}
```

### Error de Validación

```
🚀 Starting AI analysis for: SV001
📱 User authenticated: true [user-id]
🔍 Calling salonier-assistant Edge Function...
🔄 Attempt 1/3 calling salonier-assistant
🌐 Raw HTTP Status: 400
🌐 Raw HTTP Body: {"success":false,"error":"No se pudo identificar..."}
❌ Attempt 1 failed: {type: 'validation', canRetry: false}
💥 Error analyzing product: [Error]
🔍 Categorized error: {type: 'validation', userFriendlyMessage: '...'}
```

### Error con Retry

```
🚀 Starting AI analysis for: Wella Koleston 8/0
📱 User authenticated: true [user-id]
🔍 Calling salonier-assistant Edge Function...
🔄 Attempt 1/3 calling salonier-assistant
❌ Attempt 1 failed: {type: 'network', canRetry: true}
⏱️ Waiting 1000ms before retry...
🔄 Attempt 2/3 calling salonier-assistant
🌐 Raw HTTP Status: 200
✅ Success on retry!
```

## Comandos de Testing

### Test Manual desde Terminal

```bash
# Probar producto válido
curl -X POST 'https://ajsamgugqfbttkrlgvbr.supabase.co/functions/v1/salonier-assistant' \
  -H 'Authorization: Bearer [KEY]' \
  -H 'Content-Type: application/json' \
  -d '{"task": "analyze_product", "payload": {"productName": "Wella Koleston 8/0", "language": "es"}}'

# Probar producto inválido
curl -X POST 'https://ajsamgugqfbttkrlgvbr.supabase.co/functions/v1/salonier-assistant' \
  -H 'Authorization: Bearer [KEY]' \
  -H 'Content-Type: application/json' \
  -d '{"task": "analyze_product", "payload": {"productName": "SV001", "language": "es"}}'
```

### Limpieza de Cache (si hay problemas)

```bash
# Limpiar cache de Metro
rm -rf .expo/web/cache
rm -rf node_modules/react-native-css-interop/.cache

# Reiniciar servidor
npx expo start --clear
```

## Checklist de Validación

- [ ] Productos válidos se procesan correctamente
- [ ] Productos inválidos muestran error amigable
- [ ] Errores de red se manejan con retry
- [ ] Logging detallado funciona
- [ ] Alerts muestran títulos y mensajes correctos
- [ ] No hay más errores de "InternalBytecode.js"
- [ ] Retry automático funciona (máximo 3 intentos)
- [ ] Experiencia de usuario fluida incluso con errores

## Solución de Problemas

### Si sigue apareciendo "InternalBytecode.js"

1. Detener servidor: `pkill -f "expo start"`
2. Limpiar cache: `rm -rf .expo/web/cache`
3. Reiniciar: `npx expo start --clear`

### Si la IA no responde

1. Verificar logs de la aplicación
2. Verificar que el usuario esté autenticado
3. Probar con curl directamente
4. Verificar API key de OpenAI en Supabase

### Si los errores no se categorizan correctamente

1. Revisar `ai-error-handler.ts`
2. Verificar que el import esté correcto
3. Añadir más casos de error según sea necesario
