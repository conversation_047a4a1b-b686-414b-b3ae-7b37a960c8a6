# Fixes Adicionales - 2025-01-12

## 1. Error: consent_type null constraint violation

**Problema**: Al insertar en `client_consents`, los campos obligatorios `consent_type` y `consent_text` no se estaban proporcionando.

**Solución**: Agregados los campos faltantes en todas las operaciones de insert:

```typescript
consent_type: 'safety_verification',
consent_text: 'Consentimiento informado para servicio de coloración capilar',
```

### Archivos modificados:

- `stores/client-history-store.ts` - Agregados campos obligatorios en 3 lugares

## 2. Error: ImagePicker.MediaType undefined

**Problema**: La API de expo-image-picker cambió. `MediaTypeOptions` está deprecado y `MediaType` no existe.

**Solución**: Usar arrays de strings en lugar de enums:

```typescript
// Antes (incorrecto):
mediaTypes: ImagePicker.MediaType.Images,
mediaTypes: ImagePicker.MediaTypeOptions.Images,

// Después (correcto):
mediaTypes: ['images'],
```

### Cambios aplicados:

- Para imágenes solamente: `['images']`
- Para videos solamente: `['videos']`
- Para ambos: `['images', 'videos']`

### Archivos modificados:

- `app/service/new.tsx` - Actualizado en todas las llamadas a `launchImageLibraryAsync`

## 3. Error: Invalid response from OpenAI

**Problema**: La Edge Function está fallando al llamar a OpenAI

**Posibles causas**:

1. API key no configurada en Supabase
2. API key inválida o expirada
3. Límite de rate exceeded
4. Problema con el formato de la imagen

**Script de verificación creado**:

```bash
./scripts/check-openai-config.sh
```

### Pasos para resolver:

1. Verificar que `OPENAI_API_KEY` esté configurada en Supabase Dashboard
2. Si no está configurada, agregarla en Settings → Edge Functions → Secrets
3. Redeploy la Edge Function después de configurar
4. Verificar que la API key sea válida y tenga créditos

## Resumen de Estado

### ✅ Resueltos:

- Error de campos obligatorios en client_consents
- Warning de ImagePicker deprecado
- Warning de viewBox inválido (fix anterior)
- Error de columna signature faltante (fix anterior)

### ⚠️ Pendiente de verificación:

- Configuración de OpenAI API key en Supabase

### Recomendaciones:

1. Siempre verificar que todos los campos obligatorios estén presentes al insertar en la base de datos
2. Mantener las dependencias actualizadas y revisar breaking changes
3. Tener un sistema de monitoreo para APIs externas como OpenAI
