# Sistema de Inventario - Documentación Técnica

## Arquitectura

### Visión General

El sistema de inventario está diseñado con una arquitectura modular que respeta los principios de separación de responsabilidades y permite tres niveles de control configurables.

```
┌─────────────────┐     ┌──────────────────┐     ┌─────────────────┐
│   UI Components │────▶│  Zustand Stores  │────▶│  AsyncStorage   │
└─────────────────┘     └──────────────────┘     └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐     ┌──────────────────┐
│    Services     │────▶│      Types       │
└─────────────────┘     └──────────────────┘
```

### Stores (Estado Global)

#### inventory-store.ts

Maneja todo el estado relacionado con productos e inventario:

- CRUD de productos
- Movimientos de stock
- Alertas de inventario
- Búsqueda y filtrado
- Persistencia automática

```typescript
interface InventoryStore {
  products: Product[];
  movements: StockMovement[];
  alerts: InventoryAlert[];
  // ... métodos
}
```

#### salon-config-store.ts

Gestiona la configuración del salón y políticas de precios:

- Nivel de control de inventario
- Configuración de precios y márgenes
- Políticas de redondeo
- Formato de moneda

```typescript
interface SalonConfigStore {
  configuration: SalonConfiguration;
  updateInventoryControlLevel: (level: InventoryControlLevel) => void;
  updatePricing: (pricing: Partial<PricingConfiguration>) => void;
  // ... métodos
}
```

### Services

#### inventoryConsumptionService.ts

Lógica de negocio para consumo y matching de productos:

1. **parseFormula**: Extrae productos de texto de fórmula
2. **findMatchingProducts**: Matching inteligente con normalización
3. **calculateCost**: Calcula costos basados en inventario real
4. **consumeFormulation**: Ejecuta consumo con validaciones

### Flujo de Datos

#### 1. Configuración Inicial

```
Usuario selecciona nivel → salon-config-store → AsyncStorage
                                    ↓
                          Habilita/deshabilita features
```

#### 2. Cálculo de Costos

```
Formula texto → parseFormula → findMatchingProducts → calculateCost
                                        ↓
                              Productos en inventario
                                        ↓
                                 Costo real/estimado
```

#### 3. Validación de Stock

```
Formula → checkStock → Comparar cantidades → Resultado
              ↓                                   ↓
        Stock actual                    hasStock + missingProducts
```

#### 4. Consumo de Inventario

```
Completar servicio → consumeFormulation → updateStock → AsyncStorage
                            ↓                   ↓
                    Stock movements      Alertas si stock bajo
```

## Decisiones de Diseño

### 1. Tres Niveles de Control

- **Solo Fórmulas**: Sin features de inventario (freelancers)
- **Smart Cost**: Costos sin consumo (salones pequeños)
- **Control Total**: Sistema completo (salones grandes)

### 2. Matching Inteligente

- Normalización de nombres (mayúsculas, espacios, acentos)
- Detección de variaciones comunes (vol/volúmenes, oz/onzas)
- Score de similitud para mejores matches

### 3. Persistencia Optimizada

- Debounce de 500ms para evitar escrituras excesivas
- Migración automática de datos antiguos
- Validación de datos al cargar

### 4. UX No Intrusiva

- Features aparecen solo cuando están habilitadas
- Validaciones opcionales (no bloquean flujo)
- Feedback visual claro y consistente

## Guía para Desarrolladores

### Agregar un Nuevo Tipo de Producto

1. Actualizar el tipo `category` en `types/inventory.ts`
2. Agregar lógica de matching en `inventoryConsumptionService.ts`
3. Actualizar UI en componentes relevantes

### Implementar Nueva Política de Precios

1. Agregar campo en `PricingConfiguration`
2. Implementar lógica en `calculateSuggestedPrice`
3. Agregar UI en `PricingSettingsModal`

### Extender Sistema de Alertas

1. Definir nuevo tipo de alerta en `InventoryAlert`
2. Implementar lógica de detección en store
3. Agregar notificación en UI

## Optimizaciones Futuras

### Performance

- Índices para búsqueda rápida de productos
- Caché de cálculos de costos frecuentes
- Lazy loading de movimientos históricos

### Features

- Sincronización en la nube
- Importación/exportación de inventario
- Predicción de consumo con ML
- Integración con proveedores

### UX

- Búsqueda por voz
- Escaneo de códigos de barras
- Sugerencias de reorden automático
- Dashboard de analytics avanzado

## Troubleshooting

### Problema: Costos no se calculan

1. Verificar nivel de control != 'solo-formulas'
2. Confirmar productos en inventario
3. Revisar formato de fórmula

### Problema: Stock no se actualiza

1. Verificar nivel de control == 'control-total'
2. Confirmar switch de consumo activado
3. Revisar logs de consumeFormulation

### Problema: Productos no se encuentran

1. Revisar normalización de nombres
2. Verificar categoría correcta
3. Ajustar threshold de matching si necesario
