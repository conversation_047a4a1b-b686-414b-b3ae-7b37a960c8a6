# Sistema de Conversión Inteligente entre Marcas de Tintes

## Resumen

El sistema de conversión inteligente permite a los coloristas mantener resultados consistentes cuando cambian de marca de tintes, resolviendo uno de los problemas más comunes en salones de belleza.

## Problema que Resuelve

### Escenarios Comunes:

1. **Cliente con fórmula de otro salón**: "En mi peluquería anterior usaban Wella 7/1"
2. **Cambio de proveedor**: El salón cambia de L'Oréal a Schwarzkopf
3. **Stock limitado**: No hay disponibilidad de la marca habitual
4. **Preferencias del cliente**: Alergia o preferencia por marca específica

## Características Principales

### 1. Base de Datos de Conversiones Reales

- 60+ marcas profesionales incluidas
- Conversiones basadas en investigación real
- Niveles de confianza (60-95%)
- Notas técnicas específicas por conversión

### 2. Ajustes Inteligentes

- **Mapeo de tonos**: No es simple cambio de formato (7/1 → 7.13)
- **Proporciones de mezcla**: Ajustadas según concentración de pigmentos
- **Tiempos de procesamiento**: Compensan diferencias entre marcas
- **Advertencias específicas**: Cuando se requieren precauciones especiales

### 3. Interfaz Intuitiva

- Badge "Conversión disponible" en el selector de marcas
- Comparación lado a lado
- Nivel de confianza visible
- Recomendaciones claras

## Cómo Funciona

### Flujo de Usuario:

1. Activar "Mantener el color actual del cliente"
2. Seleccionar marca/línea original
3. Describir la fórmula actual
4. El sistema genera automáticamente la conversión

### Ejemplo de Conversión:

```
Original: Wella Koleston 7/1 + 20vol (1:1)
Convertido a: L'Oréal Majirel 7.13 + 20vol (1:1.5)

Ajustes:
• Tono: 7/1 → 7.13 (incluye beige para compensar)
• Proporción: 1:1 → 1:1.5 (mayor proporción de oxidante)
• Tiempo: +5 minutos (Majirel procesa más lento)
• Confianza: 85%
```

## Datos Técnicos

### Estructura de Conversión:

```typescript
{
  originalFormula: {
    brand: "Wella",
    line: "Koleston",
    tone: "7/1",
    developer: 20,
    ratio: "1:1"
  },
  targetFormula: {
    brand: "L'Oréal",
    line: "Majirel",
    tone: "7.13",
    developer: 20,
    ratio: "1:1.5"
  },
  adjustments: {
    toneMapping: "7/1 → 7.13",
    mixRatio: "1:1 → 1:1.5",
    processingTime: +5,
    notes: [
      "Majirel 7.1 es ceniza puro, usar 7.13 para incluir beige",
      "Mayor proporción de oxidante para mejor cobertura"
    ]
  },
  confidence: 85
}
```

## Marcas Incluidas

### Conversiones de Alta Confianza (90-95%):

- Wella ↔ L'Oréal
- Schwarzkopf ↔ Wella
- L'Oréal ↔ Schwarzkopf

### Conversiones de Media Confianza (80-89%):

- Alfaparf ↔ L'Oréal/Wella
- Matrix ↔ L'Oréal
- Goldwell ↔ Schwarzkopf

### Conversiones Genéricas (60-79%):

- Marcas sin datos específicos
- Basadas en formato de numeración
- Requieren prueba de mechón obligatoria

## Mejores Prácticas

### Para el Colorista:

1. **Siempre realizar prueba de mechón** con conversiones <90% confianza
2. **Documentar resultados** para mejorar futuras conversiones
3. **Considerar el historial químico** del cabello
4. **Ajustar según observación** durante el proceso

### Advertencias Importantes:

- Las conversiones tienen precisión variable (60-95%)
- Los resultados pueden variar según:
  - Estado del cabello
  - Historial químico
  - Técnica de aplicación
  - Lote del producto

## Futuras Mejoras

### Fase 2 (Planificada):

- Integración con IA para conversiones no documentadas
- Aprendizaje de conversiones exitosas
- Sugerencias basadas en historial del salón

### Fase 3 (Visión):

- Predicción visual del resultado
- Ajustes automáticos por tipo de cabello
- Integración con inventario en tiempo real

## Soporte

Para agregar nuevas conversiones o reportar problemas:

1. Documentar la conversión exitosa
2. Incluir fotos antes/después
3. Especificar todos los detalles técnicos
4. Enviar a soporte técnico
