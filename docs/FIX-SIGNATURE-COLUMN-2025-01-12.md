# Fix: Missing 'signature' Column Error

**Date**: 2025-01-12
**Issue**: "Could not find the 'signature' column in the 'client_consents' table"

## Problem

The client-history-store.ts was trying to access a `signature` column that doesn't exist in the database. The actual column name is `signature_data`.

## Root Cause

1. The TypeScript types in `types/database.ts` were outdated
2. The code was using the wrong column name (`signature` instead of `signature_data`)
3. The database schema had evolved through migrations but the types weren't updated

## Solution Applied

### 1. Updated Code References

Changed all occurrences of `consent.signature` to `consent.signature_data` in:

- `stores/client-history-store.ts` (conversion function and insert operations)

### 2. Updated TypeScript Types

Added all missing columns to `types/database.ts` for the `client_consents` table:

- consent_data
- safety_checklist
- patch_test_result
- critical_verifications
- signature_data
- form_version
- language
- completed_steps
- skip_safety
- user_agent

### 3. Created Update Script

Added `scripts/update-database-types.sh` to regenerate types from Supabase:

```bash
./scripts/update-database-types.sh
```

## Database Schema Reference

The actual client_consents table has these columns:

- id (uuid)
- salon_id (uuid)
- client_id (uuid)
- service_id (uuid, nullable)
- consent_type (text)
- consent_text (text)
- signature_url (text, nullable) - For image URLs
- signature_data (text, nullable) - For base64 signature data
- signed_at (timestamp)
- ip_address (inet)
- created_at (timestamp)
- consent_data (jsonb) - Stores consent items array
- safety_checklist (jsonb)
- patch_test_result (text)
- critical_verifications (jsonb)
- form_version (text)
- language (text)
- completed_steps (jsonb)

## Prevention

To prevent this issue in the future:

1. Always regenerate TypeScript types after database migrations
2. Use the provided script to update types: `./scripts/update-database-types.sh`
3. Consider adding this to the deployment process

## Testing

After applying this fix:

1. The consent saving functionality should work without errors
2. No more "missing column" errors should appear in the logs
3. Signatures should be properly stored in the `signature_data` field
