# Chat Assistant Setup Guide

## Overview

The Salonier Chat Assistant is a public Edge Function that provides AI-powered hair color consultation for international users. It supports multiple languages, image analysis, and includes built-in rate limiting for production use.

## Edge Functions Deployed

### 1. `chat-public` (PRODUCTION - Use this one)

- **URL**: `https://ajsamgugqfbttkrlgvbr.supabase.co/functions/v1/chat-public`
- **Version**: 1.0
- **Status**: Active
- **Authentication**: None required (public endpoint)
- **Rate Limit**: 10 requests/minute per IP

### 2. `chat-assistant-public` (Backup)

- **URL**: `https://ajsamgugqfbttkrlgvbr.supabase.co/functions/v1/chat-assistant-public`
- **Version**: 1
- **Status**: Active
- **Authentication**: JWT required (need to disable for public use)

### 3. `chat-assistant` (Legacy)

- **URL**: `https://ajsamgugqfbttkrlgvbr.supabase.co/functions/v1/chat-assistant`
- **Version**: 7
- **Status**: Active
- **Authentication**: JWT required

## Configuration Required

### 1. Set OpenAI API Key

1. Go to [Supabase Dashboard](https://supabase.com/dashboard/project/ajsamgugqfbttkrlgvbr/functions)
2. Click on `chat-public` function
3. Go to "Secrets" tab
4. Add secret:
   - Name: `OPENAI_API_KEY`
   - Value: Your OpenAI API key

### 2. Disable JWT Verification (IMPORTANT)

By default, all Edge Functions require JWT verification. To make the chat public:

1. Go to the function settings in Supabase Dashboard
2. Find "Verify JWT" toggle
3. Turn it OFF
4. Save changes

**Note**: As of deployment, JWT verification may still be enabled. You need to disable it manually through the Supabase Dashboard or use the Supabase CLI.

## API Usage

### Request Format

```json
{
  "conversationId": "unique-conversation-id",
  "message": "User's message",
  "salonId": "salon-uuid",
  "userId": "user-uuid",
  "attachments": [
    {
      "type": "image",
      "url": "data:image/jpeg;base64,...",
      "mimeType": "image/jpeg"
    }
  ]
}
```

### Response Format

```json
{
  "content": "AI assistant's response",
  "usage": {
    "promptTokens": 150,
    "completionTokens": 250,
    "totalTokens": 400,
    "cost": 0.0035
  }
}
```

### Error Response

```json
{
  "error": "Error message",
  "retryAfter": 60 // Only for rate limit errors
}
```

## Testing the Endpoint

### Using cURL (No Auth Required)

```bash
curl -X POST https://ajsamgugqfbttkrlgvbr.supabase.co/functions/v1/chat-public \
  -H "Content-Type: application/json" \
  -d '{
    "conversationId": "test-123",
    "message": "How to neutralize orange tones?",
    "salonId": "550e8400-e29b-41d4-a716-************",
    "userId": "550e8400-e29b-41d4-a716-************"
  }'
```

### Using the Test Script

```bash
./scripts/test-chat-public.sh
```

## Features

### 1. Multi-language Support

The assistant automatically responds in the user's language.

### 2. Image Analysis

Supports up to 3 images per request using OpenAI's Vision API.

### 3. Rate Limiting

- 10 requests per minute per IP address
- Returns 429 status with retry information

### 4. Privacy Features

- No authentication required
- Minimal logging (partial IP only)
- No personal data stored

### 5. International Considerations

- Adapts recommendations by region
- Considers different hair types
- Uses metric measurements
- Considers local regulations

## Troubleshooting

### Error: "Missing authorization header" (401)

**Cause**: JWT verification is still enabled
**Solution**: Disable JWT verification in Supabase Dashboard

### Error: "Chat service not available" (503)

**Cause**: OPENAI_API_KEY not configured
**Solution**: Add the API key in Edge Function secrets

### Error: "Too many requests" (429)

**Cause**: Rate limit exceeded
**Solution**: Wait 60 seconds before retry

### Error: "AI service is busy" (500)

**Cause**: OpenAI rate limit or service issue
**Solution**: Retry after a few moments

## Cost Monitoring

Each request costs approximately:

- Text only: $0.001 - $0.005
- With images: $0.005 - $0.020

Monitor usage through:

1. OpenAI Dashboard for API usage
2. Supabase Dashboard for function invocations

## Security Considerations

1. **Rate Limiting**: Prevents abuse with 10 req/min limit
2. **Input Validation**: Message limited to 1000 characters
3. **Image Limits**: Max 3 images, 5MB each
4. **Error Handling**: No internal errors exposed
5. **CORS**: Open for international access

## Deployment Updates

To update the function:

```bash
# Using Supabase CLI
supabase functions deploy chat-public

# Or using MCP
# Update the index.ts file then deploy through Supabase Dashboard
```

## Integration in App

The app is already configured to use `chat-public`:

- File: `stores/chat-store.ts`
- Line: 409
- Function: `sendMessage()`

No changes needed in the app code.

## Support

For issues:

1. Check Edge Function logs in Supabase Dashboard
2. Verify OpenAI API key is valid and has credits
3. Ensure JWT verification is disabled
4. Check rate limits aren't exceeded

---

Last Updated: 2025-02-10
