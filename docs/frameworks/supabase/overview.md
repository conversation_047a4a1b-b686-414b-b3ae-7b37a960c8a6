# Supabase - Documentación General

**Versión del proyecto**: 2.50.5  
**Fecha de actualización**: Enero 2025  
**Fuente**: [Supabase Documentation](https://supabase.com/docs)

## Descripción General

Supabase es una plataforma de backend como servicio (BaaS) que proporciona una base de datos Postgres completa con funcionalidades en tiempo real, autenticación, almacenamiento y Edge Functions.

## Características Principales

### 🗄️ Database

- **Postgres completo** con funcionalidades en tiempo real
- **Row Level Security (RLS)** integrado
- **Backups automáticos**
- **Extensiones** y módulos adicionales
- **Migraciones** de bases de datos

### 🔐 Authentication

- **Email y password** tradicional
- **Passwordless** (magic links)
- **OAuth** (Google, GitHub, etc.)
- **Mobile logins**
- **Gestión de identidades** multiproveedor

### 📁 Storage

- **Almacenamiento de archivos** grandes
- **Organización** y transformación
- **Servicio** de archivos integrado
- **Políticas de seguridad** personalizadas

### ⚡ Realtime

- **Escucha cambios** en la base de datos
- **Sincronización** de estados entre clientes
- **Broadcast** de datos a canales suscritos
- **Presencia** de usuarios en tiempo real

### 🌐 Edge Functions

- **Funciones distribuidas** globalmente
- **Ejecución** cerca del usuario
- **Latencia mínima**
- **Serverless** functions

## Configuración con React Native / Expo

### 1. Instalación (Actualizado con Context7)

```bash
# Gestores de paquetes soportados
npm install @supabase/supabase-js
yarn add @supabase/supabase-js
pnpm add @supabase/supabase-js

# Para React Native con Expo
npx expo install @supabase/supabase-js @react-native-async-storage/async-storage

# Para React Native puro
npm install @supabase/supabase-js @react-native-async-storage/async-storage

# Para Deno (via JSR)
import { createClient } from 'npm:@supabase/supabase-js@2'
```

### 🌐 CDN Options (Web)

```html
<!-- Via jsdelivr -->
<script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

<!-- Via unpkg -->
<script src="https://unpkg.com/@supabase/supabase-js@2"></script>
```

### 📋 Áreas Funcionales Principales

1. **Database Operations**: Select, Insert, Update, Delete, Filtering
2. **Authentication**: User management, Sign-in methods, Session handling
3. **Real-time Subscriptions**: Database change listeners
4. **Edge Functions**: Serverless functions
5. **Storage**: File upload, download, management
6. **Multi-factor Authentication**: MFA support

### 2. Configuración del Cliente

```typescript
// lib/supabase.ts
import 'react-native-url-polyfill/auto';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://your-project.supabase.co';
const supabaseAnonKey = 'your-anon-key';

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});
```

### 3. Configuración de Tipos

```typescript
// types/supabase.ts
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}
```

## Autenticación

### 1. Registro de Usuario

```typescript
const signUp = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
  });

  if (error) throw error;
  return data;
};
```

### 2. Inicio de Sesión

```typescript
const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) throw error;
  return data;
};
```

### 3. Cerrar Sesión

```typescript
const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  if (error) throw error;
};
```

### 4. Escuchar Cambios de Autenticación

```typescript
import { useEffect, useState } from 'react';
import { Session } from '@supabase/supabase-js';

export function useAuth() {
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Obtener sesión actual
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setLoading(false);
    });

    // Escuchar cambios de autenticación
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  return { session, loading };
}
```

## Operaciones de Base de Datos

### 1. Insertar Datos

```typescript
const insertData = async (data: any) => {
  const { data: result, error } = await supabase.from('table_name').insert(data).select();

  if (error) throw error;
  return result;
};
```

### 2. Consultar Datos

```typescript
const fetchData = async () => {
  const { data, error } = await supabase
    .from('table_name')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data;
};
```

### 3. Actualizar Datos

```typescript
const updateData = async (id: string, updates: any) => {
  const { data, error } = await supabase.from('table_name').update(updates).eq('id', id).select();

  if (error) throw error;
  return data;
};
```

### 4. Eliminar Datos

```typescript
const deleteData = async (id: string) => {
  const { error } = await supabase.from('table_name').delete().eq('id', id);

  if (error) throw error;
};
```

## Realtime

### 1. Suscripción a Cambios

```typescript
useEffect(() => {
  const channel = supabase
    .channel('table_changes')
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'table_name',
      },
      payload => {
        console.log('Change received!', payload);
      }
    )
    .subscribe();

  return () => {
    supabase.removeChannel(channel);
  };
}, []);
```

### 2. Broadcast

```typescript
const sendBroadcast = async (message: any) => {
  const channel = supabase.channel('room-1');

  await channel.send({
    type: 'broadcast',
    event: 'message',
    payload: message,
  });
};
```

### 3. Presence

```typescript
const trackPresence = async (userId: string) => {
  const channel = supabase.channel('room-1');

  await channel.track({
    user_id: userId,
    online_at: new Date().toISOString(),
  });
};
```

## Storage

### 1. Subir Archivos

```typescript
const uploadFile = async (file: File, path: string) => {
  const { data, error } = await supabase.storage.from('bucket-name').upload(path, file);

  if (error) throw error;
  return data;
};
```

### 2. Descargar Archivos

```typescript
const downloadFile = async (path: string) => {
  const { data, error } = await supabase.storage.from('bucket-name').download(path);

  if (error) throw error;
  return data;
};
```

### 3. Obtener URL Pública

```typescript
const getPublicUrl = (path: string) => {
  const { data } = supabase.storage.from('bucket-name').getPublicUrl(path);

  return data.publicUrl;
};
```

## Edge Functions

### 1. Crear Edge Function

```typescript
// supabase/functions/hello-world/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';

serve(async req => {
  const { name } = await req.json();

  const data = {
    message: `Hello ${name}!`,
  };

  return new Response(JSON.stringify(data), { headers: { 'Content-Type': 'application/json' } });
});
```

### 2. Invocar Edge Function

```typescript
const callFunction = async (functionName: string, body: any) => {
  const { data, error } = await supabase.functions.invoke(functionName, {
    body: JSON.stringify(body),
  });

  if (error) throw error;
  return data;
};
```

## Row Level Security (RLS)

### 1. Habilitar RLS

```sql
-- Habilitar RLS en la tabla
ALTER TABLE table_name ENABLE ROW LEVEL SECURITY;
```

### 2. Crear Políticas

```sql
-- Política para permitir que los usuarios vean solo sus propios datos
CREATE POLICY "Users can view own data" ON table_name
  FOR SELECT USING (auth.uid() = user_id);

-- Política para permitir que los usuarios insertan solo sus propios datos
CREATE POLICY "Users can insert own data" ON table_name
  FOR INSERT WITH CHECK (auth.uid() = user_id);
```

### 3. Usar JWT en Políticas

```sql
-- Política basada en claims del JWT
CREATE POLICY "Admin access" ON table_name
  FOR ALL USING (
    auth.jwt() ->> 'role' = 'admin'
  );
```

## Mejores Prácticas

### 1. Manejo de Errores

```typescript
const handleSupabaseError = (error: any) => {
  console.error('Supabase error:', error);

  if (error.code === 'PGRST301') {
    // Manejar error de RLS
    throw new Error('No tienes permisos para realizar esta acción');
  }

  throw new Error(error.message || 'Error desconocido');
};
```

### 2. Optimización de Consultas

```typescript
const optimizedQuery = async () => {
  const { data, error } = await supabase
    .from('table_name')
    .select('id, name, created_at') // Solo campos necesarios
    .range(0, 9) // Paginación
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data;
};
```

### 3. Caché y Offline

```typescript
import AsyncStorage from '@react-native-async-storage/async-storage';

const cachedQuery = async (cacheKey: string) => {
  try {
    // Intentar obtener datos del caché
    const cached = await AsyncStorage.getItem(cacheKey);
    if (cached) {
      return JSON.parse(cached);
    }

    // Si no hay caché, obtener de Supabase
    const { data, error } = await supabase.from('table_name').select('*');

    if (error) throw error;

    // Guardar en caché
    await AsyncStorage.setItem(cacheKey, JSON.stringify(data));
    return data;
  } catch (error) {
    console.error('Error in cached query:', error);
    throw error;
  }
};
```

### 4. Tipos TypeScript

```typescript
// Generar tipos automáticamente
npx supabase gen types typescript --project-id your-project-id > types/supabase.ts

// Usar tipos en el cliente
import { Database } from './types/supabase';

const typedSupabase = createClient<Database>(supabaseUrl, supabaseKey);
```

## Debugging

### 1. Logs en Edge Functions

```typescript
// En Edge Functions
console.log('Debug info:', { data, timestamp: new Date().toISOString() });
```

### 2. Monitoring

```typescript
const monitorQuery = async () => {
  const startTime = Date.now();

  try {
    const data = await supabase.from('table_name').select('*');
    const duration = Date.now() - startTime;

    console.log(`Query completed in ${duration}ms`);
    return data;
  } catch (error) {
    console.error('Query failed:', error);
    throw error;
  }
};
```

## Recursos Adicionales

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase React Native Guide](https://supabase.com/docs/guides/getting-started/tutorials/with-expo-react-native)
- [Supabase GitHub](https://github.com/supabase/supabase)
- [Supabase Community](https://github.com/supabase/supabase/discussions)

## Compatibilidad

### Versiones Soportadas

- React Native: 0.70+
- Expo: SDK 48+
- Node.js: 18+
- TypeScript: 4.9+

### Limitaciones Conocidas

- Algunas funcionalidades requieren polyfills para React Native
- Edge Functions tienen limitaciones de runtime de Deno
- RLS puede impactar performance en consultas complejas
