# Zustand 5.0.5 - Documentación General

**Versión del proyecto**: 5.0.5  
**Fecha de actualización**: Enero 2025  
**Fuente**: [Zustand Documentation](https://zustand.docs.pmnd.rs/)

## Descripción General

Zustand es una librería de gestión de estado ligera y flexible para React que elimina el boilerplate y proporciona una API simple basada en hooks. Es más simple que Redux y más performante que React Context.

## Características Principales

### 🐻 Características Clave

- **Minimal boilerplate**: Sin providers, reducers o actions complejas
- **Hook-based**: Integración natural con React hooks
- **TypeScript**: Soporte completo para TypeScript
- **Performance**: Evita re-renders innecesarios
- **Flexible**: Funciona dentro y fuera de componentes React
- **Lightweight**: ~2.5kb minified + gzipped

### 🚀 Ventajas sobre otras librerías

- **vs Redux**: Menos boilerplate, más simple
- **vs Context**: Mejor performance, menos re-renders
- **vs Recoil**: Más estable, menos experimental
- **vs Jotai**: Más simple para casos de uso básicos

## Instalación

```bash
# npm
npm install zustand

# yarn
yarn add zustand

# pnpm
pnpm add zustand
```

## Uso Básico

### 1. Crear un Store Simple

```typescript
import { create } from 'zustand';

interface BearState {
  bears: number;
  increase: (by: number) => void;
  decrease: (by: number) => void;
  reset: () => void;
}

const useBearStore = create<BearState>()(set => ({
  bears: 0,
  increase: by => set(state => ({ bears: state.bears + by })),
  decrease: by => set(state => ({ bears: state.bears - by })),
  reset: () => set({ bears: 0 }),
}));
```

### 2. Usar en Componentes

```typescript
import { useBearStore } from './store';

function BearCounter() {
  const bears = useBearStore((state) => state.bears);
  const increase = useBearStore((state) => state.increase);

  return (
    <div>
      <h1>{bears} bears around here...</h1>
      <button onClick={() => increase(1)}>Add bear</button>
    </div>
  );
}
```

### 3. Selector Múltiple

```typescript
function BearControls() {
  const { bears, increase, decrease, reset } = useBearStore((state) => ({
    bears: state.bears,
    increase: state.increase,
    decrease: state.decrease,
    reset: state.reset,
  }));

  return (
    <div>
      <p>Bears: {bears}</p>
      <button onClick={() => increase(1)}>+</button>
      <button onClick={() => decrease(1)}>-</button>
      <button onClick={reset}>Reset</button>
    </div>
  );
}
```

## TypeScript Avanzado

### 1. Usando `create<State>()()`

```typescript
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

interface BearState {
  bears: number;
  increase: (by: number) => void;
}

const useBearStore = create<BearState>()(
  devtools(
    persist(
      set => ({
        bears: 0,
        increase: by => set(state => ({ bears: state.bears + by })),
      }),
      {
        name: 'bear-storage',
      }
    )
  )
);
```

### 2. Usando `combine` para Inferencia

```typescript
import { create } from 'zustand';
import { combine } from 'zustand/middleware';

const useBearStore = create(
  combine({ bears: 0 }, set => ({
    increase: (by: number) => set(state => ({ bears: state.bears + by })),
  }))
);
```

### 3. Slices Pattern

```typescript
import { create, StateCreator } from 'zustand';

interface BearSlice {
  bears: number;
  addBear: () => void;
  eatFish: () => void;
}

interface FishSlice {
  fishes: number;
  addFish: () => void;
}

const createBearSlice: StateCreator<BearSlice & FishSlice, [], [], BearSlice> = set => ({
  bears: 0,
  addBear: () => set(state => ({ bears: state.bears + 1 })),
  eatFish: () => set(state => ({ fishes: state.fishes - 1 })),
});

const createFishSlice: StateCreator<BearSlice & FishSlice, [], [], FishSlice> = set => ({
  fishes: 0,
  addFish: () => set(state => ({ fishes: state.fishes + 1 })),
});

const useBoundStore = create<BearSlice & FishSlice>()((...a) => ({
  ...createBearSlice(...a),
  ...createFishSlice(...a),
}));
```

## Acciones Asíncronas

### 1. Fetch Básico

```typescript
interface ApiState {
  data: any[] | null;
  loading: boolean;
  error: string | null;
  fetchData: () => Promise<void>;
}

const useApiStore = create<ApiState>()(set => ({
  data: null,
  loading: false,
  error: null,
  fetchData: async () => {
    set({ loading: true, error: null });
    try {
      const response = await fetch('/api/data');
      const data = await response.json();
      set({ data, loading: false });
    } catch (error) {
      set({ error: error.message, loading: false });
    }
  },
}));
```

### 2. Acciones Asíncronas con Axios

```typescript
import axios from 'axios';

const useCounterStore = create<{
  counter: number;
  loading: boolean;
  incrementCounter: () => Promise<void>;
}>()(set => ({
  counter: 0,
  loading: false,
  incrementCounter: async () => {
    set({ loading: true });
    try {
      const { data } = await axios.post('/api/counter/increment');
      set({ counter: data.counter, loading: false });
    } catch (error) {
      set({ loading: false });
      throw error;
    }
  },
}));
```

### 3. Patrón de Loading States

```typescript
interface AsyncState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

const createAsyncSlice =
  <T>(fetchFunction: () => Promise<T>): StateCreator<AsyncState<T>> =>
  set => ({
    data: null,
    loading: false,
    error: null,
    fetch: async () => {
      set({ loading: true, error: null });
      try {
        const data = await fetchFunction();
        set({ data, loading: false });
      } catch (error) {
        set({ error: error.message, loading: false });
      }
    },
  });
```

## Middleware

### 1. Persist Middleware

```typescript
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const useBearStore = create<BearState>()(
  persist(
    set => ({
      bears: 0,
      increase: by => set(state => ({ bears: state.bears + by })),
    }),
    {
      name: 'bear-storage', // nombre único
      storage: createJSONStorage(() => sessionStorage), // opcional
    }
  )
);
```

### 2. DevTools Middleware

```typescript
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

const useBearStore = create<BearState>()(
  devtools(
    set => ({
      bears: 0,
      increase: by => set(state => ({ bears: state.bears + by })),
    }),
    {
      name: 'bear-store',
    }
  )
);
```

### 3. Immer Middleware

```typescript
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

const useBearStore = create<BearState>()(
  immer(set => ({
    bears: 0,
    increase: by =>
      set(state => {
        state.bears += by;
      }),
  }))
);
```

### 4. Subscriptions

```typescript
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

const useBearStore = create<BearState>()(
  subscribeWithSelector(set => ({
    bears: 0,
    increase: by => set(state => ({ bears: state.bears + by })),
  }))
);

// Suscribirse a cambios específicos
useBearStore.subscribe(
  state => state.bears,
  bears => console.log('Bears changed:', bears)
);
```

## Patrones Avanzados

### 1. Store Fuera de React

```typescript
// store.ts
import { create } from 'zustand';

const useStore = create(() => ({
  count: 0,
  increment: () => useStore.setState(state => ({ count: state.count + 1 })),
}));

// Usar fuera de componentes
const { increment } = useStore.getState();
increment();

// Obtener estado actual
const currentCount = useStore.getState().count;
```

### 2. Resetear Store

```typescript
const initialState = {
  bears: 0,
  fishes: 0,
};

const useStore = create<StoreState>()(set => ({
  ...initialState,
  increase: by => set(state => ({ bears: state.bears + by })),
  reset: () => set(initialState),
}));
```

### 3. Computación Derivada

```typescript
const useStore = create<{
  items: Item[];
  completedItems: Item[];
  addItem: (item: Item) => void;
}>()((set, get) => ({
  items: [],
  get completedItems() {
    return get().items.filter(item => item.completed);
  },
  addItem: item => set(state => ({ items: [...state.items, item] })),
}));
```

### 4. Equality Functions

```typescript
import { shallow } from 'zustand/shallow';

// Comparación superficial
const { nuts, honey } = useBearStore(state => ({ nuts: state.nuts, honey: state.honey }), shallow);

// Comparación custom
const bears = useBearStore(
  state => state.bears,
  (prev, next) => prev === next
);
```

## Optimización de Performance

### 1. Selectores Eficientes

```typescript
// ❌ Mal - crea nuevo objeto en cada render
const Component = () => {
  const { bears, increase } = useBearStore(state => ({
    bears: state.bears,
    increase: state.increase,
  }));

  // ...
};

// ✅ Bien - selectores separados
const Component = () => {
  const bears = useBearStore(state => state.bears);
  const increase = useBearStore(state => state.increase);

  // ...
};
```

### 2. Memoización con React.memo

```typescript
import React from 'react';

const BearDisplay = React.memo(({ bears }: { bears: number }) => {
  return <div>Bears: {bears}</div>;
});

const App = () => {
  const bears = useBearStore(state => state.bears);
  return <BearDisplay bears={bears} />;
};
```

### 3. Lazy Initialization

```typescript
const useStore = create<State>()(set => ({
  // Lazy initialization para valores costosos
  get expensiveValue() {
    return computeExpensiveValue();
  },

  // O usar una función
  getExpensiveValue: () => {
    return computeExpensiveValue();
  },
}));
```

## Testing

### 1. Test Básico

```typescript
import { renderHook, act } from '@testing-library/react';
import { useBearStore } from './store';

describe('Bear Store', () => {
  test('should increase bear count', () => {
    const { result } = renderHook(() => useBearStore());

    act(() => {
      result.current.increase(1);
    });

    expect(result.current.bears).toBe(1);
  });
});
```

### 2. Test con Reset

```typescript
import { useBearStore } from './store';

beforeEach(() => {
  useBearStore.setState({ bears: 0 });
});
```

### 3. Mock del Store

```typescript
import { create } from 'zustand';

const createMockStore = (initialState = {}) => {
  return create(() => ({
    bears: 0,
    increase: jest.fn(),
    ...initialState,
  }));
};
```

## Mejores Prácticas

### 1. Estructura del Store

```typescript
// ✅ Organizar por dominio
const useAuthStore = create<AuthState>()(set => ({
  user: null,
  login: async credentials => {
    /* ... */
  },
  logout: () => {
    /* ... */
  },
}));

const useCartStore = create<CartState>()(set => ({
  items: [],
  addItem: item => {
    /* ... */
  },
  removeItem: id => {
    /* ... */
  },
}));
```

### 2. Separar Lógica

```typescript
// actions.ts
export const bearActions = (set: SetState<BearState>, get: GetState<BearState>) => ({
  increase: (by: number) => set(state => ({ bears: state.bears + by })),
  decrease: (by: number) => set(state => ({ bears: state.bears - by })),
  reset: () => set({ bears: 0 }),
});

// store.ts
import { bearActions } from './actions';

const useBearStore = create<BearState>()((set, get) => ({
  bears: 0,
  ...bearActions(set, get),
}));
```

### 3. Tipado Estricto

```typescript
interface StoreState {
  count: number;
  increment: () => void;
  decrement: () => void;
}

// Asegurarse de que todas las propiedades estén tipadas
const useStore = create<StoreState>()(set => ({
  count: 0,
  increment: () => set(state => ({ count: state.count + 1 })),
  decrement: () => set(state => ({ count: state.count - 1 })),
}));
```

## Migración y Compatibilidad

### 1. Migrar desde Redux

```typescript
// Redux
const INCREMENT = 'INCREMENT';
const increment = () => ({ type: INCREMENT });
const reducer = (state = { count: 0 }, action) => {
  switch (action.type) {
    case INCREMENT:
      return { count: state.count + 1 };
    default:
      return state;
  }
};

// Zustand
const useStore = create<{ count: number; increment: () => void }>()(set => ({
  count: 0,
  increment: () => set(state => ({ count: state.count + 1 })),
}));
```

### 2. Migrar desde Context

```typescript
// Context
const CountContext = createContext();
const CountProvider = ({ children }) => {
  const [count, setCount] = useState(0);
  return (
    <CountContext.Provider value={{ count, setCount }}>
      {children}
    </CountContext.Provider>
  );
};

// Zustand
const useCountStore = create<{ count: number; setCount: (count: number) => void }>()((set) => ({
  count: 0,
  setCount: (count) => set({ count }),
}));
```

## Recursos Adicionales

- [Zustand Documentation](https://zustand.docs.pmnd.rs/)
- [Zustand GitHub](https://github.com/pmndrs/zustand)
- [Zustand Examples](https://github.com/pmndrs/zustand/tree/main/examples)
- [Zustand Community](https://discord.gg/poimandres)

## Compatibilidad

### Versiones Soportadas

- React: 16.8+
- TypeScript: 4.1+
- Node.js: 16+

### Navegadores Soportados

- Chrome: 91+
- Firefox: 90+
- Safari: 14.1+
- Edge: 91+
