# React Native 0.79 - Documentación General

**Versión del proyecto**: 0.79.1 (actualizada)  
**Fecha de actualización**: Enero 2025  
**Fuente**: [React Native Docs](https://reactnative.dev/)

## Descripción General

React Native permite construir aplicaciones móviles nativas usando React. Es una biblioteca que proporciona una base sólida para desarrollar aplicaciones multiplataforma con JavaScript.

## Características Principales de React Native 0.79

### 🚀 Nuevas Características (Abril 2025)

1. **JSC Moving to Community Package**: Migración hacia una versión de JavaScriptCore mantenida por la comunidad
2. **New Native Module Registration**: Nueva forma de registrar módulos nativos a través de modificaciones en package.json
3. **Metro v0.82**: Mejoras de rendimiento con tiempos de inicio hasta 3x más rápidos
4. **Android Performance**: Mejoras en el tiempo de inicio de apps Android (hasta 400ms más rápido)
5. **Package Resolution**: Soporte mejorado para resolución de "exports" e "imports"

### 🔧 Cambios Importantes

- **Eliminación de Remote JS Debugging**: Se removió el debugging remoto vía Chrome
- **API Modernization**: Actualización del ExceptionsManager
- **Compatibilidad**: Incompatibilidades conocidas con Firebase y AWS Amplify

## Conceptos Clave

### 1. Componentes Nativos vs. Componentes Core

- **Componentes Core**: View, Text, Image, ScrollView, TextInput
- **Componentes Nativos**: Componentes específicos de plataforma

### 2. Fundamentos de React

- JSX syntax
- Components y Props
- State y Lifecycle
- Hooks (useState, useEffect, etc.)

### 3. Navegación

- Stack Navigation
- Tab Navigation
- Drawer Navigation

## Arquitectura

```
React Native App
├── JavaScript Thread
├── Native Thread (UI)
├── Bridge (comunicación)
└── Shadow Thread (Layout)
```

## Configuración del Entorno

### Requisitos Previos

- Node.js (versión LTS)
- React Native CLI o Expo CLI
- Android Studio / Xcode

### Instalación Básica

```bash
npx react-native init MyApp
cd MyApp
npx react-native run-android
npx react-native run-ios
```

## Componentes Principales (Actualizado con Context7)

### 1. Componentes Básicos

#### View

**Descripción**: El componente más fundamental para construir una UI en React Native.

```jsx
import { View } from 'react-native';

<View style={{ flex: 1, backgroundColor: '#fff' }}>{/* Contenido */}</View>;
```

#### Text

**Descripción**: Componente para mostrar texto en la aplicación.

```jsx
import { Text } from 'react-native';

<Text style={{ fontSize: 18, color: '#333' }}>Hello, React Native!</Text>;
```

#### Image

**Descripción**: Componente para mostrar imágenes.

```jsx
import { Image } from 'react-native';

<Image source={{ uri: 'https://example.com/image.jpg' }} style={{ width: 200, height: 200 }} />;
```

#### TextInput

**Descripción**: Para ingresar texto en la aplicación vía teclado.

```jsx
import { TextInput } from 'react-native';

<TextInput
  style={{ borderWidth: 1, padding: 10 }}
  placeholder="Ingresa texto aquí"
  onChangeText={text => console.log(text)}
/>;
```

### 2. Componentes de Interfaz de Usuario

#### Button

**Descripción**: Componente básico de botón para manejar toques.

```jsx
import { Button } from 'react-native';

<Button title="Presiona aquí" onPress={() => console.log('Botón presionado')} />;
```

#### Switch

**Descripción**: Renderiza una entrada booleana.

```jsx
import { Switch } from 'react-native';

<Switch value={isEnabled} onValueChange={value => setIsEnabled(value)} />;
```

### 3. Componentes de Lista

#### ScrollView

**Descripción**: Contenedor desplazable que puede alojar múltiples componentes.

```jsx
import { ScrollView } from 'react-native';

<ScrollView>{/* Contenido scrolleable */}</ScrollView>;
```

#### FlatList

**Descripción**: Renderizado performante de listas desplazables.

```jsx
import { FlatList } from 'react-native';

<FlatList
  data={data}
  renderItem={({ item }) => <Text>{item.title}</Text>}
  keyExtractor={item => item.id}
/>;
```

#### SectionList

**Descripción**: Como FlatList, pero para listas seccionadas.

```jsx
import { SectionList } from 'react-native';

<SectionList
  sections={sections}
  renderItem={({ item }) => <Text>{item.title}</Text>}
  renderSectionHeader={({ section }) => <Text>{section.title}</Text>}
  keyExtractor={item => item.id}
/>;
```

### 4. Componentes de Utilidad

#### ActivityIndicator

**Descripción**: Muestra un indicador de carga circular.

```jsx
import { ActivityIndicator } from 'react-native';

<ActivityIndicator size="large" color="#0000ff" />;
```

#### SafeAreaView

**Descripción**: Renderiza contenido dentro del área segura del dispositivo.

```jsx
import { SafeAreaView } from 'react-native';

<SafeAreaView style={{ flex: 1 }}>{/* Contenido */}</SafeAreaView>;
```

#### StatusBar

**Descripción**: Controla la barra de estado de la aplicación.

```jsx
import { StatusBar } from 'react-native';

<StatusBar barStyle="dark-content" backgroundColor="#ffffff" />;
```

## APIs Principales

### 1. StyleSheet

```jsx
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
```

### 2. Dimensions

```jsx
import { Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');
```

### 3. Platform

```jsx
import { Platform } from 'react-native';

const isIOS = Platform.OS === 'ios';
const isAndroid = Platform.OS === 'android';
```

### 4. Alert

```jsx
import { Alert } from 'react-native';

Alert.alert('Título', 'Mensaje', [
  { text: 'Cancelar', style: 'cancel' },
  { text: 'OK', onPress: () => console.log('OK') },
]);
```

## Debugging

### 1. React DevTools

```bash
npx react-devtools
```

### 2. Flipper

- Herramienta de debugging moderna
- Integración con React Native DevTools

### 3. Console Logs

```jsx
console.log('Debug message');
console.warn('Warning message');
console.error('Error message');
```

## Performance

### 1. Optimizaciones Clave

- Uso de `FlatList` para listas grandes
- Optimización de imágenes
- Uso de `React.memo` para componentes
- Lazy loading de componentes

### 2. Herramientas de Profiling

- React DevTools Profiler
- Flipper Performance Monitor
- Metro Bundle Analyzer

## Testing

### 1. Unit Testing

```jsx
import { render, fireEvent } from '@testing-library/react-native';
import MyComponent from './MyComponent';

test('renders correctly', () => {
  const { getByText } = render(<MyComponent />);
  expect(getByText('Hello')).toBeTruthy();
});
```

### 2. E2E Testing

- Detox
- Maestro
- Appium

## Mejores Prácticas

### 1. Estructura de Proyecto

```
src/
├── components/
├── screens/
├── navigation/
├── services/
├── utils/
└── types/
```

### 2. Gestión de Estado

- Context API para estado global simple
- Redux Toolkit para estado complejo
- Zustand para estado ligero

### 3. Styling

- StyleSheet API
- Styled Components
- NativeWind (Tailwind CSS)

### 4. Networking

- Fetch API
- Axios
- React Query para cache

## Recursos Adicionales

- [React Native Docs](https://reactnative.dev/)
- [React Native Blog](https://reactnative.dev/blog)
- [React Native GitHub](https://github.com/facebook/react-native)
- [React Native Community](https://github.com/react-native-community)

## Compatibilidad

### Versiones Soportadas

- React: 19.0.0
- Node.js: 18+ (LTS)
- iOS: 13.4+
- Android: API 23+ (Android 6.0)

### Herramientas Relacionadas

- Metro (v0.82+)
- Hermes JavaScript Engine
- New Architecture (Fabric + TurboModules)
