# Sistema de Anonimización de Imágenes - Salonier

## Resumen

Este documento describe el sistema de anonimización híbrida implementado para garantizar la privacidad de las clientas en Salonier. El sistema asegura que nunca se almacene permanentemente una imagen con rostros identificables.

## Arquitectura

### Flujo de Anonimización

```mermaid
graph TD
    A[Usuario toma foto] --> B[Upload a bucket temporal]
    B --> C[Edge Function: anonymize-and-store]
    C --> D[Detectar rostros]
    D --> E[Aplicar blur/pixelado]
    E --> F[Comprimir imagen]
    F --> G[Subir a bucket público]
    G --> H[Eliminar original]
    H --> I[Devolver URL pública]
    I --> J[Análisis con IA usando URL]
```

### Componentes Principales

1. **Bucket `originals-for-anonymization`**
   - Almacenamiento temporal para imágenes originales
   - Políticas RLS estrictas por salón
   - Auto-eliminación después de 24 horas (fail-safe)

2. **Bucket `client-photos`**
   - Almacenamiento público para imágenes anonimizadas
   - Acceso de lectura público
   - Escritura protegida por RLS

3. **Edge Function `anonymize-and-store`**
   - Detecta rostros en las imágenes
   - Aplica blur/pixelado a áreas identificadas
   - Comprime imágenes (200-400KB)
   - Elimina original después de procesar

4. **Utilidad `secure-image-upload.ts`**
   - Maneja todo el flujo en el cliente
   - Reintentos automáticos
   - Feedback de progreso al usuario

## Uso en el Cliente

### Subir y Anonimizar una Imagen

```typescript
import { uploadAndAnonymizeImage } from '@/utils/secure-image-upload';

const result = await uploadAndAnonymizeImage(imageUri, {
  clientId: 'client-uuid',
  photoType: 'before', // 'before' | 'after' | 'desired'
  onProgress: progress => {
    console.log(`Progress: ${progress}%`);
  },
});

if (result.success) {
  // Use result.publicUrl for AI analysis or storage
  const publicUrl = result.publicUrl;
} else {
  console.error(result.error);
}
```

### Eliminar una Imagen Anonimizada

```typescript
import { deleteAnonymizedImage } from '@/utils/secure-image-upload';

const success = await deleteAnonymizedImage(publicUrl);
if (success) {
  console.log('Image deleted successfully');
}
```

## Integración con IA

El sistema está integrado con el análisis de IA:

1. **ai-analysis-store.ts** detecta automáticamente si recibe una URL o URI local
2. URLs públicas se envían directamente a la Edge Function
3. URIs locales mantienen compatibilidad con base64 (temporal)

```typescript
// En performImageAnalysis
if (imageUri.startsWith('http://') || imageUri.startsWith('https://')) {
  // URL pública - usar directamente
  payload.imageUrl = imageUri;
} else {
  // URI local - usar base64 por compatibilidad
  payload.imageBase64 = await compressImage(imageUri);
}
```

## Edge Functions

### salonier-assistant

Actualizada para aceptar tanto URLs públicas como base64:

```typescript
if (imageUrl) {
  // Nuevo flujo con URLs públicas
  imageDataUrl = imageUrl;
} else if (imageBase64) {
  // Compatibilidad con base64
  imageDataUrl = `data:image/jpeg;base64,${imageBase64}`;
}
```

## Seguridad y Privacidad

### Garantías de Privacidad

1. **Anonimización inmediata**: Los rostros se detectan y anonimizan antes de cualquier procesamiento
2. **Sin almacenamiento permanente**: Las imágenes originales se eliminan inmediatamente
3. **Fail-safe de 24 horas**: Función scheduled elimina cualquier original remanente
4. **Derecho al olvido**: API para eliminar imágenes anonimizadas

### Políticas de Seguridad

- RLS en todos los buckets
- Aislamiento por salón (multi-tenant)
- Solo service role puede eliminar de buckets
- Logs de auditoría para todas las operaciones

## Migración

### Para Código Existente

1. **Fase 1**: Compatibilidad dual (URLs y base64) ✅
2. **Fase 2**: Migrar componentes gradualmente
3. **Fase 3**: Eliminar soporte base64

### Componentes por Migrar

- [ ] PhotoGallery.tsx
- [ ] DesiredPhotoGallery.tsx
- [ ] CompletionStep.tsx
- [ ] Otros componentes que capturen fotos

## Testing

### Casos de Prueba Críticos

1. **Anonimización exitosa**
   - Verificar que rostros se blur correctamente
   - Confirmar eliminación de original

2. **Manejo de errores**
   - Fallo en detección de rostros
   - Timeout en Edge Function
   - Error de red

3. **Performance**
   - Tiempo de procesamiento < 10s
   - Tamaño final < 400KB

### Comandos de Testing

```bash
# Verificar buckets
supabase storage ls originals-for-anonymization
supabase storage ls client-photos

# Logs de Edge Function
supabase functions logs anonymize-and-store --tail

# Verificar políticas RLS
supabase db dump --data-only | grep storage.objects
```

## Troubleshooting

### Problemas Comunes

1. **"Failed to upload image"**
   - Verificar permisos del usuario
   - Confirmar salon_id disponible

2. **"Anonymization failed"**
   - Revisar logs de Edge Function
   - Verificar límites de tamaño

3. **Imágenes no se eliminan**
   - Verificar función scheduled activa
   - Revisar permisos service role

## Próximos Pasos

1. Implementar detección de rostros real (TensorFlow.js)
2. Mejorar algoritmo de blur/pixelado
3. Agregar watermark opcional
4. Métricas de uso y auditoría
