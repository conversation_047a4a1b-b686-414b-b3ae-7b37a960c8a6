# Generación de Fórmulas con Configuración Regional

## Descripción

A partir de la versión 2.0.2, el sistema de generación de fórmulas es completamente consciente de la configuración regional del usuario, adaptando:

- **Unidades de medida**: ml/fl oz para volumen, g/oz para peso
- **Terminología**: oxidante/developer, tinte/color, etc.
- **Formato numérico**: separador decimal (. o ,)
- **Idioma**: Español o Inglés completo
- **Regulaciones**: Límites de volumen de oxidante por país
- **Moneda**: Para estimaciones de costo

## Configuración Regional Soportada

### Campos de Configuración

```typescript
interface RegionalConfig {
  volumeUnit: 'ml' | 'fl oz';
  weightUnit: 'g' | 'oz';
  developerTerminology: string; // 'oxidante', 'developer', 'révélateur', etc.
  colorTerminology: string; // 'tinte', 'color', 'couleur', etc.
  maxDeveloperVolume: number; // 30 o 40 según país
  currencySymbol: string; // '€', '$', '£', etc.
  measurementSystem: 'metric' | 'imperial';
  decimalSeparator: '.' | ',';
  language: string; // 'es', 'en', 'fr', etc.
}
```

## Ejemplos de Fórmulas Generadas

### España (Métrico/Español)

```
## Fórmula de Coloración

### Mezcla Principal
- 40ml de tinte 8.1 Rubio Claro Ceniza
- 20ml de tinte 7.0 Rubio Natural
- 90ml de oxidante 30 vol (proporción 1:1,5)

### Aplicación por Zonas
- **Raíces**: Aplicar primero, dejar procesar 35 minutos
- **Medios y puntas**: Aplicar después de 20 minutos

Costo estimado: 12,50€
```

### Estados Unidos (Imperial/Inglés)

```
## Hair Color Formula

### Main Mix
- 1.35fl oz of color 8.1 Light Ash Blonde
- 0.67fl oz of color 7.0 Natural Blonde
- 3fl oz of developer 30 vol (ratio 1:1.5)

### Zone Application
- **Roots**: Apply first, process for 35 minutes
- **Mids and ends**: Apply after 20 minutes

Estimated cost: $15.75
```

### Francia (Métrico/Francés - con límite 30 vol)

```
## Formule de Coloration

### Mélange Principal
- 40ml de couleur 8.1 Blond Clair Cendré
- 20ml de couleur 7.0 Blond Naturel
- 90ml de révélateur 30 vol (proportion 1:1,5)

### Application par Zones
- **Racines**: Appliquer d'abord, laisser poser 35 minutes
- **Longueurs et pointes**: Appliquer après 20 minutes

Coût estimé: 12,50€

⚠️ IMPORTANT: Volume maximum autorisé de révélateur: 30 volumes
```

## Implementación Técnica

### Frontend (app/service/new.tsx)

```typescript
// La configuración regional se obtiene automáticamente
const salonConfig = useSalonConfigStore.getState();

// Se pasa al Edge Function
const { data, error } = await supabase.functions.invoke('salonier-assistant', {
  body: {
    task: 'generate_formula',
    payload: {
      diagnosis: formulaContext.currentDiagnosis,
      desiredResult: formulaContext.desiredResult,
      brand: formulaContext.brand,
      line: formulaContext.line,
      clientHistory: formulaContext.clientHistory,
      regionalConfig: salonConfig.regionalConfig, // Nuevo campo
    },
  },
});
```

### Edge Function (salonier-assistant/index.ts)

```typescript
// El prompt se adapta dinámicamente
const isEnglish = regionalConfig?.language === 'en';
const volumeUnit = regionalConfig?.volumeUnit || 'ml';
const developerTerm = regionalConfig?.developerTerminology || 'oxidante';

// Ejemplos de formato según región
const formatExamples =
  measurementSystem === 'metric'
    ? `- Cantidades: "40${volumeUnit} de ${colorTerm} 7.1"`
    : `- Quantities: "1.35${volumeUnit} of ${colorTerm} 7.1"`;

// Restricciones regulatorias
if (maxDeveloperVolume < 40) {
  prompt += `IMPORTANTE: Volumen máximo ${maxDeveloperVolume} vol`;
}
```

## Beneficios

1. **Experiencia Localizada**: Los estilistas ven fórmulas en unidades y terminología familiar
2. **Cumplimiento Regulatorio**: Respeta límites legales de cada país automáticamente
3. **Reducción de Errores**: Evita confusiones por conversión de unidades
4. **Profesionalismo**: Fórmulas que parecen escritas por un experto local

## Configuración del Salón

Los usuarios pueden cambiar su configuración regional en:

- **Ajustes > Configuración Regional**
- Se actualiza automáticamente al seleccionar un país
- Pueden personalizar unidades individuales si lo desean

## Pruebas

Ver el script `scripts/test-regional-formula.sh` para ejemplos de pruebas con diferentes configuraciones regionales.

## Consideraciones Futuras

- Soporte para más idiomas (italiano, alemán, portugués)
- Terminología específica por marca
- Conversión automática entre sistemas de medida
- Plantillas de fórmula por región
