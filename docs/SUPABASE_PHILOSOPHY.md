# La Arquitectura y Filosofía de Backend de Salonier en Supabase

Este documento es nuestra "Constitución Técnica" y la fuente de verdad sobre nuestra filosofía de backend.

## 1. La Visión: ¿Quiénes Somos?

No estamos construyendo una simple "app". Estamos construyendo una **plataforma SaaS profesional, robusta y fiable para negocios de belleza (B2B)**. Nuestros usuarios son profesionales cuyo sustento depende de nuestra herramienta. La fiabilidad, seguridad y velocidad no son características opcionales; son el núcleo de nuestro producto.

## 2. La Arquitectura Central: El Modelo Híbrido "Offline-First"

La aplicación **DEBE** ser 100% funcional para las operaciones críticas sin conexión a internet.

```
┌─────────────────┐     ┌──────────────┐     ┌─────────────┐
│   Cliente App   │     │   Supabase   │     │    OpenAI   │
│    (Zustand)    │────▶│  PostgreSQL  │────▶│     API     │
└────────┬────────┘     └──────┬───────┘     └─────────────┘
         │                     │
         ▼                     ▼
┌─────────────────┐     ┌──────────────┐
│  AsyncStorage   │     │ Edge Functions│
│  (Cola Offline) │     │  (IA Segura) │
└─────────────────┘     └──────────────┘
```

### Principios Fundamentales:

- **Zustand es la Fuente de Verdad para la UI:** Garantiza una experiencia instantánea.
- **Supabase es la Fuente de Verdad para el Negocio:** Es el punto de sincronización central en la nube.
- **La Sincronización es un Proceso en Segundo Plano:** Las operaciones offline se encolan y se procesan de forma transparente para el usuario.

## 3. El Rol Estratégico de Supabase: Nuestro "Cerebro Inteligente y Seguro"

### 3.1 La Seguridad es la Capa Cero (RLS)

La lógica de seguridad vive en la base de datos (con políticas RLS), no en la aplicación. La privacidad y el aislamiento de los datos de cada salón son nuestra máxima prioridad.

```sql
-- Ejemplo: Política RLS para aislar datos por salón
CREATE POLICY "Salon isolation" ON clients
  FOR ALL USING (salon_id = auth.jwt() ->> 'salon_id');
```

### 3.2 La Automatización es Profesionalismo (Triggers y Funciones SQL)

Automatizamos todo lo posible (creación de cuentas, mantenimiento) para crear una experiencia fluida y profesional.

```sql
-- Ejemplo: Trigger para setup automático de nuevo salón
CREATE TRIGGER setup_new_salon
  AFTER INSERT ON salons
  FOR EACH ROW EXECUTE FUNCTION initialize_salon_defaults();
```

### 3.3 La IA es un Secreto Bien Guardado (Edge Functions)

La Edge Function `salonier-assistant` es nuestra única puerta de entrada segura a la IA. Las claves secretas de la IA NUNCA se exponen en el cliente.

## 4. Nuestro Manifiesto de Desarrollo

- **Pragmatismo sobre Purismo:** Aprovechamos las fortalezas de PostgreSQL (`arrays`, `JSONB`) para crear soluciones simples y de alto rendimiento.
- **Construir por Capas, Verificar por Capas:** Diseñamos, construimos y probamos cada capa de forma aislada antes de integrarlas.
- **La Experiencia del Usuario es la Métrica Final:** Cada decisión técnica debe hacer la aplicación más rápida, fiable y segura para el estilista.

## 5. Principios de Diseño de API

### 5.1 RESTful por Defecto

- Endpoints semánticos y predecibles
- Uso correcto de verbos HTTP
- Respuestas consistentes con códigos de estado apropiados

### 5.2 Paginación y Filtrado Eficiente

```typescript
// Ejemplo: Paginación con cursor
const { data, error, count } = await supabase
  .from('services')
  .select('*', { count: 'exact' })
  .range(0, 19)
  .order('created_at', { ascending: false });
```

### 5.3 Versionado y Retrocompatibilidad

- Cambios aditivos siempre que sea posible
- Deprecación gradual con avisos claros
- Mantener soporte de versiones anteriores mínimo 6 meses

## 6. Manejo de Conflictos y Sincronización

### 6.1 Estrategia "Last Write Wins" con Timestamps

```typescript
interface SyncableRecord {
  id: string;
  updated_at: string;
  local_updated_at?: string;
  sync_status: 'synced' | 'pending' | 'conflict';
}
```

### 6.2 Cola de Sincronización Robusta

```typescript
// Ejemplo: Sistema de reintentos exponencial
const syncWithRetry = async (operation, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await wait(Math.pow(2, i) * 1000); // Backoff exponencial
    }
  }
};
```

### 6.3 Resolución de Conflictos Transparente

- UI clara cuando hay conflictos
- Opción de ver ambas versiones
- Historial de cambios accesible

## 7. Anti-Patrones: Lo que NO Hacemos

### ❌ NO exponemos secretos en el cliente

```typescript
// MAL
const openAiKey = 'sk-...'; // NUNCA hacer esto

// BIEN
const response = await supabase.functions.invoke('salonier-assistant', {
  body: { prompt: userInput },
});
```

### ❌ NO confiamos en validación client-side únicamente

```typescript
// La validación del cliente es para UX, no para seguridad
// SIEMPRE validar en el servidor/base de datos
```

### ❌ NO hacemos llamadas directas a APIs externas desde el cliente

```typescript
// MAL
fetch('https://api.openai.com/...'); // Expone API keys

// BIEN
supabase.functions.invoke('analyze-image'); // Proxy seguro
```

### ❌ NO ignoramos el manejo de errores

```typescript
// SIEMPRE manejar errores gracefully
try {
  await syncOperation();
} catch (error) {
  // Log, notify, retry, o degradar funcionalidad
  handleSyncError(error);
}
```

## 8. Métricas de Éxito

### 8.1 Rendimiento

- **Tiempo de respuesta UI**: < 100ms (interacciones locales)
- **Tiempo de sincronización**: < 500ms (P95)
- **Tiempo de análisis IA**: < 3s (P90)

### 8.2 Fiabilidad

- **Disponibilidad**: > 99.9%
- **Sincronización exitosa**: > 99.5%
- **Pérdida de datos**: 0%

### 8.3 Experiencia de Usuario

- **Tiempo hasta primera interacción**: < 2s
- **Operaciones offline exitosas**: 100%
- **Recuperación de errores sin intervención**: > 95%

## 9. Decisiones Técnicas Fundamentales

### 9.1 ¿Por qué PostgreSQL?

- **ACID compliance**: Transacciones confiables para datos críticos de negocio
- **JSONB nativo**: Flexibilidad para evolucionar esquemas
- **Arrays nativos**: Perfecto para listas de productos, alergias, etc.
- **Row Level Security**: Seguridad granular integrada

### 9.2 ¿Por qué Zustand sobre Redux?

- **Simplicidad**: Menos boilerplate, más productividad
- **Rendimiento**: Sin re-renders innecesarios
- **TypeScript nativo**: Type-safety sin configuración extra
- **Tamaño**: ~8KB vs ~60KB de Redux + middleware

### 9.3 ¿Por qué Edge Functions?

- **Latencia global**: Ejecución cerca del usuario
- **Secretos seguros**: Variables de entorno protegidas
- **Auto-escalado**: Sin preocupaciones de infraestructura
- **Integración nativa**: Acceso directo a Supabase

### 9.4 ¿Por qué Expo?

- **Desarrollo rápido**: Hot reload, OTA updates
- **Ecosistema rico**: Librerías pre-integradas
- **Cross-platform real**: iOS, Android, Web desde una base
- **Managed workflow**: Menos complejidad de configuración

## 10. Flujos de Trabajo Críticos

### 10.1 Onboarding de Nuevo Salón

1. Registro con email/password
2. Trigger SQL crea estructura inicial
3. Edge Function envía email de bienvenida
4. Primera sincronización establece datos base

### 10.2 Servicio de Coloración Offline

1. UI opera 100% desde Zustand
2. Operaciones se encolan en AsyncStorage
3. Al recuperar conexión, sync automático
4. Notificación discreta de sincronización exitosa

### 10.3 Análisis de IA

1. Foto se comprime localmente
2. Upload a Supabase Storage
3. Edge Function procesa con OpenAI
4. Resultado se cachea para uso offline

## 11. Evolución y Mantenimiento

### 11.1 Migraciones de Base de Datos

- Siempre forward-compatible
- Rollback plan documentado
- Testing en ambiente staging
- Comunicación clara a usuarios

### 11.2 Actualizaciones de App

- Semantic versioning estricto
- Changelog detallado
- Beta testing con salones piloto
- Rollout gradual

### 11.3 Monitoreo y Observabilidad

- Logs estructurados en Supabase
- Métricas de uso y rendimiento
- Alertas proactivas
- Dashboard de salud del sistema

## 12. Conclusión

Esta filosofía no es solo sobre tecnología; es sobre respetar el tiempo y el negocio de nuestros usuarios. Cada decisión arquitectónica debe pasar la prueba: **"¿Esto hace la vida del estilista más fácil, segura y productiva?"**

---

_Documento vivo - Última actualización: 2025-07-11_
_Próxima revisión programada: 2025-08-11_
