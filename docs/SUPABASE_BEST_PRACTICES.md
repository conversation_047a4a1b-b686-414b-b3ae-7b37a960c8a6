# Mejores Prácticas para Supabase - Rork Salonier Copilot

Este documento contiene las mejores prácticas y recomendaciones para mantener el rendimiento y seguridad óptimos en Supabase.

## 📊 Monitoreo y Performance

### 1. Monitoreo Regular

**Frecuencia recomendada:**

- **Diario**: Revisar logs de errores y alertas
- **Semanal**: Ejecutar `scripts/monitor-performance.sql`
- **Mensual**: Análisis profundo de performance y costos

**Métricas clave a monitorear:**

- Cache hit ratio (debe ser > 90%)
- Queries lentas (> 100ms)
- Conexiones activas (< 80% del límite)
- Dead tuples (< 20% por tabla)
- Uso de índices

### 2. Optimización de Queries

**Buenas prácticas:**

```sql
-- ❌ EVITAR: Multiple llamadas a auth.uid()
WHERE salon_id = (SELECT salon_id FROM profiles WHERE id = auth.uid())

-- ✅ MEJOR: Una sola evaluación
WHERE salon_id = (SELECT salon_id FROM profiles WHERE id = (SELECT auth.uid()))

-- ❌ EVITAR: SELECT * en producción
SELECT * FROM clients

-- ✅ MEJOR: Seleccionar solo campos necesarios
SELECT id, name, email FROM clients

-- ❌ EVITAR: JOINs sin índices
SELECT * FROM services s
JOIN clients c ON s.client_id = c.id

-- ✅ MEJOR: Asegurar índices en foreign keys
CREATE INDEX idx_services_client_id ON services(client_id);
```

### 3. Gestión de Índices

**Cuándo crear índices:**

- Foreign keys que se usan en JOINs frecuentes
- Campos usados en WHERE clauses frecuentes
- Campos de ordenamiento (ORDER BY)

**Cuándo NO crear índices:**

- Tablas pequeñas (< 1000 registros)
- Columnas con poca cardinalidad (ej: boolean)
- Columnas que se actualizan frecuentemente

## 🔐 Seguridad

### 1. Row Level Security (RLS)

**Principios fundamentales:**

- Siempre habilitar RLS en todas las tablas
- Usar `(SELECT auth.uid())` en lugar de `auth.uid()`
- Política por defecto: denegar todo, permitir específicamente

**Ejemplo de política segura:**

```sql
-- Política bien estructurada
CREATE POLICY "salon_members_manage_clients" ON public.clients
FOR ALL
TO authenticated
USING (
  salon_id IN (
    SELECT salon_id
    FROM profiles
    WHERE id = (SELECT auth.uid())
  )
)
WITH CHECK (
  salon_id IN (
    SELECT salon_id
    FROM profiles
    WHERE id = (SELECT auth.uid())
  )
);
```

### 2. Funciones SQL

**Siempre incluir:**

```sql
CREATE OR REPLACE FUNCTION public.my_function()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER        -- Ejecutar con permisos del creador
SET search_path = public -- Prevenir ataques de path injection
AS $$
BEGIN
  -- función segura
END;
$$;
```

### 3. Validación de Datos

**Usar constraints a nivel de base de datos:**

```sql
-- Validaciones importantes
ALTER TABLE products
ADD CONSTRAINT check_positive_stock CHECK (stock_ml >= 0);

ALTER TABLE clients
ADD CONSTRAINT check_valid_email CHECK (
  email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'
);

ALTER TABLE profiles
ADD CONSTRAINT check_valid_role CHECK (
  role IN ('owner', 'manager', 'stylist', 'apprentice')
);
```

## 🔄 Sincronización y Offline

### 1. Patrón UI Optimistic

**Implementación correcta:**

```typescript
// Store con UI Optimistic
async function addProduct(product: Product) {
  // 1. Generar ID temporal
  const tempId = `temp_${Date.now()}`;

  // 2. Actualizar UI inmediatamente
  set(state => ({
    products: [...state.products, { ...product, id: tempId }],
  }));

  // 3. Sincronizar con servidor
  try {
    const { data } = await supabase.from('products').insert(product).select().single();

    // 4. Reemplazar temporal con real
    set(state => ({
      products: state.products.map(p => (p.id === tempId ? data : p)),
    }));
  } catch (error) {
    // 5. Revertir en caso de error
    set(state => ({
      products: state.products.filter(p => p.id !== tempId),
    }));
  }
}
```

### 2. Cola de Sincronización

**Gestión de operaciones offline:**

```typescript
// Agregar a cola cuando offline
if (!navigator.onLine) {
  await syncQueue.add({
    table: 'products',
    operation: 'insert',
    data: product,
    timestamp: Date.now(),
  });
  return;
}

// Procesar cola cuando vuelve online
window.addEventListener('online', async () => {
  const pending = await syncQueue.getAll();
  for (const operation of pending) {
    await processOperation(operation);
    await syncQueue.remove(operation.id);
  }
});
```

## 🚀 Edge Functions

### 1. Manejo de Rate Limiting

**Implementar retry con backoff:**

```typescript
async function callWithRetry<T>(
  fn: () => Promise<T>,
  maxRetries = 3,
  baseDelay = 1000
): Promise<T> {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (error.status === 429 && i < maxRetries - 1) {
        const delay = baseDelay * Math.pow(2, i);
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        throw error;
      }
    }
  }
  throw new Error('Max retries exceeded');
}
```

### 2. Optimización de Costos

**Cache inteligente:**

```typescript
// Verificar cache antes de llamar a OpenAI
const cacheKey = generateHash(input);
const cached = await checkCache(cacheKey);

if (cached && cached.expires_at > new Date()) {
  return { data: cached.result, cached: true };
}

// Llamar a OpenAI solo si no hay cache válido
const result = await callOpenAI(input);

// Guardar en cache
await saveCache(cacheKey, result, 30); // 30 días
```

## 🗄️ Mantenimiento de Base de Datos

### 1. Tareas Programadas

**Configurar con pg_cron:**

```sql
-- Limpieza diaria de cache expirado
SELECT cron.schedule(
  'cleanup-ai-cache',
  '0 2 * * *',
  'SELECT cleanup_expired_ai_cache();'
);

-- Vacuum semanal de tablas con alto tráfico
SELECT cron.schedule(
  'vacuum-high-traffic',
  '0 3 * * 0',
  'VACUUM ANALYZE products, stock_movements, services;'
);

-- Reindex mensual
SELECT cron.schedule(
  'monthly-reindex',
  '0 4 1 * *',
  'REINDEX TABLE products; REINDEX TABLE services;'
);
```

### 2. Backups

**Estrategia recomendada:**

- **Backups automáticos**: Diarios, retención 30 días
- **Point-in-time recovery**: Habilitado
- **Backups manuales**: Antes de cambios mayores
- **Pruebas de restauración**: Mensuales

### 3. Gestión de Espacio

**Monitorear y limpiar:**

```sql
-- Verificar tamaño de tablas
SELECT
  tablename,
  pg_size_pretty(pg_total_relation_size(tablename::regclass)) as size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(tablename::regclass) DESC;

-- Limpiar datos antiguos
DELETE FROM ai_analysis_cache WHERE expires_at < NOW();
DELETE FROM stock_movements WHERE created_at < NOW() - INTERVAL '2 years';
```

## 📈 Escalabilidad

### 1. Particionamiento de Tablas

**Para tablas que crecen rápidamente:**

```sql
-- Ejemplo: Particionar services por año
CREATE TABLE services_2025 PARTITION OF services
FOR VALUES FROM ('2025-01-01') TO ('2026-01-01');

CREATE TABLE services_2026 PARTITION OF services
FOR VALUES FROM ('2026-01-01') TO ('2027-01-01');
```

### 2. Connection Pooling

**Configuración recomendada:**

- Usar PgBouncer en modo transaction
- Pool size: 25% de conexiones máximas
- Timeout: 30 segundos

### 3. Caché de Aplicación

**Implementar cache en cliente:**

```typescript
// Cache con TTL
class CacheManager {
  private cache = new Map<string, { data: any; expires: number }>();

  set(key: string, data: any, ttlSeconds: number) {
    this.cache.set(key, {
      data,
      expires: Date.now() + ttlSeconds * 1000,
    });
  }

  get(key: string) {
    const item = this.cache.get(key);
    if (!item || item.expires < Date.now()) {
      this.cache.delete(key);
      return null;
    }
    return item.data;
  }
}
```

## 🐛 Debugging y Troubleshooting

### 1. Logs Útiles

**Habilitar logs detallados temporalmente:**

```sql
-- En funciones críticas
RAISE LOG 'Function: %, User: %, Params: %', 'function_name', auth.uid(), params;

-- Para debugging de RLS
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
SET log_statement = 'all';
```

### 2. Queries de Diagnóstico

**Verificar problemas comunes:**

```sql
-- Conexiones bloqueadas
SELECT * FROM pg_stat_activity
WHERE wait_event_type IS NOT NULL;

-- Queries largas
SELECT * FROM pg_stat_activity
WHERE state = 'active'
AND query_start < NOW() - INTERVAL '5 minutes';

-- Índices faltantes sugeridos
SELECT schemaname, tablename, attname, n_distinct, correlation
FROM pg_stats
WHERE schemaname = 'public'
AND n_distinct > 100
AND correlation < 0.1
ORDER BY n_distinct DESC;
```

## 📋 Checklist de Despliegue

Antes de cada actualización importante:

- [ ] Backup completo de la base de datos
- [ ] Ejecutar scripts de validación
- [ ] Revisar logs de errores recientes
- [ ] Verificar uso de recursos (CPU, memoria, storage)
- [ ] Probar migraciones en ambiente de staging
- [ ] Documentar cambios en CHANGELOG
- [ ] Notificar al equipo sobre ventana de mantenimiento
- [ ] Tener plan de rollback preparado

## 🚨 Respuesta a Incidentes

### Plan de acción:

1. **Identificar**: Revisar logs y métricas
2. **Contener**: Aislar el problema (ej: deshabilitar función)
3. **Comunicar**: Notificar a usuarios afectados
4. **Resolver**: Aplicar fix con mínimo impacto
5. **Documentar**: Crear post-mortem

### Contactos de emergencia:

- Soporte Supabase: <EMAIL>
- Dashboard: https://app.supabase.com
- Status: https://status.supabase.com

---

Documento actualizado: 2025-01-11
Versión: 1.0
