# Image Analysis Pipeline Diagnostic Guide

This guide helps diagnose and resolve "No image provided" errors and missing detailed logs in the image analysis flow.

## 🔧 Quick Fix Verification

### 1. Get a Valid Auth Token

**Option A: From Mobile App (Expo)**
```bash
# Start the mobile app
npm run mobile

# In Expo DevTools console, after login:
console.log(await AsyncStorage.getItem('@supabase_session'));
```

**Option B: From Web Browser**
```javascript
// After logging into the web version:
localStorage.getItem('supabase.auth.token')
// Copy the access_token value
```

### 2. Test the Fixed Edge Function
```bash
# Replace YOUR_JWT_TOKEN with actual token
export AUTH_TOKEN="YOUR_JWT_TOKEN"
node scripts/test-edge-function-diagnostic.js
```

### 3. Check Diagnostic Logs
```bash
# View Edge Function logs (should now show detailed analysis)
supabase logs --type=functions --filter=salonier-assistant
```

## 📊 Expected Log Output

After the fix, you should see detailed logs like:

```
[DIAGNOSTIC] AI Diagnosis Analysis Completed Successfully {
  "hairAnalysis": {
    "averageLevel": 6,
    "overallTone": "Natural",
    "overallReflect": "Warm",
    "hairThickness": "Medium",
    "hairDensity": "Medium",
    "overallCondition": "Good"
  },
  "zoneAnalysis": {
    "roots": { "level": 7, "tone": "Natural", "confidence": 85 },
    "mids": { "level": 6, "tone": "Golden", "confidence": 80 },
    "ends": { "level": 5, "tone": "Golden", "confidence": 75 }
  },
  "bucketInfo": { "bucket": "hair-photos", "isSignedUrl": true },
  "tokensUsed": 1247,
  "costUsd": "0.0019",
  "modelUsed": "gpt-4o-mini",
  "complexity": "simple"
}
```

## 🐛 Common Issues & Solutions

### Issue: "No image provided" Error

**Symptoms**: Edge Function returns `{"success": false, "error": "No image provided"}`

**Causes & Solutions**:
1. **Empty payload**: Check that `imageBase64` or `imageUrl` is properly set
2. **Network issues**: Verify Supabase connection and authentication
3. **Invalid image data**: Ensure base64 is properly formatted

**Debug Steps**:
```bash
# Check payload structure
node -e "console.log(JSON.stringify({task: 'diagnose_image', payload: {imageBase64: 'test'}}, null, 2))"

# Test with minimal payload
curl -X POST "$SUPABASE_URL/functions/v1/salonier-assistant" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -d '{"task":"diagnose_image","payload":{"imageBase64":"/9j/test"}}'
```

### Issue: Missing Detailed Logs

**Symptoms**: Only basic "Analysis completed" without hair details

**Solutions**:
1. ✅ **Fixed**: Logger now always outputs detailed information
2. ✅ **Fixed**: Added DIAGNOSTIC logging that shows complete analysis
3. Check logs using: `supabase logs --type=functions`

### Issue: 401 Authentication Errors

**Symptoms**: HTTP 401 responses from Edge Function

**Solutions**:
1. Ensure valid JWT token in Authorization header
2. Check token expiration (tokens expire after 1 hour)
3. Verify user has proper salon_id association

**Debug Steps**:
```bash
# Verify token format
echo $AUTH_TOKEN | cut -d'.' -f2 | base64 -d | jq .

# Check token expiration
node -e "console.log(new Date(JSON.parse(atob('$AUTH_TOKEN'.split('.')[1])).exp * 1000))"
```

## 📱 Client-Side Debugging

### Check AIAnalysisStore State
```javascript
// In React Native debugger or web console
import { useAIAnalysisStore } from './stores/ai-analysis-store';

const store = useAIAnalysisStore.getState();
console.log('Analysis State:', {
  isAnalyzing: store.isAnalyzing,
  analysisResult: store.analysisResult,
  settings: store.settings
});
```

### Monitor Network Requests
```javascript
// Add to ai-analysis-store.ts for debugging
console.log('Sending to Edge Function:', {
  task: 'diagnose_image',
  payloadKeys: Object.keys(payload),
  hasImageUrl: !!payload.imageUrl,
  hasImageBase64: !!payload.imageBase64
});
```

## 🔄 End-to-End Test Flow

1. **Capture Image**: Use camera or select from gallery
2. **Upload/Process**: Image goes through compression and validation
3. **Client Call**: `performImageAnalysis()` sends to Edge Function
4. **Edge Function**: Processes image with OpenAI Vision API
5. **Response**: Returns detailed hair analysis
6. **Client Update**: Updates UI with analysis results
7. **Logs**: Check for DIAGNOSTIC entries in Supabase logs

## 🆘 Emergency Rollback

If issues persist, you can rollback the Edge Function:

```bash
# Deploy previous stable version
git checkout HEAD~1 -- supabase/functions/salonier-assistant/
npx supabase functions deploy salonier-assistant
```

## 📞 Support Checklist

When reporting issues, include:
- [ ] Auth token status (valid/expired)
- [ ] Exact error message from logs
- [ ] Image source (camera/gallery/URL)
- [ ] Device type and OS version
- [ ] Network conditions
- [ ] Recent code changes

---

**Status**: ✅ Enhanced logging deployed (Version 43)
**Next Review**: After user testing confirms detailed logs are visible