#!/bin/bash

# Deploy Chat Assistant Edge Function
# Version: 3
# Last Updated: 2025-08-04
# Fixes: Corregido rechazo de análisis de imágenes con personas

echo "🚀 Desplegando Chat Assistant Edge Function con correcciones..."
echo "📝 Versión 3: An<PERSON><PERSON><PERSON> de cabello sin restricciones"

# Verificar que estamos en el directorio correcto
if [ ! -f "supabase/functions/chat-assistant/index.ts" ]; then
    echo "❌ Error: No se encuentra el archivo de la función. Asegúrate de estar en el directorio raíz del proyecto."
    exit 1
fi

# Verificar que Supabase CLI está instalado
if ! command -v supabase &> /dev/null; then
    echo "❌ Error: Supabase CLI no está instalado. Instálalo con: brew install supabase/tap/supabase"
    exit 1
fi

# Desplegar la función
echo "📦 Desplegando función chat-assistant..."
supabase functions deploy chat-assistant --no-verify-jwt

if [ $? -eq 0 ]; then
    echo "✅ Chat Assistant Edge Function desplegada exitosamente!"
    echo ""
    echo "🔧 Correcciones aplicadas en v3:"
    echo "- ✅ Ya NO rechaza imágenes con personas"
    echo "- ✅ Analiza SOLO aspectos técnicos del cabello"
    echo "- ✅ Ignora identidades, se enfoca en colorimetría"
    echo "- ✅ Prompts explícitos para prevenir rechazos"
    echo ""
    echo "📝 Notas importantes:"
    echo "- Asegúrate de que OPENAI_API_KEY esté configurada en los secrets de Supabase"
    echo "- La función usa el modelo gpt-4o con soporte para Vision API"
    echo "- Logs disponibles con: supabase functions logs chat-assistant --tail"
    echo ""
    echo "🧪 Para probar: Envía cualquier foto con cabello visible"
else
    echo "❌ Error al desplegar la función"
    echo "💡 Si Docker no está disponible, usa:"
    echo "   supabase functions deploy chat-assistant --project-ref ajsamgugqfbttkrlgvbr"
    exit 1
fi