#!/bin/bash

# <PERSON>ript to test the desired photo AI analysis

echo "🔍 Testing Desired Photo AI Analysis..."
echo ""

# Check if SUPABASE_URL and SUPABASE_ANON_KEY are set
if [ -z "$SUPABASE_URL" ] || [ -z "$SUPABASE_ANON_KEY" ]; then
    echo "❌ Error: SUPABASE_URL or SUPABASE_ANON_KEY not set"
    echo "Please run: source .env.local"
    exit 1
fi

# Test image in base64 (small red square for testing)
TEST_IMAGE_BASE64="/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAIBAQIBAQICAgICAgICAwUDAwMDAwYEBAMFBwYHBwcGBwcICQsJCAgKCAcHCg0KCgsMDAwMBwkODw0MDgsMDAz/2wBDAQICAgMDAwYDAwYMCAcIDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAz/wAARCABAAEADASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD5/ooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooA//2Q=="

# Test the analyze_desired_look function
echo "📸 Sending test image to Edge Function..."
echo ""

curl -X POST "$SUPABASE_URL/functions/v1/salonier-assistant" \
  -H "Authorization: Bearer $SUPABASE_ANON_KEY" \
  -H "Content-Type: application/json" \
  -d "{
    \"task\": \"analyze_desired_look\",
    \"payload\": {
      \"imageBase64\": \"$TEST_IMAGE_BASE64\",
      \"currentLevel\": 6
    }
  }" | jq '.'

echo ""
echo "✅ Test completed. Check the response above."
echo ""
echo "Expected response structure:"
echo "{
  \"success\": true,
  \"data\": {
    \"detectedLevel\": 8.5,
    \"detectedTone\": \"color tone\",
    \"detectedTechnique\": \"technique\",
    \"detectedTones\": [...],
    \"viabilityScore\": 75,
    \"estimatedSessions\": 1,
    \"requiredProcesses\": [...],
    \"confidence\": 85,
    \"warnings\": []
  }
}"