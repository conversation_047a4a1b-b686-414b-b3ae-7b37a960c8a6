#!/bin/bash

# Script para probar la generación de fórmulas con configuración regional

echo "🧪 Testing formula generation (no requiere imagen)..."
echo ""

# Get project URL and anon key from .env.local
SUPABASE_URL=$(grep EXPO_PUBLIC_SUPABASE_URL .env.local | cut -d '=' -f2 | tr -d '"')
ANON_KEY=$(grep EXPO_PUBLIC_SUPABASE_ANON_KEY .env.local | cut -d '=' -f2 | tr -d '"')

# Necesitas un token de usuario autenticado para esto
# Por ahora usaremos el anon key para ver el error
echo "Nota: Este test fallará con 401 porque necesita autenticación"
echo ""

curl -i --location --request POST \
  "${SUPABASE_URL}/functions/v1/salonier-assistant" \
  --header "Authorization: Bearer ${ANON_KEY}" \
  --header "Content-Type: application/json" \
  --data '{
    "task": "generate_formula",
    "payload": {
      "diagnosis": {
        "averageLevel": 6,
        "tone": "Cobrizo",
        "state": "Procesado"
      },
      "desiredResult": {
        "level": 8,
        "tone": "Rubio ceniza"
      },
      "brand": "L'\''Oréal",
      "line": "Majirel",
      "clientHistory": "Primera vez",
      "regionalConfig": {
        "volumeUnit": "ml",
        "weightUnit": "g",
        "developerTerminology": "oxidante",
        "colorTerminology": "tinte",
        "maxDeveloperVolume": 40,
        "currencySymbol": "€",
        "measurementSystem": "metric",
        "decimalSeparator": ",",
        "language": "es"
      }
    }
  }'

echo ""
echo "✅ Test completed!"