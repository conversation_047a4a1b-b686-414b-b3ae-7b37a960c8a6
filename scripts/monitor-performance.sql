-- Script de Monitoreo de Performance para Supabase
-- Ejecutar periódicamente para detectar problemas de rendimiento
-- Fecha: 2025-01-11

-- ============================================
-- 1. QUERIES MÁS LENTAS
-- ============================================
SELECT 
    '=== TOP 10 QUERIES MÁS LENTAS ===' as section;

SELECT 
    round(total_time::numeric, 2) as total_time_ms,
    round(mean_time::numeric, 2) as mean_time_ms,
    round(stddev_time::numeric, 2) as stddev_time_ms,
    calls,
    round((100.0 * total_time / sum(total_time) OVER ())::numeric, 2) as percentage_cpu,
    substring(query, 1, 100) as query_preview
FROM pg_stat_statements
WHERE query NOT LIKE '%pg_stat_statements%'
ORDER BY total_time DESC
LIMIT 10;

-- ============================================
-- 2. ÍNDICES NO UTILIZADOS (CANDIDATOS A ELIMINAR)
-- ============================================
SELECT 
    '=== ÍNDICES NUNCA UTILIZADOS ===' as section;

SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as index_scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes
WHERE idx_scan = 0
AND schemaname = 'public'
ORDER BY pg_relation_size(indexrelid) DESC;

-- ============================================
-- 3. TABLAS CON MUCHO BLOAT
-- ============================================
SELECT 
    '=== TABLAS CON BLOAT (NECESITAN VACUUM) ===' as section;

SELECT 
    schemaname,
    tablename,
    n_live_tup as live_tuples,
    n_dead_tup as dead_tuples,
    round((n_dead_tup::numeric / NULLIF(n_live_tup + n_dead_tup, 0)) * 100, 2) as dead_percentage,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables
WHERE n_dead_tup > 1000
AND schemaname = 'public'
ORDER BY dead_percentage DESC;

-- ============================================
-- 4. CONEXIONES ACTIVAS
-- ============================================
SELECT 
    '=== CONEXIONES ACTIVAS POR ESTADO ===' as section;

SELECT 
    state,
    count(*) as connections,
    max(query_start) as oldest_query_start,
    max(EXTRACT(EPOCH FROM (now() - query_start))) as oldest_query_seconds
FROM pg_stat_activity
WHERE datname = current_database()
GROUP BY state
ORDER BY connections DESC;

-- ============================================
-- 5. TAMAÑO DE TABLAS E ÍNDICES
-- ============================================
SELECT 
    '=== TOP 10 TABLAS POR TAMAÑO ===' as section;

SELECT
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as indexes_size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
LIMIT 10;

-- ============================================
-- 6. CACHE HIT RATIO
-- ============================================
SELECT 
    '=== CACHE HIT RATIO (DEBE SER > 90%) ===' as section;

SELECT 
    'index hit rate' as metric,
    round((sum(idx_blks_hit) / NULLIF(sum(idx_blks_hit + idx_blks_read), 0))::numeric * 100, 2) as ratio
FROM pg_statio_user_indexes
UNION ALL
SELECT 
    'table hit rate' as metric,
    round((sum(heap_blks_hit) / NULLIF(sum(heap_blks_hit + heap_blks_read), 0))::numeric * 100, 2) as ratio
FROM pg_statio_user_tables;

-- ============================================
-- 7. LOCKS ACTIVOS
-- ============================================
SELECT 
    '=== LOCKS ACTIVOS (POTENCIALES BLOQUEOS) ===' as section;

SELECT 
    locktype,
    relation::regclass,
    mode,
    transactionid,
    granted,
    pid,
    query
FROM pg_locks
JOIN pg_stat_activity ON pg_locks.pid = pg_stat_activity.pid
WHERE NOT granted
ORDER BY pid;

-- ============================================
-- 8. RLS POLICIES PERFORMANCE CHECK
-- ============================================
SELECT 
    '=== RLS POLICIES USANDO (SELECT auth.uid()) ===' as section;

SELECT 
    tablename,
    policyname,
    CASE 
        WHEN qual LIKE '%(SELECT auth.uid())%' THEN '✅ Optimizada'
        WHEN qual LIKE '%auth.uid()%' THEN '❌ NO OPTIMIZADA - ARREGLAR!'
        ELSE '⚠️ Revisar'
    END as optimization_status,
    cmd as policy_command
FROM pg_policies
WHERE schemaname = 'public'
AND qual LIKE '%auth.uid()%'
ORDER BY 
    CASE 
        WHEN qual LIKE '%(SELECT auth.uid())%' THEN 1
        ELSE 0
    END,
    tablename;

-- ============================================
-- 9. ESTADO DEL CACHE DE IA
-- ============================================
SELECT 
    '=== CACHE DE ANÁLISIS IA ===' as section;

SELECT 
    analysis_type,
    COUNT(*) as cached_entries,
    COUNT(*) FILTER (WHERE expires_at > NOW()) as active_entries,
    COUNT(*) FILTER (WHERE expires_at <= NOW()) as expired_entries,
    pg_size_pretty(SUM(pg_column_size(result))::bigint) as cache_size,
    MIN(created_at) as oldest_entry,
    MAX(created_at) as newest_entry
FROM ai_analysis_cache
GROUP BY analysis_type
ORDER BY cached_entries DESC;

-- ============================================
-- 10. RESUMEN DE SALUD DEL SISTEMA
-- ============================================
SELECT 
    '=== RESUMEN DE SALUD DEL SISTEMA ===' as section;

WITH health_metrics AS (
    SELECT 
        'Total Conexiones' as metric,
        COUNT(*)::text as value,
        CASE 
            WHEN COUNT(*) < 50 THEN '✅'
            WHEN COUNT(*) < 80 THEN '⚠️'
            ELSE '❌'
        END as status
    FROM pg_stat_activity
    WHERE datname = current_database()
    
    UNION ALL
    
    SELECT 
        'Queries Activas' as metric,
        COUNT(*)::text as value,
        CASE 
            WHEN COUNT(*) < 10 THEN '✅'
            WHEN COUNT(*) < 20 THEN '⚠️'
            ELSE '❌'
        END as status
    FROM pg_stat_activity
    WHERE state = 'active'
    AND datname = current_database()
    
    UNION ALL
    
    SELECT 
        'Transacciones Largas (>5min)' as metric,
        COUNT(*)::text as value,
        CASE 
            WHEN COUNT(*) = 0 THEN '✅'
            WHEN COUNT(*) < 3 THEN '⚠️'
            ELSE '❌'
        END as status
    FROM pg_stat_activity
    WHERE state != 'idle'
    AND query_start < NOW() - INTERVAL '5 minutes'
    AND datname = current_database()
    
    UNION ALL
    
    SELECT 
        'Tablas con >20% dead tuples' as metric,
        COUNT(*)::text as value,
        CASE 
            WHEN COUNT(*) = 0 THEN '✅'
            WHEN COUNT(*) < 3 THEN '⚠️'
            ELSE '❌'
        END as status
    FROM pg_stat_user_tables
    WHERE n_dead_tup > 0
    AND (n_dead_tup::numeric / NULLIF(n_live_tup + n_dead_tup, 0)) > 0.2
    
    UNION ALL
    
    SELECT 
        'Índices sin usar' as metric,
        COUNT(*)::text as value,
        CASE 
            WHEN COUNT(*) < 5 THEN '✅'
            WHEN COUNT(*) < 10 THEN '⚠️'
            ELSE '❌'
        END as status
    FROM pg_stat_user_indexes
    WHERE idx_scan = 0
    AND schemaname = 'public'
)
SELECT 
    status,
    metric,
    value
FROM health_metrics
ORDER BY 
    CASE status 
        WHEN '❌' THEN 1
        WHEN '⚠️' THEN 2
        WHEN '✅' THEN 3
    END,
    metric;