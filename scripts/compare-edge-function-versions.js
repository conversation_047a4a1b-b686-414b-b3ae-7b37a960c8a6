#!/usr/bin/env node

/**
 * Script para comparar v9 vs v10 de la Edge Function
 * Ejecuta las mismas pruebas en ambas versiones
 */

const { exec } = require('child_process');
const fs = require('fs');

// Configuración
const SUPABASE_URL = 'https://ajsamgugqfbttkrlgvbr.supabase.co';
const ANON_KEY =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqc2FtZ3VncWZidHRrcmxndmJyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjA0NzEzMTEsImV4cCI6MjAzNjA0NzMxMX0.JvI2T_3DWKhO6DJQhO3qfrCKFV5EygLJrIg5pVDRT3c';

// Token de prueba - necesitas actualizarlo
const AUTH_TOKEN = process.env.SUPABASE_AUTH_TOKEN || 'TU_TOKEN_AQUI';

// Datos de prueba
const TEST_CASES = [
  {
    name: 'Diagnóstico simple',
    payload: {
      task: 'diagnose_image',
      payload: {
        imageBase64:
          '/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCABAAEADASIAAhEBAxEB/8QAGwAAAgMBAQEAAAAAAAAAAAAABQYDBAcCAQD/xAA1EAACAQMCBAMGBQMFAAAAAAABAgMABBEFIQYSMUETUWEHFCJxgZEVMkKhsRYjwVJi0eHw/8QAGAEAAwEBAAAAAAAAAAAAAAAAAQIDAAT/xAAYEQEBAQEBAAAAAAAAAAAAAAAAARECEv/aAAwDAQACEQMRAD8AWuJuLtS1SVljkMUQ6KpqG71m4nt1E0zOQNt6rPaFF8WRRt51yITIMRjIFcsXCtK0gKOwI9Kht7e6vJAIEdz5AZonLBPGMSBlPmRTZ7M7y00y1me4ZQ0jdWO2KPM2gVlocdrP/qB23zRyOwXA2A9avHW9AmPM0iFvMjcVZtr7RZyBHMufImqZBsRa1zjTq9tMrqzD4c9q9l0KW9uRFGjHfc42FN0NnprLnEf71Laj3ef4VUfL1oCXtf4JltuVHLOD0BOwpWu9Ilhm8PkYnPQCtcju4xqxknuVWJVx8RxmlbiHWIJbkeDMrqBgFTkE0Jo6L9QoWldoMHkY9s0Zu+DbuS1Mr8sIHVnbb9qW2aYgZLZPUb0/8FcXNPI2m6ukkkBHKJG6oaMqbClxHY6tBZCztsSzyNgOwwFXG+fvUfAejrcakGlJWO0HOR5v2H8/amnXNP8AeNZjuLZ1khKjl36YH+c1HbQvbMByBX6EL51XdYFx2K4BGDnpXVrKgABbB+dSxBWAqteo6EgI3yNJUMSajeR6bp8lzO4CIMDfqaS5rgzvJcyHLyMWJ+Zqpr2qy6nKkJXw4U/SepPmao+PygZJ+tdcc9PICcZ/3dfl8qgZRnLHA9KjEjSZOaqdyPQDqaIkfr2r0MD03+tQSZYjmqPmIPetGP/Z',
      },
    },
  },
  {
    name: 'Análisis de look deseado',
    payload: {
      task: 'analyze_desired_look',
      payload: {
        imageBase64:
          '/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCABAAEADASIAAhEBAxEB/8QAGwAAAgMBAQEAAAAAAAAAAAAABQYDBAcCAQD/xAA1EAACAQMCBAMGBQMFAAAAAAABAgMABBEFIQYSMUETUWEHFCJxgZEVMkKhsRYjwVJi0eHw/8QAGAEAAwEBAAAAAAAAAAAAAAAAAQIDAAT/xAAYEQEBAQEBAAAAAAAAAAAAAAAAARECEv/aAAwDAQACEQMRAD8AWuJuLtS1SVljkMUQ6KpqG71m4nt1E0zOQNt6rPaFF8WRRt51yITIMRjIFcsXCtK0gKOwI9Kht7e6vJAIEdz5AZonLBPGMSBlPmRTZ7M7y00y1me4ZQ0jdWO2KPM2gVlocdrP/qB23zRyOwXA2A9avHW9AmPM0iFvMjcVZtr7RZyBHMufImqZBsRa1zjTq9tMrqzD4c9q9l0KW9uRFGjHfc42FN0NnprLnEf71Laj3ef4VUfL1oCXtf4JltuVHLOD0BOwpWu9Ilhm8PkYnPQCtcju4xqxknuVWJVx8RxmlbiHWIJbkeDMrqBgFTkE0Jo6L9QoWldoMHkY9s0Zu+DbuS1Mr8sIHVnbb9qW2aYgZLZPUb0/8FcXNPI2m6ukkkBHKJG6oaMqbClxHY6tBZCztsSzyNgOwwFXG+fvUfAejrcakGlJWO0HOR5v2H8/amnXNP8AeNZjuLZ1khKjl36YH+c1HbQvbMByBX6EL51XdYFx2K4BGDnpXVrKgABbB+dSxBWAqteo6EgI3yNJUMSajeR6bp8lzO4CIMDfqaS5rgzvJcyHLyMWJ+Zqpr2qy6nKkJXw4U/SepPmao+PygZJ+tdcc9PICcZ/3dfl8qgZRnLHA9KjEjSZOaqdyPQDqaIkfr2r0MD03+tQSZYjmqPmIPetGP/Z',
        currentLevel: 6,
      },
    },
  },
  {
    name: 'Generación de fórmula',
    payload: {
      task: 'generate_formula',
      payload: {
        diagnosis: {
          averageDepthLevel: 6,
          overallTone: 'Castaño claro',
          zoneAnalysis: {
            roots: { depth: 5, tone: 'Castaño', grayPercentage: 20 },
            mids: { depth: 6, tone: 'Castaño claro' },
            ends: { depth: 7, tone: 'Rubio oscuro' },
          },
        },
        desiredResult: {
          level: 8,
          tone: 'Rubio claro ceniza',
          general: { technique: 'balayage' },
        },
        brand: "L'Oreal",
        line: 'Inoa',
      },
    },
  },
];

// Función para ejecutar una prueba
async function runTest(testCase, version = 'v10') {
  return new Promise(resolve => {
    const startTime = Date.now();

    const curlCommand = `curl -s -X POST "${SUPABASE_URL}/functions/v1/salonier-assistant" \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer ${AUTH_TOKEN}" \
      -H "apikey: ${ANON_KEY}" \
      -d '${JSON.stringify(testCase.payload)}'`;

    exec(curlCommand, (error, stdout, stderr) => {
      const endTime = Date.now();
      const duration = endTime - startTime;

      if (error || stderr) {
        resolve({
          name: testCase.name,
          version,
          success: false,
          error: error?.message || stderr,
          duration,
        });
        return;
      }

      try {
        const response = JSON.parse(stdout);
        resolve({
          name: testCase.name,
          version,
          success: response.success,
          tokensUsed: response.metrics?.tokensUsed || 0,
          cost: response.metrics?.cost || 0,
          cacheHit: response.metrics?.cacheHit || false,
          duration,
          templateUsed: response.debug?.templateType || 'unknown',
        });
      } catch {
        resolve({
          name: testCase.name,
          version,
          success: false,
          error: 'Failed to parse response',
          duration,
        });
      }
    });
  });
}

// Función para obtener datos de v9 del cache
async function getV9DataFromCache() {
  console.log('\n📊 Buscando datos históricos de v9...\n');

  // const _query = `
    SELECT 
      analysis_type,
      AVG(tokens_used) avg_tokens,
      AVG(cost_usd) avg_cost,
      COUNT(*) samples
    FROM ai_analysis_cache
    WHERE tokens_used > 0
    GROUP BY analysis_type
  // `;

  // Aquí simularemos datos de v9 basados en las estimaciones del documento
  // En producción, deberías obtener estos datos reales de la base de datos
  return {
    diagnose_image: { avg_tokens: 2430, avg_cost: 0.0107 }, // +67% más que v10
    analyze_desired_look: { avg_tokens: 985, avg_cost: 0.0038 }, // +67% más que v10
    generate_formula: { avg_tokens: 1950, avg_cost: 0.0135 }, // +43% más que v10
  };
}

// Función principal
async function main() {
  console.log('🔬 Comparación de Edge Function v9 vs v10\n');

  if (AUTH_TOKEN === 'TU_TOKEN_AQUI') {
    console.error('❌ Error: Necesitas configurar SUPABASE_AUTH_TOKEN');
    console.log('export SUPABASE_AUTH_TOKEN="tu_token_aqui"');
    process.exit(1);
  }

  // Obtener datos estimados de v9
  const v9Data = await getV9DataFromCache();

  console.log('📈 Datos estimados de v9 (basados en proyecciones):');
  console.log('- diagnose_image: ~2,430 tokens');
  console.log('- analyze_desired_look: ~985 tokens');
  console.log('- generate_formula: ~1,950 tokens\n');

  console.log('🚀 Ejecutando pruebas en v10...\n');

  const v10Results = [];

  for (const testCase of TEST_CASES) {
    console.log(`⏳ Ejecutando: ${testCase.name}...`);
    const result = await runTest(testCase, 'v10');
    v10Results.push(result);

    if (result.success) {
      console.log(`✅ Completado: ${result.tokensUsed} tokens, $${result.cost.toFixed(4)}`);
    } else {
      console.log(`❌ Error: ${result.error}`);
    }

    // Esperar un poco entre pruebas
    // await new Promise(r => setTimeout(r, 1000));
  }

  // Análisis de resultados
  console.log('\n' + '='.repeat(60));
  console.log('📊 ANÁLISIS COMPARATIVO');
  console.log('='.repeat(60) + '\n');

  const comparison = {
    diagnose_image: {
      v9_tokens: v9Data.diagnose_image.avg_tokens,
      v10_tokens: v10Results.find(r => r.name.includes('Diagnóstico'))?.tokensUsed || 0,
      reduction: 0,
    },
    analyze_desired_look: {
      v9_tokens: v9Data.analyze_desired_look.avg_tokens,
      v10_tokens: v10Results.find(r => r.name.includes('look deseado'))?.tokensUsed || 0,
      reduction: 0,
    },
    generate_formula: {
      v9_tokens: v9Data.generate_formula.avg_tokens,
      v10_tokens: v10Results.find(r => r.name.includes('fórmula'))?.tokensUsed || 0,
      reduction: 0,
    },
  };

  // Calcular reducciones
  for (const task in comparison) {
    const data = comparison[task];
    if (data.v10_tokens > 0) {
      data.reduction = (((data.v9_tokens - data.v10_tokens) / data.v9_tokens) * 100).toFixed(1);
    }
  }

  console.log('📉 Reducción de Tokens por Tarea:\n');
  console.log('diagnose_image:');
  console.log(`  v9: ${comparison.diagnose_image.v9_tokens} tokens (estimado)`);
  console.log(`  v10: ${comparison.diagnose_image.v10_tokens} tokens (real)`);
  console.log(`  Reducción: ${comparison.diagnose_image.reduction}%\n`);

  console.log('analyze_desired_look:');
  console.log(`  v9: ${comparison.analyze_desired_look.v9_tokens} tokens (estimado)`);
  console.log(`  v10: ${comparison.analyze_desired_look.v10_tokens} tokens (real)`);
  console.log(`  Reducción: ${comparison.analyze_desired_look.reduction}%\n`);

  console.log('generate_formula:');
  console.log(`  v9: ${comparison.generate_formula.v9_tokens} tokens (estimado)`);
  console.log(`  v10: ${comparison.generate_formula.v10_tokens} tokens (real)`);
  console.log(`  Reducción: ${comparison.generate_formula.reduction}%\n`);

  // Promedio de reducción
  const avgReduction =
    (parseFloat(comparison.diagnose_image.reduction) +
      parseFloat(comparison.analyze_desired_look.reduction) +
      parseFloat(comparison.generate_formula.reduction)) /
    3;

  console.log('='.repeat(60));
  console.log(`🎯 REDUCCIÓN PROMEDIO: ${avgReduction.toFixed(1)}%`);
  console.log('='.repeat(60) + '\n');

  // Guardar resultados
  const report = {
    timestamp: new Date().toISOString(),
    v9_estimated: v9Data,
    v10_actual: v10Results,
    comparison,
    average_reduction: avgReduction,
  };

  fs.writeFileSync('edge-function-comparison-results.json', JSON.stringify(report, null, 2));

  console.log('📄 Resultados guardados en: edge-function-comparison-results.json\n');

  // Recomendaciones
  console.log('💡 RECOMENDACIONES:\n');

  if (avgReduction < 30) {
    console.log('⚠️  La reducción es menor al 30% esperado. Se recomienda:');
    console.log('   - Revisar que los templates optimizados se estén usando');
    console.log('   - Ajustar el selector de templates');
    console.log('   - Optimizar más agresivamente los prompts');
  } else {
    console.log('✅ La reducción supera el 30%. Los templates están funcionando correctamente.');
  }

  // Proyección de costos actualizada
  const monthlyRequests = 7650; // Basado en datos reales
  const avgCostV10 = v10Results.reduce((sum, r) => sum + (r.cost || 0), 0) / v10Results.length;
  const avgCostV9 = avgCostV10 * (100 / (100 - avgReduction));

  const monthlyCostV10 = avgCostV10 * monthlyRequests;
  const monthlyCostV9 = avgCostV9 * monthlyRequests;
  const monthlySavings = monthlyCostV9 - monthlyCostV10;

  console.log('\n💰 PROYECCIÓN DE COSTOS (150 salones, 7,650 requests/mes):');
  console.log(`   v9: $${monthlyCostV9.toFixed(2)}/mes`);
  console.log(`   v10: $${monthlyCostV10.toFixed(2)}/mes`);
  console.log(`   Ahorro: $${monthlySavings.toFixed(2)}/mes`);
}

// Ejecutar
main().catch(console.error);
