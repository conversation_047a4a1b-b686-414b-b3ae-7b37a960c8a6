-- Script de Validación de Optimizaciones de Supabase
-- Ejecutar este script para verificar que todas las optimizaciones se aplicaron correctamente
-- Fecha: 2025-01-11

-- ============================================
-- 1. VERIFICAR POLÍTICAS RLS OPTIMIZADAS
-- ============================================
SELECT 
    '=== POLÍTICAS RLS ===' as section;

SELECT 
    tablename,
    policyname,
    CASE 
        WHEN qual LIKE '%(SELECT auth.uid())%' THEN '✅ Optimizada'
        WHEN qual LIKE '%auth.uid()%' THEN '❌ No optimizada'
        ELSE '⚠️ Revisar'
    END as estado,
    qual as definicion
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('profiles', 'salons', 'clients', 'products', 'services', 'formulas', 'stock_movements', 'client_consents', 'ai_analysis_cache')
ORDER BY tablename, policyname;

-- ============================================
-- 2. VERIFICAR FUNCIONES CON SEARCH_PATH
-- ============================================
SELECT 
    '=== FUNCIONES SEGURAS ===' as section;

SELECT 
    p.proname as function_name,
    CASE 
        WHEN p.proconfig::text LIKE '%search_path%' THEN '✅ Segura'
        ELSE '❌ Vulnerable'
    END as estado,
    p.proconfig as configuracion
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public'
AND p.proname IN ('delete_old_temp_photos', 'manual_user_setup', 'update_updated_at_column', 'clean_temp_photos', 'cleanup_expired_ai_cache', 'handle_new_user')
ORDER BY p.proname;

-- ============================================
-- 3. VERIFICAR ÍNDICES EN FOREIGN KEYS
-- ============================================
SELECT 
    '=== ÍNDICES EN FOREIGN KEYS ===' as section;

SELECT 
    schemaname,
    tablename,
    indexname,
    CASE 
        WHEN indexname LIKE 'idx_%' THEN '✅ Índice personalizado'
        ELSE '⚠️ Índice automático'
    END as tipo
FROM pg_indexes
WHERE schemaname = 'public'
AND indexname IN (
    'idx_client_consents_service_id',
    'idx_clients_created_by',
    'idx_formulas_created_by',
    'idx_salons_owner_id',
    'idx_stock_movements_created_by'
)
ORDER BY tablename, indexname;

-- ============================================
-- 4. VERIFICAR CONSTRAINTS
-- ============================================
SELECT 
    '=== CONSTRAINTS DE VALIDACIÓN ===' as section;

SELECT 
    conname as constraint_name,
    conrelid::regclass as table_name,
    CASE 
        WHEN conname = 'check_stock_positive' THEN '✅ Stock no negativo'
        WHEN conname = 'check_email_format' THEN '✅ Formato email'
        WHEN conname = 'check_service_date_not_future' THEN '✅ Fecha no futura'
        WHEN conname = 'check_valid_permissions' THEN '✅ Permisos válidos'
        ELSE '⚠️ Otro'
    END as descripcion,
    pg_get_constraintdef(oid) as definicion
FROM pg_constraint
WHERE connamespace = 'public'::regnamespace
AND contype = 'c'
AND conname IN ('check_stock_positive', 'check_email_format', 'check_service_date_not_future', 'check_valid_permissions')
ORDER BY conname;

-- ============================================
-- 5. VERIFICAR TRIGGERS PARA UPDATED_AT
-- ============================================
SELECT 
    '=== TRIGGERS UPDATED_AT ===' as section;

SELECT 
    trigger_schema,
    event_object_table as table_name,
    trigger_name,
    CASE 
        WHEN trigger_name LIKE '%updated_at%' THEN '✅ Trigger correcto'
        ELSE '⚠️ Revisar'
    END as estado
FROM information_schema.triggers
WHERE trigger_schema = 'public'
AND trigger_name LIKE '%updated_at%'
ORDER BY event_object_table;

-- ============================================
-- 6. VERIFICAR ÍNDICES ELIMINADOS
-- ============================================
SELECT 
    '=== ÍNDICES NO UTILIZADOS (deberían NO existir) ===' as section;

SELECT 
    COALESCE(indexname, '✅ No existe (correcto)') as index_status,
    idx.expected_index
FROM (
    VALUES 
        ('idx_profiles_salon_id'),
        ('idx_stock_movements_product_id'),
        ('idx_services_salon_id'),
        ('idx_services_client_id'),
        ('idx_services_stylist_id'),
        ('idx_formulas_salon_id'),
        ('idx_formulas_service_id'),
        ('idx_client_consents_salon_id'),
        ('idx_client_consents_client_id'),
        ('idx_ai_analysis_cache_salon_id'),
        ('idx_ai_analysis_cache_input_hash')
) AS idx(expected_index)
LEFT JOIN pg_indexes ON indexname = expected_index AND schemaname = 'public';

-- ============================================
-- 7. VERIFICAR MIGRACIONES APLICADAS
-- ============================================
SELECT 
    '=== MIGRACIONES APLICADAS ===' as section;

SELECT 
    version,
    name,
    executed_at
FROM supabase_migrations.schema_migrations
WHERE version >= '20250711065107'
ORDER BY version DESC
LIMIT 20;

-- ============================================
-- 8. ESTADÍSTICAS DE PERFORMANCE
-- ============================================
SELECT 
    '=== ESTADÍSTICAS DE TABLAS ===' as section;

SELECT 
    schemaname,
    tablename,
    n_live_tup as registros_vivos,
    n_dead_tup as registros_muertos,
    last_vacuum,
    last_analyze
FROM pg_stat_user_tables
WHERE schemaname = 'public'
ORDER BY n_live_tup DESC;

-- ============================================
-- 9. RESUMEN DE OPTIMIZACIONES
-- ============================================
SELECT 
    '=== RESUMEN FINAL ===' as section;

WITH optimization_status AS (
    SELECT 
        'Políticas RLS optimizadas' as item,
        COUNT(*) FILTER (WHERE qual LIKE '%(SELECT auth.uid())%') as optimized,
        COUNT(*) FILTER (WHERE qual LIKE '%auth.uid()%' AND qual NOT LIKE '%(SELECT auth.uid())%') as not_optimized
    FROM pg_policies 
    WHERE schemaname = 'public'
    
    UNION ALL
    
    SELECT 
        'Funciones con search_path' as item,
        COUNT(*) FILTER (WHERE proconfig::text LIKE '%search_path%') as optimized,
        COUNT(*) FILTER (WHERE proconfig::text NOT LIKE '%search_path%' OR proconfig IS NULL) as not_optimized
    FROM pg_proc p
    JOIN pg_namespace n ON p.pronamespace = n.oid
    WHERE n.nspname = 'public'
    AND p.proname IN ('delete_old_temp_photos', 'manual_user_setup', 'update_updated_at_column', 'clean_temp_photos')
)
SELECT 
    item as "Elemento",
    optimized as "✅ Optimizados",
    not_optimized as "❌ Pendientes",
    CASE 
        WHEN not_optimized = 0 THEN '🎉 Completo'
        ELSE '⚠️ Revisar'
    END as "Estado"
FROM optimization_status;