#!/bin/bash

# Test Chat Assistant Edge Function
# Version: 1
# Last Updated: 2025-02-10

echo "🧪 Probando Chat Assistant Edge Function..."

# Configuración
SUPABASE_URL="https://ajsamgugqfbttkrlgvbr.supabase.co"
EDGE_FUNCTION_URL="${SUPABASE_URL}/functions/v1/chat-assistant"

# Verificar que tenemos las credenciales
if [ -z "$EXPO_PUBLIC_SUPABASE_ANON_KEY" ]; then
    echo "❌ Error: EXPO_PUBLIC_SUPABASE_ANON_KEY no está configurada"
    echo "Configúrala con: export EXPO_PUBLIC_SUPABASE_ANON_KEY='tu-anon-key'"
    exit 1
fi

# Crear payload de prueba
PAYLOAD='{
  "conversationId": "test-conversation-123",
  "message": "¿Cómo neutralizar tonos naranjas en un nivel 7?",
  "salonId": "test-salon-id",
  "userId": "test-user-id"
}'

echo "📤 Enviando mensaje de prueba..."
echo "Payload: $PAYLOAD"
echo ""

# Hacer la llamada
RESPONSE=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $EXPO_PUBLIC_SUPABASE_ANON_KEY" \
  -H "apikey: $EXPO_PUBLIC_SUPABASE_ANON_KEY" \
  -d "$PAYLOAD" \
  "$EDGE_FUNCTION_URL")

# Verificar respuesta
if [ $? -eq 0 ]; then
    echo "📥 Respuesta recibida:"
    echo "$RESPONSE" | jq '.' 2>/dev/null || echo "$RESPONSE"
    
    # Verificar si hay error
    if echo "$RESPONSE" | grep -q '"success":false'; then
        echo ""
        echo "❌ La función respondió con error"
    elif echo "$RESPONSE" | grep -q '"success":true'; then
        echo ""
        echo "✅ Chat Assistant funcionando correctamente!"
    fi
else
    echo "❌ Error al conectar con la Edge Function"
fi

echo ""
echo "💡 Para ver logs en tiempo real:"
echo "supabase functions logs chat-assistant --tail"