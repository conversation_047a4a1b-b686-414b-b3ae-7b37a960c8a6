/**
 * <PERSON><PERSON><PERSON> to check contrast ratios in the app
 * Run with: npx ts-node scripts/check-contrast.ts
 */

import { generateContrastReport, getFailingCombinations } from '../utils/contrast-checker';

console.log('🎨 Checking contrast ratios for Salonier...\n');

const report = generateContrastReport();
console.log(report);

const failing = getFailingCombinations();
if (failing.length > 0) {
  console.log('\n❌ Action required: Some color combinations do not meet WCAG AA standards.');
  console.log('Please update the colors in constants/Colors.ts\n');
  process.exit(1);
} else {
  console.log('\n✅ All color combinations meet WCAG AA standards!');
  process.exit(0);
}
