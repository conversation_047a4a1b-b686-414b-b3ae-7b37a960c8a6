#!/bin/bash

# Test Chat Assistant To<PERSON>-en-Uno
# Version: 2.0
# Last Updated: 2025-02-16

echo "🧪 <PERSON>bando Chat Assistant To<PERSON>-en-Uno..."
echo ""

# Configuración
SUPABASE_URL="https://ajsamgugqfbttkrlgvbr.supabase.co"
EDGE_FUNCTION_URL="${SUPABASE_URL}/functions/v1/chat-assistant"
ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqc2FtZ3VncWZidHRrcmxndmJyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxNzYwOTcsImV4cCI6MjA2Nzc1MjA5N30.r6IC-i_GYrSHvS2tKudyGONrhX8-NlyNRl1aHo_AToY"

# Menú de pruebas
echo "Selecciona el tipo de prueba:"
echo "1) Pregunta simple sobre coloración"
echo "2) Iniciar flujo de servicio"
echo "3) An<PERSON><PERSON><PERSON> con imagen (simulado)"
echo ""
read -p "Opción (1-3): " option

case $option in
  1)
    echo ""
    echo "📤 Enviando pregunta sobre coloración..."
    PAYLOAD='{
      "conversationId": "test-'$(date +%s)'",
      "message": "¿Cómo neutralizar tonos naranjas en un cabello nivel 7?",
      "salonId": "test-salon-id",
      "userId": "test-user-id"
    }'
    ;;
    
  2)
    echo ""
    echo "📤 Iniciando flujo de servicio..."
    PAYLOAD='{
      "conversationId": "test-service-'$(date +%s)'",
      "message": "Quiero crear un nuevo servicio para mi clienta María",
      "salonId": "test-salon-id",
      "userId": "test-user-id"
    }'
    ;;
    
  3)
    echo ""
    echo "📤 Simulando análisis con imagen..."
    # Base64 pequeña de prueba (1x1 pixel transparente)
    BASE64_IMAGE="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    
    PAYLOAD='{
      "conversationId": "test-image-'$(date +%s)'",
      "message": "Analiza el color actual del cabello",
      "salonId": "test-salon-id",
      "userId": "test-user-id",
      "attachments": [{
        "type": "image",
        "url": "'$BASE64_IMAGE'",
        "mimeType": "image/png"
      }]
    }'
    ;;
    
  *)
    echo "Opción inválida"
    exit 1
    ;;
esac

echo "Payload: $PAYLOAD"
echo ""

# Hacer la llamada
echo "🔄 Llamando al Edge Function..."
RESPONSE=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ANON_KEY" \
  -H "apikey: $ANON_KEY" \
  -d "$PAYLOAD" \
  "$EDGE_FUNCTION_URL")

# Verificar respuesta
echo ""
echo "📥 Respuesta recibida:"
echo "$RESPONSE" | jq '.' 2>/dev/null || echo "$RESPONSE"

# Analizar resultado
if echo "$RESPONSE" | grep -q '"success":true'; then
    echo ""
    echo "✅ Chat Assistant funcionando correctamente!"
    
    # Mostrar el contenido de la respuesta
    CONTENT=$(echo "$RESPONSE" | jq -r '.content' 2>/dev/null)
    if [ ! -z "$CONTENT" ] && [ "$CONTENT" != "null" ]; then
        echo ""
        echo "💬 Respuesta del asistente:"
        echo "----------------------------------------"
        echo "$CONTENT"
        echo "----------------------------------------"
    fi
    
    # Mostrar estado si existe
    STATE=$(echo "$RESPONSE" | jq '.state' 2>/dev/null)
    if [ ! -z "$STATE" ] && [ "$STATE" != "null" ] && [ "$STATE" != "{}" ]; then
        echo ""
        echo "📊 Estado del flujo:"
        echo "$STATE" | jq '.'
    fi
elif echo "$RESPONSE" | grep -q '"success":false'; then
    echo ""
    echo "❌ La función respondió con error"
    ERROR=$(echo "$RESPONSE" | jq -r '.error' 2>/dev/null)
    echo "Error: $ERROR"
else
    echo ""
    echo "❌ Error inesperado en la respuesta"
fi

echo ""
echo "💡 Para ver logs en tiempo real:"
echo "supabase functions logs chat-assistant --tail"