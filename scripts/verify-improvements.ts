/**
 * Script para verificar las mejoras implementadas en marcas y líneas
 */

import { 
  professionalHairColorBrands,
  getColorLinesByBrandId,
  validateBrandLines,
  getBrandLinesStats,
  getBrandsByPopularity,
  getRecommendedBrandsByRegion,
  getBrandTechnicalInfo
} from '../constants/reference-data/brands-data';

function verifyImprovements() {
  console.log('🔍 VERIFICANDO MEJORAS IMPLEMENTADAS\n');

  // 1. Verificar Salerm Cosmetics
  console.log('1. ✅ SALERM COSMETICS:');
  const salermBrand = professionalHairColorBrands.find(b => b.id === 'salerm');
  if (salermBrand) {
    const salermColorLines = getColorLinesByBrandId('salerm');
    console.log(`   Total líneas: ${salermBrand.lines.length}`);
    console.log(`   Líneas de coloración: ${salermColorLines.length}`);
    console.log('   Líneas de coloración disponibles:');
    salermColorLines.forEach(line => {
      console.log(`   ✅ ${line.name} - ${line.description}`);
    });
    
    const nonColorLines = salermBrand.lines.filter(line => !line.isColorLine);
    console.log('   Líneas NO de coloración (no aparecen en selectores):');
    nonColorLines.forEach(line => {
      console.log(`   ❌ ${line.name} - ${line.description}`);
    });
  }

  // 2. Verificar Arkhe Cosmetics
  console.log('\n2. ✅ ARKHE COSMETICS:');
  const arkheBrand = professionalHairColorBrands.find(b => b.id === 'arkhe');
  if (arkheBrand) {
    const arkheColorLines = getColorLinesByBrandId('arkhe');
    console.log(`   Total líneas: ${arkheBrand.lines.length}`);
    console.log(`   Líneas de coloración: ${arkheColorLines.length}`);
    arkheColorLines.forEach(line => {
      console.log(`   ✅ ${line.name} - ${line.description}`);
    });
  } else {
    console.log('   ❌ Arkhe Cosmetics no encontrada');
  }

  // 3. Verificar estadísticas generales
  console.log('\n3. 📊 ESTADÍSTICAS GENERALES:');
  const stats = getBrandLinesStats();
  console.log(`   Total marcas: ${professionalHairColorBrands.length}`);
  console.log(`   Total líneas: ${stats.total}`);
  console.log(`   Líneas de coloración: ${stats.colorLines}`);
  console.log(`   Líneas de tratamiento: ${stats.treatmentLines}`);
  console.log(`   Líneas de styling: ${stats.stylingLines}`);
  console.log(`   Líneas de decoloración: ${stats.bleachingLines}`);
  console.log(`   Líneas de oxidante: ${stats.developerLines}`);
  console.log(`   Otras líneas: ${stats.otherLines}`);
  console.log(`   Marcas con líneas de coloración: ${stats.brandsWithColorLines}`);
  console.log(`   Marcas sin líneas de coloración: ${stats.brandsWithoutColorLines}`);

  // 4. Verificar validación
  console.log('\n4. ✅ VALIDACIÓN:');
  const validation = validateBrandLines();
  console.log(`   Configuración válida: ${validation.valid ? '✅ SÍ' : '❌ NO'}`);
  if (!validation.valid) {
    console.log(`   Problemas encontrados: ${validation.issues.length}`);
    validation.issues.slice(0, 5).forEach(issue => {
      console.log(`   - ${issue}`);
    });
    if (validation.issues.length > 5) {
      console.log(`   ... y ${validation.issues.length - 5} más`);
    }
  }

  // 5. Verificar nuevas marcas agregadas
  console.log('\n5. 🌍 NUEVAS MARCAS INTERNACIONALES:');
  const newBrands = ['keune-professional', 'davines-professional', 'fanola-professional', 'tec-italy-professional'];
  newBrands.forEach(brandId => {
    const brand = professionalHairColorBrands.find(b => b.id === brandId);
    if (brand) {
      const colorLines = getColorLinesByBrandId(brandId);
      console.log(`   ✅ ${brand.name} (${brand.country}) - ${colorLines.length} líneas de coloración`);
    } else {
      console.log(`   ❌ ${brandId} no encontrada`);
    }
  });

  // 6. Verificar organización por popularidad
  console.log('\n6. 📈 ORGANIZACIÓN POR POPULARIDAD:');
  const brandsByPopularity = getBrandsByPopularity();
  console.log('   Top 10 marcas por popularidad:');
  brandsByPopularity.slice(0, 10).forEach((brand, index) => {
    const colorLines = getColorLinesByBrandId(brand.id);
    console.log(`   ${index + 1}. ${brand.name} (${brand.country}) - ${colorLines.length} líneas`);
  });

  // 7. Verificar recomendaciones regionales
  console.log('\n7. 🌍 RECOMENDACIONES REGIONALES:');
  const regions = ['Spain', 'Europe', 'North America', 'South America', 'Asia'];
  regions.forEach(region => {
    const recommended = getRecommendedBrandsByRegion(region);
    console.log(`   ${region}: ${recommended.length} marcas recomendadas`);
    recommended.slice(0, 3).forEach(brand => {
      console.log(`     - ${brand.name}`);
    });
  });

  // 8. Verificar información técnica
  console.log('\n8. 🔧 INFORMACIÓN TÉCNICA:');
  const brandsWithTechInfo = ['wella', 'loreal', 'schwarzkopf', 'salerm', 'arkhe'];
  brandsWithTechInfo.forEach(brandId => {
    const techInfo = getBrandTechnicalInfo(brandId);
    if (techInfo) {
      console.log(`   ✅ ${brandId}: ${techInfo.numberingSystem}`);
    } else {
      console.log(`   ❌ ${brandId}: Sin información técnica`);
    }
  });

  // 9. Verificar marcas españolas mejoradas
  console.log('\n9. 🇪🇸 MARCAS ESPAÑOLAS MEJORADAS:');
  const spanishBrands = professionalHairColorBrands.filter(b => b.country === 'Spain');
  spanishBrands.forEach(brand => {
    const colorLines = getColorLinesByBrandId(brand.id);
    console.log(`   ${brand.name}: ${colorLines.length} líneas de coloración`);
  });

  console.log('\n✅ VERIFICACIÓN COMPLETADA');
}

// Función específica para verificar Salerm
function verifySalermSpecifically() {
  console.log('🔍 VERIFICACIÓN ESPECÍFICA DE SALERM COSMETICS\n');
  
  const salerm = professionalHairColorBrands.find(b => b.id === 'salerm');
  if (!salerm) {
    console.log('❌ Salerm no encontrada');
    return;
  }

  console.log('📋 TODAS LAS LÍNEAS DE SALERM:');
  salerm.lines.forEach(line => {
    const status = line.isColorLine ? '🎨 COLORACIÓN' : '🧴 NO COLORACIÓN';
    console.log(`   ${status} - ${line.name}: ${line.description}`);
    console.log(`      Categoría: ${line.category || 'SIN CATEGORÍA'}`);
  });

  console.log('\n🎨 LÍNEAS QUE APARECEN EN FORMULACIÓN:');
  const colorLines = getColorLinesByBrandId('salerm');
  colorLines.forEach(line => {
    console.log(`   ✅ ${line.name} - ${line.description}`);
  });

  console.log('\n🚫 LÍNEAS QUE NO APARECEN EN FORMULACIÓN:');
  const nonColorLines = salerm.lines.filter(line => !line.isColorLine);
  nonColorLines.forEach(line => {
    console.log(`   ❌ ${line.name} - ${line.description}`);
  });

  // Verificar que Biokera Natura Color está incluida
  const biokera = colorLines.find(line => line.id === 'biokera-natura-color');
  if (biokera) {
    console.log('\n✅ BIOKERA NATURA COLOR CORRECTAMENTE INCLUIDA COMO LÍNEA DE COLORACIÓN');
  } else {
    console.log('\n❌ BIOKERA NATURA COLOR NO ENCONTRADA EN LÍNEAS DE COLORACIÓN');
  }
}

// Exportar funciones
export { verifyImprovements, verifySalermSpecifically };

// Si se ejecuta directamente
if (require.main === module) {
  verifyImprovements();
  console.log('\n' + '='.repeat(80) + '\n');
  verifySalermSpecifically();
}
