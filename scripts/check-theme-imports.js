#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 Checking theme imports...\n');

// Theme properties that are commonly used
const themeProperties = ['shadows', 'radius', 'spacing', 'typography', 'animations', 'zIndex'];

const checkFile = filePath => {
  const content = fs.readFileSync(filePath, 'utf8');
  const issues = [];

  for (const prop of themeProperties) {
    const usageRegex = new RegExp(`\\b${prop}\\.`, 'g');
    const importRegex = new RegExp(`import.*{[^}]*${prop}[^}]*}.*from.*@/constants/theme`);

    if (usageRegex.test(content) && !importRegex.test(content)) {
      issues.push(`Missing ${prop} import`);
    }
  }

  return issues;
};

const processDirectory = dir => {
  const files = fs.readdirSync(dir, { withFileTypes: true });
  let totalIssues = 0;

  for (const file of files) {
    const filePath = path.join(dir, file.name);

    if (
      file.isDirectory() &&
      !['node_modules', '.git', 'coverage', 'archive'].includes(file.name)
    ) {
      totalIssues += processDirectory(filePath);
    } else if (file.name.endsWith('.tsx') || file.name.endsWith('.ts')) {
      const issues = checkFile(filePath);

      if (issues.length > 0) {
        console.log(`⚠️  ${filePath}:`);
        issues.forEach(issue => console.log(`   - ${issue}`));
        totalIssues += issues.length;
      }
    }
  }

  return totalIssues;
};

// Check for require cycles in theme-related files
console.log('1️⃣ Checking theme property imports...');
const issueCount = processDirectory('./app') + processDirectory('./components');

if (issueCount === 0) {
  console.log('   ✅ All theme imports are correct!');
} else {
  console.log(`   ⚠️  Found ${issueCount} theme import issues`);
}

// Check for circular dependencies in constants
console.log('\n2️⃣ Checking constants structure...');
const themeFile = './constants/theme.ts';
const colorsFile = './constants/colors.ts';

if (fs.existsSync(themeFile)) {
  const themeContent = fs.readFileSync(themeFile, 'utf8');
  if (themeContent.includes('@/constants/colors')) {
    console.log('   ⚠️  Theme imports from colors - potential cycle');
  } else {
    console.log('   ✅ Theme file is clean');
  }
}

if (fs.existsSync(colorsFile)) {
  const colorsContent = fs.readFileSync(colorsFile, 'utf8');
  if (colorsContent.includes('@/constants/theme')) {
    console.log('   ⚠️  Colors imports from theme - potential cycle');
  } else {
    console.log('   ✅ Colors file is clean');
  }
}

console.log('\n✅ Theme import check completed!');
