#!/bin/bash
# Script para desplegar la Edge Function actualizada

echo "🚀 Desplegando Edge Function salonier-assistant..."

# Verificar que estamos en el directorio correcto
if [ ! -d "supabase/functions/salonier-assistant" ]; then
    echo "❌ Error: No se encontró el directorio de la función"
    echo "Asegúrate de ejecutar este script desde la raíz del proyecto"
    exit 1
fi

# Verificar que Supabase CLI está instalado
if ! command -v supabase &> /dev/null; then
    echo "❌ Error: Supabase CLI no está instalado"
    echo "Instálalo con: brew install supabase/tap/supabase"
    exit 1
fi

# Desplegar la función
echo "📦 Desplegando función..."
supabase functions deploy salonier-assistant

if [ $? -eq 0 ]; then
    echo "✅ Edge Function desplegada exitosamente"
    echo ""
    echo "📝 Recuerda verificar que OPENAI_API_KEY esté configurada en:"
    echo "   Dashboard → Edge Functions → salonier-assistant → Manage secrets"
else
    echo "❌ Error al desplegar la función"
    exit 1
fi