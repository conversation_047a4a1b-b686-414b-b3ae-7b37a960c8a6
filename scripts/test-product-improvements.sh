#!/bin/bash

echo "🧪 Testing Product Improvements..."
echo "================================"

# Check if .env.local exists
if [ ! -f .env.local ]; then
    echo "❌ Error: .env.local file not found"
    echo "Please create .env.local with your Supabase credentials"
    exit 1
fi

# Load environment variables
source .env.local

# Test 1: Get low stock products
echo -e "\n📦 Test 1: Getting low stock products..."
curl -s \
  -H "apikey: $EXPO_PUBLIC_SUPABASE_ANON_KEY" \
  -H "Authorization: Bearer $EXPO_PUBLIC_SUPABASE_ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "p_salon_id": "YOUR_SALON_ID_HERE"
  }' \
  "$EXPO_PUBLIC_SUPABASE_URL/rest/v1/rpc/get_low_stock_products" | jq '.'

# Test 2: Query the product summary view
echo -e "\n📊 Test 2: Querying product summary view..."
curl -s \
  -H "apikey: $EXPO_PUBLIC_SUPABASE_ANON_KEY" \
  -H "Authorization: Bearer $EXPO_PUBLIC_SUPABASE_ANON_KEY" \
  "$EXPO_PUBLIC_SUPABASE_URL/rest/v1/v_salon_products_summary?salon_id=eq.YOUR_SALON_ID_HERE&limit=5" | jq '.'

# Test 3: Update salon settings with preferred brands
echo -e "\n⚙️ Test 3: Updating salon settings..."
curl -s \
  -H "apikey: $EXPO_PUBLIC_SUPABASE_ANON_KEY" \
  -H "Authorization: Bearer $EXPO_PUBLIC_SUPABASE_ANON_KEY" \
  -H "Content-Type: application/json" \
  -X POST \
  -d '{
    "p_salon_id": "YOUR_SALON_ID_HERE",
    "p_settings": {
      "preferredBrands": ["wella", "loreal", "schwarzkopf"],
      "skipSafetyVerification": false,
      "businessName": "Test Salon"
    }
  }' \
  "$EXPO_PUBLIC_SUPABASE_URL/rest/v1/rpc/update_salon_settings" | jq '.'

echo -e "\n✅ Tests completed!"
echo "================================"
echo "⚠️  Note: Replace YOUR_SALON_ID_HERE with an actual salon ID before running"