#!/bin/bash

# Test simple del Chat Assistant
echo "🧪 Test simple del Chat Assistant..."

# URLs
SUPABASE_URL="https://ajsamgugqfbttkrlgvbr.supabase.co"
ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqc2FtZ3VncWZidHRrcmxndmJyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjcwMzc3MTEsImV4cCI6MjA0MjYxMzcxMX0.EW-Cz6KwEWAU5sU1NGL2Yiw7XNjgaSzjJNpmT4TQCR0"

# Payload simple
PAYLOAD='{
  "conversationId": "test-123",
  "message": "<PERSON><PERSON>, necesito ayuda con una fórmula",
  "salonId": "550e8400-e29b-41d4-a716-446655440000",
  "userId": "550e8400-e29b-41d4-a716-446655440001"
}'

echo "📤 Enviando al Edge Function..."
curl -i -X POST \
  "${SUPABASE_URL}/functions/v1/chat-assistant" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${ANON_KEY}" \
  -H "apikey: ${ANON_KEY}" \
  -d "$PAYLOAD"

echo ""
echo ""
echo "💡 Si ves un error 500, revisa:"
echo "1. OPENAI_API_KEY está configurada en Supabase"
echo "2. Los logs con: supabase functions logs chat-assistant --tail"