#!/bin/bash

# Deploy Chat Assistant Edge Function v2
# Version: 2.0
# Last Updated: 2025-02-16
# Purpose: Deploy the revolutionary Todo-en-Uno Chat Assistant

echo "🚀 Desplegando Chat Assistant Todo-en-Uno..."

# Verificar que estamos en el directorio correcto
if [ ! -f "supabase/functions/chat-assistant/index.ts" ]; then
    echo "❌ Error: No se encuentra el archivo de la función."
    echo "Asegúrate de estar en el directorio raíz del proyecto."
    exit 1
fi

# Verificar que Supabase CLI está instalado
if ! command -v supabase &> /dev/null; then
    echo "❌ Error: Supabase CLI no está instalado."
    echo "Instálalo con: brew install supabase/tap/supabase"
    exit 1
fi

echo "📦 Desplegando función chat-assistant..."
supabase functions deploy chat-assistant --no-verify-jwt

if [ $? -eq 0 ]; then
    echo "✅ Chat Assistant Edge Function desplegada exitosamente!"
    echo ""
    echo "🎯 Características del nuevo Chat Assistant:"
    echo "  • ChatGPT especializado en coloración"
    echo "  • Puede ejecutar flujos completos de servicio"
    echo "  • Análisis de imágenes con Vision API"
    echo "  • Mantiene estado de conversación"
    echo "  • Genera fórmulas y guarda servicios"
    echo ""
    echo "📝 Notas importantes:"
    echo "  • Usa GPT-4o para máxima calidad"
    echo "  • OPENAI_API_KEY debe estar configurada"
    echo "  • Logs: supabase functions logs chat-assistant --tail"
    echo ""
    echo "🧪 Para probar:"
    echo "  ./scripts/test-chat-assistant-v2.sh"
else
    echo "❌ Error al desplegar la función"
    exit 1
fi