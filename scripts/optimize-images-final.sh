#!/bin/bash

# Script final de optimización de imágenes para Salonier
# Usa pngquant para compresión avanzada y sips para redimensionamiento

echo "🎨 Iniciando optimización de imágenes con pngquant..."

# Directorio de imágenes
IMG_DIR="assets/images"
BACKUP_DIR="$IMG_DIR/backup"

# Función para optimizar PNG
optimize_png() {
    local input=$1
    local max_size=$2
    local quality=$3
    local filename=$(basename "$input")
    
    echo "  🔧 Procesando $filename..."
    
    # Paso 1: Redimensionar si es necesario
    if [ ! -z "$max_size" ]; then
        width=$(sips -g pixelWidth "$input" | tail -1 | awk '{print $2}')
        height=$(sips -g pixelHeight "$input" | tail -1 | awk '{print $2}')
        
        if [ "$width" -gt "$max_size" ] || [ "$height" -gt "$max_size" ]; then
            echo "    📐 Redimensionando a ${max_size}x${max_size}..."
            sips -Z "$max_size" "$input" --out "$input" >/dev/null 2>&1
        fi
    fi
    
    # Paso 2: Comprimir con pngquant
    echo "    🗜️  Comprimiendo con calidad $quality..."
    pngquant --quality=$quality --speed=1 --force --output "$input" "$input" 2>/dev/null
    
    # Mostrar tamaño resultante
    size=$(ls -lh "$input" | awk '{print $5}')
    echo "    ✅ Tamaño final: $size"
}

# Restaurar desde backup primero
echo "🔄 Restaurando imágenes originales..."
cp "$BACKUP_DIR"/*.png "$IMG_DIR"/ 2>/dev/null

echo ""
echo "📱 Optimizando iconos de la aplicación..."

# adaptive-icon.png - Redimensionar a 1024x1024 y comprimir
optimize_png "$IMG_DIR/adaptive-icon.png" 1024 "85-95"

# icon.png - Ya está en 1024x1024, solo comprimir
optimize_png "$IMG_DIR/icon.png" "" "85-95"

# splash-icon.png - Mantener en 512x512, comprimir
optimize_png "$IMG_DIR/splash-icon.png" "" "85-95"

# favicon.png - Es muy pequeño, aplicar máxima calidad
echo "  🔧 Procesando favicon.png..."
pngquant --quality=95-100 --speed=1 --force --output "$IMG_DIR/favicon.png" "$IMG_DIR/favicon.png" 2>/dev/null
size=$(ls -lh "$IMG_DIR/favicon.png" | awk '{print $5}')
echo "    ✅ Tamaño final: $size"

# Mostrar resultados finales
echo ""
echo "✅ Optimización completada!"
echo ""
echo "📊 Resultados de la optimización:"
echo ""
echo "ANTES (Original):"
echo "─────────────────"
for file in "$BACKUP_DIR"/*.png; do
    filename=$(basename "$file")
    size=$(ls -lh "$file" | awk '{print $5}')
    printf "%-20s %s\n" "$filename:" "$size"
done
before_total=$(du -h "$BACKUP_DIR" | awk '{print $1}')
echo "─────────────────"
printf "%-20s %s\n" "TOTAL:" "$before_total"

echo ""
echo "DESPUÉS (Optimizado):"
echo "─────────────────"
for file in "$IMG_DIR"/*.png; do
    if [[ ! "$file" == *"backup"* ]]; then
        filename=$(basename "$file")
        size=$(ls -lh "$file" | awk '{print $5}')
        printf "%-20s %s\n" "$filename:" "$size"
    fi
done
after_total=$(du -h "$IMG_DIR"/*.png | grep -v backup | awk '{sum+=$1} END {print sum "K"}')
echo "─────────────────"
printf "%-20s %s\n" "TOTAL:" "$after_total"

# Calcular porcentaje de reducción
before_kb=$(du -k "$BACKUP_DIR" | awk '{print $1}')
after_kb=$(du -k "$IMG_DIR"/*.png | grep -v backup | awk '{sum+=$1} END {print sum}')
reduction=$((100 - (after_kb * 100 / before_kb)))

echo ""
echo "📉 Reducción total: ${reduction}% del tamaño original"
echo "💾 Espacio ahorrado: $((before_kb - after_kb))KB"