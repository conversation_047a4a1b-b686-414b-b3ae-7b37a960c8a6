#!/usr/bin/env node

/**
 * Build script to combine modular Edge Function files into a single deployable file
 * This is needed because Supabase Edge Functions don't support ES modules imports
 */

const fs = require('fs');
const path = require('path');

const sourceDir = path.join(__dirname, '..', 'supabase', 'functions', 'salonier-assistant');
const outputFile = path.join(sourceDir, 'index-bundled.ts');

// Files to combine in order
const filesToCombine = [
  'types.ts',
  'constants.ts',
  'helpers/image-validation.ts',
  'helpers/openai-client.ts',
  'utils/response-validator.ts',
  'utils/cache-manager.ts',
  'utils/prompt-templates.ts',
  'index.ts',
];

function readAndProcessFile(filePath) {
  const content = fs.readFileSync(path.join(sourceDir, filePath), 'utf8');

  // Remove import statements and adjust exports
  const processedContent = content
    .split('\n')
    .map(line => {
      // Remove import statements from local files
      if (line.trim().startsWith('import') && line.includes('./')) {
        return '';
      }
      // Remove export statements at file level
      if (line.trim().startsWith('export {')) {
        return '';
      }
      // Convert 'export class' to just 'class'
      if (line.includes('export class')) {
        return line.replace('export class', 'class');
      }
      // Convert 'export interface' to just 'interface'
      if (line.includes('export interface')) {
        return line.replace('export interface', 'interface');
      }
      // Convert 'export type' to just 'type'
      if (line.includes('export type')) {
        return line.replace('export type', 'type');
      }
      // Convert 'export const' to just 'const'
      if (line.includes('export const')) {
        return line.replace('export const', 'const');
      }
      // Convert 'export function' to just 'function'
      if (line.includes('export function')) {
        return line.replace('export function', 'function');
      }
      return line;
    })
    .filter(line => line !== '')
    .join('\n');

  return `// ============ From ${filePath} ============\n${processedContent}\n`;
}

function buildBundle() {
  console.log('Building Edge Function bundle...');

  let bundledContent = `/**
 * Salonier Assistant Edge Function - Bundled Version
 * Auto-generated from modular source files
 */

`;

  // Add imports that should remain (external dependencies)
  bundledContent += `import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

`;

  // Process each file
  filesToCombine.forEach(file => {
    if (fs.existsSync(path.join(sourceDir, file))) {
      console.log(`Processing ${file}...`);
      bundledContent += readAndProcessFile(file);
    } else {
      console.warn(`Warning: ${file} not found`);
    }
  });

  // Write the bundled file
  fs.writeFileSync(outputFile, bundledContent);
  console.log(`✅ Bundle created at: ${outputFile}`);
  console.log('\nTo deploy, run:');
  console.log(`supabase functions deploy salonier-assistant --project-ref ajsamgugqfbttkrlgvbr`);
}

// Run the build
buildBundle();
