#!/bin/bash

# Test Chat Assistant Public Edge Function
# No authentication required - public endpoint

echo "🧪 Testing Chat Assistant Public Edge Function..."

# URLs
SUPABASE_URL="https://ajsamgugqfbttkrlgvbr.supabase.co"
ENDPOINT="${SUPABASE_URL}/functions/v1/chat-assistant-public"

# Test payload
PAYLOAD='{
  "conversationId": "test-123",
  "message": "¿Cómo neutralizar tonos naranjas en un nivel 7?",
  "salonId": "550e8400-e29b-41d4-a716-446655440000",
  "userId": "550e8400-e29b-41d4-a716-446655440001"
}'

echo "📤 Sending test message (no auth required)..."
echo ""

# Make the request
RESPONSE=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -d "$PAYLOAD" \
  "$ENDPOINT")

# Check if we got a response
if [ $? -eq 0 ]; then
    echo "📥 Response received:"
    echo "$RESPONSE" | jq '.' 2>/dev/null || echo "$RESPONSE"
    
    # Check for success
    if echo "$RESPONSE" | grep -q '"content"'; then
        echo ""
        echo "✅ Chat Assistant Public is working!"
        echo ""
        echo "The assistant responded with:"
        echo "$RESPONSE" | jq -r '.content' 2>/dev/null | head -3
        echo "..."
    elif echo "$RESPONSE" | grep -q '"error"'; then
        echo ""
        echo "❌ Error response:"
        echo "$RESPONSE" | jq -r '.error' 2>/dev/null
    fi
else
    echo "❌ Failed to connect to the Edge Function"
fi

echo ""
echo "💡 Notes:"
echo "- This is a public endpoint (no auth required)"
echo "- Rate limited to 10 requests/minute per IP"
echo "- Supports image attachments via base64 URLs"
echo "- Multi-language support built-in"