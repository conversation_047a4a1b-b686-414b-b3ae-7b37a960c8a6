const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Configuración
const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL.replace(/"/g, '');
const SUPABASE_ANON_KEY = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY.replace(/"/g, '');

// Credenciales de prueba - CAMBIAR POR TUS CREDENCIALES
const TEST_EMAIL = '<EMAIL>'; // Cambia esto
const TEST_PASSWORD = 'password123'; // Cambia esto

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Imagen de prueba pequeña (1x1 pixel rojo)
const TEST_IMAGE =
  'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';

async function testAIAnalysis() {
  try {
    console.log('1. Iniciando sesión...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: TEST_EMAIL,
      password: TEST_PASSWORD,
    });

    if (authError) {
      console.error('Error de autenticación:', authError.message);
      console.log('\nPor favor, actualiza TEST_EMAIL y TEST_PASSWORD con credenciales válidas');
      return;
    }

    console.log('✅ Sesión iniciada correctamente');
    console.log('User ID:', authData.user.id);

    console.log('\n2. Llamando al Edge Function...');
    const { data, error } = await supabase.functions.invoke('salonier-assistant', {
      body: {
        task: 'diagnose_image',
        payload: {
          imageBase64: TEST_IMAGE,
        },
      },
    });

    if (error) {
      console.error('❌ Error del Edge Function:', error);
      return;
    }

    console.log('\n3. Respuesta del Edge Function:');
    console.log(JSON.stringify(data, null, 2));

    if (data.success && data.data) {
      console.log('\n✅ Análisis completado exitosamente');
      console.log('Campos detectados:', Object.keys(data.data));
    } else if (data.success && !data.data) {
      console.log('\n⚠️  El Edge Function devolvió success=true pero data=null');
      console.log('Esto indica un problema con la API de OpenAI o el procesamiento interno');
    } else {
      console.log('\n❌ Error en el análisis:', data.error);
    }
  } catch (error) {
    console.error('Error inesperado:', error);
  } finally {
    // Cerrar sesión
    await supabase.auth.signOut();
  }
}

// Ejecutar el test
console.log('🧪 Test de Edge Function con autenticación\n');
console.log('Configuración:');
console.log('- Supabase URL:', SUPABASE_URL);
console.log('- Tamaño de imagen:', (TEST_IMAGE.length * 3) / 4, 'bytes');
console.log('');

testAIAnalysis();
