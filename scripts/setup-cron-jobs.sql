-- Script para configurar jobs de limpieza automática con pg_cron
-- Ejecutar en Supabase SQL Editor después de habilitar pg_cron

-- 1. Job para limpiar cache AI expirado (diario a las 2 AM UTC)
SELECT cron.schedule(
  'cleanup-expired-ai-cache',
  '0 2 * * *',
  'SELECT public.cleanup_expired_ai_cache();'
);

-- 2. Crear función mejorada para limpiar fotos temporales
CREATE OR REPLACE FUNCTION public.delete_old_temp_photos()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, storage
AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  -- Eliminar archivos de más de 7 días del bucket temp-photos
  WITH old_files AS (
    SELECT name 
    FROM storage.objects 
    WHERE bucket_id = 'temp-photos' 
    AND created_at < NOW() - INTERVAL '7 days'
  )
  DELETE FROM storage.objects
  WHERE bucket_id = 'temp-photos'
  AND name IN (SELECT name FROM old_files);
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RAISE LOG 'Deleted % old temporary photos', deleted_count;
END;
$$;

-- 3. Job para limpiar fotos temporales (diario a las 3 AM UTC)
SELECT cron.schedule(
  'cleanup-temp-photos',
  '0 3 * * *',
  'SELECT public.delete_old_temp_photos();'
);

-- 4. Verificar que los jobs se crearon correctamente
SELECT 
  jobname,
  schedule,
  command,
  nodename,
  nodeport,
  database,
  username,
  active
FROM cron.job
ORDER BY jobname;