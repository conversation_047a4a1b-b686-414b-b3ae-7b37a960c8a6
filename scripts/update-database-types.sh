#!/bin/bash

# <PERSON>ript to update TypeScript types from Supabase database schema

echo "🔄 Updating database types from Supabase..."
echo ""

# Check if SUPABASE_URL and SUPABASE_ANON_KEY are set
if [ -z "$SUPABASE_URL" ] || [ -z "$SUPABASE_ANON_KEY" ]; then
    echo "❌ Error: SUPABASE_URL or SUPABASE_ANON_KEY not set"
    echo "Please run: source .env.local"
    exit 1
fi

# Generate TypeScript types
echo "📝 Generating TypeScript types..."
npx supabase gen types typescript --project-id ajsamgugqfbttkrlgvbr > types/database.ts

if [ $? -eq 0 ]; then
    echo "✅ Database types updated successfully!"
    echo "📁 Updated file: types/database.ts"
    echo ""
    echo "Note: The types have been regenerated to match the current database schema."
else
    echo "❌ Failed to generate types"
    exit 1
fi