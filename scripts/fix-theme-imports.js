#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing theme imports...\n');

const themeProperties = ['shadows', 'radius', 'spacing', 'typography', 'animations', 'zIndex'];

const fixFile = filePath => {
  const content = fs.readFileSync(filePath, 'utf8');
  let updatedContent = content;
  let hasChanges = false;

  // Find which theme properties are used in the file
  const usedProperties = [];
  for (const prop of themeProperties) {
    const usageRegex = new RegExp(`\\b${prop}\\.`, 'g');
    if (usageRegex.test(content)) {
      usedProperties.push(prop);
    }
  }

  if (usedProperties.length === 0) return false;

  // Check if there's already a theme import
  const themeImportRegex = /import\s*{([^}]*)}\s*from\s*['"]@\/constants\/theme['"]/;
  const themeImportMatch = content.match(themeImportRegex);

  if (themeImportMatch) {
    // Update existing import
    const currentImports = themeImportMatch[1]
      .split(',')
      .map(imp => imp.trim())
      .filter(imp => imp);

    const missingImports = usedProperties.filter(prop => !currentImports.includes(prop));

    if (missingImports.length > 0) {
      console.log(`   🔧 Adding ${missingImports.join(', ')} to ${filePath}`);

      const newImports = [...currentImports, ...missingImports].join(', ');
      updatedContent = updatedContent.replace(
        themeImportMatch[0],
        `import { ${newImports} } from '@/constants/theme'`
      );
      hasChanges = true;
    }
  } else {
    // Add new theme import
    console.log(`   🔧 Adding theme import (${usedProperties.join(', ')}) to ${filePath}`);

    // Find the last import statement
    const importLines = content.split('\n').filter(line => line.trim().startsWith('import'));
    if (importLines.length > 0) {
      const lastImportLine = importLines[importLines.length - 1];
      const lastImportIndex = content.lastIndexOf(lastImportLine);
      const afterLastImport = content.indexOf('\n', lastImportIndex) + 1;

      const newImport = `import { ${usedProperties.join(', ')} } from '@/constants/theme';\n`;
      updatedContent =
        updatedContent.slice(0, afterLastImport) +
        newImport +
        updatedContent.slice(afterLastImport);
      hasChanges = true;
    }
  }

  if (hasChanges) {
    fs.writeFileSync(filePath, updatedContent);
    return true;
  }
  return false;
};

const processDirectory = dir => {
  const files = fs.readdirSync(dir, { withFileTypes: true });
  let totalFixed = 0;

  for (const file of files) {
    const filePath = path.join(dir, file.name);

    if (
      file.isDirectory() &&
      !['node_modules', '.git', 'coverage', 'archive'].includes(file.name)
    ) {
      totalFixed += processDirectory(filePath);
    } else if (file.name.endsWith('.tsx') || file.name.endsWith('.ts')) {
      if (fixFile(filePath)) {
        totalFixed++;
      }
    }
  }

  return totalFixed;
};

const fixedFiles = processDirectory('./app') + processDirectory('./components');

console.log(`\n✅ Fixed theme imports in ${fixedFiles} files!`);
console.log('🔄 Run the theme check again to verify fixes.');
