#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 Running diagnostic checks...\n');

// 1. Check for missing Platform imports
console.log('1️⃣ Checking Platform imports...');
const checkPlatformImports = dir => {
  const files = fs.readdirSync(dir, { withFileTypes: true });

  for (const file of files) {
    const filePath = path.join(dir, file.name);

    if (file.isDirectory() && !['node_modules', '.git', 'coverage'].includes(file.name)) {
      checkPlatformImports(filePath);
    } else if (file.name.endsWith('.tsx') || file.name.endsWith('.ts')) {
      const content = fs.readFileSync(filePath, 'utf8');

      if (content.includes('Platform.') && !content.includes('import.*Platform')) {
        console.log(`   ⚠️  Missing Platform import: ${filePath}`);
      }
    }
  }
};

checkPlatformImports('./app');
checkPlatformImports('./components');

// 2. Check for missing default exports in route files
console.log('\n2️⃣ Checking route default exports...');
const routeFiles = [
  'app/client/[id].tsx',
  'app/client/edit/[id].tsx',
  'app/client/new.tsx',
  'app/inventory/new.tsx',
  'app/inventory/edit/[id].tsx',
];

for (const routeFile of routeFiles) {
  if (fs.existsSync(routeFile)) {
    const content = fs.readFileSync(routeFile, 'utf8');
    if (!content.includes('export default')) {
      console.log(`   ❌ Missing default export: ${routeFile}`);
    } else {
      console.log(`   ✅ Default export found: ${routeFile}`);
    }
  }
}

// 3. Check for undefined imports
console.log('\n3️⃣ Checking for potentially missing imports...');
const commonMissingImports = [
  { pattern: /shadows\./, import: 'shadows', file: '@/constants/theme' },
  { pattern: /Platform\./, import: 'Platform', file: 'react-native' },
  { pattern: /Alert\./, import: 'Alert', file: 'react-native' },
];

const checkMissingImports = dir => {
  const files = fs.readdirSync(dir, { withFileTypes: true });

  for (const file of files) {
    const filePath = path.join(dir, file.name);

    if (file.isDirectory() && !['node_modules', '.git', 'coverage'].includes(file.name)) {
      checkMissingImports(filePath);
    } else if (file.name.endsWith('.tsx') || file.name.endsWith('.ts')) {
      const content = fs.readFileSync(filePath, 'utf8');

      for (const check of commonMissingImports) {
        if (check.pattern.test(content)) {
          const importRegex = new RegExp(`import.*${check.import}`);
          if (!importRegex.test(content)) {
            console.log(`   ⚠️  Potentially missing ${check.import} import: ${filePath}`);
          }
        }
      }
    }
  }
};

checkMissingImports('./app');
checkMissingImports('./components');

// 4. Check for require cycles (common cause of uninitialized values)
console.log('\n4️⃣ Checking for potential require cycles...');
console.log('   ℹ️  Use Metro bundler output for detailed cycle analysis');

console.log('\n✅ Diagnostic check completed!');
