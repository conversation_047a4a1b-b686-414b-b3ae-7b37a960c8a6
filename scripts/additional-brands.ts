/**
 * Marcas adicionales importantes para agregar al archivo brands-data.ts
 * Estas son marcas reconocidas internacionalmente que faltan en el listado actual
 */

import { Brand } from '../constants/reference-data/brands-data';

export const additionalImportantBrands: Brand[] = [
  // Marcas Premium Internacionales
  {
    id: 'keune',
    name: 'Keune Professional',
    country: 'Netherlands',
    description: 'Dutch premium professional hair color and care',
    lines: [
      {
        id: 'tinta-color',
        name: 'Tinta Color',
        description: 'Ammonia-free permanent color with silk protein',
        category: 'hair-color',
        isColorLine: true
      },
      {
        id: 'semi-color',
        name: 'Semi Color',
        description: 'Demi-permanent color for gentle coloring',
        category: 'hair-color',
        isColorLine: true
      },
      {
        id: 'bleaching-powder',
        name: 'Bleaching Powder',
        description: 'Professional lightening up to 7 levels',
        category: 'bleaching',
        isColorLine: true
      },
      {
        id: 'care-line',
        name: 'Care Line',
        description: 'Professional hair care and treatment',
        category: 'treatment',
        isColorLine: false
      },
    ],
  },
  
  {
    id: 'davines',
    name: 'Davines Professional',
    country: 'Italy',
    description: 'Italian sustainable professional hair color',
    lines: [
      {
        id: 'mask-vibrachrom',
        name: 'Mask Vibrachrom',
        description: 'Tone-on-tone color mask for vibrant results',
        category: 'hair-color',
        isColorLine: true
      },
      {
        id: 'alchemic',
        name: 'Alchemic',
        description: 'Color-enhancing shampoo and conditioner system',
        category: 'hair-color',
        isColorLine: true
      },
      {
        id: 'oi',
        name: 'OI',
        description: 'Beauty treatment line with Roucou oil',
        category: 'treatment',
        isColorLine: false
      },
    ],
  },

  {
    id: 'inebrya',
    name: 'Inebrya Professional',
    country: 'Italy',
    description: 'Italian professional hair color innovation',
    lines: [
      {
        id: 'color',
        name: 'Inebrya Color',
        description: 'Professional permanent color with keratin',
        category: 'hair-color',
        isColorLine: true
      },
      {
        id: 'bionic',
        name: 'Bionic Color',
        description: 'Ammonia-free color with botanical extracts',
        category: 'hair-color',
        isColorLine: true
      },
      {
        id: 'bleach',
        name: 'Bleach Powder',
        description: 'Professional lightening system',
        category: 'bleaching',
        isColorLine: true
      },
    ],
  },

  // Marcas Americanas Premium
  {
    id: 'paul-mitchell',
    name: 'Paul Mitchell Professional',
    country: 'USA',
    description: 'American professional hair color and styling',
    lines: [
      {
        id: 'the-color',
        name: 'The Color',
        description: 'Permanent color with exclusive Inkworks technology',
        category: 'hair-color',
        isColorLine: true
      },
      {
        id: 'shines',
        name: 'Shines',
        description: 'Demi-permanent color for glossy results',
        category: 'hair-color',
        isColorLine: true
      },
      {
        id: 'lightener',
        name: 'Lightener Plus',
        description: 'Professional bleaching system',
        category: 'bleaching',
        isColorLine: true
      },
    ],
  },

  {
    id: 'kenra',
    name: 'Kenra Professional',
    country: 'USA',
    description: 'American professional color and styling innovation',
    lines: [
      {
        id: 'color-maintenance',
        name: 'Color Maintenance',
        description: 'Permanent color with advanced technology',
        category: 'hair-color',
        isColorLine: true
      },
      {
        id: 'rapid-toner',
        name: 'Rapid Toner',
        description: 'Fast-acting toning system',
        category: 'hair-color',
        isColorLine: true
      },
      {
        id: 'lightener',
        name: 'Lightener',
        description: 'Professional bleaching powder',
        category: 'bleaching',
        isColorLine: true
      },
    ],
  },

  // Marcas Asiáticas
  {
    id: 'milbon',
    name: 'Milbon Professional',
    country: 'Japan',
    description: 'Japanese professional hair color technology',
    lines: [
      {
        id: 'ordeve',
        name: 'Ordeve',
        description: 'Professional color with advanced Japanese technology',
        category: 'hair-color',
        isColorLine: true
      },
      {
        id: 'addicthy',
        name: 'Addicthy',
        description: 'Fashion color line for creative looks',
        category: 'hair-color',
        isColorLine: true
      },
      {
        id: 'deesses',
        name: 'Deesses',
        description: 'Professional hair treatment line',
        category: 'treatment',
        isColorLine: false
      },
    ],
  },

  // Marcas Brasileñas
  {
    id: 'lowell',
    name: 'Lowell Professional',
    country: 'Brazil',
    description: 'Brazilian professional hair color and care',
    lines: [
      {
        id: 'color-art',
        name: 'Color Art',
        description: 'Professional permanent color system',
        category: 'hair-color',
        isColorLine: true
      },
      {
        id: 'complex-care',
        name: 'Complex Care',
        description: 'Professional hair treatment line',
        category: 'treatment',
        isColorLine: false
      },
    ],
  },

  {
    id: 'felps',
    name: 'Felps Professional',
    country: 'Brazil',
    description: 'Brazilian professional hair color innovation',
    lines: [
      {
        id: 'color',
        name: 'Felps Color',
        description: 'Professional permanent color with Brazilian technology',
        category: 'hair-color',
        isColorLine: true
      },
      {
        id: 'omega-zero',
        name: 'Omega Zero',
        description: 'Ammonia-free color system',
        category: 'hair-color',
        isColorLine: true
      },
    ],
  },

  // Marcas Argentinas
  {
    id: 'kativa',
    name: 'Kativa Professional',
    country: 'Argentina',
    description: 'Argentinian professional hair color and care',
    lines: [
      {
        id: 'color-therapy',
        name: 'Color Therapy',
        description: 'Professional color with argan oil',
        category: 'hair-color',
        isColorLine: true
      },
      {
        id: 'keratin',
        name: 'Keratin Treatment',
        description: 'Professional keratin treatment line',
        category: 'treatment',
        isColorLine: false
      },
    ],
  },

  // Marcas Emergentes Europeas
  {
    id: 'tec-italy',
    name: 'TEC Italy',
    country: 'Italy',
    description: 'Italian professional color innovation',
    lines: [
      {
        id: 'designer-color',
        name: 'Designer Color',
        description: 'Professional color with real tonalities and vibrant reflects',
        category: 'hair-color',
        isColorLine: true
      },
      {
        id: 'lumina',
        name: 'Lumina',
        description: 'Professional lightening system',
        category: 'bleaching',
        isColorLine: true
      },
    ],
  },

  // Marcas Especializadas en Rubios
  {
    id: 'fanola',
    name: 'Fanola Professional',
    country: 'Italy',
    description: 'Italian professional hair color, blonde specialist',
    lines: [
      {
        id: 'color',
        name: 'Fanola Color',
        description: 'Professional permanent color system',
        category: 'hair-color',
        isColorLine: true
      },
      {
        id: 'bleach',
        name: 'Polvere Decolorante',
        description: 'Professional bleaching powder up to 8 levels',
        category: 'bleaching',
        isColorLine: true
      },
      {
        id: 'no-yellow',
        name: 'No Yellow',
        description: 'Anti-yellow toning system for blondes',
        category: 'hair-color',
        isColorLine: true
      },
    ],
  },
];

// Función para generar el código TypeScript de estas marcas
export function generateAdditionalBrandsCode(): string {
  return additionalImportantBrands.map(brand => {
    const linesCode = brand.lines.map(line => `      {
        id: '${line.id}',
        name: '${line.name}',
        description: '${line.description}',
        category: '${line.category}',
        isColorLine: ${line.isColorLine}
      }`).join(',\n');

    return `  {
    id: '${brand.id}',
    name: '${brand.name}',
    country: '${brand.country}',
    description: '${brand.description}',
    lines: [
${linesCode}
    ],
  }`;
  }).join(',\n');
}

console.log('Marcas adicionales cargadas. Usa generateAdditionalBrandsCode() para obtener el código TypeScript.');
