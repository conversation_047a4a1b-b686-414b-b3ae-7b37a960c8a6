#!/bin/bash

# Script para aplicar la migración de satisfaction_score

echo "======================================"
echo "Aplicar migración satisfaction_score"
echo "======================================"
echo ""
echo "Esta migración agrega la columna satisfaction_score a la tabla services."
echo ""
echo "OPCIÓN 1: Ejecutar manualmente en Supabase Dashboard"
echo "1. Ve a tu proyecto en https://app.supabase.com"
echo "2. Ve a SQL Editor"
echo "3. Copia y pega el contenido de supabase/migrations/015_add_satisfaction_score.sql"
echo "4. Ejecuta el SQL"
echo ""
echo "OPCIÓN 2: Usar Supabase CLI (si está configurado)"
echo "Ejecuta: supabase db push"
echo ""
echo "Después de aplicar la migración:"
echo "1. Regenera los tipos: supabase gen types typescript --local > types/database.ts"
echo "2. Reinicia tu aplicación"
echo ""
echo "El código ya está preparado para usar esta columna."