# Configuración Manual en Dashboard de Supabase

Este documento contiene las tareas pendientes que deben configurarse manualmente en el Dashboard de Supabase.

## 1. Habilitar Protección de Contraseñas Comprometidas

### Pasos:

1. Ir a **Authentication** → **Providers** → **Email**
2. En la sección **Password Security**, activar:
   - ✅ **Enable leaked password protection**
   - ✅ **Check passwords against HaveIBeenPwned**
3. Configurar **Minimum password length**: 8 caracteres (recomendado)
4. Click en **Save**

### Beneficios:

- Previene uso de contraseñas que han sido comprometidas en data breaches
- Mejora significativamente la seguridad de las cuentas
- Cumple con mejores prácticas de seguridad

## 2. Configurar pg_cron para Limpieza Automática

### Pasos:

#### A. Habilitar extensión pg_cron

1. Ir a **Database** → **Extensions**
2. Buscar `pg_cron`
3. Click en **Enable**

#### B. Crear job de limpieza de cache AI

1. Ir a **SQL Editor**
2. Ejecutar:

```sql
-- Programar limpieza diaria de cache expirado a las 2 AM
SELECT cron.schedule(
  'cleanup-expired-ai-cache',
  '0 2 * * *',
  'SELECT public.cleanup_expired_ai_cache();'
);
```

#### C. Crear job de limpieza de fotos temporales

1. En **SQL Editor**, ejecutar:

```sql
-- Función mejorada para limpiar fotos temporales
CREATE OR REPLACE FUNCTION public.delete_old_temp_photos()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, storage
AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  -- Eliminar archivos de más de 7 días del bucket temp-photos
  WITH old_files AS (
    SELECT name
    FROM storage.objects
    WHERE bucket_id = 'temp-photos'
    AND created_at < NOW() - INTERVAL '7 days'
  )
  DELETE FROM storage.objects
  WHERE bucket_id = 'temp-photos'
  AND name IN (SELECT name FROM old_files);

  GET DIAGNOSTICS deleted_count = ROW_COUNT;

  RAISE LOG 'Deleted % old temporary photos', deleted_count;
END;
$$;

-- Programar limpieza diaria a las 3 AM
SELECT cron.schedule(
  'cleanup-temp-photos',
  '0 3 * * *',
  'SELECT public.delete_old_temp_photos();'
);
```

#### D. Verificar jobs programados

```sql
-- Ver todos los jobs de cron
SELECT * FROM cron.job;

-- Ver historial de ejecuciones
SELECT * FROM cron.job_run_details
ORDER BY start_time DESC
LIMIT 20;
```

## 3. Configurar Variables de Entorno para Edge Functions

### Pasos:

1. Ir a **Edge Functions** → **salonier-assistant**
2. Click en **Manage secrets**
3. Verificar/Agregar:
   - `OPENAI_API_KEY`: Tu clave de API de OpenAI
4. Click en **Save**

## 4. Configurar Alertas y Monitoreo (Opcional)

### Alertas de Stock Bajo

1. Ir a **Database** → **Webhooks**
2. Crear nuevo webhook:
   - **Name**: `low-stock-alert`
   - **Table**: `products`
   - **Events**: `UPDATE`
   - **Condition**: `NEW.stock_ml < OLD.stock_ml AND NEW.stock_ml <= NEW.minimum_stock_ml`
   - **URL**: Tu endpoint de notificaciones

### Monitoreo de Performance

1. Ir a **Reports** → **Query Performance**
2. Revisar queries lentas
3. Considerar crear índices adicionales según uso real

## 5. Configurar Backups Automáticos

### Pasos:

1. Ir a **Settings** → **Backups**
2. Configurar:
   - **Frequency**: Daily
   - **Retention**: 30 days
   - **Time**: Durante horas de baja actividad
3. Activar **Point-in-time Recovery** (si está disponible en tu plan)

## 6. Revisar Límites y Quotas

### Verificar:

1. **Database** → **Usage**:
   - Tamaño de base de datos
   - Conexiones concurrentes
   - Compute hours

2. **Storage** → **Usage**:
   - Espacio usado en buckets
   - Bandwidth mensual

3. **Edge Functions** → **Usage**:
   - Invocaciones
   - Tiempo de ejecución

### Ajustar según necesidad:

- Considerar upgrade de plan si se acercan límites
- Optimizar queries pesadas
- Implementar paginación donde sea necesario

## 7. Configuración de Seguridad Adicional

### A. Rate Limiting

1. Ir a **Authentication** → **Rate Limits**
2. Configurar límites apropiados para:
   - Sign ups por IP
   - Sign ins por IP
   - Password reset requests

### B. Allowed Redirects

1. En **Authentication** → **URL Configuration**
2. Agregar URLs permitidas para redirección post-login

### C. JWT Expiry

1. En **Authentication** → **JWT Settings**
2. Configurar tiempos de expiración apropiados

## Verificación Final

### Checklist:

- [ ] Protección de contraseñas habilitada
- [ ] pg_cron configurado con jobs de limpieza
- [ ] OPENAI_API_KEY configurada en Edge Functions
- [ ] Alertas configuradas (opcional)
- [ ] Backups automáticos activos
- [ ] Límites revisados y adecuados
- [ ] Configuración de seguridad completa

## Notas Importantes

1. **pg_cron**: Los jobs se ejecutan en timezone UTC por defecto
2. **Limpieza**: Ajustar períodos de retención según necesidades
3. **Monitoreo**: Revisar logs periódicamente para detectar problemas
4. **Costos**: Algunas funciones pueden incurrir en costos adicionales

---

Fecha de creación: 2025-01-11
Última actualización: 2025-01-11
