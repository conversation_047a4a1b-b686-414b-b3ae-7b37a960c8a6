#!/bin/bash

# Test Chat Assistant with Image
echo "🧪 Testing Chat Assistant with base64 image..."

# URLs
SUPABASE_URL="https://ajsamgugqfbttkrlgvbr.supabase.co"
ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqc2FtZ3VncWZidHRrcmxndmJyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxNzYwOTcsImV4cCI6MjA2Nzc1MjA5N30.r6IC-i_GYrSHvS2tKudyGONrhX8-NlyNRl1aHo_AToY"

# Create a small test image (1x1 red pixel)
BASE64_IMAGE="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg=="

# Payload with image
PAYLOAD=$(cat <<EOF
{
  "conversationId": "test-image-123",
  "message": "¿Qué ves en esta imagen?",
  "salonId": "test-salon",
  "userId": "test-user",
  "attachments": [
    {
      "type": "image",
      "url": "$BASE64_IMAGE",
      "mimeType": "image/png"
    }
  ]
}
EOF
)

echo "📤 Sending message with image..."
echo ""

# Make request
RESPONSE=$(curl -s -X POST \
  "${SUPABASE_URL}/functions/v1/chat-public" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${ANON_KEY}" \
  -d "$PAYLOAD")

# Show response
echo "📥 Response:"
echo "$RESPONSE" | jq '.' 2>/dev/null || echo "$RESPONSE"

if echo "$RESPONSE" | grep -q '"content"'; then
  echo ""
  echo "✅ Chat Assistant with image analysis is working!"
else
  echo ""
  echo "❌ Something went wrong"
fi