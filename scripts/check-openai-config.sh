#!/bin/bash

# <PERSON><PERSON>t to check OpenAI configuration in Supabase Edge Functions

echo "🔍 Checking OpenAI configuration..."
echo ""

# Check if SUPABASE_URL and SUPABASE_ANON_KEY are set
if [ -z "$SUPABASE_URL" ] || [ -z "$SUPABASE_ANON_KEY" ]; then
    echo "❌ Error: SUPABASE_URL or SUPABASE_ANON_KEY not set"
    echo "Please run: source .env.local"
    exit 1
fi

echo "📝 Instructions to check OpenAI API Key configuration:"
echo ""
echo "1. Go to Supabase Dashboard: https://supabase.com/dashboard/project/ajsamgugqfbttkrlgvbr"
echo "2. Navigate to Settings → Edge Functions"
echo "3. Click on 'Secrets' tab"
echo "4. Check if OPENAI_API_KEY is configured"
echo ""
echo "If not configured:"
echo "1. Click 'Add new secret'"
echo "2. Name: OPENAI_API_KEY"
echo "3. Value: Your OpenAI API key (starts with sk-...)"
echo "4. Click 'Save'"
echo ""
echo "After configuring:"
echo "1. Redeploy the Edge Function:"
echo "   npx supabase functions deploy salonier-assistant"
echo ""
echo "Alternative: Set in local .env for testing:"
echo "   export OPENAI_API_KEY=sk-your-key-here"