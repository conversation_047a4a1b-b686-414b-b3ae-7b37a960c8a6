#!/bin/bash

# Script mejorado de optimización de imágenes para Salonier
# Usa técnicas avanzadas de compresión PNG con sips

echo "🎨 Iniciando optimización de imágenes v2..."

# Directorio de imágenes
IMG_DIR="assets/images"
BACKUP_DIR="$IMG_DIR/backup"

# Función para comprimir PNG usando re-encoding
compress_png() {
    local input=$1
    local filename=$(basename "$input")
    local temp_file="$IMG_DIR/temp_$filename"
    
    echo "  🔧 Procesando $filename..."
    
    # Paso 1: Re-codificar la imagen para optimizar la compresión
    # Esto elimina metadatos innecesarios y optimiza la estructura del PNG
    sips -s format png -s formatOptions best "$input" --out "$temp_file" 2>/dev/null
    
    # Paso 2: Mover el archivo optimizado sobre el original
    if [ -f "$temp_file" ]; then
        mv "$temp_file" "$input"
    fi
}

# Optimizar adaptive-icon.png (redimensionar primero si es necesario)
echo "📱 Optimizando adaptive-icon.png..."
if [ -f "$BACKUP_DIR/adaptive-icon.png" ]; then
    # Redimensionar a 1024x1024 desde el backup
    sips -z 1024 1024 "$BACKUP_DIR/adaptive-icon.png" --out "$IMG_DIR/adaptive-icon.png" >/dev/null 2>&1
fi
compress_png "$IMG_DIR/adaptive-icon.png"

# Optimizar icon.png
echo "📱 Optimizando icon.png..."
if [ -f "$BACKUP_DIR/icon.png" ]; then
    cp "$BACKUP_DIR/icon.png" "$IMG_DIR/icon.png"
fi
compress_png "$IMG_DIR/icon.png"

# Optimizar splash-icon.png
echo "🎯 Optimizando splash-icon.png..."
if [ -f "$BACKUP_DIR/splash-icon.png" ]; then
    cp "$BACKUP_DIR/splash-icon.png" "$IMG_DIR/splash-icon.png"
fi
compress_png "$IMG_DIR/splash-icon.png"

# favicon.png es muy pequeño, no necesita optimización
echo "🔍 Manteniendo favicon.png (ya optimizado)..."

# Limpiar archivos temporales
rm -f "$IMG_DIR"/temp_*.png

# Mostrar resultados
echo ""
echo "✅ Optimización completada!"
echo ""
echo "📊 Comparación de tamaños:"
echo ""
echo "ANTES (Backup):"
ls -lh "$BACKUP_DIR"/*.png | awk '{print $9 ": " $5}'
echo "Total: $(du -h "$BACKUP_DIR" | awk '{print $1}')"
echo ""
echo "DESPUÉS (Optimizado):"
ls -lh "$IMG_DIR"/*.png | grep -v backup | awk '{print $9 ": " $5}'
echo "Total: $(du -h "$IMG_DIR" | grep -v backup | awk '{print $1}')"

# Calcular porcentaje de reducción
before_size=$(du -k "$BACKUP_DIR" | awk '{print $1}')
after_size=$(du -k "$IMG_DIR"/*.png | grep -v backup | awk '{sum+=$1} END {print sum}')
reduction=$((100 - (after_size * 100 / before_size)))
echo ""
echo "📉 Reducción: ${reduction}% del tamaño original"