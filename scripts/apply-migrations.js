const { createClient } = require('@supabase/supabase-js');
const fs = require('fs').promises;
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey =
  process.env.SUPABASE_SERVICE_KEY || process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

// Create Supabase admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function applyMigration(filePath, name) {
  try {
    console.log(`\n📄 Applying migration: ${name}`);
    const sql = await fs.readFile(filePath, 'utf8');

    // Split by semicolons but be careful with functions
    const statements = sql
      .split(/;\s*$/gm)
      .filter(stmt => stmt.trim())
      .map(stmt => stmt.trim() + ';');

    for (const statement of statements) {
      if (statement.trim()) {
        const { error } = await supabase
          .rpc('exec_sql', {
            sql_query: statement,
          })
          .single();

        if (error) {
          // If exec_sql doesn't exist, try direct execution
          console.error(`Error in statement: ${error.message}`);
          console.log('Statement that failed:', statement.substring(0, 100) + '...');
        }
      }
    }

    console.log(`✅ Migration ${name} applied successfully`);
  } catch (error) {
    console.error(`❌ Error applying migration ${name}:`, error.message);
    throw error;
  }
}

async function main() {
  const migrationsDir = path.join(__dirname, '..', 'supabase', 'migrations');

  try {
    // Get all migration files
    const files = await fs.readdir(migrationsDir);
    const sqlFiles = files.filter(f => f.endsWith('.sql')).sort(); // Ensure they run in order

    console.log(`Found ${sqlFiles.length} migration files`);

    // Apply each migration
    for (const file of sqlFiles) {
      await applyMigration(path.join(migrationsDir, file), file);
    }

    console.log('\n✅ All migrations applied successfully!');

    // Verify tables were created
    console.log('\n📊 Verifying tables...');
    const { error } = await supabase.from('salons').select('id').limit(1);

    if (error && error.code === '42P01') {
      console.error(
        '❌ Tables were not created. You may need to apply migrations manually via the Supabase dashboard.'
      );
    } else if (!error) {
      console.log('✅ Tables verified successfully!');
    }
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Alternative: Instructions for manual application
console.log(`
🔧 Alternative: Apply migrations manually

If this script doesn't work, you can apply the migrations manually:

1. Go to https://supabase.com/dashboard/project/${supabaseUrl.match(/https:\/\/([^.]+)/)[1]}
2. Navigate to the SQL Editor
3. Copy and paste each migration file in order:
   - 001_initial_schema.sql
   - 002_row_level_security.sql
   - 003_auth_triggers.sql
   - 004_storage_buckets.sql
4. Execute each one and verify success
`);

main();
