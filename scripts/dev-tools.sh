#!/bin/bash

# Script de herramientas de desarrollo para Rork Salonier Copilot
# Facilita tareas comunes de desarrollo y mantenimiento

set -e

# Colores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para imprimir con color
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Verificar que existen las variables de entorno
check_env() {
    if [ -z "$EXPO_PUBLIC_SUPABASE_URL" ] || [ -z "$EXPO_PUBLIC_SUPABASE_ANON_KEY" ]; then
        print_error "Variables de entorno de Supabase no configuradas"
        print_info "Por favor, configura tu archivo .env.local"
        exit 1
    fi
    print_success "Variables de entorno verificadas"
}

# Menú principal
show_menu() {
    echo -e "\n${BLUE}=== Rork Salonier Copilot - Dev Tools ===${NC}\n"
    echo "1) Verificar salud del sistema"
    echo "2) Ejecutar validación de optimizaciones"
    echo "3) Limpiar cache de desarrollo"
    echo "4) Generar reporte de performance"
    echo "5) Sincronizar tipos de TypeScript"
    echo "6) Backup local de migraciones"
    echo "7) Verificar dependencias"
    echo "8) Ejecutar tests"
    echo "9) Build para producción"
    echo "0) Salir"
    echo -e "\n"
}

# 1. Verificar salud del sistema
check_system_health() {
    print_info "Verificando salud del sistema..."
    
    # Verificar conexión a Supabase
    if curl -s "$EXPO_PUBLIC_SUPABASE_URL/rest/v1/" > /dev/null; then
        print_success "Conexión a Supabase OK"
    else
        print_error "No se puede conectar a Supabase"
    fi
    
    # Verificar dependencias
    print_info "Verificando dependencias..."
    npm list --depth=0 > /dev/null 2>&1 || {
        print_warning "Algunas dependencias pueden estar desactualizadas"
        print_info "Ejecuta 'npm install' para actualizar"
    }
    
    # Verificar espacio en disco
    DISK_USAGE=$(df -h . | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$DISK_USAGE" -gt 90 ]; then
        print_warning "Espacio en disco bajo: ${DISK_USAGE}%"
    else
        print_success "Espacio en disco OK: ${DISK_USAGE}%"
    fi
}

# 2. Ejecutar validación de optimizaciones
run_optimization_validation() {
    print_info "Ejecutando validación de optimizaciones..."
    
    if [ -f "scripts/validate-optimizations.sql" ]; then
        print_info "Ejecuta el siguiente comando en Supabase SQL Editor:"
        echo -e "${YELLOW}"
        cat scripts/validate-optimizations.sql | head -20
        echo -e "${NC}"
        print_info "... (archivo completo en scripts/validate-optimizations.sql)"
    else
        print_error "Archivo de validación no encontrado"
    fi
}

# 3. Limpiar cache de desarrollo
clean_dev_cache() {
    print_info "Limpiando cache de desarrollo..."
    
    # Limpiar cache de Metro
    if [ -d ".metro" ]; then
        rm -rf .metro
        print_success "Cache de Metro limpiado"
    fi
    
    # Limpiar cache de Expo
    if command -v expo &> /dev/null; then
        expo start -c --no-dev --minify > /dev/null 2>&1 &
        EXPO_PID=$!
        sleep 5
        kill $EXPO_PID 2>/dev/null || true
        print_success "Cache de Expo limpiado"
    fi
    
    # Limpiar node_modules y reinstalar
    read -p "¿Limpiar node_modules y reinstalar? (y/n) " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -rf node_modules package-lock.json
        npm install
        print_success "Dependencias reinstaladas"
    fi
}

# 4. Generar reporte de performance
generate_performance_report() {
    print_info "Generando reporte de performance..."
    
    REPORT_FILE="reports/performance-$(date +%Y%m%d-%H%M%S).md"
    mkdir -p reports
    
    cat > "$REPORT_FILE" << EOF
# Reporte de Performance - $(date +"%Y-%m-%d %H:%M:%S")

## Información del Sistema

- **Plataforma**: $(uname -s)
- **Node Version**: $(node --version)
- **NPM Version**: $(npm --version)

## Análisis de Bundle

\`\`\`
$(npx expo export --dump-sourcemap 2>&1 | grep -E "bundle|chunk" || echo "No hay información de bundle disponible")
\`\`\`

## Dependencias

### Producción
\`\`\`
$(npm list --prod --depth=0 2>/dev/null || echo "Error listando dependencias")
\`\`\`

### Desarrollo
\`\`\`
$(npm list --dev --depth=0 2>/dev/null || echo "Error listando dependencias de desarrollo")
\`\`\`

## Recomendaciones

1. Revisar el tamaño del bundle
2. Actualizar dependencias desactualizadas
3. Ejecutar validación de optimizaciones en Supabase

---
Generado automáticamente por dev-tools.sh
EOF

    print_success "Reporte generado en: $REPORT_FILE"
}

# 5. Sincronizar tipos de TypeScript
sync_typescript_types() {
    print_info "Sincronizando tipos de TypeScript desde Supabase..."
    
    # Generar tipos usando Supabase CLI
    if command -v supabase &> /dev/null; then
        supabase gen types typescript --project-id "${EXPO_PUBLIC_SUPABASE_URL##*/}" > types/database.generated.ts
        print_success "Tipos generados en types/database.generated.ts"
    else
        print_warning "Supabase CLI no instalado"
        print_info "Instala con: npm install -g supabase"
    fi
}

# 6. Backup local de migraciones
backup_migrations() {
    print_info "Creando backup de migraciones..."
    
    BACKUP_DIR="backups/migrations-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    if [ -d "supabase/migrations" ]; then
        cp -r supabase/migrations/* "$BACKUP_DIR/"
        print_success "Backup creado en: $BACKUP_DIR"
        
        # Crear archivo de información
        cat > "$BACKUP_DIR/README.md" << EOF
# Backup de Migraciones

- **Fecha**: $(date +"%Y-%m-%d %H:%M:%S")
- **Total de archivos**: $(ls -1 supabase/migrations/*.sql 2>/dev/null | wc -l)

## Archivos incluidos:
$(ls -1 supabase/migrations/*.sql 2>/dev/null || echo "No hay migraciones")
EOF
    else
        print_error "Directorio de migraciones no encontrado"
    fi
}

# 7. Verificar dependencias
check_dependencies() {
    print_info "Verificando dependencias..."
    
    # Verificar vulnerabilidades
    print_info "Buscando vulnerabilidades..."
    npm audit --production 2>/dev/null || print_warning "Algunas vulnerabilidades encontradas"
    
    # Verificar actualizaciones
    print_info "Verificando actualizaciones disponibles..."
    npx npm-check-updates -u --target minor 2>/dev/null || print_info "Usa 'npx npm-check-updates' para más detalles"
}

# 8. Ejecutar tests
run_tests() {
    print_info "Ejecutando tests..."
    
    if [ -f "package.json" ] && grep -q "\"test\":" package.json; then
        npm test
    else
        print_warning "No hay script de test configurado"
        print_info "Agrega un script de test en package.json"
    fi
}

# 9. Build para producción
build_production() {
    print_info "Preparando build de producción..."
    
    # Verificar configuración
    check_env
    
    # Seleccionar plataforma
    echo "Selecciona plataforma:"
    echo "1) iOS"
    echo "2) Android"
    echo "3) Ambas"
    read -p "Opción: " platform_choice
    
    case $platform_choice in
        1)
            print_info "Building para iOS..."
            eas build --platform ios --profile production
            ;;
        2)
            print_info "Building para Android..."
            eas build --platform android --profile production
            ;;
        3)
            print_info "Building para iOS y Android..."
            eas build --platform all --profile production
            ;;
        *)
            print_error "Opción inválida"
            ;;
    esac
}

# Loop principal
main() {
    check_env
    
    while true; do
        show_menu
        read -p "Selecciona una opción: " choice
        
        case $choice in
            1) check_system_health ;;
            2) run_optimization_validation ;;
            3) clean_dev_cache ;;
            4) generate_performance_report ;;
            5) sync_typescript_types ;;
            6) backup_migrations ;;
            7) check_dependencies ;;
            8) run_tests ;;
            9) build_production ;;
            0) 
                print_info "¡Hasta luego!"
                exit 0
                ;;
            *)
                print_error "Opción inválida"
                ;;
        esac
        
        echo -e "\nPresiona Enter para continuar..."
        read
    done
}

# Ejecutar script
main