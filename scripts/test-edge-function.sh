#!/bin/bash
# Script para probar la Edge Function directamente

echo "🧪 Probando Edge Function salonier-assistant..."
echo ""

# Verificar variables de entorno
if [ -z "$EXPO_PUBLIC_SUPABASE_URL" ]; then
    echo "❌ Error: EXPO_PUBLIC_SUPABASE_URL no está configurada"
    exit 1
fi

if [ -z "$EXPO_PUBLIC_SUPABASE_ANON_KEY" ]; then
    echo "❌ Error: EXPO_PUBLIC_SUPABASE_ANON_KEY no está configurada"
    exit 1
fi

# URL de la Edge Function
FUNCTION_URL="${EXPO_PUBLIC_SUPABASE_URL}/functions/v1/salonier-assistant"

# Crear un payload de prueba simple
PAYLOAD='{
  "task": "diagnose_image",
  "payload": {
    "imageBase64": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
  }
}'

echo "📡 Enviando request a: $FUNCTION_URL"
echo ""

# Hacer la petición
RESPONSE=$(curl -s -w "\n%{http_code}" \
  -X POST \
  -H "Authorization: Bearer $EXPO_PUBLIC_SUPABASE_ANON_KEY" \
  -H "Content-Type: application/json" \
  -H "apikey: $EXPO_PUBLIC_SUPABASE_ANON_KEY" \
  -d "$PAYLOAD" \
  "$FUNCTION_URL")

# Separar respuesta y código HTTP
HTTP_CODE=$(echo "$RESPONSE" | tail -n 1)
BODY=$(echo "$RESPONSE" | sed '$d')

echo "📥 Código HTTP: $HTTP_CODE"
echo "📄 Respuesta:"
echo "$BODY" | jq . 2>/dev/null || echo "$BODY"
echo ""

# Verificar resultado
if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ Edge Function respondió correctamente"
else
    echo "❌ Edge Function devolvió error"
    echo ""
    echo "Posibles causas:"
    echo "- 401: Token de autenticación inválido"
    echo "- 403: Sin permisos para ejecutar la función"
    echo "- 500: Error en la función (revisar logs en Supabase)"
    echo "- 503: Función no desplegada o no disponible"
fi