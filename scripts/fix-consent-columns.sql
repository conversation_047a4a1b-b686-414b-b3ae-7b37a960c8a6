-- <PERSON>ript rápido para ejecutar en Supabase SQL Editor
-- Soluciona el error: "Could not find the 'consent_data' column"

-- Ejecutar este script en el SQL Editor de Supabase

-- 1. Agregar columnas faltantes
ALTER TABLE client_consents 
ADD COLUMN IF NOT EXISTS consent_data JSONB,
ADD COLUMN IF NOT EXISTS safety_checklist TEXT[],
ADD COLUMN IF NOT EXISTS skip_safety BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS user_agent TEXT;

-- 2. Crear índice para mejor performance
CREATE INDEX IF NOT EXISTS idx_client_consents_consent_data ON client_consents USING GIN (consent_data);

-- 3. Verificar que las columnas se crearon
SELECT 
  column_name, 
  data_type, 
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'client_consents' 
  AND column_name IN ('consent_data', 'safety_checklist', 'skip_safety', 'user_agent')
ORDER BY column_name;