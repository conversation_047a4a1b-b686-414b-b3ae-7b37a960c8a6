#!/bin/bash
# Script para desplegar Edge Function y ver logs

echo "🚀 Desplegando Edge Function actualizada..."

# Verificar que estamos en el directorio correcto
if [ ! -d "supabase/functions/salonier-assistant" ]; then
    echo "❌ Error: No se encontró el directorio de la función"
    echo "Asegúrate de ejecutar este script desde la raíz del proyecto"
    exit 1
fi

# Mostrar versión actual
echo "📋 Información de la función:"
echo "  - Nombre: salonier-assistant"
echo "  - Directorio: supabase/functions/salonier-assistant"
echo "  - Prompt actualizado: v5 (campos completos)"
echo ""

# Desplegar la función
echo "📦 Desplegando función..."
supabase functions deploy salonier-assistant

if [ $? -eq 0 ]; then
    echo "✅ Edge Function desplegada exitosamente"
    echo ""
    echo "🔍 Campos que ahora devuelve la IA:"
    echo "  - Análisis de color: depth, tone, undertone, state, unwantedTone"
    echo "  - Aná<PERSON>is físico: porosity, elasticity, resistance, damage"
    echo "  - Estado cutícula: cuticleState"
    echo "  - Canas: grayPercentage, grayType, grayPattern"
    echo "  - Acumulación: pigmentAccumulation"
    echo "  - Bandas: demarkationBands"
    echo ""
    echo "📋 Mostrando logs en tiempo real..."
    echo "Presiona Ctrl+C para salir"
    echo ""
    
    # Mostrar logs
    supabase functions logs salonier-assistant --follow
else
    echo "❌ Error al desplegar la función"
    exit 1
fi