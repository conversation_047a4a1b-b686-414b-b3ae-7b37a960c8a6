#!/bin/bash

# Script para probar la generación de fórmulas con configuración regional

echo "🧪 Testing regional formula generation..."
echo ""

# Test 1: Spanish/Metric configuration (default)
echo "1️⃣ Testing Spanish/Metric configuration..."
curl -i --location --request POST \
  'http://localhost:54321/functions/v1/salonier-assistant' \
  --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
  --header 'Content-Type: application/json' \
  --data '{
    "task": "generate_formula",
    "payload": {
      "diagnosis": {
        "averageLevel": 6,
        "tone": "<PERSON>bri<PERSON>",
        "state": "Procesado"
      },
      "desiredResult": {
        "level": 8,
        "tone": "Rubio ceniza"
      },
      "brand": "L'\''Oréal",
      "line": "Majirel",
      "clientHistory": "Primera vez",
      "regionalConfig": {
        "volumeUnit": "ml",
        "weightUnit": "g",
        "developerTerminology": "oxidante",
        "colorTerminology": "tinte",
        "maxDeveloperVolume": 40,
        "currencySymbol": "€",
        "measurementSystem": "metric",
        "decimalSeparator": ",",
        "language": "es"
      }
    }
  }'

echo ""
echo ""

# Test 2: English/Imperial configuration
echo "2️⃣ Testing English/Imperial configuration..."
curl -i --location --request POST \
  'http://localhost:54321/functions/v1/salonier-assistant' \
  --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
  --header 'Content-Type: application/json' \
  --data '{
    "task": "generate_formula",
    "payload": {
      "diagnosis": {
        "averageLevel": 6,
        "tone": "Copper",
        "state": "Processed"
      },
      "desiredResult": {
        "level": 8,
        "tone": "Ash blonde"
      },
      "brand": "Redken",
      "line": "Shades EQ",
      "clientHistory": "First time",
      "regionalConfig": {
        "volumeUnit": "fl oz",
        "weightUnit": "oz",
        "developerTerminology": "developer",
        "colorTerminology": "color",
        "maxDeveloperVolume": 40,
        "currencySymbol": "$",
        "measurementSystem": "imperial",
        "decimalSeparator": ".",
        "language": "en"
      }
    }
  }'

echo ""
echo ""

# Test 3: French configuration with volume restrictions
echo "3️⃣ Testing French configuration with 30vol limit..."
curl -i --location --request POST \
  'http://localhost:54321/functions/v1/salonier-assistant' \
  --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
  --header 'Content-Type: application/json' \
  --data '{
    "task": "generate_formula",
    "payload": {
      "diagnosis": {
        "averageLevel": 6,
        "tone": "Cuivré",
        "state": "Traité"
      },
      "desiredResult": {
        "level": 8,
        "tone": "Blond cendré"
      },
      "brand": "L'\''Oréal",
      "line": "Majirel",
      "clientHistory": "Première fois",
      "regionalConfig": {
        "volumeUnit": "ml",
        "weightUnit": "g",
        "developerTerminology": "révélateur",
        "colorTerminology": "couleur",
        "maxDeveloperVolume": 30,
        "currencySymbol": "€",
        "measurementSystem": "metric",
        "decimalSeparator": ",",
        "language": "fr"
      }
    }
  }'

echo ""
echo "✅ Regional formula tests completed!"