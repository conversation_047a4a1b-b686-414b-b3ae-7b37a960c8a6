#!/usr/bin/env node

/**
 * Comprehensive diagnostic test for the salonier-assistant <PERSON> Function
 * This test validates the image analysis pipeline end-to-end
 */

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const SUPABASE_URL = 'https://ajsamgugqfbttkrlgvbr.supabase.co';
const ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqc2FtZ3VncWZidHRrcmxndmJyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxNzYwOTcsImV4cCI6MjA2Nzc1MjA5N30.r6IC-i_GYrSHvS2tKudyGONrhX8-NlyNRl1aHo_AToY';

// Sample base64 image (small test image)
const SAMPLE_IMAGE_BASE64 = '/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCABAAEADASIAAhEBAxEB/8QAGwAAAgMBAQEAAAAAAAAAAAAABQYDBAcCAQD/xAA1EAACAQMCBAMGBQMFAAAAAAABAgMABBEFIQUTMUETUWEHFCJxgZEVMkKhsRYjwVJi0eHw/8QAGAEAAwEBAAAAAAAAAAAAAAAAAQIDAAT/xAAYEQEBAQEBAAAAAAAAAAAAAAAAARECEv/aAAwDAQACEQMRAD8AWuJuLtS1SVljkMUQ6KpqG71m4nt1E0zOQNt6rPaFF8WRRt51yITIMRjIFcsXCtK0gKOwI9Kht7e6vJAIEdz5AZonLBPGMSBlPmRTZ7M7y00y1me4ZQ0jdWO2KPM2gVlocdrP/qB23zRyOwXA2A9avHW9AmPM0iFvMjcVZtr7RZyBHMufImqZBsRa1zjTq9tMrqzD4c9q9l0KW9uRFGjHfc52FN0NnprLnEf71Laj3ef4VUfL1oCXtf4JltuVHLOD0BOwpWu9Ilhm8PkYnPQCtcju4xqxknuVWJVx8RxmljiHWIJbkeDMrqBgFTkE0Jo6L9QoWldoMHkY9s0Zu+DbuS1Mr8sIHVnbb9qW2aYgZLZPUb0/8FcXNPI2m6ukkkBHKJG6oaMqbClxHY6tBZCztsSzyNgOwwFXG+fvUfAejrcakGlJWO0HOR5v2H8/amnXNP8AeNZjuLZ1khKjl36YH+c1HbQvbMByBX6EL51XdYFx2K4BGDnpXVrKgABbB+dSxBWAqteo6EgI3yNJUMSajeR6bp8lzO4CIMDfqaS5rgzvJcyHLyMWJ+Zqpr2qy6nKkJXw4U/SepPmao+PygZJ+tdcc9PICcZ/3dfl8qgZRnLHA9KjEjSZOaqdyPQDqaIkfr2r0MD03+tQSZYjmqPmIPetGP/Z';

// Test configurations
const TEST_CASES = [
  {
    name: 'Base64 Image Analysis',
    task: 'diagnose_image',
    payload: {
      imageBase64: SAMPLE_IMAGE_BASE64
    }
  },
  {
    name: 'Empty Payload (Should Fail)',
    task: 'diagnose_image',
    payload: {}
  },
  {
    name: 'Invalid Base64 (Should Fail)',
    task: 'diagnose_image',
    payload: {
      imageBase64: 'invalid-base64-data'
    }
  }
];

async function runTest(testCase, authToken) {
  console.log(`\n🧪 Testing: ${testCase.name}`);
  console.log('─'.repeat(50));

  const testPayload = {
    task: testCase.task,
    payload: testCase.payload
  };

  const curlCommand = `curl -X POST "${SUPABASE_URL}/functions/v1/salonier-assistant" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer ${authToken}" \
    -H "apikey: ${ANON_KEY}" \
    -d '${JSON.stringify(testPayload)}' \
    -w "\n\nHTTP_CODE: %{http_code}\nTIME_TOTAL: %{time_total}s\n" \
    --max-time 60`;

  return new Promise((resolve) => {
    exec(curlCommand, { maxBuffer: 1024 * 1024 * 10 }, (error, stdout, stderr) => {
      let result = {
        testName: testCase.name,
        success: false,
        httpCode: null,
        timeTotal: null,
        response: null,
        error: null,
        diagnosticLogs: []
      };

      if (error) {
        result.error = error.message;
        console.log('❌ Test failed with error:', error.message);
        resolve(result);
        return;
      }

      // Extract HTTP code and timing
      const httpCodeMatch = stdout.match(/HTTP_CODE: (\d+)/);
      const timeMatch = stdout.match(/TIME_TOTAL: ([\d.]+)s/);
      
      if (httpCodeMatch) result.httpCode = parseInt(httpCodeMatch[1]);
      if (timeMatch) result.timeTotal = parseFloat(timeMatch[1]);

      // Extract JSON response
      try {
        const jsonMatch = stdout.match(/\{.*\}/s);
        if (jsonMatch) {
          result.response = JSON.parse(jsonMatch[0]);
          result.success = result.response.success === true;
        }
      } catch (parseError) {
        result.error = `JSON parse error: ${parseError.message}`;
      }

      // Log results
      console.log(`📊 HTTP Status: ${result.httpCode}`);
      console.log(`⏱️  Response Time: ${result.timeTotal}s`);
      
      if (result.success) {
        console.log('✅ Test PASSED');
        const data = result.response.data;
        if (data) {
          console.log('📋 Analysis Results:');
          console.log(`   - Average Level: ${data.averageLevel}`);
          console.log(`   - Overall Tone: ${data.overallTone}`);
          console.log(`   - Hair Thickness: ${data.hairThickness}`);
          console.log(`   - Hair Density: ${data.hairDensity}`);
          console.log(`   - Overall Condition: ${data.overallCondition}`);
          
          if (data.zoneAnalysis) {
            console.log('🎯 Zone Analysis:');
            ['roots', 'mids', 'ends'].forEach(zone => {
              if (data.zoneAnalysis[zone]) {
                console.log(`   - ${zone}: Level ${data.zoneAnalysis[zone].level}, Tone ${data.zoneAnalysis[zone].tone}`);
              }
            });
          }
        }
      } else {
        console.log('❌ Test FAILED');
        if (result.response?.error) {
          console.log(`   Error: ${result.response.error}`);
        }
      }

      resolve(result);
    });
  });
}

async function main() {
  console.log('🚀 Comprehensive Edge Function Diagnostic Test');
  console.log('='.repeat(60));
  
  // Check for auth token
  const authToken = process.env.AUTH_TOKEN || 'PLACEHOLDER';
  
  if (authToken === 'PLACEHOLDER') {
    console.log('⚠️  No AUTH_TOKEN provided. Some tests may fail.');
    console.log('   Set AUTH_TOKEN environment variable for full testing.');
    console.log('   Example: AUTH_TOKEN="your-jwt-token" node test-edge-function-diagnostic.js\n');
  }

  const results = [];
  
  // Run all test cases
  for (const testCase of TEST_CASES) {
    const result = await runTest(testCase, authToken);
    results.push(result);
    
    // Wait between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Summary
  console.log('\n📊 TEST SUMMARY');
  console.log('='.repeat(60));
  
  const passed = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / results.length) * 100).toFixed(1)}%`);
  
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    console.log(`${status} ${result.testName} - ${result.httpCode} (${result.timeTotal}s)`);
  });
  
  console.log('\n💡 Next Steps:');
  console.log('1. Check Supabase logs for detailed diagnostic output');
  console.log('2. Look for [DIAGNOSTIC] entries in the logs');
  console.log('3. Verify image analysis pipeline is working correctly');
  
  return results;
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Test interrupted by user');
  process.exit(0);
});

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { runTest, TEST_CASES };