# Optimización de Imágenes - Salonier

## Resumen

Script de optimización de imágenes PNG para reducir el tamaño de la aplicación Expo/React Native.

## Resultados

- **Reducción total**: 73% (de 424KB a 116KB)
- **Espacio ah<PERSON>do**: 308KB
- **Calidad visual**: Sin pérdida perceptible

## Uso

```bash
# Ejecutar el script de optimización
./scripts/optimize-images-final.sh
```

## Detalles técnicos

### Herramientas utilizadas

- **sips**: Redimensionamiento de imágenes (nativo macOS)
- **pngquant**: Compresión PNG avanzada con algoritmo de cuantización

### Configuración por imagen

- `adaptive-icon.png`: Redimensionado a 1024x1024, calidad 85-95%
- `icon.png`: Mantenido en 1024x1024, calidad 85-95%
- `splash-icon.png`: Mantenido en 512x512, calidad 85-95%
- `favicon.png`: Tamaño original, calidad 95-100%

### Notas importantes

- Expo NO soporta WebP para iconos y splash screens
- Las imágenes deben ser PNG exactamente cuadradas
- Se mantiene un backup en `assets/images/backup/`

## Instalación de dependencias

```bash
# En macOS con Homebrew
brew install pngquant
```

## Estructura del script

1. Crea backup de imágenes originales
2. Redimensiona si es necesario (usando sips)
3. Comprime con pngquant manteniendo calidad visual
4. Muestra estadísticas de optimización

## Para nuevas imágenes

Al agregar nuevas imágenes al proyecto:

1. Colocarlas en `assets/images/`
2. Ejecutar el script de optimización
3. Verificar la calidad visual resultante
4. Ajustar parámetros de calidad si es necesario
