#!/bin/bash

# Script para hacer un test simple del edge function

echo "🧪 Testing edge function status..."
echo ""

# Get project URL and anon key from .env.local
SUPABASE_URL=$(grep EXPO_PUBLIC_SUPABASE_URL .env.local | cut -d '=' -f2 | tr -d '"')
ANON_KEY=$(grep EXPO_PUBLIC_SUPABASE_ANON_KEY .env.local | cut -d '=' -f2 | tr -d '"')

echo "Using Supabase URL: ${SUPABASE_URL}"
echo ""

# Test with a minimal base64 image (1x1 black pixel)
MINIMAL_IMAGE="iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="

echo "Testing with minimal base64 image..."
curl -i --location --request POST \
  "${SUPABASE_URL}/functions/v1/salonier-assistant" \
  --header "Authorization: Bearer ${ANON_KEY}" \
  --header "Content-Type: application/json" \
  --data '{
    "task": "diagnose_image",
    "payload": {
      "imageBase64": "'$MINIMAL_IMAGE'"
    }
  }'

echo ""
echo ""
echo "✅ Test completed!"
echo ""
echo "Check the response above. Common issues:"
echo "- 401 Unauthorized: Token issue"
echo "- 500 Internal Server Error: Check OpenAI API key"
echo "- success: false with error message: Check the specific error"