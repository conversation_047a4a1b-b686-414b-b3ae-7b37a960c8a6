#!/bin/bash

# Script para depurar el análisis de IA

echo "🔍 Debugging AI analysis edge function..."
echo ""

# Crear una imagen base64 pequeña de prueba (1x1 pixel negro)
TEST_IMAGE="iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="

# Test con imagen base64
echo "Testing with base64 image..."
curl -i --location --request POST \
  'http://localhost:54321/functions/v1/salonier-assistant' \
  --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
  --header 'Content-Type: application/json' \
  --data '{
    "task": "diagnose_image",
    "payload": {
      "imageBase64": "'$TEST_IMAGE'"
    }
  }'

echo ""
echo ""

# Ver logs del edge function
echo "Fetching edge function logs..."
npx supabase functions logs --tail 50

echo ""
echo "✅ Debug completed!"