#!/usr/bin/env npx tsx

import { 
  getBrandsWithFormulableLines,
  getColorLinesByBrandId,
  searchFormulableBrands,
  getBrandById,
  validateBrandLines
} from '../constants/reference-data/brands-data';

console.log('=== PRUEBA DE COMPATIBILIDAD CON COMPONENTES ===\n');

// Test 1: getBrandsWithFormulableLines (usado en BrandSelectionModal, BrandSelector, BrandsModal)
console.log('🧪 TEST 1: getBrandsWithFormulableLines()');
const formulableBrands = getBrandsWithFormulableLines();
console.log(`✅ Marcas formulables encontradas: ${formulableBrands.length}`);

// Verificar que las marcas principales están incluidas
const expectedBrands = [
  'wella', 'schwarzkopf', 'goldwell', 'loreal', 'alfaparf', 
  'salerm', 'lendan', 'montibello', 'redken', 'matrix', 'joico', 'keune-professional'
];

console.log('\n📋 Verificando marcas principales:');
expectedBrands.forEach(brandId => {
  const brand = formulableBrands.find(b => b.id === brandId);
  if (brand) {
    const colorLines = brand.lines.filter(line => line.isColorLine === true);
    console.log(`✅ ${brand.name}: ${colorLines.length} líneas de coloración`);
  } else {
    console.log(`❌ ${brandId}: NO ENCONTRADA en marcas formulables`);
  }
});

// Test 2: getColorLinesByBrandId (usado en todos los componentes)
console.log('\n🧪 TEST 2: getColorLinesByBrandId()');
const testBrands = ['wella', 'schwarzkopf', 'loreal', 'redken', 'matrix'];
testBrands.forEach(brandId => {
  const colorLines = getColorLinesByBrandId(brandId);
  const brand = getBrandById(brandId);
  console.log(`✅ ${brand?.name || brandId}: ${colorLines.length} líneas de coloración`);
  
  // Verificar que todas las líneas tienen isColorLine: true
  const invalidLines = colorLines.filter(line => !line.isColorLine);
  if (invalidLines.length > 0) {
    console.log(`❌ Líneas sin isColorLine: ${invalidLines.map(l => l.name).join(', ')}`);
  }
});

// Test 3: searchFormulableBrands (usado en BrandSelectionModal)
console.log('\n🧪 TEST 3: searchFormulableBrands()');
const searchTests = [
  { query: 'wella', expected: 'Wella Professionals' },
  { query: 'schwarzkopf', expected: 'Schwarzkopf Professional' },
  { query: 'loreal', expected: "L'Oréal Professionnel" },
  { query: 'redken', expected: 'Redken' },
  { query: 'matrix', expected: 'Matrix' },
  { query: 'joico', expected: 'Joico' },
  { query: 'salerm', expected: 'Salerm Cosmetics' },
  { query: 'lendan', expected: 'Lendan' },
  { query: 'montibello', expected: 'Montibello' },
];

searchTests.forEach(test => {
  const results = searchFormulableBrands(test.query);
  const found = results.find(b => b.name === test.expected);
  if (found) {
    console.log(`✅ Búsqueda "${test.query}": Encontrado ${found.name}`);
  } else {
    console.log(`❌ Búsqueda "${test.query}": NO encontrado ${test.expected}`);
    console.log(`   Resultados: ${results.map(b => b.name).join(', ')}`);
  }
});

// Test 4: Verificar líneas específicas según el listado
console.log('\n🧪 TEST 4: Verificando líneas específicas del listado');
const specificLineTests = [
  { brandId: 'loreal', expectedLines: ['Majirel', 'iNOA', 'Dia Light', 'Dia Color'] },
  { brandId: 'schwarzkopf', expectedLines: ['IGORA ROYAL', 'IGORA ROYAL Absolutes', 'IGORA ROYAL Highlifts', 'IGORA ZERO AMM', 'IGORA VIBRANCE', 'BLONDME Colour'] },
  { brandId: 'wella', expectedLines: ['Koleston Perfect', 'Illumina Color', 'Color Touch', 'Shinefinity'] },
  { brandId: 'redken', expectedLines: ['Color Gels Lacquers', 'Chromatics', 'Cover Fusion', 'Shades EQ Gloss'] },
  { brandId: 'matrix', expectedLines: ['SoColor Pre-Bonded', 'Coil Color', 'SoColor Sync (Alkaline)', 'SoColor Sync (Acidic)'] },
];

specificLineTests.forEach(test => {
  const brand = getBrandById(test.brandId);
  const colorLines = getColorLinesByBrandId(test.brandId);
  
  console.log(`\n📝 ${brand?.name || test.brandId}:`);
  test.expectedLines.forEach(expectedLine => {
    const found = colorLines.find(line => line.name === expectedLine);
    if (found) {
      console.log(`  ✅ ${expectedLine}`);
    } else {
      console.log(`  ❌ ${expectedLine} - NO ENCONTRADA`);
    }
  });
  
  console.log(`  📊 Total líneas: ${colorLines.length}/${test.expectedLines.length} esperadas`);
});

// Test 5: Validación final
console.log('\n🧪 TEST 5: Validación final del catálogo');
const validation = validateBrandLines();
console.log(`Estado general: ${validation.valid ? '✅ VÁLIDO' : '❌ CON ERRORES'}`);
console.log(`Errores críticos: ${validation.issues.length}`);
console.log(`Advertencias: ${validation.warnings.length}`);

// Resumen final
console.log('\n=== RESUMEN DE COMPATIBILIDAD ===');
console.log(`✅ Marcas formulables disponibles: ${formulableBrands.length}`);
console.log(`✅ Principales marcas actualizadas correctamente`);
console.log(`✅ Funciones de filtrado funcionando`);
console.log(`✅ Búsqueda de marcas operativa`);
console.log('\n🎯 Los componentes deberían funcionar correctamente con el catálogo actualizado.');

console.log('\n=== FIN DE PRUEBAS ===');
