#!/bin/bash

# Script de optimización de imágenes para Salonier
# Reduce el tamaño de las imágenes PNG manteniendo la calidad visual

echo "🎨 Iniciando optimización de imágenes..."

# Directorio de imágenes
IMG_DIR="assets/images"
BACKUP_DIR="$IMG_DIR/backup"

# Crear directorio temporal
TEMP_DIR="$IMG_DIR/temp"
mkdir -p "$TEMP_DIR"

# Función para optimizar PNG
optimize_png() {
    local input=$1
    local output=$2
    local max_size=$3
    
    # Obtener dimensiones actuales
    width=$(sips -g pixelWidth "$input" | tail -1 | awk '{print $2}')
    height=$(sips -g pixelHeight "$input" | tail -1 | awk '{print $2}')
    
    # Si la imagen es más grande que el tamaño máximo, redimensionar
    if [ "$width" -gt "$max_size" ] || [ "$height" -gt "$max_size" ]; then
        echo "  📐 Redimensionando a ${max_size}x${max_size}..."
        sips -Z "$max_size" "$input" --out "$output" >/dev/null 2>&1
    else
        cp "$input" "$output"
    fi
    
    # Aplicar compresión mediante conversión a JPEG y vuelta a PNG
    # Esto reduce significativamente el tamaño sin pérdida visual notable
    temp_jpg="$TEMP_DIR/temp.jpg"
    sips -s format jpeg -s formatOptions 85 "$output" --out "$temp_jpg" >/dev/null 2>&1
    sips -s format png "$temp_jpg" --out "$output" >/dev/null 2>&1
    rm -f "$temp_jpg"
}

# Optimizar cada imagen
echo "📱 Optimizando adaptive-icon.png..."
optimize_png "$IMG_DIR/adaptive-icon.png" "$IMG_DIR/adaptive-icon.png" 1024

echo "📱 Optimizando icon.png..."
# Icon ya está en 1024x1024, solo comprimir
optimize_png "$IMG_DIR/icon.png" "$IMG_DIR/icon.png" 1024

echo "🎯 Optimizando splash-icon.png..."
# Splash mantener en 512x512
optimize_png "$IMG_DIR/splash-icon.png" "$IMG_DIR/splash-icon.png" 512

echo "🔍 Optimizando favicon.png..."
# Favicon es pequeño, solo copiar
cp "$IMG_DIR/favicon.png" "$IMG_DIR/favicon.png"

# Limpiar archivos temporales
rm -rf "$TEMP_DIR"
rm -f "$IMG_DIR/adaptive-icon-optimized.png"

# Mostrar resultados
echo ""
echo "✅ Optimización completada!"
echo ""
echo "📊 Tamaños antes y después:"
echo "Antes:"
du -h "$BACKUP_DIR"/*.png | sort -hr
echo ""
echo "Después:"
du -h "$IMG_DIR"/*.png | grep -v backup | sort -hr
echo ""
echo "Total antes: $(du -sh "$BACKUP_DIR" | awk '{print $1}')"
echo "Total después: $(du -sh "$IMG_DIR" | awk '{print $1}' | grep -v backup)"