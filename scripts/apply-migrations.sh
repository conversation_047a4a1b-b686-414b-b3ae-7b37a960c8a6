#!/bin/bash

# Load environment variables
source .env.local

# Extract project ref from URL
PROJECT_REF=$(echo $EXPO_PUBLIC_SUPABASE_URL | sed -n 's/https:\/\/\([^.]*\).*/\1/p')

echo "🚀 Applying migrations to Supabase project: $PROJECT_REF"
echo ""
echo "📝 Instructions for manual application:"
echo ""
echo "1. Go to: https://supabase.com/dashboard/project/$PROJECT_REF/sql/new"
echo ""
echo "2. Copy and paste each migration file in this order:"
echo "   - supabase/migrations/001_initial_schema.sql"
echo "   - supabase/migrations/002_row_level_security.sql"
echo "   - supabase/migrations/003_auth_triggers.sql"
echo "   - supabase/migrations/004_storage_buckets.sql"
echo ""
echo "3. Execute each one and verify success"
echo ""
echo "4. After all migrations are applied, verify:"
echo "   - Go to Table Editor and confirm all tables exist"
echo "   - Go to Authentication > Policies and verify RLS is enabled"
echo "   - Go to Storage and verify buckets are created"
echo ""
echo "Project Dashboard URL: https://supabase.com/dashboard/project/$PROJECT_REF"