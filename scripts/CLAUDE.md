# CLAUDE.md - Deployment & Testing Scripts

## 🎯 Prop<PERSON><PERSON>

Scripts para deployment automatizado, testing de Edge Functions, optimización de assets y mantenimiento de base de datos. Todos los scripts DEBEN ser ejecutados desde la raíz del proyecto y verificar permisos antes de ejecutar.

## 📁 Estructura de Scripts

### 🚀 Deployment Scripts

- `deploy-edge-function.sh` - Deploy principal de salonier-assistant
- `deploy-and-logs.sh` - Deploy con logs en tiempo real
- `deploy-chat-assistant.sh` - Deploy del chat assistant
- `verify-stable-deployment.sh` - Verificación post-deploy

### 🧪 Testing Scripts

- `test-edge-function-simple.sh` - Test básico del Edge Function
- `test-edge-function.sh` - Test completo con casos complejos
- `test-formula-generation.sh` - Test específico de generación de fórmulas
- `test-chat-assistant.sh` - Test del sistema de chat
- `test-ai-with-auth.js` - Test con autenticación

### 🗄️ Database Scripts

- `apply-migrations.sh` - Aplicación segura de migraciones
- `update-database-types.sh` - Regeneración de tipos TypeScript
- `maintenance-tasks.sql` - Tareas de mantenimiento rutinario

### 🖼️ Asset Optimization

- `optimize-images-final.sh` - Optimización final de imágenes
- `optimize-images.sh` - Optimización estándar
- `check-contrast.ts` - Verificación de contraste UI

## 🚀 Deployment Principal

### deploy-edge-function.sh

```bash
#!/bin/bash
# Script principal para deployment del Edge Function

echo "🚀 Desplegando Edge Function salonier-assistant..."

# 1. Verificar prerrequisitos
check_prerequisites() {
    if [ ! -d "supabase/functions/salonier-assistant" ]; then
        echo "❌ Error: No se encontró el directorio de la función"
        exit 1
    fi

    if ! command -v supabase &> /dev/null; then
        echo "❌ Error: Supabase CLI no está instalado"
        echo "Instálalo con: brew install supabase/tap/supabase"
        exit 1
    fi
}

# 2. Verificar autenticación
check_auth() {
    echo "🔐 Verificando autenticación..."
    if ! supabase status &> /dev/null; then
        echo "❌ No estás autenticado con Supabase"
        echo "Ejecuta: supabase login"
        exit 1
    fi
}

# 3. Deploy con verificación
deploy_function() {
    echo "📦 Desplegando función..."
    supabase functions deploy salonier-assistant

    if [ $? -eq 0 ]; then
        echo "✅ Edge Function desplegada exitosamente"
    else
        echo "❌ Error al desplegar la función"
        exit 1
    fi
}

# 4. Verificación post-deploy
verify_deployment() {
    echo "🔍 Verificando deployment..."
    ./scripts/verify-stable-deployment.sh
}

# Ejecutar pipeline
check_prerequisites
check_auth
deploy_function
verify_deployment

echo "🎉 Deployment completado exitosamente"
```

### Uso del Script

```bash
# Otorgar permisos de ejecución
chmod +x scripts/deploy-edge-function.sh

# Ejecutar desde la raíz del proyecto
./scripts/deploy-edge-function.sh

# Con logs en tiempo real
./scripts/deploy-and-logs.sh
```

## 🧪 Testing Automatizado

### test-edge-function-simple.sh

```bash
#!/bin/bash
# Test básico del Edge Function principal

FUNCTION_URL="https://your-project.supabase.co/functions/v1/salonier-assistant"

echo "🧪 Testing Edge Function..."

# Test 1: Health check
echo "📍 Test 1: Health check"
response=$(curl -s -w "%{http_code}" -o /dev/null -X POST "$FUNCTION_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $SUPABASE_ANON_KEY" \
  -d '{"type": "health_check"}')

if [ "$response" = "200" ]; then
    echo "✅ Health check passed"
else
    echo "❌ Health check failed (HTTP $response)"
    exit 1
fi

# Test 2: Formula generation
echo "📍 Test 2: Formula generation"
test_formula() {
    local response=$(curl -s -X POST "$FUNCTION_URL" \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer $SUPABASE_ANON_KEY" \
      -d '{
        "type": "formula_generation",
        "currentState": {
          "baseColor": "6",
          "condition": "good",
          "previouslyColored": false
        },
        "targetColor": {
          "level": "8",
          "tone": "natural"
        }
      }')

    # Verificar que la respuesta contiene una fórmula
    if echo "$response" | grep -q "formula"; then
        echo "✅ Formula generation test passed"
    else
        echo "❌ Formula generation test failed"
        echo "Response: $response"
        exit 1
    fi
}

test_formula

echo "🎉 Todos los tests pasaron exitosamente"
```

### test-formula-generation.sh

```bash
#!/bin/bash
# Test exhaustivo de generación de fórmulas

echo "🧪 Testing formula generation scenarios..."

# Array de casos de test
declare -a test_cases=(
    "Virgin hair level 6 to 8 blonde"
    "Colored hair level 4 to 7 with red undertones"
    "Gray coverage 50% on level 5 base"
    "Color correction orange to ash blonde"
    "Fashion color purple on bleached base"
)

test_scenario() {
    local scenario="$1"
    local test_data="$2"

    echo "📍 Testing: $scenario"

    local response=$(curl -s -X POST "$FUNCTION_URL" \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer $SUPABASE_ANON_KEY" \
      -d "$test_data")

    # Verificar respuesta válida
    if echo "$response" | jq -e '.formula' >/dev/null 2>&1; then
        local confidence=$(echo "$response" | jq -r '.confidence')
        echo "✅ $scenario - Confidence: $confidence%"
    else
        echo "❌ $scenario failed"
        echo "Response: $response"
        return 1
    fi
}

# Ejecutar todos los casos
for i in "${!test_cases[@]}"; do
    case_data=$(generate_test_data "$i")
    test_scenario "${test_cases[$i]}" "$case_data"
done

echo "🎉 Formula generation tests completed"
```

## 🗄️ Database Management

### apply-migrations.sh

```bash
#!/bin/bash
# Aplicación segura de migraciones con backup

echo "🗄️ Aplicando migraciones de base de datos..."

# 1. Verificar estado actual
check_migration_status() {
    echo "📊 Verificando estado de migraciones..."
    npx supabase migration list
}

# 2. Crear backup antes de migrar
create_backup() {
    echo "💾 Creando backup de seguridad..."
    local backup_name="backup_$(date +%Y%m%d_%H%M%S)"

    # Solo en producción
    if [ "$ENVIRONMENT" = "production" ]; then
        pg_dump "$DATABASE_URL" > "backups/$backup_name.sql"
        echo "✅ Backup creado: $backup_name.sql"
    else
        echo "ℹ️ Backup omitido (entorno no productivo)"
    fi
}

# 3. Aplicar migraciones
apply_migrations() {
    echo "⚡ Aplicando migraciones..."

    npx supabase db push

    if [ $? -eq 0 ]; then
        echo "✅ Migraciones aplicadas exitosamente"
    else
        echo "❌ Error al aplicar migraciones"
        restore_backup
        exit 1
    fi
}

# 4. Verificar integridad post-migración
verify_integrity() {
    echo "🔍 Verificando integridad de la base de datos..."

    # Verificar RLS en todas las tablas
    psql "$DATABASE_URL" -c "
        SELECT tablename
        FROM pg_tables
        WHERE schemaname = 'public' AND rowsecurity = false;
    " | grep -v "0 rows" && {
        echo "⚠️ Warning: Tablas sin RLS encontradas"
    }

    # Verificar índices críticos
    psql "$DATABASE_URL" -c "
        SELECT tablename
        FROM pg_tables t
        WHERE schemaname = 'public'
        AND NOT EXISTS (
            SELECT 1 FROM pg_indexes i
            WHERE i.tablename = t.tablename
            AND i.indexname LIKE '%salon_id%'
        );
    " | grep -v "0 rows" && {
        echo "⚠️ Warning: Tablas sin índice salon_id encontradas"
    }
}

# Ejecutar pipeline
check_migration_status
create_backup
apply_migrations
verify_integrity

echo "🎉 Migraciones completadas exitosamente"
```

### update-database-types.sh

```bash
#!/bin/bash
# Regeneración de tipos TypeScript desde schema

echo "🔄 Actualizando tipos de base de datos..."

# 1. Generar tipos desde Supabase
generate_types() {
    echo "📝 Generando tipos TypeScript..."

    npx supabase gen types typescript --local > types/database.ts

    if [ $? -eq 0 ]; then
        echo "✅ Tipos generados exitosamente"
    else
        echo "❌ Error al generar tipos"
        exit 1
    fi
}

# 2. Validar tipos generados
validate_types() {
    echo "🔍 Validando tipos generados..."

    # Verificar que el archivo no esté vacío
    if [ ! -s "types/database.ts" ]; then
        echo "❌ Archivo de tipos está vacío"
        exit 1
    fi

    # Verificar sintaxis TypeScript
    npx tsc --noEmit types/database.ts

    if [ $? -eq 0 ]; then
        echo "✅ Tipos válidos"
    else
        echo "❌ Tipos contienen errores de sintaxis"
        exit 1
    fi
}

# 3. Actualizar imports en stores
update_imports() {
    echo "🔄 Actualizando imports en stores..."

    # Verificar que los stores compilen con nuevos tipos
    npm run typecheck

    if [ $? -eq 0 ]; then
        echo "✅ Stores compatibles con nuevos tipos"
    else
        echo "⚠️ Warning: Algunos stores requieren actualización"
    fi
}

# Ejecutar pipeline
generate_types
validate_types
update_imports

echo "🎉 Tipos de base de datos actualizados"
```

## 🖼️ Asset Optimization

### optimize-images-final.sh

```bash
#!/bin/bash
# Optimización final de imágenes para producción

echo "🖼️ Optimizando imágenes para producción..."

# Configuración
QUALITY=85
MAX_WIDTH=1920
MAX_HEIGHT=1080

# 1. Encontrar imágenes a optimizar
find_images() {
    find assets/ \( -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" \) -type f
}

# 2. Optimizar cada imagen
optimize_image() {
    local image="$1"
    local temp_file="${image}.tmp"

    echo "📸 Optimizando: $image"

    # Redimensionar y comprimir
    magick "$image" \
        -resize "${MAX_WIDTH}x${MAX_HEIGHT}>" \
        -quality $QUALITY \
        -strip \
        "$temp_file"

    # Verificar que la optimización redujo el tamaño
    original_size=$(stat -f%z "$image")
    optimized_size=$(stat -f%z "$temp_file")

    if [ $optimized_size -lt $original_size ]; then
        mv "$temp_file" "$image"
        echo "✅ $image optimizada ($(($original_size - $optimized_size)) bytes saved)"
    else
        rm "$temp_file"
        echo "ℹ️ $image ya optimizada"
    fi
}

# 3. Generar WebP variants
generate_webp() {
    local image="$1"
    local webp_image="${image%.*}.webp"

    if [ ! -f "$webp_image" ]; then
        magick "$image" -quality $QUALITY "$webp_image"
        echo "✅ WebP generado: $webp_image"
    fi
}

# Ejecutar optimización
while IFS= read -r -d '' image; do
    optimize_image "$image"
    generate_webp "$image"
done < <(find_images -print0)

echo "🎉 Optimización de imágenes completada"
```

## ⚙️ Comandos Útiles

### Permisos de Ejecución

```bash
# Otorgar permisos a todos los scripts
chmod +x scripts/*.sh

# Verificar permisos
ls -la scripts/
```

### Ejecución desde Raíz

```bash
# ✅ CORRECTO: Ejecutar desde raíz del proyecto
./scripts/deploy-edge-function.sh

# ❌ INCORRECTO: Ejecutar desde directorio scripts
cd scripts && ./deploy-edge-function.sh
```

### Debugging de Scripts

```bash
# Ejecutar con debug
bash -x ./scripts/deploy-edge-function.sh

# Verificar sintaxis
bash -n ./scripts/deploy-edge-function.sh
```

## 🚨 Errores Comunes y Soluciones

### 1. Permission denied

```bash
Error: ./scripts/script.sh: Permission denied
Solución: chmod +x scripts/script.sh
```

### 2. Supabase CLI no encontrado

```bash
Error: supabase: command not found
Solución: brew install supabase/tap/supabase
```

### 3. Variables de entorno faltantes

```bash
Error: SUPABASE_ANON_KEY not set
Solución: Verificar archivo .env o variables de sistema
```

### 4. Script ejecutado desde directorio incorrecto

```bash
Error: No se encontró el directorio supabase/functions
Solución: Ejecutar desde la raíz del proyecto
```

## 📊 Monitoreo y Alertas

### Health Check Automation

```bash
# Cron job para verificación automática
# Ejecutar cada 5 minutos
*/5 * * * * /path/to/project/scripts/test-edge-function-simple.sh
```

### Alertas de Deployment

```bash
# Notificación Slack en deployment
send_slack_notification() {
    local message="$1"
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"$message\"}" \
        "$SLACK_WEBHOOK_URL"
}
```

## 🤖 Agentes Recomendados

### Para este módulo usar:

**deployment-engineer** - Deployment y CI/CD

- Script de deployment automatizado y seguro
- Zero-downtime deployment strategies
- CI/CD pipeline configuration
- MUST BE USED para todos los deployments

**test-runner** - Testing automatizado

- Scripts de testing comprehensivos
- Automation de test suites
- Integration testing con Edge Functions
- PROACTIVAMENTE usar después de cambios

**database-architect** - Scripts de base de datos

- Scripts de migración seguros
- Backup y restore automatizado
- Performance testing de queries
- PROACTIVAMENTE usar para cambios de schema

**debug-specialist** - Debugging de scripts

- Análisis de fallos en deployment
- Debugging de scripts complejos
- Root cause analysis de errores
- PROACTIVAMENTE usar para errores de scripts

**security-privacy-auditor** - Seguridad en scripts

- Validar scripts no expongan secretos
- Auditar permisos de scripts
- Verificar manejo seguro de variables de entorno
- Usar antes de nuevos scripts

### 💡 Ejemplos de Uso

```bash
# Deploy seguro con verificaciones
Task: Use deployment-engineer to enhance deploy-edge-function.sh with rollback capabilities

# Crear suite de tests completa
Task: Use test-runner to create comprehensive test suite for all Edge Functions

# Script de migración zero-downtime
Task: Use database-architect to create zero-downtime migration script

# Debug fallo en deployment
Task: Use debug-specialist to investigate deployment script failure in CI/CD
```

## 🔌 MCPs Disponibles

### Funciones MCP útiles:

**Para Supabase (principal para scripts):**

- `mcp__supabase__list_edge_functions` - Verificar funciones deployadas
- `mcp__supabase__deploy_edge_function` - Deploy programático
- `mcp__supabase__get_logs` - Debug de deployments
- `mcp__supabase__apply_migration` - Aplicar migraciones
- `mcp__supabase__list_migrations` - Estado de migraciones
- `mcp__supabase__get_advisors` - Verificaciones post-deploy

**Para análisis de scripts:**

- `mcp__serena__search_for_pattern` - Buscar patterns en scripts
- `mcp__serena__get_symbols_overview` - Estructura de scripts complejos

**Para testing:**

- `mcp__ide__getDiagnostics` - Verificar sintaxis de scripts
- `mcp__supabase__get_logs` - Logs para debugging

### 📝 Ejemplos MCP

```bash
# Verificar estado de Edge Functions
mcp__supabase__list_edge_functions
mcp__supabase__get_logs: "edge-function"

# Deploy función programáticamente
mcp__supabase__deploy_edge_function: {
  "name": "salonier-assistant",
  "files": [{"name": "index.ts", "content": "..."}]
}

# Verificar migraciones
mcp__supabase__list_migrations
mcp__supabase__get_advisors: "security"

# Buscar scripts con problemas de permisos
mcp__serena__search_for_pattern: "chmod.*777" in "scripts/"

# Verificar sintaxis de scripts
mcp__ide__getDiagnostics: "scripts/"
```

### 🔄 Combinaciones Recomendadas

**Deployment Seguro:**

1. `deployment-engineer` + `mcp__supabase__deploy_edge_function`
2. `security-privacy-auditor` + `mcp__supabase__get_advisors`

**Testing Completo:**

1. `test-runner` + `mcp__supabase__get_logs`
2. `debug-specialist` + `mcp__ide__getDiagnostics`

**Migración Segura:**

1. `database-architect` + `mcp__supabase__apply_migration`
2. `data-migration-specialist` + `mcp__supabase__list_migrations`

## 📊 Mejores Prácticas para Scripts

### 🔒 Security Checklist para Scripts

- [ ] No hardcodear secrets o API keys
- [ ] Usar variables de entorno para configuración
- [ ] Validar permisos antes de ejecutar
- [ ] Logging seguro sin exponer datos sensibles
- [ ] Cleanup de archivos temporales

### 🧪 Testing de Scripts

```bash
# Usar test-runner para validar scripts
Task: Use test-runner to create test suite for deployment scripts

# Ejemplo de testing
test_deploy_script() {
  # Setup test environment
  # Run script with test data
  # Verify expected outcomes
  # Cleanup test artifacts
}
```

### 📈 Monitoring y Alertas

```bash
# Integrar con deployment-engineer para monitoring
Task: Use deployment-engineer to add monitoring and alerting to critical scripts

# Health checks automatizados
./scripts/verify-stable-deployment.sh
mcp__supabase__get_advisors: "performance"
```

## 🔗 Archivos Relacionados

- `../supabase/functions/` - Edge Functions a deployar
- `../supabase/migrations/` - Migraciones de base de datos
- `../package.json` - Scripts npm relacionados
- `../.env` - Variables de entorno

---

**⚡ Recuerda:** Los scripts son herramientas críticas para deployment. Siempre verificar en entorno de desarrollo antes de producción. MUST USE `deployment-engineer` para todos los deployments y `test-runner` para validación.
