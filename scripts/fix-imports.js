#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing missing imports...\n');

const fixes = [
  {
    pattern: /Platform\./,
    import: 'Platform',
    from: 'react-native',
    check: content =>
      content.includes('Platform.') && !content.match(/import.*Platform.*from.*react-native/),
  },
  {
    pattern: /Alert\./,
    import: 'Alert',
    from: 'react-native',
    check: content =>
      content.includes('Alert.') && !content.match(/import.*Alert.*from.*react-native/),
  },
  {
    pattern: /shadows\./,
    import: 'shadows',
    from: '@/constants/theme',
    check: content =>
      content.includes('shadows.') && !content.match(/import.*shadows.*from.*@\/constants\/theme/),
  },
];

const fixFile = filePath => {
  const content = fs.readFileSync(filePath, 'utf8');
  let updatedContent = content;
  let hasChanges = false;

  for (const fix of fixes) {
    if (fix.check(content)) {
      console.log(`   🔧 Adding ${fix.import} import to ${filePath}`);

      // Find the last import statement
      const importLines = content.split('\n').filter(line => line.trim().startsWith('import'));
      const lastImportIndex = content.lastIndexOf(importLines[importLines.length - 1]);
      const afterLastImport = content.indexOf('\n', lastImportIndex) + 1;

      // Check if there's already an import from the same module
      const existingImportRegex = new RegExp(
        `import\\s*{([^}]*)}\\s*from\\s*['"]${fix.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]`
      );
      const existingImportMatch = content.match(existingImportRegex);

      if (existingImportMatch && !existingImportMatch[1].includes(fix.import)) {
        // Add to existing import
        const newImports = existingImportMatch[1].trim() + ', ' + fix.import;
        updatedContent = updatedContent.replace(
          existingImportMatch[0],
          `import { ${newImports} } from '${fix.from}'`
        );
      } else if (!existingImportMatch) {
        // Add new import
        const newImport = `import { ${fix.import} } from '${fix.from}';\n`;
        updatedContent =
          updatedContent.slice(0, afterLastImport) +
          newImport +
          updatedContent.slice(afterLastImport);
      }
      hasChanges = true;
    }
  }

  if (hasChanges) {
    fs.writeFileSync(filePath, updatedContent);
    return true;
  }
  return false;
};

const processDirectory = dir => {
  const files = fs.readdirSync(dir, { withFileTypes: true });
  let totalFixed = 0;

  for (const file of files) {
    const filePath = path.join(dir, file.name);

    if (
      file.isDirectory() &&
      !['node_modules', '.git', 'coverage', 'archive'].includes(file.name)
    ) {
      totalFixed += processDirectory(filePath);
    } else if (file.name.endsWith('.tsx') || file.name.endsWith('.ts')) {
      if (fixFile(filePath)) {
        totalFixed++;
      }
    }
  }

  return totalFixed;
};

const fixedFiles = processDirectory('./app') + processDirectory('./components');

console.log(`\n✅ Fixed ${fixedFiles} files!`);
console.log('🔄 Run the diagnostic check again to verify fixes.');
