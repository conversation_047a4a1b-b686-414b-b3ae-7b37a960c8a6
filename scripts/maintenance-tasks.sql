-- Script de Tareas de Mantenimiento para Supabase
-- Ejecutar según sea necesario para mantener la salud de la base de datos
-- Fecha: 2025-01-11

-- ============================================
-- 1. VACUUM Y ANALYZE
-- ============================================
-- Ejecutar VACUUM para recuperar espacio de tuplas muertas
-- y ANALYZE para actualizar estadísticas

-- Para todas las tablas del esquema public
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN 
        SELECT schemaname, tablename 
        FROM pg_tables 
        WHERE schemaname = 'public'
    LOOP
        EXECUTE format('VACUUM ANALYZE %I.%I', r.schemaname, r.tablename);
        RAISE NOTICE 'Vacuum analyze completado para %.%', r.schemaname, r.tablename;
    END LOOP;
END $$;

-- ============================================
-- 2. REINDEX DE TABLAS FRAGMENTADAS
-- ============================================
-- Reindexar tablas con alto nivel de fragmentación

-- Identificar tablas que necesitan reindex
WITH fragmented_tables AS (
    SELECT 
        schemaname,
        tablename,
        n_dead_tup,
        n_live_tup,
        round((n_dead_tup::numeric / NULLIF(n_live_tup + n_dead_tup, 0)) * 100, 2) as fragmentation_percent
    FROM pg_stat_user_tables
    WHERE schemaname = 'public'
    AND n_dead_tup > 1000
    AND (n_dead_tup::numeric / NULLIF(n_live_tup + n_dead_tup, 0)) > 0.2
)
SELECT 
    format('REINDEX TABLE %I.%I; -- Fragmentación: %s%%', schemaname, tablename, fragmentation_percent) as reindex_command
FROM fragmented_tables
ORDER BY fragmentation_percent DESC;

-- ============================================
-- 3. LIMPIAR CACHE DE IA EXPIRADO
-- ============================================
-- Eliminar entradas de cache que han expirado

DO $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM ai_analysis_cache
    WHERE expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RAISE NOTICE 'Eliminadas % entradas expiradas del cache de IA', deleted_count;
END $$;

-- ============================================
-- 4. ACTUALIZAR ESTADÍSTICAS DE TABLAS
-- ============================================
-- Actualizar estadísticas para el planificador de queries

-- Estadísticas detalladas para tablas principales
ANALYZE VERBOSE public.profiles;
ANALYZE VERBOSE public.salons;
ANALYZE VERBOSE public.clients;
ANALYZE VERBOSE public.products;
ANALYZE VERBOSE public.services;
ANALYZE VERBOSE public.formulas;
ANALYZE VERBOSE public.stock_movements;
ANALYZE VERBOSE public.ai_analysis_cache;

-- ============================================
-- 5. VERIFICAR Y REPARAR SECUENCIAS
-- ============================================
-- Asegurar que las secuencias estén sincronizadas

DO $$
DECLARE
    r RECORD;
    max_id BIGINT;
    seq_value BIGINT;
BEGIN
    -- Para cada tabla con columna serial o identity
    FOR r IN 
        SELECT 
            table_name,
            column_name,
            pg_get_serial_sequence(table_schema||'.'||table_name, column_name) as seq_name
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND pg_get_serial_sequence(table_schema||'.'||table_name, column_name) IS NOT NULL
    LOOP
        -- Obtener el valor máximo actual
        EXECUTE format('SELECT COALESCE(MAX(%I), 0) FROM %I', r.column_name, r.table_name) INTO max_id;
        
        -- Obtener el valor actual de la secuencia
        EXECUTE format('SELECT last_value FROM %s', r.seq_name) INTO seq_value;
        
        -- Si la secuencia está detrás, actualizarla
        IF max_id >= seq_value THEN
            EXECUTE format('SELECT setval(%L, %s)', r.seq_name, max_id + 1);
            RAISE NOTICE 'Secuencia % actualizada a %', r.seq_name, max_id + 1;
        END IF;
    END LOOP;
END $$;

-- ============================================
-- 6. LIMPIAR LOGS ANTIGUOS
-- ============================================
-- Limpiar logs de más de 30 días (si existen tablas de logs)

-- Ejemplo para tabla de logs personalizada (ajustar según tu esquema)
/*
DELETE FROM app_logs
WHERE created_at < NOW() - INTERVAL '30 days';
*/

-- ============================================
-- 7. OPTIMIZAR TABLAS CON MUCHAS ACTUALIZACIONES
-- ============================================
-- Ejecutar VACUUM FULL en tablas con alto churn rate

-- ADVERTENCIA: VACUUM FULL bloquea la tabla completamente
-- Ejecutar solo durante ventanas de mantenimiento

/*
-- Descomentar y ejecutar durante mantenimiento
VACUUM FULL public.products;
VACUUM FULL public.stock_movements;
VACUUM FULL public.services;
*/

-- ============================================
-- 8. REVISAR Y ACTUALIZAR CONFIGURACIÓN DE AUTOVACUUM
-- ============================================
-- Verificar configuración actual de autovacuum

SELECT 
    name,
    setting,
    unit,
    short_desc
FROM pg_settings
WHERE name LIKE 'autovacuum%'
ORDER BY name;

-- Sugerencias de configuración para tablas con alto tráfico
/*
ALTER TABLE public.products SET (
    autovacuum_vacuum_scale_factor = 0.1,
    autovacuum_analyze_scale_factor = 0.05
);

ALTER TABLE public.stock_movements SET (
    autovacuum_vacuum_scale_factor = 0.1,
    autovacuum_analyze_scale_factor = 0.05
);
*/

-- ============================================
-- 9. GENERAR REPORTE DE MANTENIMIENTO
-- ============================================
SELECT 
    '=== REPORTE DE MANTENIMIENTO ===' as section,
    NOW() as fecha_ejecucion;

-- Estado de vacuum
SELECT 
    schemaname,
    tablename,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze,
    n_dead_tup as tuplas_muertas
FROM pg_stat_user_tables
WHERE schemaname = 'public'
ORDER BY GREATEST(
    COALESCE(last_vacuum, '1900-01-01'::timestamptz),
    COALESCE(last_autovacuum, '1900-01-01'::timestamptz)
) ASC
LIMIT 10;

-- ============================================
-- 10. BACKUP DE CONFIGURACIÓN CRÍTICA
-- ============================================
-- Exportar configuración de RLS policies para backup

SELECT 
    '-- Backup de RLS Policies generado el ' || NOW() as comment
UNION ALL
SELECT 
    format('CREATE POLICY %I ON %I.%I FOR %s TO %s %s %s;',
        policyname,
        schemaname,
        tablename,
        cmd,
        roles::text,
        CASE WHEN qual IS NOT NULL THEN 'USING (' || qual || ')' ELSE '' END,
        CASE WHEN with_check IS NOT NULL THEN 'WITH CHECK (' || with_check || ')' ELSE '' END
    )
FROM pg_policies
WHERE schemaname = 'public'
ORDER BY tablename, policyname;