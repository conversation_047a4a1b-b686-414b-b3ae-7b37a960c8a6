# Salonier - Asistente de Coloración Capilar con IA

## Propósito del Proyecto

Aplicación móvil para salones de belleza que usa GPT-4o Vision para análisis de cabello y generación de fórmulas de coloración personalizadas. Sistema 100% impulsado por IA sin algoritmos tradicionales.

## Tech Stack

- **Frontend**: React Native + Expo (v53)
- **UI**: NativeWind (Tailwind for RN) + Custom Design System
- **State**: <PERSON>ustand (offline-first con AsyncStorage)
- **Backend**: Supabase (PostgreSQL + Edge Functions)
- **AI**: OpenAI GPT-4o Vision + GPT-3.5-turbo
- **Testing**: Jest + React Testing Library
- **Build**: Metro bundler
- **Package Manager**: npm/bun

## Arquitectura

- Offline-first con optimistic UI updates
- Multi-tenant con RLS (Row Level Security)
- Queue system para sincronización en background
- Edge Functions para procesamiento AI
- Sistema de cache para respuestas AI

## Estado Actual

- Versión: v41.1 ESTABLE en producción
- 285 archivos TypeScript
- 10 test suites, 209 tests (27 fallando)
- 2255 problemas de linting (707 errores)
