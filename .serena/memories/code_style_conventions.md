# Estilo de Código y Convenciones - Salonier

## TypeScript

- Strict mode habilitado
- NO usar `any` - siempre tipos específicos
- Interfaces para objetos, types para uniones
- Naming: PascalCase para componentes, camelCase para funciones

## React Native

- Functional components con hooks
- NO inline styles - usar clases de NativeWind
- NO color literals - usar constants/Colors
- Props destructuring en parámetros

## Patrones

- Offline-first: UI updates antes que sync
- Optimistic updates siempre
- Error boundaries para componentes críticos
- Lazy loading para screens pesadas

## Zustand Stores

- Persist solo datos esenciales
- NO persistir: isLoading, errors, UI state
- Computed values como getters, no estado derivado
- Siempre incluir salon_id en queries

## Seguridad

- NUNCA logs con datos sensibles
- Siempre validar permisos antes de operaciones críticas
- RLS policies obligatorias en todas las tablas
- Environment variables para secrets

## Testing

- Tests unitarios para toda lógica de negocio
- Mocks para AsyncStorage y Supabase
- Coverage mínimo 80%
