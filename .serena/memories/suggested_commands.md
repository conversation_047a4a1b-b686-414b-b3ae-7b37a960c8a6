# Comandos Sugeridos - Salonier

## Desarrollo

```bash
npm run mobile          # Desarrollo móvil con LAN
npm run mobile:tunnel   # Desarrollo con tunnel
npm run ios            # iOS Simulator
npm run android        # Android Emulator
```

## Calidad de Código

```bash
npm run lint           # Verificar errores de linting
npm run lint:fix       # Auto-fix linting
npm run format         # Formatear con Prettier
npm run code-quality   # Check completo
```

## Testing

```bash
npm test              # Correr todos los tests
npm run test:watch    # Tests en watch mode
npm run test:coverage # Coverage report
```

## Supabase

```bash
npx supabase status                    # Ver estado de conexión
npx supabase functions deploy [name]   # Deploy Edge Function
npx supabase db push                  # Aplicar migraciones
```

## Git

```bash
git status
git add .
git commit -m "message"
git push origin main
```
