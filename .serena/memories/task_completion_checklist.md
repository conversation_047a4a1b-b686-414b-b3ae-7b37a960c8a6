# Checklist de Finalización de Tareas

## Antes de Commit

1. **Linting**: `npm run lint` - Debe pasar sin errores críticos
2. **Formatting**: `npm run format` - Aplicar formato consistente
3. **Tests**: `npm test` - Todos los tests relacionados deben pasar
4. **TypeScript**: `npx tsc --noEmit` - Sin errores de tipos

## Validaciones Específicas

- NO console.log/error/warn en código de producción
- NO usar `any` - especificar tipos correctos
- NO inline styles - usar clases NativeWind
- NO color literals - usar constants/Colors
- Siempre incluir salon_id en queries Supabase
- Verificar RLS policies en tablas nuevas

## Post-Implementation

- Actualizar todo.md con tareas completadas
- Documentar cambios significativos en CHANGELOG.md
- Si es feature nueva, actualizar CLAUDE.md

## Performance Check

- Bundle size no debe aumentar >5%
- Verificar memory leaks en subscriptions
- Tests de rendimiento para operaciones críticas
