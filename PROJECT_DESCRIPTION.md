# PROJECT DESCRIPTION - Salonier

## Descripción General

**Salonier** es una aplicación móvil revolucionaria que democratiza el expertise en coloración capilar profesional mediante inteligencia artificial generativa pura. Es la primera aplicación mundial que elimina completamente los algoritmos tradicionales, utilizando GPT-4o Vision para análisis visual y GPT-4o para generación de fórmulas únicas.

## Propuesta de Valor Única

- **100% IA Generativa**: Sin tablas predefinidas ni algoritmos hardcodeados
- **Razonamiento Contextual**: Cada fórmula es única, creada por IA que comprende el contexto completo
- **Análisis Visual Avanzado**: Utiliza GPT-4o Vision para diagnóstico capilar preciso
- **Democratización del Conocimiento**: Expertise de coloristas de élite accesible para cualquier salón

## Stack Tecnológico Principal

### Frontend

- **React Native 0.79.1** + **Expo SDK 53** para desarrollo multiplataforma
- **Expo Router** para navegación file-based
- **Zustand 5.0** para manejo de estado global con UI Optimistic pattern
- **TypeScript 5.6** para type safety
- **NativeWind** para styling con Tailwind CSS

### Backend

- **Supabase** como Backend-as-a-Service
- **PostgreSQL 17** con Row Level Security (RLS) para multi-tenancy
- **Edge Functions** (Deno) para procesamiento de IA
- **OpenAI API** (GPT-4o Vision + GPT-4o Text)

## Arquitectura del Sistema

### Offline-First con Sincronización Inteligente

- **UI Optimistic**: Actualizaciones instantáneas, sync en background
- **Cola de sincronización**: Manejo de operaciones pendientes
- **Auto-save proactivo**: Previene pérdida de datos
- **Resolución de conflictos**: Last-write-wins con merge inteligente

### Sistema Multi-Tenant Seguro

- **Row Level Security (RLS)**: Aislamiento total de datos por salón
- **7 permisos granulares**: Control de acceso detallado
- **Encriptación**: TLS 1.3 en tránsito, datos sensibles nunca en logs

## Funcionalidades Clave

### 1. Análisis de IA Visual

- **Diagnóstico automatizado**: Análisis de nivel, reflejo, porcentaje de canas
- **Multi-zona**: Análisis separado de raíz, medios y puntas
- **Detección inteligente**: Procesos químicos previos y condición del cabello
- **Anonimización**: Detección y difuminado automático de rostros

### 2. Sistema de Formulación Inteligente

- **Generación única**: GPT-4o crea fórmulas personalizadas sin algoritmos
- **10 técnicas soportadas**: Desde tinte global hasta corrección de color
- **Proporciones exactas**: Cálculo preciso por longitud y densidad
- **Adaptación regional**: Conversión automática ml/oz según país

### 3. Gestión de Inventario Multi-Nivel

- **3 niveles de control**: Solo Fórmulas, Smart Cost, Control Total
- **Multi-marca**: Soporte ilimitado con sistema de matching inteligente
- **Análisis de costos**: Cálculo automático de rentabilidad por servicio
- **Alertas proactivas**: Notificaciones de stock bajo

### 4. Chat Assistant Especializado

- **Experto en colorimetría**: Asistente conversacional con conocimiento técnico
- **Análisis de imágenes**: Integración con GPT-4o Vision
- **Historial persistente**: Conversaciones organizadas y navegables
- **Contexto del salón**: Acceso completo a datos de inventario y clientes

### 5. Sistema de Seguridad Integral

- **Wizard de 4 pasos**: Checklist completo de seguridad
- **Gestión de alergias**: Base de datos de 23+ alergias comunes
- **Consentimiento digital**: Firma digital con timestamp
- **Validaciones críticas**: Prevención de incompatibilidades químicas

## Arquitectura de Archivos Principales

### Navegación y Pantallas (`/app`)

```
app/
├── (tabs)/                    # Navegación principal
│   ├── index.tsx             # Dashboard con métricas
│   ├── clients.tsx           # Lista de clientes
│   ├── inventory.tsx         # Gestión de productos
│   ├── assistant.tsx         # Chat assistant
│   └── settings.tsx          # Configuración
├── service/
│   └── new.tsx              # Flujo de servicios
└── auth/                     # Autenticación
```

### Componentes Core (`/components`)

```
components/
├── chat/                     # Sistema de chat
│   ├── ChatGPTInterface.tsx  # Interfaz principal
│   ├── SmartSuggestions.tsx  # Sugerencias contextuales
│   └── TypingIndicator.tsx   # Indicador de escritura
├── formulation/              # Sistema de fórmulas
│   ├── MaterialsSummaryCard.tsx
│   └── FormulaDisplay.tsx
└── base/                     # Componentes reutilizables
    ├── BaseButton.tsx
    └── BaseCard.tsx
```

### Lógica de Negocio (`/stores` + `/src`)

```
stores/
├── ai-analysis-store.ts      # Estado de análisis IA
├── chat-store.ts            # Estado del chat
├── inventory-store.ts       # Inventario
└── auth-store.ts           # Autenticación

src/service/
├── components/              # Componentes del flujo de servicio
├── hooks/                   # Hooks especializados
└── utils/                  # Utilidades del servicio
```

### Edge Functions (`/supabase/functions`)

```
supabase/functions/
├── salonier-assistant/      # IA principal (v41)
├── chat-assistant/          # Chat especializado
└── upload-photo/           # Procesamiento de imágenes
```

## Flujo Principal de la Aplicación

1. **Onboarding**: Configuración de salón, marcas preferidas, configuración regional
2. **Dashboard**: Métricas del día, servicios pendientes, alertas de stock
3. **Nuevo Servicio**:
   - Selección de cliente
   - Captura y análisis de imágenes con IA
   - Diagnóstico automatizado (nivel, reflejo, canas)
   - Color deseado y análisis de viabilidad
   - Generación de fórmula personalizada
   - Wizard de seguridad (4 pasos)
   - Finalización con cálculo de costos
4. **Chat Assistant**: Consultas técnicas en tiempo real
5. **Inventario**: Control de stock, costos y alertas

## Estado de Desarrollo

### Versión Actual: v2.0.9

- ✅ Sistema 100% IA generativa implementado
- ✅ Edge Functions optimizadas (v41) con retry logic
- ✅ Soporte multi-región (40+ países)
- ✅ Sistema offline-first completo
- ✅ Chat Assistant con GPT-4o Vision
- ✅ Corrección masiva de 89+ errores críticos (2025-08-07)
- ✅ Sistema de seguridad robusta implementada

### Métricas de Producción

- **Success Rate IA**: 98%
- **Crash Rate**: <0.1%
- **Uptime**: 99.9%
- **Auto-save Recovery**: 100%

## Innovación Técnica

### Paradigma Revolucionario

A diferencia de otras aplicaciones que usan:

- Tablas de conversión predefinidas
- Algoritmos if-then-else codificados
- Bases de datos de fórmulas limitadas

**Salonier utiliza**:

- IA que razona como un colorista experto
- Generación de fórmulas únicas para cada caso
- Comprensión visual del cabello sin reglas predefinidas
- Adaptación infinita a casos nunca antes vistos

### Ventajas Competitivas

1. **Escalabilidad del Conocimiento**: Sin límite de casos o combinaciones
2. **Personalización Extrema**: Contexto completo del cliente considerado
3. **Innovación Continua**: Mejora automática con actualizaciones de OpenAI
4. **Offline-First**: Funcionalidad completa sin conectividad

---

_Salonier representa la próxima generación de herramientas profesionales, donde la inteligencia artificial no solo asiste, sino que realmente comprende y razona como un experto humano._
