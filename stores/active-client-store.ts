import { create } from 'zustand';
import { Client } from './client-store';

interface ActiveClientStore {
  activeClient: Client | null;

  // Actions
  setActiveClient: (client: Client) => void;
  clearActiveClient: () => void;
}

export const useActiveClientStore = create<ActiveClientStore>(set => ({
  activeClient: null,

  setActiveClient: client => {
    set({ activeClient: client });
  },

  clearActiveClient: () => {
    set({ activeClient: null });
  },
}));
