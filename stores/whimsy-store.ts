/**
 * Whimsy Store - Configuración para micro-interacciones y detalles delightful
 * Permite a usuarios avanzados personalizar su experiencia
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { logger } from '@/utils/logger';

interface WhimsySettings {
  // Animation preferences
  enableBreathingAnimations: boolean;
  enableHapticFeedback: boolean;
  enableCelebrationAnimations: boolean;
  enableRippleEffects: boolean;
  enableEasterEggs: boolean;

  // Loading preferences
  enableContextualLoadingStates: boolean;
  showLoadingMessages: boolean;

  // Sound preferences (for future implementation)
  enableSoundEffects: boolean;
  soundVolume: number; // 0-100

  // Performance preferences
  reduceMotion: boolean;
  animationSpeed: 'slow' | 'normal' | 'fast';

  // User metrics (for achievements)
  totalServicesCompleted: number;
  easterEggsFound: number;
  perfectSatisfactionCount: number;
  streakDays: number;
}

interface WhimsyActions {
  updateSettings: (settings: Partial<WhimsySettings>) => void;
  resetToDefaults: () => void;
  incrementMetric: (
    metric: keyof Pick<
      WhimsySettings,
      'totalServicesCompleted' | 'easterEggsFound' | 'perfectSatisfactionCount' | 'streakDays'
    >
  ) => void;

  // Achievement checkers
  isEligibleForAchievement: (achievement: string) => boolean;
  unlockAchievement: (achievement: string) => void;

  // Quick toggles for common preferences
  toggleHaptics: () => void;
  toggleAnimations: () => void;
  toggleCelebrations: () => void;
}

type WhimsyStore = WhimsySettings & WhimsyActions;

const DEFAULT_SETTINGS: WhimsySettings = {
  // Animations enabled by default for delightful experience
  enableBreathingAnimations: true,
  enableHapticFeedback: true,
  enableCelebrationAnimations: true,
  enableRippleEffects: true,
  enableEasterEggs: true,

  // Loading states
  enableContextualLoadingStates: true,
  showLoadingMessages: true,

  // Sound (disabled by default - professional environment)
  enableSoundEffects: false,
  soundVolume: 50,

  // Performance
  reduceMotion: false,
  animationSpeed: 'normal',

  // User metrics
  totalServicesCompleted: 0,
  easterEggsFound: 0,
  perfectSatisfactionCount: 0,
  streakDays: 0,
};

const ACHIEVEMENTS = {
  firstService: {
    name: 'Primera Transformación',
    description: 'Completaste tu primer servicio',
    requirement: (metrics: WhimsySettings) => metrics.totalServicesCompleted >= 1,
  },
  perfectWeek: {
    name: 'Semana Perfecta',
    description: '7 días consecutivos con servicios',
    requirement: (metrics: WhimsySettings) => metrics.streakDays >= 7,
  },
  happyClients: {
    name: 'Clientas Radiantes',
    description: '10 servicios con satisfacción perfecta',
    requirement: (metrics: WhimsySettings) => metrics.perfectSatisfactionCount >= 10,
  },
  secretExplorer: {
    name: 'Exploradora Secreta',
    description: 'Encontraste todos los easter eggs',
    requirement: (metrics: WhimsySettings) => metrics.easterEggsFound >= 5,
  },
  colorMaster: {
    name: 'Maestra del Color',
    description: '100 servicios completados',
    requirement: (metrics: WhimsySettings) => metrics.totalServicesCompleted >= 100,
  },
};

export const useWhimsyStore = create<WhimsyStore>()(
  persist(
    (set, get) => ({
      ...DEFAULT_SETTINGS,

      updateSettings: newSettings => {
        set(state => ({ ...state, ...newSettings }));
      },

      resetToDefaults: () => {
        set(DEFAULT_SETTINGS);
      },

      incrementMetric: metric => {
        set(state => ({
          ...state,
          [metric]: state[metric] + 1,
        }));
      },

      isEligibleForAchievement: achievementKey => {
        const achievement = ACHIEVEMENTS[achievementKey as keyof typeof ACHIEVEMENTS];
        if (!achievement) return false;

        return achievement.requirement(get());
      },

      unlockAchievement: achievementKey => {
        const { isEligibleForAchievement } = get();

        if (isEligibleForAchievement(achievementKey)) {
          // Here you could show an achievement notification
          logger.debug(
            `🏆 Achievement unlocked: ${ACHIEVEMENTS[achievementKey as keyof typeof ACHIEVEMENTS].name}`
          );
        }
      },

      toggleHaptics: () => {
        set(state => ({
          ...state,
          enableHapticFeedback: !state.enableHapticFeedback,
        }));
      },

      toggleAnimations: () => {
        set(state => ({
          ...state,
          enableBreathingAnimations: !state.enableBreathingAnimations,
          enableRippleEffects: !state.enableBreathingAnimations, // Toggle both together
        }));
      },

      toggleCelebrations: () => {
        set(state => ({
          ...state,
          enableCelebrationAnimations: !state.enableCelebrationAnimations,
        }));
      },
    }),
    {
      name: 'whimsy-settings',
      storage: createJSONStorage(() => AsyncStorage),

      // Only persist settings, not computed values
      partialize: state => ({
        enableBreathingAnimations: state.enableBreathingAnimations,
        enableHapticFeedback: state.enableHapticFeedback,
        enableCelebrationAnimations: state.enableCelebrationAnimations,
        enableRippleEffects: state.enableRippleEffects,
        enableEasterEggs: state.enableEasterEggs,
        enableContextualLoadingStates: state.enableContextualLoadingStates,
        showLoadingMessages: state.showLoadingMessages,
        enableSoundEffects: state.enableSoundEffects,
        soundVolume: state.soundVolume,
        reduceMotion: state.reduceMotion,
        animationSpeed: state.animationSpeed,
        totalServicesCompleted: state.totalServicesCompleted,
        easterEggsFound: state.easterEggsFound,
        perfectSatisfactionCount: state.perfectSatisfactionCount,
        streakDays: state.streakDays,
      }),
    }
  )
);

// Convenience hooks for specific features
export const useHapticsEnabled = () => useWhimsyStore(state => state.enableHapticFeedback);
export const useAnimationsEnabled = () =>
  useWhimsyStore(state => !state.reduceMotion && state.enableBreathingAnimations);
export const useCelebrationsEnabled = () =>
  useWhimsyStore(state => state.enableCelebrationAnimations);
export const useRippleEffectsEnabled = () => useWhimsyStore(state => state.enableRippleEffects);

// Achievement helpers
export const useAchievements = () => {
  const store = useWhimsyStore();

  return {
    available: Object.keys(ACHIEVEMENTS),
    unlocked: Object.keys(ACHIEVEMENTS).filter(key => store.isEligibleForAchievement(key)),
    progress: {
      services: store.totalServicesCompleted,
      perfectRatings: store.perfectSatisfactionCount,
      streakDays: store.streakDays,
      easterEggs: store.easterEggsFound,
    },
  };
};

export default useWhimsyStore;
