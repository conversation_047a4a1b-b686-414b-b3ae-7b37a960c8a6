import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { logger } from '@/utils/logger';
import { useSyncQueueStore, generateLocalId, isLocalId } from './sync-queue-store';

// Types for formula feedback
export interface FormulaFeedback {
  id: string;
  formula_id: string;
  salon_id: string;
  user_id: string;
  service_id?: string;

  // Quick feedback
  worked_as_expected: boolean;
  rating: number; // 1-5 stars
  would_use_again: boolean;

  // Optional details
  actual_result?: string;
  adjustments_made?: string;
  hair_type?: string;
  environmental_factors?: string;

  // Metadata
  created_at: string;
  updated_at: string;

  // Local state
  is_synced: boolean;
  pending_sync: boolean;
}

export interface PendingFeedbackRequest {
  id: string;
  formula_id: string;
  service_id: string;
  client_name: string;
  formula_summary: string;
  scheduled_time: string; // ISO string for when to show
  reminder_count: number;
  is_dismissed: boolean;
  created_at: string;
}

interface FormulaFeedbackStore {
  feedbacks: FormulaFeedback[];
  pendingRequests: PendingFeedbackRequest[];

  // Actions
  addFeedback: (
    feedback: Omit<
      FormulaFeedback,
      'id' | 'created_at' | 'updated_at' | 'is_synced' | 'pending_sync'
    >
  ) => Promise<string>;
  updateFeedback: (id: string, updates: Partial<FormulaFeedback>) => Promise<void>;
  getFeedbackByFormula: (formulaId: string) => FormulaFeedback[];
  getFeedbackByService: (serviceId: string) => FormulaFeedback | undefined;

  // Pending requests management
  scheduleFeedbackRequest: (
    request: Omit<PendingFeedbackRequest, 'id' | 'reminder_count' | 'is_dismissed' | 'created_at'>
  ) => void;
  dismissFeedbackRequest: (id: string) => void;
  getActiveFeedbackRequests: () => PendingFeedbackRequest[];
  snoozeRequest: (id: string, minutes: number) => void;

  // Analytics
  getFormulaStats: (formulaId: string) => {
    total_uses: number;
    success_rate: number;
    avg_rating: number;
    would_use_again_rate: number;
  };

  // Sync management
  syncFeedbacks: () => Promise<void>;
  markAsSynced: (id: string) => void;
}

export const useFormulaFeedbackStore = create<FormulaFeedbackStore>()(
  persist(
    (set, get) => ({
      feedbacks: [],
      pendingRequests: [],

      addFeedback: async feedbackData => {
        const id = generateLocalId('feedback');
        const now = new Date().toISOString();

        const feedback: FormulaFeedback = {
          ...feedbackData,
          id,
          created_at: now,
          updated_at: now,
          is_synced: false,
          pending_sync: true,
        };

        set(state => ({
          feedbacks: [...state.feedbacks, feedback],
        }));

        // Queue for sync
        const syncQueue = useSyncQueueStore.getState();
        syncQueue.addToQueue({
          type: 'create',
          table: 'formula_feedback',
          data: {
            ...feedback,
            id: undefined, // Let DB generate real ID
            _tempId: id, // Keep temp ID for local reference
          },
        });

        logger.debug('Formula feedback added', 'FormulaFeedbackStore', {
          feedbackId: id,
          formulaId: feedbackData.formula_id,
          rating: feedbackData.rating,
        });

        return id;
      },

      updateFeedback: async (id, updates) => {
        const now = new Date().toISOString();

        set(state => ({
          feedbacks: state.feedbacks.map(feedback =>
            feedback.id === id
              ? {
                  ...feedback,
                  ...updates,
                  updated_at: now,
                  pending_sync: !feedback.is_synced, // Mark for sync if not already synced
                }
              : feedback
          ),
        }));

        // Queue for sync if it's a real (non-local) ID
        if (!isLocalId(id)) {
          const syncQueue = useSyncQueueStore.getState();
          syncQueue.addToQueue({
            type: 'update',
            table: 'formula_feedback',
            data: { id, ...updates, updated_at: now },
          });
        }

        logger.debug('Formula feedback updated', 'FormulaFeedbackStore', { feedbackId: id });
      },

      getFeedbackByFormula: formulaId => {
        return get().feedbacks.filter(feedback => feedback.formula_id === formulaId);
      },

      getFeedbackByService: serviceId => {
        return get().feedbacks.find(feedback => feedback.service_id === serviceId);
      },

      scheduleFeedbackRequest: requestData => {
        const id = generateLocalId('feedback_request');
        const now = new Date().toISOString();

        const request: PendingFeedbackRequest = {
          ...requestData,
          id,
          reminder_count: 0,
          is_dismissed: false,
          created_at: now,
        };

        set(state => ({
          pendingRequests: [...state.pendingRequests, request],
        }));

        logger.debug('Feedback request scheduled', 'FormulaFeedbackStore', {
          requestId: id,
          serviceId: requestData.service_id,
          scheduledTime: requestData.scheduled_time,
        });
      },

      dismissFeedbackRequest: id => {
        set(state => ({
          pendingRequests: state.pendingRequests.map(request =>
            request.id === id ? { ...request, is_dismissed: true } : request
          ),
        }));

        logger.debug('Feedback request dismissed', 'FormulaFeedbackStore', { requestId: id });
      },

      getActiveFeedbackRequests: () => {
        const now = new Date();
        return get().pendingRequests.filter(
          request => !request.is_dismissed && new Date(request.scheduled_time) <= now
        );
      },

      snoozeRequest: (id, minutes) => {
        const newTime = new Date(Date.now() + minutes * 60 * 1000).toISOString();

        set(state => ({
          pendingRequests: state.pendingRequests.map(request =>
            request.id === id
              ? {
                  ...request,
                  scheduled_time: newTime,
                  reminder_count: request.reminder_count + 1,
                }
              : request
          ),
        }));

        logger.debug('Feedback request snoozed', 'FormulaFeedbackStore', {
          requestId: id,
          minutes,
          newTime,
        });
      },

      getFormulaStats: formulaId => {
        const feedbacks = get().getFeedbackByFormula(formulaId);

        if (feedbacks.length === 0) {
          return {
            total_uses: 0,
            success_rate: 0,
            avg_rating: 0,
            would_use_again_rate: 0,
          };
        }

        const successCount = feedbacks.filter(f => f.worked_as_expected).length;
        const wouldUseAgainCount = feedbacks.filter(f => f.would_use_again).length;
        const avgRating = feedbacks.reduce((sum, f) => sum + f.rating, 0) / feedbacks.length;

        return {
          total_uses: feedbacks.length,
          success_rate: (successCount / feedbacks.length) * 100,
          avg_rating: Math.round(avgRating * 10) / 10, // Round to 1 decimal
          would_use_again_rate: (wouldUseAgainCount / feedbacks.length) * 100,
        };
      },

      syncFeedbacks: async () => {
        const pendingFeedbacks = get().feedbacks.filter(f => f.pending_sync && !f.is_synced);

        if (pendingFeedbacks.length === 0) return;

        logger.debug('Syncing pending feedbacks', 'FormulaFeedbackStore', {
          count: pendingFeedbacks.length,
        });

        // The sync queue will handle the actual syncing
        // This is just to trigger the process
        const syncQueue = useSyncQueueStore.getState();
        await syncQueue.processSyncQueue();
      },

      markAsSynced: id => {
        set(state => ({
          feedbacks: state.feedbacks.map(feedback =>
            feedback.id === id ? { ...feedback, is_synced: true, pending_sync: false } : feedback
          ),
        }));

        logger.debug('Feedback marked as synced', 'FormulaFeedbackStore', { feedbackId: id });
      },
    }),
    {
      name: 'formula-feedback-storage',
      storage: {
        getItem: async name => {
          const value = await AsyncStorage.getItem(name);
          return value ? JSON.parse(value) : null;
        },
        setItem: async (name, value) => {
          await AsyncStorage.setItem(name, JSON.stringify(value));
        },
        removeItem: async name => {
          await AsyncStorage.removeItem(name);
        },
      },
    }
  )
);
