/**
 * Comprehensive tests for Chat Store
 * Testing conversation management, message handling, and streaming
 */

// import AsyncStorage from '@react-native-async-storage/async-storage';
import { useChatStore, ChatMessage, ChatConversation } from '../chat-store';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '../auth-store';

// Mock dependencies
jest.mock('@/lib/supabase');
jest.mock('../auth-store');

const mockSupabase = supabase as jest.Mocked<typeof supabase>;
const mockUseAuthStore = useAuthStore as jest.MockedFunction<typeof useAuthStore>;

// Mock data
const mockUser = {
  id: 'user-123',
  salonId: 'salon-123',
  email: '<EMAIL>',
};

const mockConversation: ChatConversation = {
  id: 'conv-123',
  salonId: 'salon-123',
  userId: 'user-123',
  title: 'Test Conversation',
  status: 'active',
  createdAt: new Date(),
  updatedAt: new Date(),
  messageCount: 2,
  totalTokensUsed: 150,
  totalCostUsd: 0.003,
  isFavorite: false,
  lastMessage: 'Hello there',
};

const mockMessage: ChatMessage = {
  id: 'msg-123',
  conversationId: 'conv-123',
  role: 'user',
  content: 'Hello, I need help with hair color',
  createdAt: new Date(),
  synced: true,
  promptTokens: 50,
  totalTokens: 75,
  costUsd: 0.0015,
};

const __mockAssistantMessage: ChatMessage = {
  id: 'msg-124',
  conversationId: 'conv-123',
  role: 'assistant',
  content: "Hello! I'd be happy to help you with hair color questions.",
  createdAt: new Date(),
  synced: true,
  completionTokens: 60,
  totalTokens: 110,
  costUsd: 0.002,
};

describe('Chat Store', () => {
  beforeEach(() => {
    // Reset store state
    useChatStore.setState({
      conversations: [],
      messages: {},
      activeConversationId: null,
      isLoading: false,
      isSending: false,
      error: null,
      uploadingImages: {},
      streamingMessage: null,
      typingStatus: 'idle',
      streamingIntervalId: null,
      pendingSyncMessages: [],
      lastSync: null,
    });

    jest.clearAllMocks();

    // Setup default mocks
    mockUseAuthStore.mockReturnValue({
      user: mockUser,
    } as any);

    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: mockUser },
      error: null,
    });
  });

  afterEach(() => {
    // Clear any running intervals
    const state = useChatStore.getState();
    if (state.streamingIntervalId) {
      clearInterval(state.streamingIntervalId);
    }
    jest.restoreAllMocks();
  });

  describe('Conversation Management', () => {
    it('should load conversations successfully', async () => {
      const mockConversationsData = [
        {
          id: 'conv-123',
          salon_id: 'salon-123',
          user_id: 'user-123',
          title: 'Test Conversation',
          status: 'active',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T01:00:00Z',
          message_count: 2,
          total_tokens_used: 150,
          total_cost_usd: '0.003',
          last_message: 'Hello there',
          last_message_at: '2024-01-01T00:30:00Z',
        },
      ];

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: mockConversationsData,
              error: null,
            }),
          }),
        }),
      } as any);

      const store = useChatStore.getState();
      await store.loadConversations();

      const state = useChatStore.getState();
      expect(state.conversations).toHaveLength(1);
      expect(state.conversations[0]).toEqual(
        expect.objectContaining({
          id: 'conv-123',
          title: 'Test Conversation',
          messageCount: 2,
          totalTokensUsed: 150,
          totalCostUsd: 0.003,
        })
      );
      expect(state.isLoading).toBe(false);
    });

    it('should handle conversation loading errors', async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: null,
              error: new Error('Database error'),
            }),
          }),
        }),
      } as any);

      const store = useChatStore.getState();
      await store.loadConversations();

      const state = useChatStore.getState();
      expect(state.error).toBeInstanceOf(Error);
      expect(state.isLoading).toBe(false);
    });

    it('should create new conversation', async () => {
      const newConversationData = {
        id: 'conv-new',
        salon_id: 'salon-123',
        user_id: 'user-123',
        title: 'New Conversation',
        status: 'active',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };

      mockSupabase.from.mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: newConversationData,
              error: null,
            }),
          }),
        }),
      } as any);

      const store = useChatStore.getState();
      const result = await store.createConversation({
        title: 'New Conversation',
        contextType: 'client',
        contextId: 'client-123',
      });

      expect(result).toEqual(
        expect.objectContaining({
          id: 'conv-new',
          title: 'New Conversation',
        })
      );

      const state = useChatStore.getState();
      expect(state.conversations).toHaveLength(1);
      expect(state.activeConversationId).toBe('conv-new');
    });

    it('should update conversation', async () => {
      useChatStore.setState({ conversations: [mockConversation] });

      mockSupabase.from.mockReturnValue({
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockResolvedValue({
            data: null,
            error: null,
          }),
        }),
      } as any);

      const store = useChatStore.getState();
      await store.updateConversation('conv-123', { title: 'Updated Title' });

      const state = useChatStore.getState();
      expect(state.conversations[0].title).toBe('Updated Title');
    });

    it('should archive conversation', async () => {
      useChatStore.setState({
        conversations: [mockConversation],
        activeConversationId: 'conv-123',
      });

      mockSupabase.from.mockReturnValue({
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockResolvedValue({
            data: null,
            error: null,
          }),
        }),
      } as any);

      const store = useChatStore.getState();
      await store.archiveConversation('conv-123');

      const state = useChatStore.getState();
      expect(state.conversations).toHaveLength(0);
      expect(state.activeConversationId).toBeNull();
    });

    it('should delete conversation permanently', async () => {
      useChatStore.setState({
        conversations: [mockConversation],
        messages: { 'conv-123': [mockMessage] },
        activeConversationId: 'conv-123',
      });

      mockSupabase.from.mockReturnValue({
        delete: jest.fn().mockReturnValue({
          eq: jest.fn().mockResolvedValue({
            data: null,
            error: null,
          }),
        }),
      } as any);

      const store = useChatStore.getState();
      await store.deleteConversation('conv-123');

      const state = useChatStore.getState();
      expect(state.conversations).toHaveLength(0);
      expect(state.messages['conv-123']).toBeUndefined();
      expect(state.activeConversationId).toBeNull();
    });

    it('should toggle favorite status', async () => {
      useChatStore.setState({ conversations: [mockConversation] });

      mockSupabase.from.mockReturnValue({
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockResolvedValue({
            data: null,
            error: null,
          }),
        }),
      } as any);

      const store = useChatStore.getState();
      await store.toggleFavorite('conv-123');

      const state = useChatStore.getState();
      expect(state.conversations[0].isFavorite).toBe(true);
    });
  });

  describe('Message Management', () => {
    it('should load messages successfully', async () => {
      const mockMessagesData = [
        {
          id: 'msg-123',
          conversation_id: 'conv-123',
          role: 'user',
          content: 'Hello',
          prompt_tokens: 50,
          total_tokens: 75,
          cost_usd: '0.0015',
          created_at: '2024-01-01T00:00:00Z',
          has_attachments: false,
          metadata: {},
        },
      ];

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: mockMessagesData,
              error: null,
            }),
          }),
        }),
      } as any);

      const store = useChatStore.getState();
      await store.loadMessages('conv-123');

      const state = useChatStore.getState();
      expect(state.messages['conv-123']).toHaveLength(1);
      expect(state.messages['conv-123'][0]).toEqual(
        expect.objectContaining({
          id: 'msg-123',
          role: 'user',
          content: 'Hello',
          synced: true,
        })
      );
    });

    it('should add optimistic message', () => {
      const store = useChatStore.getState();
      const localId = store.addOptimisticMessage({
        conversationId: 'conv-123',
        role: 'user',
        content: 'Test message',
      });

      expect(localId).toMatch(/^local_/);

      const state = useChatStore.getState();
      expect(state.messages['conv-123']).toHaveLength(1);
      expect(state.messages['conv-123'][0]).toEqual(
        expect.objectContaining({
          localId,
          content: 'Test message',
          synced: false,
        })
      );
    });

    it('should update optimistic message', () => {
      const store = useChatStore.getState();
      const localId = store.addOptimisticMessage({
        conversationId: 'conv-123',
        role: 'user',
        content: 'Test message',
      });

      store.updateOptimisticMessage(localId, {
        id: 'msg-real',
        synced: true,
        promptTokens: 50,
      });

      const state = useChatStore.getState();
      expect(state.messages['conv-123'][0]).toEqual(
        expect.objectContaining({
          id: 'msg-real',
          synced: true,
          promptTokens: 50,
        })
      );
    });
  });

  describe('Message Sending', () => {
    it('should send message successfully', async () => {
      useChatStore.setState({
        conversations: [mockConversation],
        activeConversationId: 'conv-123',
      });

      const mockResponse = {
        success: true,
        content: 'AI response here',
        usage: {
          promptTokens: 50,
          completionTokens: 60,
          totalTokens: 110,
          cost: 0.002,
        },
      };

      mockSupabase.functions.invoke.mockResolvedValue({
        data: mockResponse,
        error: null,
      });

      const store = useChatStore.getState();
      await store.sendMessage('Hello AI', 'conv-123');

      const state = useChatStore.getState();
      expect(state.isSending).toBe(false);

      // Should have user message and streaming should start
      expect(state.messages['conv-123']).toHaveLength(1);
      expect(state.streamingMessage).toBeTruthy();
    });

    it('should create new conversation when none exists', async () => {
      const newConversationData = {
        id: 'conv-new',
        salon_id: 'salon-123',
        user_id: 'user-123',
        title: 'Hello AI',
        status: 'active',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };

      mockSupabase.from.mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: newConversationData,
              error: null,
            }),
          }),
        }),
      } as any);

      mockSupabase.functions.invoke.mockResolvedValue({
        data: {
          success: true,
          content: 'AI response',
        },
        error: null,
      });

      const store = useChatStore.getState();
      await store.sendMessage('Hello AI');

      const state = useChatStore.getState();
      expect(state.conversations).toHaveLength(1);
      expect(state.activeConversationId).toBe('conv-new');
    });

    it('should handle sending errors gracefully', async () => {
      useChatStore.setState({
        conversations: [mockConversation],
        activeConversationId: 'conv-123',
      });

      mockSupabase.functions.invoke.mockRejectedValue(new Error('Network error'));

      const store = useChatStore.getState();
      await store.sendMessage('Hello AI', 'conv-123');

      const state = useChatStore.getState();
      expect(state.error).toBeInstanceOf(Error);
      expect(state.isSending).toBe(false);
      expect(state.pendingSyncMessages).toHaveLength(1);
    });

    it('should handle message with attachments', async () => {
      useChatStore.setState({
        conversations: [mockConversation],
        activeConversationId: 'conv-123',
      });

      const attachments = [
        {
          type: 'image' as const,
          url: 'https://example.com/image.jpg',
          mimeType: 'image/jpeg',
        },
      ];

      mockSupabase.functions.invoke.mockResolvedValue({
        data: {
          success: true,
          content: 'I can see the image you sent...',
        },
        error: null,
      });

      const store = useChatStore.getState();
      await store.sendMessage('What do you see in this image?', 'conv-123', attachments);

      const state = useChatStore.getState();
      expect(state.messages['conv-123'][0]).toEqual(
        expect.objectContaining({
          hasAttachments: true,
          attachments,
        })
      );
    });
  });

  describe('Streaming Functionality', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should start streaming correctly', () => {
      const store = useChatStore.getState();
      store.startStreaming('conv-123', 'msg-stream');

      const state = useChatStore.getState();
      expect(state.streamingMessage).toEqual({
        conversationId: 'conv-123',
        messageId: 'msg-stream',
        content: '',
        isComplete: false,
        currentIndex: 0,
      });
      expect(state.typingStatus).toBe('thinking');
    });

    it('should update streaming content', () => {
      const store = useChatStore.getState();
      store.startStreaming('conv-123', 'msg-stream');
      store.updateStreamingContent('Hello world');

      const state = useChatStore.getState();
      expect(state.streamingMessage?.content).toBe('Hello world');
      expect(state.typingStatus).toBe('writing');
    });

    it('should complete streaming and add message', () => {
      useChatStore.setState({
        streamingMessage: {
          conversationId: 'conv-123',
          messageId: 'msg-stream',
          content: 'Complete message',
          isComplete: false,
        },
      });

      const store = useChatStore.getState();
      store.completeStreaming();

      const state = useChatStore.getState();
      expect(state.streamingMessage).toBeNull();
      expect(state.typingStatus).toBe('idle');
      expect(state.messages['conv-123']).toHaveLength(1);
      expect(state.messages['conv-123'][0].content).toBe('Complete message');
    });

    it('should set typing status', () => {
      const store = useChatStore.getState();
      store.setTypingStatus('analyzing');

      const state = useChatStore.getState();
      expect(state.typingStatus).toBe('analyzing');
    });
  });

  describe('Image Upload', () => {
    it('should upload chat image successfully', async () => {
      const mockUploadResult = {
        data: { path: 'chat/salon-123/image.jpg' },
        error: null,
      };

      const mockSignedUrlResult = {
        data: { signedUrl: 'https://signed-url.com/image.jpg' },
        error: null,
      };

      mockSupabase.storage.from.mockReturnValue({
        upload: jest.fn().mockResolvedValue(mockUploadResult),
        createSignedUrl: jest.fn().mockResolvedValue(mockSignedUrlResult),
      } as any);

      const store = useChatStore.getState();
      const result = await store.uploadChatImage('data:image/jpeg;base64,/9j/4AAQSkZJRg==');

      expect(result).toEqual(
        expect.objectContaining({
          type: 'image',
          url: 'https://signed-url.com/image.jpg',
          uploadStatus: 'completed',
        })
      );
    });

    it('should handle upload errors', async () => {
      mockSupabase.storage.from.mockReturnValue({
        upload: jest.fn().mockResolvedValue({
          data: null,
          error: new Error('Upload failed'),
        }),
      } as any);

      const store = useChatStore.getState();
      const result = await store.uploadChatImage('data:image/jpeg;base64,invalid');

      expect(result).toBeNull();

      const state = useChatStore.getState();
      expect(state.error).toBeInstanceOf(Error);
    });

    it('should update attachment status', () => {
      const messageWithAttachment = {
        ...mockMessage,
        attachments: [
          {
            type: 'image' as const,
            url: 'test-url',
            uploadStatus: 'uploading' as const,
          },
        ],
      };

      useChatStore.setState({
        messages: { 'conv-123': [messageWithAttachment] },
      });

      const store = useChatStore.getState();
      store.updateAttachmentStatus('msg-123', 0, 'completed');

      const state = useChatStore.getState();
      expect(state.messages['conv-123'][0].attachments?.[0].uploadStatus).toBe('completed');
    });
  });

  describe('Smart Title Generation', () => {
    it('should generate title for inventory queries', () => {
      const store = useChatStore.getState();
      const title = store.generateSmartTitle('¿Cuánto stock tengo de rubio 9/1?');

      expect(title).toBe('Consulta de inventario');
    });

    it('should generate title for formula queries', () => {
      const store = useChatStore.getState();
      const title = store.generateSmartTitle('¿Cómo hago una fórmula para rubio cenizo?');

      expect(title).toBe('Consulta sobre fórmula');
    });

    it('should generate title for client queries', () => {
      const store = useChatStore.getState();
      const title = store.generateSmartTitle('Mi cliente tiene el cabello dañado');

      expect(title).toBe('Consulta sobre cliente');
    });

    it('should generate title for specific colors', () => {
      const store = useChatStore.getState();

      expect(store.generateSmartTitle('Necesito un rubio perfecto')).toBe('Fórmula rubio');
      expect(store.generateSmartTitle('Cómo cubrir canas grises')).toBe('Cobertura de canas');
      expect(store.generateSmartTitle('Tono rojo intenso')).toBe('Tonos rojizos');
    });

    it('should truncate long messages', () => {
      const store = useChatStore.getState();
      const longMessage =
        'This is a very long message that should be truncated to fit within the title length limit';
      const title = store.generateSmartTitle(longMessage);

      expect(title).toBe('This is a very long message that should ...');
      expect(title.length).toBeLessThanOrEqual(43);
    });
  });

  describe('Sync and Persistence', () => {
    it('should sync pending messages', async () => {
      const pendingMessage: ChatMessage = {
        id: 'pending-123',
        localId: 'local-123',
        conversationId: 'conv-123',
        role: 'user',
        content: 'Pending message',
        createdAt: new Date(),
        synced: false,
      };

      useChatStore.setState({
        pendingSyncMessages: [pendingMessage],
        conversations: [mockConversation],
      });

      mockSupabase.functions.invoke.mockResolvedValue({
        data: { success: true, content: 'AI response' },
        error: null,
      });

      const store = useChatStore.getState();
      await store.syncPendingMessages();

      const state = useChatStore.getState();
      expect(state.pendingSyncMessages).toHaveLength(0);
      expect(state.lastSync).toBeInstanceOf(Date);
    });

    it('should handle sync errors gracefully', async () => {
      const pendingMessage: ChatMessage = {
        id: 'pending-123',
        localId: 'local-123',
        conversationId: 'conv-123',
        role: 'user',
        content: 'Pending message',
        createdAt: new Date(),
        synced: false,
      };

      useChatStore.setState({
        pendingSyncMessages: [pendingMessage],
      });

      mockSupabase.functions.invoke.mockRejectedValue(new Error('Sync failed'));

      const store = useChatStore.getState();
      await store.syncPendingMessages();

      const state = useChatStore.getState();
      expect(state.pendingSyncMessages).toHaveLength(1); // Should remain pending
    });
  });

  describe('State Management', () => {
    it('should set active conversation and load messages', () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: [],
              error: null,
            }),
          }),
        }),
      } as any);

      const store = useChatStore.getState();
      store.setActiveConversation('conv-123');

      const state = useChatStore.getState();
      expect(state.activeConversationId).toBe('conv-123');
    });

    it('should clear error', () => {
      useChatStore.setState({ error: new Error('Test error') });

      const store = useChatStore.getState();
      store.clearError();

      const state = useChatStore.getState();
      expect(state.error).toBeNull();
    });

    it('should reset store', () => {
      useChatStore.setState({
        conversations: [mockConversation],
        messages: { 'conv-123': [mockMessage] },
        activeConversationId: 'conv-123',
        error: new Error('Test'),
      });

      const store = useChatStore.getState();
      store.reset();

      const state = useChatStore.getState();
      expect(state.conversations).toEqual([]);
      expect(state.messages).toEqual({});
      expect(state.activeConversationId).toBeNull();
      expect(state.error).toBeNull();
    });
  });

  describe('Memory Leak Prevention', () => {
    it('should cleanup streaming intervals', () => {
      const mockClearInterval = jest.spyOn(global, 'clearInterval');

      useChatStore.setState({
        streamingIntervalId: 123 as any,
        streamingMessage: {
          conversationId: 'conv-123',
          messageId: 'msg-123',
          content: 'test',
          isComplete: false,
        },
      });

      const store = useChatStore.getState();
      (store as any).cleanupStreaming();

      expect(mockClearInterval).toHaveBeenCalledWith(123);

      const state = useChatStore.getState();
      expect(state.streamingIntervalId).toBeNull();
      expect(state.streamingMessage).toBeNull();
      expect(state.typingStatus).toBe('idle');

      mockClearInterval.mockRestore();
    });
  });
});
