#!/bin/bash

# Workflow Logger for Claude Code Hooks
# Logs workflow activities and provides summaries

LOG_DIR="$HOME/.claude/logs"
LOG_FILE="$LOG_DIR/workflow-$(date +%Y%m%d).log"
SUMMARY_FILE="$LOG_DIR/workflow-summary.json"

# Create log directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Function to log with timestamp
log_entry() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Function to log tool usage
log_tool_use() {
    local tool_data=$(cat)
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Extract tool name from JSON if possible
    if command -v jq >/dev/null 2>&1; then
        local tool_name=$(echo "$tool_data" | jq -r '.toolName // "unknown"' 2>/dev/null)
        log_entry "TOOL" "Tool used: $tool_name"
    else
        log_entry "TOOL" "Tool activity detected"
    fi
    
    # Pass through the input unchanged
    echo "$tool_data"
}

# Function to generate summary
generate_summary() {
    local today=$(date +%Y%m%d)
    local workflow_count=$(grep -c "WORKFLOW ORCHESTRATOR ACTIVATED" "$LOG_FILE" 2>/dev/null || echo 0)
    local tool_count=$(grep -c "\[TOOL\]" "$LOG_FILE" 2>/dev/null || echo 0)
    local error_count=$(grep -c "\[ERROR\]" "$LOG_FILE" 2>/dev/null || echo 0)
    
    # Create summary JSON
    cat > "$SUMMARY_FILE" <<EOF
{
  "date": "$(date '+%Y-%m-%d')",
  "statistics": {
    "workflows_executed": $workflow_count,
    "tools_used": $tool_count,
    "errors": $error_count
  },
  "last_updated": "$(date -Iseconds)"
}
EOF
    
    # Output summary to stderr so it doesn't interfere with hook operation
    >&2 echo "📊 Workflow Summary for $(date '+%Y-%m-%d'):"
    >&2 echo "   Workflows executed: $workflow_count"
    >&2 echo "   Tools used: $tool_count"
    >&2 echo "   Errors: $error_count"
}

# Main logic
case "${1:-log}" in
    --summary)
        generate_summary
        ;;
    --clean)
        # Clean logs older than 30 days
        find "$LOG_DIR" -name "workflow-*.log" -mtime +30 -delete
        log_entry "INFO" "Cleaned old log files"
        ;;
    *)
        # Default: log tool usage
        if [ -t 0 ]; then
            # No stdin, just log the event
            log_entry "INFO" "Workflow logger activated"
        else
            # Process stdin and log
            log_tool_use
        fi
        ;;
esac