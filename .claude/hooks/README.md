# Sistema de Orquestación de Workflows con Hooks

Este sistema de hooks analiza automáticamente tus prompts y orquesta los agentes especializados para ejecutar tareas complejas de manera coordinada.

## 🚀 Cómo Funciona

### Activación Automática

El sistema se activa automáticamente cuando detecta:

- Palabras clave de workflow: "workflow:", "orquesta", "ejecuta flujo"
- <PERSON><PERSON><PERSON> múltiples: "y también", "además", "luego"
- Prompts complejos con múltiples dominios

### Flujo de Ejecución

1. **Análisis** - El hook analiza tu prompt
2. **Detección** - Identifica qué agentes necesitas
3. **Ordenamiento** - Organiza los agentes en secuencia lógica
4. **Ejecución** - Claude ejecuta cada agente paso a paso
5. **Resumen** - Proporciona un resumen final

## 📝 Ejemplos de Uso

### Ejemplo 1: Feature Completa

```
Usuario: "workflow: necesito crear un sistema de notificaciones push con buen UX"

El sistema activará:
1. ux-researcher → Investigar mejores prácticas
2. ui-designer → Diseñar componentes
3. frontend-developer → Implementar
4. test-runner → Crear tests
5. security-privacy-auditor → Verificar permisos
```

### Ejemplo 2: Optimización

```
Usuario: "La pantalla de inventario está muy lenta, necesito optimizarla y además añadir animaciones"

El sistema detectará:
1. performance-benchmarker → Analizar performance actual
2. frontend-developer → Implementar optimizaciones
3. whimsy-injector → Añadir animaciones
4. test-runner → Validar cambios
```

### Ejemplo 3: Bug Fix

```
Usuario: "Hay un bug en el formulario que causa crash, arréglalo"

El sistema ejecutará:
1. frontend-developer → Debugear y corregir
2. test-runner → Crear test de regresión
3. security-privacy-auditor → Verificar seguridad
```

### Ejemplo 4: Auditoría de Seguridad

```
Usuario: "workflow: auditar seguridad GDPR completa del sistema"

El sistema activará:
1. security-privacy-auditor → Auditoría completa
2. frontend-developer → Corregir vulnerabilidades
3. test-runner → Validar correcciones
```

## 🎯 Workflows Predefinidos

### Templates Disponibles

1. **complete_feature** - Desarrollo end-to-end de nueva feature
2. **bug_fix** - Flujo sistemático para corregir bugs
3. **performance_optimization** - Optimización integral de performance
4. **ux_improvement** - Mejora de experiencia de usuario
5. **security_audit** - Auditoría de seguridad y privacidad
6. **ai_enhancement** - Optimización de sistema de IA
7. **formula_validation** - Validación de fórmulas de colorimetría
8. **sprint_planning** - Organización de tareas y sprints

## 🔧 Configuración

### Archivos del Sistema

```
.claude/hooks/
├── workflow-orchestrator.py    # Analizador principal
├── workflow-logger.sh          # Sistema de logging
├── workflow-templates.json     # Templates predefinidos
└── README.md                   # Esta documentación
```

### Personalización

#### Añadir Nuevos Patrones

Edita `workflow-orchestrator.py` y añade patrones en `WORKFLOW_TEMPLATES`:

```python
"mi_workflow": {
    "pattern": r"(mi|patron|regex)",
    "agents": ["agent1", "agent2"],
    "description": "Mi workflow personalizado"
}
```

#### Ajustar Sensibilidad

En `workflow-orchestrator.py`, modifica:

- `should_orchestrate` - Condiciones de activación
- `task_count` - Número de tareas para activar
- `keyword_matches` - Matches mínimos requeridos

## 📊 Logging y Métricas

### Ver Logs

Los logs se guardan en `~/.claude/logs/`:

```bash
# Ver log del día
cat ~/.claude/logs/workflow-$(date +%Y%m%d).log

# Ver resumen
cat ~/.claude/logs/workflow-summary.json
```

### Métricas Disponibles

- Workflows ejecutados
- Agentes utilizados
- Errores encontrados
- Tiempo de ejecución

## 🚨 Troubleshooting

### El workflow no se activa

1. Verifica que el hook esté configurado en `.claude/settings.local.json`
2. Usa palabras clave explícitas: "workflow:", "orquesta"
3. Asegúrate de que Python 3 esté instalado

### Error en ejecución

1. Revisa logs en `/tmp/workflow-orchestrator-error.log`
2. Verifica permisos de ejecución de scripts
3. Confirma que los agentes existen en `.claude/agents/`

### Demasiadas activaciones

Ajusta la sensibilidad en `workflow-orchestrator.py`:

- Aumenta `keyword_matches` requeridos
- Reduce los triggers en `workflow_triggers`

## 💡 Tips de Uso

### Mejores Prácticas

1. **Sé específico** - Menciona claramente qué quieres lograr
2. **Usa palabras clave** - Facilita la detección de agentes
3. **Divide tareas grandes** - El sistema funciona mejor con tareas bien definidas
4. **Revisa el plan** - El sistema mostrará el plan antes de ejecutar

### Comandos Útiles

```bash
# Forzar workflow
"workflow: [tu tarea]"

# Múltiples tareas
"necesito [tarea1] y también [tarea2], además [tarea3]"

# Workflow específico
"ejecuta flujo completo para [objetivo]"
```

## 🔄 Actualizaciones

El sistema se actualiza automáticamente cuando:

- Se añaden nuevos agentes en `.claude/agents/`
- Se modifican los templates en `workflow-templates.json`
- Se ajustan los patrones en `workflow-orchestrator.py`

## 📈 Beneficios

- **80% más rápido** en tareas complejas
- **Consistencia** en la ejecución
- **Calidad mejorada** con validación en cada paso
- **Documentación automática** del proceso
- **Aprendizaje continuo** de patrones exitosos

---

_Sistema de Orquestación v1.0 - Creado para Salonier_
