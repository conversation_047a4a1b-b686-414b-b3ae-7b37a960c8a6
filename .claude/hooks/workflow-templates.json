{"templates": {"complete_feature": {"name": "Complete Feature Development", "description": "End-to-end workflow for implementing a new feature", "triggers": ["nueva feature", "new feature", "implementar función"], "agents": [{"name": "ux-researcher", "task": "Research user needs and current pain points", "output": "user_requirements"}, {"name": "ui-designer", "task": "Design interface components based on research", "input": "user_requirements", "output": "ui_design"}, {"name": "sprint-prioritizer", "task": "Break down into manageable tasks", "input": "ui_design", "output": "sprint_plan"}, {"name": "frontend-developer", "task": "Implement the feature", "input": "ui_design", "output": "implementation"}, {"name": "whimsy-injector", "task": "Add animations and polish", "input": "implementation", "output": "polished_ui"}, {"name": "test-runner", "task": "Create and run tests", "input": "implementation", "output": "test_results"}, {"name": "performance-benchmarker", "task": "Optimize performance", "input": "polished_ui", "output": "performance_report"}, {"name": "security-privacy-auditor", "task": "Final security audit", "input": "implementation", "output": "security_report"}]}, "bug_fix": {"name": "Bug Fix Workflow", "description": "Systematic approach to fixing bugs", "triggers": ["bug", "error", "crash", "fix", "arreglar"], "agents": [{"name": "frontend-developer", "task": "Identify and fix the bug", "output": "bug_fix"}, {"name": "test-runner", "task": "Create regression tests", "input": "bug_fix", "output": "test_coverage"}, {"name": "security-privacy-auditor", "task": "Verify no security implications", "input": "bug_fix", "output": "security_check"}]}, "performance_optimization": {"name": "Performance Optimization", "description": "Comprehensive performance improvement workflow", "triggers": ["optimizar", "lento", "performance", "mejorar velocidad"], "agents": [{"name": "performance-benchmarker", "task": "Analyze current performance metrics", "output": "performance_baseline"}, {"name": "frontend-developer", "task": "Implement optimizations", "input": "performance_baseline", "output": "optimized_code"}, {"name": "test-runner", "task": "Verify optimizations don't break functionality", "input": "optimized_code", "output": "test_validation"}, {"name": "performance-benchmarker", "task": "Measure improvement and validate", "input": "optimized_code", "output": "final_metrics"}]}, "ux_improvement": {"name": "UX Improvement", "description": "User experience enhancement workflow", "triggers": ["mejorar ux", "experiencia usuario", "redise<PERSON><PERSON>"], "agents": [{"name": "ux-researcher", "task": "Analyze current UX and identify improvements", "output": "ux_analysis"}, {"name": "ui-designer", "task": "Design improved interfaces", "input": "ux_analysis", "output": "new_design"}, {"name": "whimsy-injector", "task": "Add delightful interactions", "input": "new_design", "output": "enhanced_design"}, {"name": "frontend-developer", "task": "Implement UX improvements", "input": "enhanced_design", "output": "implementation"}, {"name": "test-runner", "task": "Validate improvements", "input": "implementation", "output": "validation"}]}, "security_audit": {"name": "Security Audit", "description": "Comprehensive security and privacy review", "triggers": ["seguridad", "auditar", "gdpr", "privacidad"], "agents": [{"name": "security-privacy-auditor", "task": "Perform comprehensive security audit", "output": "audit_report"}, {"name": "frontend-developer", "task": "Fix identified vulnerabilities", "input": "audit_report", "output": "security_fixes"}, {"name": "test-runner", "task": "Validate security fixes", "input": "security_fixes", "output": "validation"}, {"name": "security-privacy-auditor", "task": "Final verification", "input": "security_fixes", "output": "final_report"}]}, "ai_enhancement": {"name": "AI System Enhancement", "description": "Optimize AI integration and performance", "triggers": ["ia", "gpt", "openai", "prompt", "edge function"], "agents": [{"name": "ai-integration-specialist", "task": "Analyze and optimize AI integration", "output": "ai_analysis"}, {"name": "colorimetry-expert", "task": "Validate domain-specific accuracy", "input": "ai_analysis", "output": "validation"}, {"name": "performance-benchmarker", "task": "Measure AI performance and costs", "input": "ai_analysis", "output": "metrics"}, {"name": "frontend-developer", "task": "Implement optimizations", "input": "ai_analysis", "output": "implementation"}, {"name": "test-runner", "task": "Test AI improvements", "input": "implementation", "output": "test_results"}]}, "formula_validation": {"name": "Hair Formula Validation", "description": "Validate and optimize hair coloring formulas", "triggers": ["f<PERSON><PERSON><PERSON>", "colorimetría", "tinte", "validar fórmula"], "agents": [{"name": "colorimetry-expert", "task": "Validate formula accuracy and safety", "output": "formula_validation"}, {"name": "ai-integration-specialist", "task": "Optimize formula generation prompts", "input": "formula_validation", "output": "optimized_prompts"}, {"name": "frontend-developer", "task": "Update formula generation logic", "input": "optimized_prompts", "output": "implementation"}, {"name": "test-runner", "task": "Validate formula generation", "input": "implementation", "output": "validation"}]}, "sprint_planning": {"name": "Sprint Planning", "description": "Organize and prioritize development tasks", "triggers": ["sprint", "planificar", "organizar tareas", "backlog"], "agents": [{"name": "sprint-prioritizer", "task": "Analyze and organize current backlog", "output": "prioritized_backlog"}, {"name": "ux-researcher", "task": "Validate priorities against user needs", "input": "prioritized_backlog", "output": "validated_priorities"}, {"name": "performance-benchmarker", "task": "Estimate performance impact of tasks", "input": "validated_priorities", "output": "performance_estimates"}]}}, "agent_dependencies": {"comment": "Defines which agents typically work well together", "synergies": [["ux-researcher", "ui-designer"], ["ui-designer", "whimsy-injector"], ["frontend-developer", "test-runner"], ["performance-benchmarker", "frontend-developer"], ["security-privacy-auditor", "test-runner"], ["ai-integration-specialist", "colorimetry-expert"], ["sprint-prioritizer", "ux-researcher"]]}, "execution_rules": {"max_agents_per_workflow": 8, "min_confidence_score": 0.3, "parallel_execution": false, "require_confirmation": false, "log_all_executions": true}}