#!/usr/bin/env python3
"""
Workflow Orchestrator Hook for Claude Code
Analyzes user prompts and orchestrates specialized agents for complex tasks
"""

import json
import sys
import re
from typing import List, Dict, Tuple
import os

# Agent capabilities and keywords
AGENT_CAPABILITIES = {
    "ux-researcher": {
        "keywords": ["ux", "usuario", "experiencia", "flujo", "usabilidad", "fricción", "journey", "investigar"],
        "description": "Investigación de experiencia de usuario",
        "typical_tasks": ["analizar flujos", "identificar problemas UX", "proponer mejoras"]
    },
    "sprint-prioritizer": {
        "keywords": ["sprint", "priorizar", "organizar", "tareas", "backlog", "planificar", "roadmap"],
        "description": "Organización ágil y priorización",
        "typical_tasks": ["organizar backlog", "crear sprints", "priorizar features"]
    },
    "ui-designer": {
        "keywords": ["diseño", "ui", "interfaz", "componente", "pantalla", "mockup", "layout", "visual"],
        "description": "Diseño de interfaces React Native",
        "typical_tasks": ["diseñar componentes", "crear mockups", "mejorar UI"]
    },
    "whimsy-injector": {
        "keywords": ["animación", "animar", "transición", "micro", "polish", "smooth", "delightful", "haptic"],
        "description": "Animaciones y micro-interacciones",
        "typical_tasks": ["añadir animaciones", "crear transiciones", "polish visual"]
    },
    "frontend-developer": {
        "keywords": ["implementar", "código", "desarrollar", "programar", "react", "typescript", "componente", "feature", "bug", "fix", "arreglar"],
        "description": "Desarrollo React Native/TypeScript",
        "typical_tasks": ["implementar features", "fix bugs", "refactorizar código"]
    },
    "test-runner": {
        "keywords": ["test", "prueba", "testing", "validar", "verificar", "coverage", "jest", "unit", "integration"],
        "description": "Testing automatizado",
        "typical_tasks": ["crear tests", "ejecutar pruebas", "mejorar coverage"]
    },
    "performance-benchmarker": {
        "keywords": ["performance", "rendimiento", "optimizar", "velocidad", "fps", "memoria", "bundle", "lento", "rápido"],
        "description": "Optimización de rendimiento",
        "typical_tasks": ["analizar performance", "optimizar velocidad", "reducir bundle"]
    },
    "ai-integration-specialist": {
        "keywords": ["ia", "ai", "gpt", "openai", "prompt", "edge function", "deno", "api", "inteligencia"],
        "description": "Integración y optimización de IA",
        "typical_tasks": ["optimizar prompts", "reducir costos API", "mejorar accuracy"]
    },
    "security-privacy-auditor": {
        "keywords": ["seguridad", "security", "privacidad", "gdpr", "ccpa", "vulnerabilidad", "auditar", "rls", "compliance"],
        "description": "Auditoría de seguridad y privacidad",
        "typical_tasks": ["auditar seguridad", "verificar GDPR", "revisar RLS"]
    },
    "colorimetry-expert": {
        "keywords": ["fórmula", "color", "tinte", "colorimetría", "cabello", "nivel", "pigmento", "oxidante", "técnica"],
        "description": "Validación técnica de colorimetría",
        "typical_tasks": ["validar fórmulas", "revisar técnicas", "verificar colorimetría"]
    }
}

# Workflow templates for common scenarios
WORKFLOW_TEMPLATES = {
    "nueva_feature": {
        "pattern": r"(crear|implementar|añadir|agregar|nueva?)\s+(feature|funcionalidad|función|característica)",
        "agents": ["ux-researcher", "ui-designer", "frontend-developer", "test-runner", "performance-benchmarker"],
        "description": "Flujo completo para nueva feature"
    },
    "bug_fix": {
        "pattern": r"(bug|error|crash|fallo|problema|arreglar|fix|corregir)",
        "agents": ["frontend-developer", "test-runner", "security-privacy-auditor"],
        "description": "Flujo para corregir bugs"
    },
    "optimizacion": {
        "pattern": r"(optimizar|mejorar|acelerar|performance|rendimiento|lento)",
        "agents": ["performance-benchmarker", "frontend-developer", "test-runner"],
        "description": "Flujo de optimización"
    },
    "ux_improvement": {
        "pattern": r"(mejorar|rediseñar|ux|experiencia|usuario|interfaz)",
        "agents": ["ux-researcher", "ui-designer", "whimsy-injector", "frontend-developer"],
        "description": "Mejora de UX/UI"
    },
    "security_audit": {
        "pattern": r"(seguridad|privacidad|gdpr|auditar|vulnerabilidad|compliance)",
        "agents": ["security-privacy-auditor", "test-runner"],
        "description": "Auditoría de seguridad"
    },
    "ai_optimization": {
        "pattern": r"(ia|gpt|openai|prompt|edge function|optimizar ia)",
        "agents": ["ai-integration-specialist", "performance-benchmarker", "test-runner"],
        "description": "Optimización de IA"
    }
}

class WorkflowOrchestrator:
    def __init__(self):
        self.detected_agents = []
        self.confidence_scores = {}
        
    def analyze_prompt(self, prompt: str) -> Tuple[List[str], str]:
        """Analyze prompt and determine which agents to use"""
        prompt_lower = prompt.lower()
        
        # Check for workflow templates first
        for template_name, template in WORKFLOW_TEMPLATES.items():
            if re.search(template["pattern"], prompt_lower):
                return template["agents"], template["description"]
        
        # If no template matches, analyze keywords for individual agents
        agent_scores = {}
        
        for agent, config in AGENT_CAPABILITIES.items():
            score = 0
            matched_keywords = []
            
            for keyword in config["keywords"]:
                if keyword in prompt_lower:
                    score += 1
                    matched_keywords.append(keyword)
            
            if score > 0:
                agent_scores[agent] = {
                    "score": score,
                    "keywords": matched_keywords
                }
        
        # Sort agents by score and select top relevant ones
        sorted_agents = sorted(agent_scores.items(), key=lambda x: x[1]["score"], reverse=True)
        selected_agents = []
        
        for agent, data in sorted_agents[:5]:  # Max 5 agents
            if data["score"] >= 1:  # Minimum threshold
                selected_agents.append(agent)
                self.confidence_scores[agent] = data
        
        # Order agents logically based on typical workflow
        ordered_agents = self.order_agents(selected_agents)
        
        return ordered_agents, "Custom workflow based on detected keywords"
    
    def order_agents(self, agents: List[str]) -> List[str]:
        """Order agents in logical execution sequence"""
        # Define execution priority
        priority_order = [
            "ux-researcher",           # Research first
            "sprint-prioritizer",       # Plan
            "ui-designer",             # Design
            "colorimetry-expert",      # Domain validation
            "ai-integration-specialist", # AI setup
            "frontend-developer",      # Implementation
            "whimsy-injector",        # Polish
            "test-runner",            # Test
            "performance-benchmarker", # Optimize
            "security-privacy-auditor" # Final audit
        ]
        
        # Sort agents based on priority
        ordered = []
        for agent in priority_order:
            if agent in agents:
                ordered.append(agent)
        
        return ordered if ordered else agents
    
    def generate_instructions(self, agents: List[str], workflow_desc: str, original_prompt: str) -> str:
        """Generate instructions for Claude to execute the workflow"""
        if not agents:
            return ""  # No modification needed
        
        instructions = []
        instructions.append("\n" + "="*60)
        instructions.append("🤖 WORKFLOW ORCHESTRATOR ACTIVATED")
        instructions.append("="*60)
        instructions.append(f"📋 Detected Task: {workflow_desc}")
        instructions.append(f"🎯 Agents Selected: {len(agents)}")
        instructions.append("")
        instructions.append("📍 EXECUTION PLAN:")
        
        for i, agent in enumerate(agents, 1):
            agent_info = AGENT_CAPABILITIES.get(agent, {})
            task_context = self.get_task_context(agent, original_prompt)
            instructions.append(f"{i}. {agent} → {task_context}")
        
        instructions.append("")
        instructions.append("⚡ EXECUTING WORKFLOW:")
        instructions.append("Please execute the following steps in sequence:")
        instructions.append("")
        
        for i, agent in enumerate(agents, 1):
            task_context = self.get_task_context(agent, original_prompt)
            instructions.append(f"Step {i}: Use the {agent} subagent to {task_context}")
            if i < len(agents):
                instructions.append(f"   ↓ Pass results to next step")
        
        instructions.append("")
        instructions.append("📊 After completing all steps, provide a summary of:")
        instructions.append("- What was accomplished")
        instructions.append("- Key findings or improvements")
        instructions.append("- Any issues encountered")
        instructions.append("- Recommended next steps")
        instructions.append("="*60)
        instructions.append("")
        
        return "\n".join(instructions)
    
    def get_task_context(self, agent: str, prompt: str) -> str:
        """Generate specific task context for each agent based on the prompt"""
        agent_tasks = {
            "ux-researcher": "analyze user experience and identify improvements",
            "sprint-prioritizer": "organize tasks and create sprint plan",
            "ui-designer": "design interface components and layouts",
            "whimsy-injector": "add animations and micro-interactions",
            "frontend-developer": "implement the solution in React Native",
            "test-runner": "create and execute comprehensive tests",
            "performance-benchmarker": "analyze and optimize performance",
            "ai-integration-specialist": "optimize AI integration and prompts",
            "security-privacy-auditor": "audit security and privacy compliance",
            "colorimetry-expert": "validate hair coloring formulas and techniques"
        }
        
        base_task = agent_tasks.get(agent, "perform specialized analysis")
        
        # Customize based on detected keywords
        if agent in self.confidence_scores:
            keywords = self.confidence_scores[agent].get("keywords", [])
            if keywords:
                return f"{base_task} (focus on: {', '.join(keywords[:3])})"
        
        return base_task

def main():
    """Main hook entry point"""
    try:
        # Read input from stdin
        input_data = json.load(sys.stdin)
        
        # Extract the user's prompt
        prompt = input_data.get("userPrompt", "")
        
        if not prompt:
            # No prompt to analyze
            sys.exit(0)
        
        # Skip if this is a workflow command itself (avoid recursion)
        if prompt.strip().startswith("/workflow"):
            sys.exit(0)
        
        # Check for workflow trigger keywords
        workflow_triggers = [
            "workflow:", "orquesta", "ejecuta flujo", "ejecutar flujo",
            "multiple agents", "varios agentes", "complete flow", "flujo completo"
        ]
        
        # Only activate on explicit workflow requests or complex tasks
        should_orchestrate = any(trigger in prompt.lower() for trigger in workflow_triggers)
        
        # Also activate for complex prompts (multiple tasks detected)
        if not should_orchestrate:
            # Count potential task indicators
            task_indicators = ["y también", "además", "luego", "después", "and also", "then", "plus"]
            task_count = sum(1 for indicator in task_indicators if indicator in prompt.lower())
            should_orchestrate = task_count >= 1
        
        if not should_orchestrate:
            # Check if prompt is complex enough (long and contains multiple agent keywords)
            words = prompt.lower().split()
            if len(words) > 20:  # Reasonably complex prompt
                keyword_matches = 0
                for agent_config in AGENT_CAPABILITIES.values():
                    for keyword in agent_config["keywords"]:
                        if keyword in prompt.lower():
                            keyword_matches += 1
                            if keyword_matches >= 3:  # Multiple domains detected
                                should_orchestrate = True
                                break
                    if should_orchestrate:
                        break
        
        if not should_orchestrate:
            sys.exit(0)
        
        # Initialize orchestrator
        orchestrator = WorkflowOrchestrator()
        
        # Analyze the prompt
        agents, workflow_desc = orchestrator.analyze_prompt(prompt)
        
        if not agents:
            # No agents detected, let Claude handle normally
            sys.exit(0)
        
        # Generate workflow instructions
        instructions = orchestrator.generate_instructions(agents, workflow_desc, prompt)
        
        # Output the enhanced prompt
        output = {
            "userPrompt": prompt + "\n" + instructions
        }
        
        # Write to stdout
        json.dump(output, sys.stdout)
        
    except Exception as e:
        # Log error but don't block the prompt
        error_log = f"/tmp/workflow-orchestrator-error.log"
        with open(error_log, "a") as f:
            f.write(f"Error: {str(e)}\n")
        sys.exit(0)

if __name__ == "__main__":
    main()