#!/bin/bash

# Update Project Analysis Hook for Claude Code
# This hook updates the project analysis document with current project state
# It can be triggered manually or by Claude Code hooks

PROJECT_DIR="${CLAUDE_PROJECT_DIR:-$(pwd)}"
ANALYSIS_FILE="$PROJECT_DIR/.claude/project-analysis.md"
CLAUDE_MD="$PROJECT_DIR/CLAUDE.md"
TODO_MD="$PROJECT_DIR/todo.md"
PLANNING_MD="$PROJECT_DIR/planning.md"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}🔄 Updating Project Analysis...${NC}"

# Function to count files by extension
count_files() {
    local extension=$1
    find "$PROJECT_DIR" -name "*.$extension" -not -path "*/node_modules/*" -not -path "*/.git/*" | wc -l
}

# Function to count lines of code
count_lines() {
    local extension=$1
    find "$PROJECT_DIR" -name "*.$extension" -not -path "*/node_modules/*" -not -path "*/.git/*" -exec wc -l {} + | tail -1 | awk '{print $1}'
}

# Get current date
CURRENT_DATE=$(date +"%Y-%m-%d")

# Count various metrics
TOTAL_TS_FILES=$(count_files "ts")
TOTAL_TSX_FILES=$(count_files "tsx")
TOTAL_FILES=$((TOTAL_TS_FILES + TOTAL_TSX_FILES))

# Count lines of code (approximate)
TS_LINES=$(count_lines "ts" 2>/dev/null || echo "0")
TSX_LINES=$(count_lines "tsx" 2>/dev/null || echo "0")
TOTAL_LINES=$((TS_LINES + TSX_LINES))

# Count migrations
MIGRATIONS=$(ls -1 "$PROJECT_DIR/supabase/migrations"/*.sql 2>/dev/null | wc -l)

# Count Edge Functions
EDGE_FUNCTIONS=$(ls -d "$PROJECT_DIR/supabase/functions"/*/ 2>/dev/null | grep -v _shared | wc -l)

# Count agents if directory exists
AGENTS=0
if [ -d "$PROJECT_DIR/.claude/agents" ]; then
    AGENTS=$(ls -1 "$PROJECT_DIR/.claude/agents"/*.md 2>/dev/null | wc -l)
fi

# Check for TypeScript errors
TS_ERRORS="Unknown"
if command -v npm &> /dev/null; then
    cd "$PROJECT_DIR"
    TS_OUTPUT=$(npm run type-check 2>&1 || true)
    if echo "$TS_OUTPUT" | grep -q "error TS"; then
        TS_ERRORS=$(echo "$TS_OUTPUT" | grep -c "error TS" || echo "0")
    else
        TS_ERRORS="0"
    fi
fi

# Get latest git commit info
if [ -d "$PROJECT_DIR/.git" ]; then
    LATEST_COMMIT=$(git log -1 --pretty=format:"%h - %s" 2>/dev/null || echo "No git history")
    BRANCH=$(git branch --show-current 2>/dev/null || echo "unknown")
else
    LATEST_COMMIT="No git repository"
    BRANCH="N/A"
fi

# Update specific sections in the analysis file if it exists
if [ -f "$ANALYSIS_FILE" ]; then
    # Create a temporary file with updated metrics
    TEMP_FILE=$(mktemp)
    
    # Read the existing file and update metrics
    while IFS= read -r line; do
        case "$line" in
            "**Last Updated**: "*)
                echo "**Last Updated**: $CURRENT_DATE"
                ;;
            "- **Total Files**: "*)
                echo "- **Total Files**: $TOTAL_FILES TypeScript/TSX files"
                ;;
            "- **Lines of Code**: "*)
                echo "- **Lines of Code**: ~$TOTAL_LINES"
                ;;
            "- **Migrations**: "*)
                echo "- **Migrations**: $MIGRATIONS"
                ;;
            "- **Edge Functions**: "*)
                echo "- **Edge Functions**: $EDGE_FUNCTIONS active"
                ;;
            "- **TypeScript Errors**: "*)
                echo "- **TypeScript Errors**: $TS_ERRORS"
                ;;
            *)
                echo "$line"
                ;;
        esac
    done < "$ANALYSIS_FILE" > "$TEMP_FILE"
    
    # Replace the original file
    mv "$TEMP_FILE" "$ANALYSIS_FILE"
    
    echo -e "${GREEN}✅ Project Analysis updated successfully${NC}"
else
    echo -e "${YELLOW}⚠️  Project Analysis file not found at $ANALYSIS_FILE${NC}"
    echo -e "${YELLOW}   Run this script again after creating the file${NC}"
fi

# Output summary
echo ""
echo -e "${GREEN}📊 Project Metrics Summary:${NC}"
echo "   - TypeScript Files: $TOTAL_FILES"
echo "   - Lines of Code: ~$TOTAL_LINES"
echo "   - Migrations: $MIGRATIONS"
echo "   - Edge Functions: $EDGE_FUNCTIONS"
echo "   - Agents: $AGENTS"
echo "   - TypeScript Errors: $TS_ERRORS"
echo "   - Current Branch: $BRANCH"
echo "   - Latest Commit: $LATEST_COMMIT"

# Check for critical issues
if [ "$TS_ERRORS" != "0" ] && [ "$TS_ERRORS" != "Unknown" ]; then
    echo ""
    echo -e "${RED}⚠️  Warning: TypeScript compilation errors detected!${NC}"
    echo -e "${RED}   Run 'npm run type-check' for details${NC}"
fi

# Suggest next steps
echo ""
echo -e "${GREEN}💡 Next Steps:${NC}"
echo "   1. Review $TODO_MD for pending tasks"
echo "   2. Check $PLANNING_MD for architectural decisions"
echo "   3. Run 'npm test' to verify all tests pass"

exit 0