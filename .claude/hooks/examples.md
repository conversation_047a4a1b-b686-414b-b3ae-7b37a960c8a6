# Ejemplos de Uso del Sistema de Orquestación

## 🎯 Casos de Uso Reales para Salonier

### 1. Implementar Sistema de Reservas Online

```
Prompt: "workflow: necesito implementar un sistema completo de reservas online para los clientes del salón"

Activará:
1. ux-researcher → Investigar flujos de reserva exitosos
2. ui-designer → Diseñar interfaz de calendario y reservas
3. sprint-prioritizer → Dividir en tareas manejables
4. frontend-developer → Implementar componentes
5. ai-integration-specialist → Integrar sugerencias inteligentes
6. test-runner → Crear tests exhaustivos
7. security-privacy-auditor → Verificar manejo seguro de datos
```

### 2. Optimizar <PERSON> de Imágenes con IA

```
Prompt: "El análisis de imágenes tarda mucho y además los prompts no son precisos para fórmulas"

Detectará y ejecutará:
1. performance-benchmarker → Medir latencia actual
2. ai-integration-specialist → Optimizar prompts y reducir tokens
3. colorimetry-expert → Validar precisión técnica
4. frontend-developer → Implementar optimizaciones
5. test-runner → <PERSON><PERSON><PERSON> mejoras
```

### 3. Mejorar UX del Chat Assistant

```
Prompt: "workflow: mejorar la experiencia del chat assistant con mejor UI y animaciones"

Ejecutará:
1. ux-researcher → Analizar uso actual del chat
2. ui-designer → Rediseñar interfaz de chat
3. whimsy-injector → Añadir animaciones de typing y transiciones
4. frontend-developer → Implementar cambios
5. performance-benchmarker → Verificar que no afecte performance
```

### 4. Bug Crítico en Inventario

```
Prompt: "Hay un bug crítico en el inventario que duplica productos, arréglalo urgente"

Activará inmediatamente:
1. frontend-developer → Identificar y corregir el bug
2. test-runner → Crear test de regresión
3. security-privacy-auditor → Verificar integridad de datos
```

### 5. Auditoría GDPR Completa

```
Prompt: "workflow: necesito una auditoría completa de GDPR y privacidad antes del lanzamiento en Europa"

Ejecutará:
1. security-privacy-auditor → Auditoría exhaustiva GDPR
2. frontend-developer → Implementar consentimientos y controles
3. test-runner → Validar cumplimiento
4. security-privacy-auditor → Verificación final y certificación
```

### 6. Nueva Feature: Historial Visual de Transformaciones

```
Prompt: "workflow: crear feature para mostrar historial visual de transformaciones del cliente con fotos antes/después"

Orquestará:
1. ux-researcher → Investigar privacidad y mejores prácticas
2. security-privacy-auditor → Definir requerimientos de privacidad
3. ui-designer → Diseñar galería temporal y controles
4. frontend-developer → Implementar con anonimización
5. whimsy-injector → Añadir transiciones elegantes
6. test-runner → Tests incluyendo edge cases de privacidad
7. performance-benchmarker → Optimizar carga de imágenes
```

### 7. Optimización de Fórmulas IA

```
Prompt: "Las fórmulas generadas no consideran bien los pigmentos subyacentes"

Detectará necesidad de:
1. colorimetry-expert → Revisar reglas de colorimetría
2. ai-integration-specialist → Mejorar prompts con reglas técnicas
3. frontend-developer → Actualizar lógica de validación
4. test-runner → Crear suite de tests de fórmulas
```

### 8. Sprint Planning para Q2

```
Prompt: "workflow: organizar todas las tareas pendientes en sprints para Q2"

Activará:
1. sprint-prioritizer → Analizar backlog y priorizar
2. ux-researcher → Validar prioridades con necesidades de usuarios
3. performance-benchmarker → Estimar impacto en performance
```

## 💡 Tips para Mejores Resultados

### Palabras Clave que Activan Agentes Específicos

| Agente                    | Palabras Clave                                             |
| ------------------------- | ---------------------------------------------------------- |
| ux-researcher             | "experiencia", "usuario", "flujo", "investigar UX"         |
| ui-designer               | "diseñar", "interfaz", "mockup", "UI"                      |
| whimsy-injector           | "animación", "transición", "polish", "micro-interacciones" |
| frontend-developer        | "implementar", "bug", "código", "desarrollar"              |
| test-runner               | "test", "prueba", "validar", "coverage"                    |
| performance-benchmarker   | "lento", "optimizar", "performance", "fps"                 |
| ai-integration-specialist | "IA", "GPT", "prompt", "OpenAI"                            |
| security-privacy-auditor  | "seguridad", "GDPR", "privacidad", "auditar"               |
| colorimetry-expert        | "fórmula", "tinte", "color", "pigmento"                    |
| sprint-prioritizer        | "sprint", "organizar", "priorizar", "backlog"              |

### Formatos de Prompt Efectivos

#### Formato Explícito

```
"workflow: [descripción clara de la tarea]"
```

#### Formato Multi-tarea

```
"necesito [tarea1] y también [tarea2], además [tarea3]"
```

#### Formato Contextual

```
"[contexto del problema]. [qué necesitas]. [resultado esperado]"
```

### Ejemplos de Prompts que NO Activarán el Sistema

Estos prompts son demasiado simples y se manejarán normalmente:

- "¿Cómo funciona el inventario?"
- "Muéstrame el código de ClientCard"
- "¿Qué hace esta función?"
- "Explica el flujo de autenticación"

### Cómo Forzar la Activación

Si quieres asegurarte de que el sistema se active, usa:

- `"workflow:"` al inicio
- `"orquesta"` o `"ejecuta flujo"`
- Menciona múltiples dominios/agentes
- Usa conectores como "y también", "además"

## 📊 Métricas de Éxito

Después de usar el sistema, deberías ver:

- ⏱️ **50-80% reducción** en tiempo de desarrollo
- ✅ **Mayor cobertura** de casos edge
- 🔒 **Mejor seguridad** con auditorías automáticas
- 🎨 **UX mejorada** con investigación sistemática
- 📈 **Performance optimizado** con benchmarking continuo

---

_Estos ejemplos están basados en casos de uso reales de Salonier y se actualizarán según se descubran nuevos patrones efectivos._
