# Subagentes Especializados para Salonier

Este directorio contiene 10 subagentes especializados diseñados para acelerar el desarrollo y mejorar la calidad de la aplicación Salonier.

## 🚀 Inicio Rápido

Para usar un subagente, simplemente menciona su nombre o describe una tarea que coincida con su especialidad:

```bash
# Ejemplos de invocación
> Use the ux-researcher subagent to analyze the client onboarding flow
> Have the security-privacy-auditor review our RLS policies
> Ask the colorimetry-expert to validate this hair formula
```

## 📋 Lista de Subagentes

### 1. 🔍 UX Researcher

**Especialidad**: Investigación de experiencia de usuario  
**Cuándo usar**:

- Analizar flujos de usuario problemáticos
- Identificar puntos de fricción en la app
- Proponer mejoras de usabilidad

**Ejemplo**:

```bash
> Use the ux-researcher to identify friction points in the service creation flow
```

### 2. 📅 Sprint Prioritizer

**Especialidad**: Organización ágil y priorización de tareas  
**Cuándo usar**:

- Organizar el backlog en sprints manejables
- Priorizar features por valor vs esfuerzo
- Planificar releases

**Ejemplo**:

```bash
> Use the sprint-prioritizer to organize todo.md into 2-week sprints
```

### 3. 🎨 UI Designer

**Especialidad**: Diseño de interfaces React Native  
**Cuándo usar**:

- Crear nuevos componentes UI
- Mejorar la consistencia visual
- Implementar sistema de diseño

**Ejemplo**:

```bash
> Use the ui-designer to create a new loading state component
```

### 4. ✨ Whimsy Injector

**Especialidad**: Animaciones y micro-interacciones  
**Cuándo usar**:

- Añadir animaciones fluidas
- Implementar haptic feedback
- Crear transiciones delightful

**Ejemplo**:

```bash
> Use the whimsy-injector to add smooth animations to the client list
```

### 5. 💻 Front-End Developer

**Especialidad**: Implementación React Native/TypeScript  
**Cuándo usar**:

- Implementar nuevas features
- Refactorizar código existente
- Optimizar componentes

**Ejemplo**:

```bash
> Use the frontend-developer to implement offline sync for services
```

### 6. 🧪 Test Runner

**Especialidad**: Testing automatizado  
**Cuándo usar**:

- Crear tests para nuevas features
- Corregir tests fallidos
- Mejorar coverage

**Ejemplo**:

```bash
> Use the test-runner to create tests for the AI analysis store
```

### 7. 📊 Performance Benchmarker

**Especialidad**: Optimización de rendimiento  
**Cuándo usar**:

- Analizar y mejorar FPS
- Reducir bundle size
- Optimizar tiempos de carga

**Ejemplo**:

```bash
> Use the performance-benchmarker to analyze why the inventory screen is slow
```

### 8. 🤖 AI Integration Specialist

**Especialidad**: OpenAI GPT-4 y Edge Functions  
**Cuándo usar**:

- Optimizar prompts de IA
- Reducir costos de API
- Mejorar accuracy de respuestas

**Ejemplo**:

```bash
> Use the ai-integration-specialist to optimize the hair diagnosis prompt
```

### 9. 🔒 Security & Privacy Auditor

**Especialidad**: Seguridad y compliance GDPR/CCPA  
**Cuándo usar**:

- Auditar políticas de seguridad
- Revisar manejo de datos sensibles
- Verificar compliance legal

**Ejemplo**:

```bash
> Use the security-privacy-auditor to review our image anonymization system
```

### 10. 🎯 Colorimetry Expert

**Especialidad**: Validación técnica de colorimetría  
**Cuándo usar**:

- Validar fórmulas generadas por IA
- Revisar terminología profesional
- Asegurar precisión técnica

**Ejemplo**:

```bash
> Use the colorimetry-expert to validate the formula generation logic
```

## 🔧 Configuración Técnica

Cada subagente tiene:

- **Prompt especializado**: Instrucciones detalladas para su dominio
- **Herramientas específicas**: Solo acceso a tools necesarias
- **Contexto propio**: No contamina la conversación principal
- **Mejores prácticas**: Incluye standards de la industria

## 💡 Tips de Uso

### Uso Proactivo

Algunos subagentes están marcados para uso proactivo:

- **Test Runner**: Se activa después de implementar features
- **Security Auditor**: Revisa automáticamente cambios sensibles
- **Performance Benchmarker**: Analiza métricas regularmente

### Combinación de Subagentes

Puedes encadenar múltiples subagentes:

```bash
> First use the ux-researcher to analyze the flow, then use the ui-designer to create mockups
```

### Delegación Automática

Claude Code delegará automáticamente tareas cuando detecte que coinciden con la especialidad de un subagente.

## 📈 Métricas de Impacto

Beneficios esperados con el uso de subagentes:

- **40% más rápido**: Desarrollo acelerado con especialización
- **30% menos bugs**: Validación proactiva de código
- **50% mejor UX**: Investigación dedicada de usuarios
- **25% reducción costos IA**: Optimización de prompts
- **100% compliance**: Auditorías automáticas de seguridad

## 🆘 Soporte

Si un subagente no funciona como esperas:

1. Verifica que el nombre sea exacto
2. Asegúrate de que la tarea coincida con su especialidad
3. Proporciona contexto suficiente en tu solicitud

## 🔄 Actualizaciones

Los subagentes se actualizan regularmente para:

- Incorporar nuevas mejores prácticas
- Ajustar a cambios en el stack tecnológico
- Mejorar precisión y eficiencia
- Añadir nuevas capacidades

---

_Última actualización: 2025-08-03_
