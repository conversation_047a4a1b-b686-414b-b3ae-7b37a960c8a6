# Contexto principal para Claude Code

Usa el comando `tree` para entender la estructura del proyecto.

Empieza leyendo el archivo CLAUDE.md, si existe, para entender el proyecto.

Lee el archivo README.md para obtener una comprensión del proyecto.

Lee los archivos clave en el directorio src/ o en el directorio raíz.

IMPORTANTE: Usa Serena para buscar en la base de código. Si obtienes algún error usando <PERSON>, vuelve a intentarlo con diferentes herramientas de Serena.

Explícame de nuevo:

- Estructura del proyecto
- Propósito y objetivos del proyecto
- Archivos clave y sus propósitos
- Cualquier dependencia importante
- Cualquier archivo de configuración importante
