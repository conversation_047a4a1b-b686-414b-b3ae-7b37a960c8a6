Flujo de Trabajo para el Análisis de Proyecto
Analiza el proyecto actual siguiendo estos pasos:

Revisa toda la base de código y explica el producto de forma sencilla en una sola página.

Señala los archivos y funcionalidades clave.

Escribe esta descripción en un archivo llamado PROJECT_DESCRIPTION.md.

Usando ese archivo como contexto, haz una lluvia de ideas con 10 nuevas funcionalidades simples para mejorar el proyecto (buscando inspiración en la web).

Escribe las ideas de funcionalidades en un archivo llamado FEATURE_IDEAS.md.

Sé minucioso pero conciso en tu análisis.
