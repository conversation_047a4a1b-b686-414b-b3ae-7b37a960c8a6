# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🚀 Essential Commands

### Development

```bash
# Mobile development (primary)
npm run mobile          # Expo with LAN (stable)
npm run mobile:tunnel   # Expo with tunnel (for remote testing)
npm run ios            # iOS Simulator
npm run android        # Android Emulator

# Web development (experimental)
npm run start-web      # Web version with Rork tunnel

# Testing & Quality
npm test               # Run all tests
npm run test:watch     # Watch mode for TDD
npm run lint           # Check for linting errors
npm run lint:fix       # Auto-fix linting errors
npm run format         # Format all files with Prettier
npm run code-quality   # Full quality check (lint + format)
```

### Database & Edge Functions

```bash
# Supabase Edge Functions (use deployment-engineer agent for complex deployments)
npx supabase functions deploy [function-name]

# Database migrations (use database-architect agent for schema changes)
npx supabase db push        # Apply pending migrations
npx supabase db reset       # Reset database to initial state

# Check Supabase status
npx supabase status         # View project connection info
```

## 🏗️ Architecture Overview

### Core Paradigm: 100% AI-Powered

- **NO traditional algorithms** - Every formula is uniquely generated by GPT-4o
- **Contextual reasoning** - AI analyzes each case individually
- **Continuous improvement** - Automatically benefits from OpenAI model updates

### Technical Architecture

```
Frontend (React Native + Expo)
    ↓
Zustand Stores (Offline-First State)
    ↓
Supabase Edge Functions (AI Processing)
    ↓
PostgreSQL with RLS (Multi-tenant Data)
    ↓
OpenAI GPT-4o (Vision + Text Generation)
```

### Key Design Patterns

#### Offline-First with Optimistic UI

- All operations work offline using Zustand stores
- UI updates immediately, syncs in background
- Queue system for pending operations
- Conflict resolution via last-write-wins

#### Multi-Tenant Architecture

- Row Level Security (RLS) ensures complete data isolation
- Each salon has dedicated `salon_id` in all tables
- No cross-salon data access possible

#### AI Integration Pattern

```typescript
// 1. Capture data locally (offline)
// 2. Update UI optimistically
// 3. Queue for AI processing
// 4. Call Edge Function when online
// 5. Update with AI results
// 6. Handle failures gracefully
```

## 📁 Project Structure & Key Files

### Core Business Logic

- `stores/` - Zustand stores for offline-first state management
  - `auth-store.ts` - Authentication and user management
  - `inventory-store.ts` - Product inventory management
  - `salon-config-store.ts` - Salon configuration and settings
  - `service-store.ts` - Service workflow state

### AI & Processing

- `supabase/functions/` - Edge Functions for AI processing
  - `analyze-hair/` - Hair diagnosis via GPT-4o Vision
  - `generate-formula/` - Formula generation with chemical validation
  - `chat-assistant/` - Conversational AI assistant

- `utils/ai/` - Client-side AI utilities
  - `ai-router.ts` - Intelligent model selection (3.5/4o-mini/4o)
  - `ai-cache-system.ts` - Response caching with TTL
  - `chemical-validator.ts` - Safety validation for formulas

### UI Components Architecture

- `components/` - Organized by domain
  - `inventory/` - Inventory management components
  - `ai/` - AI-related UI (confidence indicators, explanations)
  - `chat/` - Chat interface components
  - `formulation/` - Formula creation and display
  - `ui/` - Reusable UI primitives

### Critical Flows

1. **Service Flow** (`app/service/`)
   - `DiagnosisStep` → `DesiredColorStep` → `FormulationStep` → `CompletionStep`
   - Each step works offline, syncs when possible

2. **Inventory System** (`components/inventory/`)
   - Structured products: brand → line → type → shade
   - Intelligent matching: AI output ↔ inventory
   - Consumption tracking with stock validation

## 🔑 Critical Implementation Details

### AI Prompt Optimization

- Prompts compressed from 2,087 to <300 chars for efficiency
- Template selection based on complexity
- JSON mode for structured output
- Fallback responses for reliability

### Chemical Validation System

- Detects underlying pigments in colored hair
- Validates product compatibility matrix
- Auto-suggests neutralization for unwanted tones
- Safety alerts for dangerous combinations

### Performance Targets

- **AI Latency**: <3s for diagnosis
- **Cache Hit Rate**: >40% for common cases
- **Success Rate**: >95% for formula generation
- **Crash Rate**: <0.1% in production

## 🛠️ Development Workflow

### Before Starting Work

1. Read `planning.md` - Current project state and vision
2. Check `todo.md` - Active tasks organized by sprints
3. Review recent commits: `git log --oneline -10`

### Making Changes

1. **KISS Principle** - Keep changes minimal (<50 lines when possible)
2. **Test First** - Write/run tests before committing
3. **Offline First** - Ensure functionality works without internet
4. **Update todo.md** - Mark completed tasks immediately

### Before Committing

```bash
npm run lint:fix        # Fix linting issues
npm test               # Ensure tests pass
npm run code-quality   # Final quality check
```

## 🤖 Specialized Agent Usage

### 🏗️ Infrastructure & DevOps Agents

**database-architect** - Database schema and optimization

- Schema changes, migrations, RLS policies
- Query optimization (>500ms queries)
- Use all MCP Supabase tools for analysis
- PROACTIVELY use for deployments and performance reviews

**deployment-engineer** - DevOps and production releases

- Edge Function deployments with zero-downtime
- Database migrations and version management
- CI/CD pipeline setup and rollbacks
- MUST BE USED before ANY deployment

**debug-specialist** - Bug hunting and root cause analysis

- Production errors, crashes, test failures
- Systematic debugging with stack trace analysis
- Access to logs and diagnostic tools
- PROACTIVELY use for error investigation

**security-privacy-auditor** - Security and compliance

- Code security reviews, RLS policies
- GDPR/CCPA compliance auditing
- Data anonymization and privacy controls
- Use before releases and when handling sensitive data

**offline-sync-specialist** - Offline-first architecture

- Data synchronization and conflict resolution
- Queue management for offline operations
- Optimistic UI and sync issue debugging
- Use for sync conflicts and offline features

**data-migration-specialist** - Zero-downtime data operations

- Database schema evolution and data transformations
- Multi-tenant migration strategies
- Large-scale data operations
- MUST BE USED before production migrations

### 🎨 Frontend & UX Agents

**frontend-developer** - React Native implementation

- Complete feature implementation with clean code
- Expo, Zustand, and offline-first patterns
- PROACTIVELY use for new features and refactoring

**ui-designer** - Interface design and components

- React Native UI components and design systems
- Material Design and iOS HIG compliance
- PROACTIVELY use for new screens and UI redesigns

**whimsy-injector** - Micro-interactions and polish

- Smooth animations and haptic feedback
- Delightful UI moments without performance impact
- Use when base functionality is complete

**ux-researcher** - User experience analysis

- User flow analysis and friction identification
- Mobile app UX research and improvements
- PROACTIVELY use for new features and usability issues

**performance-benchmarker** - Performance optimization

- React Native performance metrics and bottlenecks
- FPS, memory, and load time optimization
- PROACTIVELY use when detecting slowness

### 🧠 AI & Business Logic Agents

**ai-integration-specialist** - OpenAI optimization

- GPT-4 Vision integration and prompt optimization
- API cost reduction and accuracy improvements
- PROACTIVELY use when working with AI features

**colorimetry-expert** - Hair coloration expertise

- Chemical formulation validation and terminology
- AI-generated formula accuracy verification
- PROACTIVELY use when reviewing prompts or formulas

**product-ceo** - Strategic business decisions

- Feature prioritization and unit economics
- Pricing strategy and go-to-market planning
- Use every Monday for business reviews

### 🧪 Quality & Testing Agents

**test-runner** - Automated testing

- Unit, integration, and E2E test execution
- Test coverage improvement (target >80%)
- PROACTIVELY use after implementing new features

**sprint-prioritizer** - Task organization

- Sprint planning and task prioritization
- Business value and technical dependency analysis
- PROACTIVELY use weekly or when >10 tasks accumulate

### 💡 Usage Examples

```bash
# Deploy with safety checks
Task: Use deployment-engineer to deploy salonier-assistant Edge Function

# Optimize slow database queries
Task: Use database-architect to analyze and optimize products table queries

# Fix production bug
Task: Use debug-specialist to investigate crash reports in service flow

# Add micro-interactions
Task: Use whimsy-injector to add haptic feedback to button interactions

# Strategic feature decision
Task: Use product-ceo to evaluate ROI of new AI diagnosis feature
```

## 🔌 MCP Integrations Available

### 🗄️ Supabase MCP - Database Operations

**Core Functions:**

- `mcp__supabase__list_tables` - Inspect database schema
- `mcp__supabase__execute_sql` - Run SQL queries safely
- `mcp__supabase__apply_migration` - Apply schema changes
- `mcp__supabase__get_logs` - Debug Edge Functions
- `mcp__supabase__generate_typescript_types` - Update type definitions

**Edge Functions:**

- `mcp__supabase__list_edge_functions` - List deployed functions
- `mcp__supabase__deploy_edge_function` - Deploy with validation
- `mcp__supabase__get_advisors` - Security and performance recommendations

**Branching & Development:**

- `mcp__supabase__create_branch` - Create development branches
- `mcp__supabase__merge_branch` - Merge to production
- `mcp__supabase__list_branches` - Manage development workflow

### 📚 Context7 MCP - Documentation Access

**Library Documentation:**

- `mcp__context7__resolve_library_id` - Find correct library references
- `mcp__context7__get_library_docs` - Get up-to-date documentation
- Use for React Native, Expo, Zustand, and other dependencies

```bash
# Get React Native documentation
mcp__context7__resolve_library_id: "react-native"
mcp__context7__get_library_docs: "/facebook/react-native"
```

### 🔍 Serena MCP - Code Analysis & Memory

**Code Navigation:**

- `mcp__serena__find_symbol` - Locate functions, classes, components
- `mcp__serena__find_referencing_symbols` - Find usage references
- `mcp__serena__get_symbols_overview` - Understand file structure

**Code Modification:**

- `mcp__serena__replace_symbol_body` - Update function implementations
- `mcp__serena__insert_after_symbol` - Add new code after existing
- `mcp__serena__search_for_pattern` - Pattern-based code search

**Memory Management:**

- `mcp__serena__write_memory` - Store project knowledge
- `mcp__serena__read_memory` - Retrieve project context
- `mcp__serena__list_memories` - Browse available knowledge

**Project Analysis:**

- `mcp__serena__check_onboarding_performed` - Verify project setup
- `mcp__serena__think_about_collected_information` - Analysis summary
- `mcp__serena__think_about_task_adherence` - Task validation

### 🛠️ IDE MCP - Development Tools

**Diagnostics:**

- `mcp__ide__getDiagnostics` - Get TypeScript/ESLint errors
- `mcp__ide__executeCode` - Run Python/JavaScript in Jupyter kernel

### 💡 MCP Usage Examples

```bash
# Database analysis and optimization
mcp__supabase__list_tables
mcp__supabase__get_advisors: "performance"

# Code exploration
mcp__serena__get_symbols_overview: "stores/inventory-store.ts"
mcp__serena__find_symbol: "addProduct"

# Get library help
mcp__context7__resolve_library_id: "zustand"

# Check code quality
mcp__ide__getDiagnostics
```

### 🔄 Agent + MCP Combinations

**Recommended Patterns:**

1. **Database Work**: `database-architect` + Supabase MCP
2. **Code Analysis**: `debug-specialist` + Serena MCP
3. **Documentation**: `ai-integration-specialist` + Context7 MCP
4. **Quality Checks**: `test-runner` + IDE MCP

## ⚠️ Security Checklist

For EVERY change involving:

- User data: Verify RLS policies
- API calls: Check HTTPS usage
- Secrets: Use environment variables
- AI responses: Validate and sanitize
- File uploads: Check size and type limits

## 🔧 Troubleshooting Common Issues

### Expo/React Native Issues

- Clear cache: `npx expo start --clear`
- Reset Metro: `npx react-native start --reset-cache`
- iOS build issues: `cd ios && pod install`

### Supabase Connection Issues

- Check status: `npx supabase status`
- Verify env vars: `SUPABASE_URL` and `SUPABASE_ANON_KEY`
- Test connection: Check network tab in browser

### TypeScript Errors

- Strict mode is enabled - all types required
- Run `npm run lint:fix` for auto-fixes
- Check `tsconfig.json` for configuration

## 📊 Current Priorities (v2.2.0)

1. **ESLint Cleanup** - Reduce from 607 errors to <100
2. **Test Coverage** - Expand from 33 to 100+ tests
3. **Performance** - Achieve <3s AI latency consistently
4. **UX Polish** - Complete micro-interactions implementation

Remember: This is a pre-launch product. Focus on stability, performance, and user experience over new features.
