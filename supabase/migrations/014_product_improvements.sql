-- =============================================
-- Product Improvements and Brand Preferences
-- =============================================
-- This migration adds:
-- 1. New columns to products table for better inventory management
-- 2. Category field with professional colorist categories
-- 3. View for product autocompletion
-- 4. Function for low stock alerts
-- 5. Performance indexes

-- 1. Add new columns to products table
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS category TEXT,
ADD COLUMN IF NOT EXISTS supplier TEXT,
ADD COLUMN IF NOT EXISTS notes TEXT,
ADD COLUMN IF NOT EXISTS max_stock DECIMAL,
ADD COLUMN IF NOT EXISTS last_purchase_date TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS color_code TEXT;

-- 2. Migrate existing data from type to category
UPDATE products 
SET category = 
  CASE 
    WHEN type = 'color' THEN 'tinte'
    WHEN type = 'developer' THEN 'oxidante'
    WHEN type = 'treatment' THEN 'tratamiento'
    WHEN type = 'shampoo' THEN 'tratamiento'
    WHEN type = 'conditioner' THEN 'tratamiento'
    WHEN type = 'styling' THEN 'tratamiento'
    ELSE 'otro'
  END
WHERE category IS NULL;

-- 3. Add constraint for category values
ALTER TABLE products 
DROP CONSTRAINT IF EXISTS products_category_check;

ALTER TABLE products 
ADD CONSTRAINT products_category_check 
CHECK (category IN ('tinte', 'oxidante', 'decolorante', 'matizador', 'tratamiento', 'aditivo', 'pre-pigmentacion', 'otro'));

-- 4. Create view for product summary (for autocomplete functionality)
CREATE OR REPLACE VIEW v_salon_products_summary AS
SELECT DISTINCT
    p.salon_id,
    p.brand,
    p.line,
    p.category,
    COUNT(*) as product_count,
    SUM(p.stock_ml) as total_stock_ml,
    -- Aggregate color codes for tintes
    CASE 
        WHEN p.category = 'tinte' THEN 
            ARRAY_AGG(DISTINCT p.color_code) FILTER (WHERE p.color_code IS NOT NULL)
        ELSE NULL
    END as available_colors
FROM products p
WHERE p.is_active = true
GROUP BY p.salon_id, p.brand, p.line, p.category;

-- Grant access to the view
GRANT SELECT ON v_salon_products_summary TO authenticated;

-- 5. Create function for low stock products
CREATE OR REPLACE FUNCTION get_low_stock_products(p_salon_id UUID)
RETURNS TABLE(
    product_id UUID,
    brand TEXT,
    name TEXT,
    category TEXT,
    stock_ml DECIMAL,
    minimum_stock_ml DECIMAL,
    percentage_remaining DECIMAL,
    color_code TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp
AS $$
BEGIN
    -- Check that user has access to this salon
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() 
        AND salon_id = p_salon_id 
        AND is_active = true
    ) THEN
        RAISE EXCEPTION 'Access denied';
    END IF;

    RETURN QUERY
    SELECT 
        p.id,
        p.brand,
        p.name,
        p.category,
        p.stock_ml,
        p.minimum_stock_ml,
        ROUND((p.stock_ml / NULLIF(p.minimum_stock_ml, 0) * 100)::numeric, 2) as percentage,
        p.color_code
    FROM products p
    WHERE p.salon_id = p_salon_id
        AND p.is_active = true
        AND p.minimum_stock_ml > 0
        AND p.stock_ml <= p.minimum_stock_ml
    ORDER BY (p.stock_ml / NULLIF(p.minimum_stock_ml, 0));
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_low_stock_products(UUID) TO authenticated;

-- 6. Create function to update salon settings (for preferred brands)
CREATE OR REPLACE FUNCTION update_salon_settings(
    p_salon_id UUID,
    p_settings JSONB
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp
AS $$
BEGIN
    -- Check that user has access to this salon
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() 
        AND salon_id = p_salon_id 
        AND is_active = true
    ) THEN
        RAISE EXCEPTION 'Access denied';
    END IF;

    -- Update salon settings
    UPDATE salons 
    SET 
        settings = p_settings,
        updated_at = NOW()
    WHERE id = p_salon_id;

    RETURN FOUND;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION update_salon_settings(UUID, JSONB) TO authenticated;

-- 7. Create performance indexes
CREATE INDEX IF NOT EXISTS idx_products_category 
ON products(salon_id, category) 
WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_products_stock_alerts 
ON products(salon_id, stock_ml, minimum_stock_ml) 
WHERE is_active = true AND minimum_stock_ml > 0;

CREATE INDEX IF NOT EXISTS idx_products_brand_line 
ON products(salon_id, brand, line) 
WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_products_color_code 
ON products(salon_id, color_code) 
WHERE is_active = true AND category = 'tinte';

-- 8. Add comment documentation
COMMENT ON COLUMN products.category IS 'Product category: tinte, oxidante, decolorante, matizador, tratamiento, aditivo, pre-pigmentacion, otro';
COMMENT ON COLUMN products.supplier IS 'Product supplier or distributor name';
COMMENT ON COLUMN products.notes IS 'Additional notes about the product';
COMMENT ON COLUMN products.max_stock IS 'Maximum stock level for this product';
COMMENT ON COLUMN products.last_purchase_date IS 'Date of last purchase for this product';
COMMENT ON COLUMN products.color_code IS 'Color code for tints (e.g., 7.1, 8/43)';
COMMENT ON VIEW v_salon_products_summary IS 'Summary view of products by brand/line for autocomplete functionality';
COMMENT ON FUNCTION get_low_stock_products IS 'Returns products with stock below minimum threshold';
COMMENT ON FUNCTION update_salon_settings IS 'Updates salon settings including preferred brands';