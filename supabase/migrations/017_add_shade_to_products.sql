-- Add shade column to products table for structured product data
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS shade VARCHAR(50);

-- Comment on the new column
COMMENT ON COLUMN products.shade IS 'Product shade/tone (e.g., "7", "9.1", "30 vol")';

-- Update existing products by parsing the shade from the name field
-- This will extract common patterns like numbers, volume indicators, etc.
UPDATE products
SET shade = 
  CASE 
    -- Extract volume patterns (e.g., "20 vol", "30vol")
    WHEN name ~* '\d+\s*vol' THEN 
      (regexp_match(name, '(\d+\s*vol)', 'i'))[1]
    -- Extract decimal tone patterns (e.g., "7.1", "9/3")
    WHEN name ~* '\d+[/.]\d+' THEN 
      (regexp_match(name, '(\d+[/.]\d+)', 'i'))[1]
    -- Extract letter-number patterns (e.g., "N7", "C4")
    WHEN name ~* '[A-Z]\d+' THEN 
      (regexp_match(name, '([A-Z]\d+)', 'i'))[1]
    -- Extract simple number patterns (e.g., "7", "10")
    WHEN name ~* '\b\d+\b' THEN 
      (regexp_match(name, '(\d+)', 'i'))[1]
    -- Also check color_code field if it exists
    WHEN color_code IS NOT NULL AND color_code != '' THEN
      color_code
    ELSE NULL
  END
WHERE shade IS NULL;

-- Update type field for products where it's null or 'other'
UPDATE products
SET type = 
  CASE 
    WHEN lower(name) LIKE '%tinte%' OR lower(name) LIKE '%color%' THEN 'Tinte'
    WHEN lower(name) LIKE '%oxidante%' OR lower(name) LIKE '%developer%' OR lower(name) LIKE '%peróxido%' THEN 'Oxidante'
    WHEN lower(name) LIKE '%decolorante%' OR lower(name) LIKE '%bleach%' OR lower(name) LIKE '%polvo%' THEN 'Decolorante'
    WHEN lower(name) LIKE '%matizador%' OR lower(name) LIKE '%toner%' THEN 'Matizador'
    WHEN lower(name) LIKE '%tratamiento%' OR lower(name) LIKE '%treatment%' THEN 'Tratamiento'
    WHEN lower(name) LIKE '%champú%' OR lower(name) LIKE '%shampoo%' THEN 'Champú'
    WHEN lower(name) LIKE '%acondicionador%' OR lower(name) LIKE '%conditioner%' THEN 'Acondicionador'
    ELSE COALESCE(type, 'Producto')
  END
WHERE type IS NULL OR type = 'other';

-- Create index for better performance on shade lookups
CREATE INDEX IF NOT EXISTS idx_products_shade ON products(shade);
CREATE INDEX IF NOT EXISTS idx_products_type ON products(type);

-- Update RLS policies to include new fields (no changes needed as they use SELECT *)