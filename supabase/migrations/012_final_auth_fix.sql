-- Final comprehensive auth fix
-- This ensures the trigger can create profiles and salons without <PERSON><PERSON> blocking

-- 1. First, let's check if the issue is with missing grants
GRANT USAGE ON SCHEMA auth TO service_role;
GRANT SELECT ON auth.users TO service_role;

-- 2. Drop and recreate the trigger function with explicit permissions
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  new_salon_id UUID;
  salon_name TEXT;
BEGIN
  -- Extract salon name
  salon_name := COALESCE(
    new.raw_user_meta_data->>'salon_name',
    new.raw_user_meta_data->>'full_name',
    split_part(new.email, '@', 1)
  ) || '''s Salon';
  
  -- Use a block with exception handling for the entire operation
  BEGIN
    -- Create salon first
    INSERT INTO public.salons (name, owner_id, settings)
    VALUES (
      salon_name,
      new.id,
      jsonb_build_object(
        'hasCompletedOnboarding', false,
        'skipSafetyVerification', false,
        'currencySymbol', '€',
        'volumeUnit', 'ml',
        'weightUnit', 'g'
      )::jsonb
    )
    RETURNING id INTO new_salon_id;
    
    -- Then create profile
    INSERT INTO public.profiles (
      id, 
      salon_id, 
      email, 
      full_name, 
      role, 
      permissions, 
      is_active,
      created_at,
      updated_at
    )
    VALUES (
      new.id,
      new_salon_id,
      new.email,
      COALESCE(new.raw_user_meta_data->>'full_name', split_part(new.email, '@', 1)),
      'owner',
      ARRAY['VIEW_ALL_CLIENTS', 'VIEW_COSTS', 'MODIFY_PRICES', 'MANAGE_INVENTORY', 'VIEW_REPORTS', 'CREATE_USERS', 'DELETE_DATA']::text[],
      true,
      NOW(),
      NOW()
    );
    
    -- Log success
    RAISE LOG 'handle_new_user: Successfully created salon % and profile for user %', new_salon_id, new.id;
    
  EXCEPTION
    WHEN OTHERS THEN
      -- Log the error but don't prevent user creation
      RAISE LOG 'handle_new_user: Error creating salon/profile for user %: %', new.id, SQLERRM;
  END;
  
  -- Always return new to allow user creation to proceed
  RETURN new;
END;
$$;

-- 3. Ensure the function is owned by postgres for maximum permissions
ALTER FUNCTION public.handle_new_user() OWNER TO postgres;

-- 4. Grant execute permission
GRANT EXECUTE ON FUNCTION public.handle_new_user() TO postgres;

-- 5. Recreate trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW 
  EXECUTE FUNCTION public.handle_new_user();

-- 6. Create a manual setup function that can be called if the trigger fails
CREATE OR REPLACE FUNCTION public.manual_user_setup(user_id UUID, user_email TEXT, user_name TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_salon_id UUID;
  profile_exists BOOLEAN;
BEGIN
  -- Check if profile already exists
  SELECT EXISTS(SELECT 1 FROM public.profiles WHERE id = user_id) INTO profile_exists;
  
  IF profile_exists THEN
    RETURN TRUE; -- Already set up
  END IF;
  
  -- Create salon
  INSERT INTO public.salons (name, owner_id, settings)
  VALUES (
    COALESCE(user_name, split_part(user_email, '@', 1)) || '''s Salon',
    user_id,
    jsonb_build_object(
      'hasCompletedOnboarding', false,
      'skipSafetyVerification', false,
      'currencySymbol', '€',
      'volumeUnit', 'ml',
      'weightUnit', 'g'
    )::jsonb
  )
  RETURNING id INTO new_salon_id;
  
  -- Create profile
  INSERT INTO public.profiles (
    id, 
    salon_id, 
    email, 
    full_name, 
    role, 
    permissions, 
    is_active
  )
  VALUES (
    user_id,
    new_salon_id,
    user_email,
    COALESCE(user_name, split_part(user_email, '@', 1)),
    'owner',
    ARRAY['VIEW_ALL_CLIENTS', 'VIEW_COSTS', 'MODIFY_PRICES', 'MANAGE_INVENTORY', 'VIEW_REPORTS', 'CREATE_USERS', 'DELETE_DATA']::text[],
    true
  );
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE LOG 'manual_user_setup: Error for user %: %', user_id, SQLERRM;
    RETURN FALSE;
END;
$$;

-- Grant execute on the manual function
GRANT EXECUTE ON FUNCTION public.manual_user_setup TO authenticated;
GRANT EXECUTE ON FUNCTION public.manual_user_setup TO service_role;