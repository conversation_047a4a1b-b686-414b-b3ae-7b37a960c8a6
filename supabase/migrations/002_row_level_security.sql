-- Enable Row Level Security on all tables
ALTER TABLE salons ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE stock_movements ENABLE ROW LEVEL SECURITY;
ALTER TABLE services ENABLE ROW LEVEL SECURITY;
ALTER TABLE formulas ENABLE ROW LEVEL SECURITY;
ALTER TABLE client_consents ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_analysis_cache ENABLE ROW LEVEL SECURITY;

-- Helper function to get user's salon_id
CREATE OR REPLACE FUNCTION auth.salon_id() 
RETURNS UUID AS $$
BEGIN
  RETURN (
    SELECT salon_id 
    FROM profiles 
    WHERE id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user has permission
CREATE OR REPLACE FUNCTION auth.has_permission(permission_name TEXT) 
RETURNS BOOLEAN AS $$
BEGIN
  RETURN (
    SELECT permission_name = ANY(permissions) OR role = 'owner'
    FROM profiles 
    WHERE id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- SALONS policies
-- Owners can only see and update their own salon
CREATE POLICY "Users can view their own salon" ON salons
  FOR SELECT USING (owner_id = auth.uid() OR id = auth.salon_id());

CREATE POLICY "Owners can update their salon" ON salons
  FOR UPDATE USING (owner_id = auth.uid());

-- PROFILES policies
-- Users can only see profiles in their salon
CREATE POLICY "Users can view profiles in their salon" ON profiles
  FOR SELECT USING (salon_id = auth.salon_id());

-- Users can update their own profile
CREATE POLICY "Users can update their own profile" ON profiles
  FOR UPDATE USING (id = auth.uid());

-- Owners can create and update profiles in their salon
CREATE POLICY "Owners can manage profiles in their salon" ON profiles
  FOR ALL USING (
    salon_id = auth.salon_id() AND 
    auth.has_permission('CREATE_USERS')
  );

-- CLIENTS policies
-- Users can view clients based on permissions
CREATE POLICY "Users can view clients" ON clients
  FOR SELECT USING (
    salon_id = auth.salon_id() AND (
      auth.has_permission('VIEW_ALL_CLIENTS') OR 
      created_by = auth.uid()
    )
  );

-- Users can create clients in their salon
CREATE POLICY "Users can create clients" ON clients
  FOR INSERT WITH CHECK (salon_id = auth.salon_id());

-- Users can update clients they created or with permission
CREATE POLICY "Users can update clients" ON clients
  FOR UPDATE USING (
    salon_id = auth.salon_id() AND (
      created_by = auth.uid() OR 
      auth.has_permission('VIEW_ALL_CLIENTS')
    )
  );

-- Only users with DELETE_DATA permission can delete clients
CREATE POLICY "Users can delete clients with permission" ON clients
  FOR DELETE USING (
    salon_id = auth.salon_id() AND 
    auth.has_permission('DELETE_DATA')
  );

-- PRODUCTS policies
-- All users can view products in their salon
CREATE POLICY "Users can view products" ON products
  FOR SELECT USING (salon_id = auth.salon_id());

-- Only users with MANAGE_INVENTORY permission can manage products
CREATE POLICY "Users can manage products with permission" ON products
  FOR INSERT WITH CHECK (
    salon_id = auth.salon_id() AND 
    auth.has_permission('MANAGE_INVENTORY')
  );

CREATE POLICY "Users can update products with permission" ON products
  FOR UPDATE USING (
    salon_id = auth.salon_id() AND 
    auth.has_permission('MANAGE_INVENTORY')
  );

CREATE POLICY "Users can delete products with permission" ON products
  FOR DELETE USING (
    salon_id = auth.salon_id() AND 
    auth.has_permission('MANAGE_INVENTORY')
  );

-- STOCK_MOVEMENTS policies
-- Users can view stock movements based on permissions
CREATE POLICY "Users can view stock movements" ON stock_movements
  FOR SELECT USING (
    salon_id = auth.salon_id() AND (
      auth.has_permission('MANAGE_INVENTORY') OR 
      auth.has_permission('VIEW_REPORTS')
    )
  );

-- Only users with MANAGE_INVENTORY can create stock movements
CREATE POLICY "Users can create stock movements with permission" ON stock_movements
  FOR INSERT WITH CHECK (
    salon_id = auth.salon_id() AND 
    auth.has_permission('MANAGE_INVENTORY')
  );

-- SERVICES policies
-- Users can view services they created or with permission
CREATE POLICY "Users can view services" ON services
  FOR SELECT USING (
    salon_id = auth.salon_id() AND (
      stylist_id = auth.uid() OR 
      auth.has_permission('VIEW_ALL_CLIENTS')
    )
  );

-- Users can create services in their salon
CREATE POLICY "Users can create services" ON services
  FOR INSERT WITH CHECK (salon_id = auth.salon_id());

-- Users can update their own services
CREATE POLICY "Users can update their services" ON services
  FOR UPDATE USING (
    salon_id = auth.salon_id() AND 
    stylist_id = auth.uid()
  );

-- FORMULAS policies
-- Users can view formulas for services they can see
CREATE POLICY "Users can view formulas" ON formulas
  FOR SELECT USING (
    salon_id = auth.salon_id() AND (
      created_by = auth.uid() OR
      EXISTS (
        SELECT 1 FROM services 
        WHERE services.id = formulas.service_id 
        AND (services.stylist_id = auth.uid() OR auth.has_permission('VIEW_ALL_CLIENTS'))
      )
    )
  );

-- Users can create formulas for their services
CREATE POLICY "Users can create formulas" ON formulas
  FOR INSERT WITH CHECK (salon_id = auth.salon_id());

-- CLIENT_CONSENTS policies
-- Users can view consents for clients they can access
CREATE POLICY "Users can view consents" ON client_consents
  FOR SELECT USING (
    salon_id = auth.salon_id() AND
    EXISTS (
      SELECT 1 FROM clients 
      WHERE clients.id = client_consents.client_id 
      AND (clients.created_by = auth.uid() OR auth.has_permission('VIEW_ALL_CLIENTS'))
    )
  );

-- Users can create consents for their clients
CREATE POLICY "Users can create consents" ON client_consents
  FOR INSERT WITH CHECK (salon_id = auth.salon_id());

-- AI_ANALYSIS_CACHE policies
-- All users in salon can view cached results
CREATE POLICY "Users can view AI cache" ON ai_analysis_cache
  FOR SELECT USING (salon_id = auth.salon_id());

-- All users can create cache entries for their salon
CREATE POLICY "Users can create AI cache" ON ai_analysis_cache
  FOR INSERT WITH CHECK (salon_id = auth.salon_id());