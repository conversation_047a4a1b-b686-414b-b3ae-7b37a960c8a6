-- Create cache metrics table for tracking performance
CREATE TABLE IF NOT EXISTS cache_metrics (
  id INT PRIMARY KEY DEFAULT 1,
  metrics JSONB NOT NULL DEFAULT '{
    "totalHits": 0,
    "totalMisses": 0,
    "avgSavingsPerHit": 0,
    "mostCachedQueries": [],
    "totalSavedUSD": 0,
    "totalSavedTokens": 0
  }',
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add constraint to ensure only one row
ALTER TABLE cache_metrics ADD CONSTRAINT cache_metrics_single_row CHECK (id = 1);

-- Add prompt_version column to ai_analysis_cache for cache invalidation
ALTER TABLE ai_analysis_cache 
ADD COLUMN IF NOT EXISTS prompt_version VARCHAR(20) DEFAULT '1.0.0';

-- Create index for better cache performance
CREATE INDEX IF NOT EXISTS idx_ai_cache_lookup 
ON ai_analysis_cache(salon_id, analysis_type, input_hash, expires_at);

-- Add comment
COMMENT ON TABLE cache_metrics IS 'Global metrics for AI cache performance tracking';
COMMENT ON COLUMN ai_analysis_cache.prompt_version IS 'Version of prompts used, for cache invalidation';