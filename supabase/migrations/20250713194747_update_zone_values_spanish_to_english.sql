-- Migration: Update hair zone values from Spanish to English
-- Created: 2025-01-13
-- Description: Updates all zone values in JSONB columns from Spanish (Ra<PERSON>ces, Medios, Puntas) to English (ROOTS, MIDS, ENDS)

-- UP Migration
BEGIN;

-- Update services.ai_analysis JSONB column
-- This handles nested zone values in the ai_analysis field
UPDATE services
SET ai_analysis = jsonb_set(
    jsonb_set(
        jsonb_set(
            ai_analysis,
            '{zoneAnalysis}',
            CASE 
                WHEN ai_analysis->'zoneAnalysis' IS NOT NULL THEN
                    jsonb_build_object(
                        'roots', COALESCE(ai_analysis->'zoneAnalysis'->'roots', ai_analysis->'zoneAnalysis'->'Raíces'),
                        'mids', COALESCE(ai_analysis->'zoneAnalysis'->'mids', ai_analysis->'zoneAnalysis'->'Medios'),
                        'ends', COALESCE(ai_analysis->'zoneAnalysis'->'ends', ai_analysis->'zoneAnalysis'->'Puntas')
                    )
                ELSE ai_analysis->'zoneAnalysis'
            END,
            true
        ),
        '{zoneColorAnalysis}',
        CASE 
            WHEN ai_analysis->'zoneColorAnalysis' IS NOT NULL THEN
                (
                    SELECT jsonb_agg(
                        CASE 
                            WHEN elem->>'zone' = 'Raíces' THEN jsonb_set(elem, '{zone}', '"ROOTS"')
                            WHEN elem->>'zone' = 'Medios' THEN jsonb_set(elem, '{zone}', '"MIDS"')
                            WHEN elem->>'zone' = 'Puntas' THEN jsonb_set(elem, '{zone}', '"ENDS"')
                            ELSE elem
                        END
                    )
                    FROM jsonb_array_elements(ai_analysis->'zoneColorAnalysis') AS elem
                )
            ELSE ai_analysis->'zoneColorAnalysis'
        END,
        true
    ),
    '{zonePhysicalAnalysis}',
    CASE 
        WHEN ai_analysis->'zonePhysicalAnalysis' IS NOT NULL THEN
            (
                SELECT jsonb_agg(
                    CASE 
                        WHEN elem->>'zone' = 'Raíces' THEN jsonb_set(elem, '{zone}', '"ROOTS"')
                        WHEN elem->>'zone' = 'Medios' THEN jsonb_set(elem, '{zone}', '"MIDS"')
                        WHEN elem->>'zone' = 'Puntas' THEN jsonb_set(elem, '{zone}', '"ENDS"')
                        ELSE elem
                    END
                )
                FROM jsonb_array_elements(ai_analysis->'zonePhysicalAnalysis') AS elem
            )
        ELSE ai_analysis->'zonePhysicalAnalysis'
    END,
    true
)
WHERE ai_analysis IS NOT NULL
  AND (
    ai_analysis::text LIKE '%Raíces%' 
    OR ai_analysis::text LIKE '%Medios%' 
    OR ai_analysis::text LIKE '%Puntas%'
  );

-- Update formulas.formula_data JSONB column
-- This handles zone values in formula data
UPDATE formulas
SET formula_data = 
    CASE 
        WHEN formula_data ? 'zones' THEN
            jsonb_set(
                formula_data,
                '{zones}',
                (
                    SELECT jsonb_agg(
                        CASE 
                            WHEN elem->>'zone' = 'Raíces' THEN jsonb_set(elem, '{zone}', '"ROOTS"')
                            WHEN elem->>'zone' = 'Medios' THEN jsonb_set(elem, '{zone}', '"MIDS"')
                            WHEN elem->>'zone' = 'Puntas' THEN jsonb_set(elem, '{zone}', '"ENDS"')
                            ELSE elem
                        END
                    )
                    FROM jsonb_array_elements(formula_data->'zones') AS elem
                ),
                true
            )
        ELSE formula_data
    END
WHERE formula_data IS NOT NULL
  AND (
    formula_data::text LIKE '%Raíces%' 
    OR formula_data::text LIKE '%Medios%' 
    OR formula_data::text LIKE '%Puntas%'
  );

-- Update ai_analysis_cache.result JSONB column
-- This table caches AI analysis results that may contain zone data
UPDATE ai_analysis_cache
SET result = 
    CASE 
        -- Handle zoneAnalysis object with Spanish keys
        WHEN result->'zoneAnalysis' IS NOT NULL 
            AND (result->'zoneAnalysis' ? 'Raíces' OR result->'zoneAnalysis' ? 'Medios' OR result->'zoneAnalysis' ? 'Puntas') THEN
            jsonb_set(
                result,
                '{zoneAnalysis}',
                jsonb_build_object(
                    'roots', COALESCE(result->'zoneAnalysis'->'roots', result->'zoneAnalysis'->'Raíces'),
                    'mids', COALESCE(result->'zoneAnalysis'->'mids', result->'zoneAnalysis'->'Medios'),
                    'ends', COALESCE(result->'zoneAnalysis'->'ends', result->'zoneAnalysis'->'Puntas')
                ),
                true
            )
        -- Handle zoneColorAnalysis array
        WHEN result->'zoneColorAnalysis' IS NOT NULL THEN
            jsonb_set(
                result,
                '{zoneColorAnalysis}',
                (
                    SELECT jsonb_agg(
                        CASE 
                            WHEN elem->>'zone' = 'Raíces' THEN jsonb_set(elem, '{zone}', '"ROOTS"')
                            WHEN elem->>'zone' = 'Medios' THEN jsonb_set(elem, '{zone}', '"MIDS"')
                            WHEN elem->>'zone' = 'Puntas' THEN jsonb_set(elem, '{zone}', '"ENDS"')
                            ELSE elem
                        END
                    )
                    FROM jsonb_array_elements(result->'zoneColorAnalysis') AS elem
                ),
                true
            )
        -- Handle zonePhysicalAnalysis array
        WHEN result->'zonePhysicalAnalysis' IS NOT NULL THEN
            jsonb_set(
                result,
                '{zonePhysicalAnalysis}',
                (
                    SELECT jsonb_agg(
                        CASE 
                            WHEN elem->>'zone' = 'Raíces' THEN jsonb_set(elem, '{zone}', '"ROOTS"')
                            WHEN elem->>'zone' = 'Medios' THEN jsonb_set(elem, '{zone}', '"MIDS"')
                            WHEN elem->>'zone' = 'Puntas' THEN jsonb_set(elem, '{zone}', '"ENDS"')
                            ELSE elem
                        END
                    )
                    FROM jsonb_array_elements(result->'zonePhysicalAnalysis') AS elem
                ),
                true
            )
        ELSE result
    END
WHERE result IS NOT NULL
  AND (
    result::text LIKE '%Raíces%' 
    OR result::text LIKE '%Medios%' 
    OR result::text LIKE '%Puntas%'
  );

-- Update ai_analysis_cache.input_data JSONB column (if it contains zone data)
UPDATE ai_analysis_cache
SET input_data = 
    CASE 
        WHEN input_data ? 'zones' THEN
            jsonb_set(
                input_data,
                '{zones}',
                (
                    SELECT jsonb_agg(
                        CASE 
                            WHEN elem->>'zone' = 'Raíces' THEN jsonb_set(elem, '{zone}', '"ROOTS"')
                            WHEN elem->>'zone' = 'Medios' THEN jsonb_set(elem, '{zone}', '"MIDS"')
                            WHEN elem->>'zone' = 'Puntas' THEN jsonb_set(elem, '{zone}', '"ENDS"')
                            ELSE elem
                        END
                    )
                    FROM jsonb_array_elements(input_data->'zones') AS elem
                ),
                true
            )
        ELSE input_data
    END
WHERE input_data IS NOT NULL
  AND (
    input_data::text LIKE '%Raíces%' 
    OR input_data::text LIKE '%Medios%' 
    OR input_data::text LIKE '%Puntas%'
  );

COMMIT;

-- DOWN Migration (Revert from English to Spanish)
-- Uncomment the following to revert the changes

/*
BEGIN;

-- Revert services.ai_analysis
UPDATE services
SET ai_analysis = jsonb_set(
    jsonb_set(
        jsonb_set(
            ai_analysis,
            '{zoneAnalysis}',
            CASE 
                WHEN ai_analysis->'zoneAnalysis' IS NOT NULL THEN
                    jsonb_build_object(
                        'Raíces', ai_analysis->'zoneAnalysis'->'roots',
                        'Medios', ai_analysis->'zoneAnalysis'->'mids',
                        'Puntas', ai_analysis->'zoneAnalysis'->'ends'
                    )
                ELSE ai_analysis->'zoneAnalysis'
            END,
            true
        ),
        '{zoneColorAnalysis}',
        CASE 
            WHEN ai_analysis->'zoneColorAnalysis' IS NOT NULL THEN
                (
                    SELECT jsonb_agg(
                        CASE 
                            WHEN elem->>'zone' = 'ROOTS' THEN jsonb_set(elem, '{zone}', '"Raíces"')
                            WHEN elem->>'zone' = 'MIDS' THEN jsonb_set(elem, '{zone}', '"Medios"')
                            WHEN elem->>'zone' = 'ENDS' THEN jsonb_set(elem, '{zone}', '"Puntas"')
                            ELSE elem
                        END
                    )
                    FROM jsonb_array_elements(ai_analysis->'zoneColorAnalysis') AS elem
                )
            ELSE ai_analysis->'zoneColorAnalysis'
        END,
        true
    ),
    '{zonePhysicalAnalysis}',
    CASE 
        WHEN ai_analysis->'zonePhysicalAnalysis' IS NOT NULL THEN
            (
                SELECT jsonb_agg(
                    CASE 
                        WHEN elem->>'zone' = 'ROOTS' THEN jsonb_set(elem, '{zone}', '"Raíces"')
                        WHEN elem->>'zone' = 'MIDS' THEN jsonb_set(elem, '{zone}', '"Medios"')
                        WHEN elem->>'zone' = 'ENDS' THEN jsonb_set(elem, '{zone}', '"Puntas"')
                        ELSE elem
                    END
                )
                FROM jsonb_array_elements(ai_analysis->'zonePhysicalAnalysis') AS elem
            )
        ELSE ai_analysis->'zonePhysicalAnalysis'
    END,
    true
)
WHERE ai_analysis IS NOT NULL
  AND (
    ai_analysis::text LIKE '%ROOTS%' 
    OR ai_analysis::text LIKE '%MIDS%' 
    OR ai_analysis::text LIKE '%ENDS%'
  );

-- Revert formulas.formula_data
UPDATE formulas
SET formula_data = 
    CASE 
        WHEN formula_data ? 'zones' THEN
            jsonb_set(
                formula_data,
                '{zones}',
                (
                    SELECT jsonb_agg(
                        CASE 
                            WHEN elem->>'zone' = 'ROOTS' THEN jsonb_set(elem, '{zone}', '"Raíces"')
                            WHEN elem->>'zone' = 'MIDS' THEN jsonb_set(elem, '{zone}', '"Medios"')
                            WHEN elem->>'zone' = 'ENDS' THEN jsonb_set(elem, '{zone}', '"Puntas"')
                            ELSE elem
                        END
                    )
                    FROM jsonb_array_elements(formula_data->'zones') AS elem
                ),
                true
            )
        ELSE formula_data
    END
WHERE formula_data IS NOT NULL
  AND (
    formula_data::text LIKE '%ROOTS%' 
    OR formula_data::text LIKE '%MIDS%' 
    OR formula_data::text LIKE '%ENDS%'
  );

-- Revert ai_analysis_cache.result
UPDATE ai_analysis_cache
SET result = 
    CASE 
        WHEN result->'zoneAnalysis' IS NOT NULL 
            AND (result->'zoneAnalysis' ? 'roots' OR result->'zoneAnalysis' ? 'mids' OR result->'zoneAnalysis' ? 'ends') THEN
            jsonb_set(
                result,
                '{zoneAnalysis}',
                jsonb_build_object(
                    'Raíces', result->'zoneAnalysis'->'roots',
                    'Medios', result->'zoneAnalysis'->'mids',
                    'Puntas', result->'zoneAnalysis'->'ends'
                ),
                true
            )
        WHEN result->'zoneColorAnalysis' IS NOT NULL THEN
            jsonb_set(
                result,
                '{zoneColorAnalysis}',
                (
                    SELECT jsonb_agg(
                        CASE 
                            WHEN elem->>'zone' = 'ROOTS' THEN jsonb_set(elem, '{zone}', '"Raíces"')
                            WHEN elem->>'zone' = 'MIDS' THEN jsonb_set(elem, '{zone}', '"Medios"')
                            WHEN elem->>'zone' = 'ENDS' THEN jsonb_set(elem, '{zone}', '"Puntas"')
                            ELSE elem
                        END
                    )
                    FROM jsonb_array_elements(result->'zoneColorAnalysis') AS elem
                ),
                true
            )
        WHEN result->'zonePhysicalAnalysis' IS NOT NULL THEN
            jsonb_set(
                result,
                '{zonePhysicalAnalysis}',
                (
                    SELECT jsonb_agg(
                        CASE 
                            WHEN elem->>'zone' = 'ROOTS' THEN jsonb_set(elem, '{zone}', '"Raíces"')
                            WHEN elem->>'zone' = 'MIDS' THEN jsonb_set(elem, '{zone}', '"Medios"')
                            WHEN elem->>'zone' = 'ENDS' THEN jsonb_set(elem, '{zone}', '"Puntas"')
                            ELSE elem
                        END
                    )
                    FROM jsonb_array_elements(result->'zonePhysicalAnalysis') AS elem
                ),
                true
            )
        ELSE result
    END
WHERE result IS NOT NULL
  AND (
    result::text LIKE '%ROOTS%' 
    OR result::text LIKE '%MIDS%' 
    OR result::text LIKE '%ENDS%'
  );

-- Revert ai_analysis_cache.input_data
UPDATE ai_analysis_cache
SET input_data = 
    CASE 
        WHEN input_data ? 'zones' THEN
            jsonb_set(
                input_data,
                '{zones}',
                (
                    SELECT jsonb_agg(
                        CASE 
                            WHEN elem->>'zone' = 'ROOTS' THEN jsonb_set(elem, '{zone}', '"Raíces"')
                            WHEN elem->>'zone' = 'MIDS' THEN jsonb_set(elem, '{zone}', '"Medios"')
                            WHEN elem->>'zone' = 'ENDS' THEN jsonb_set(elem, '{zone}', '"Puntas"')
                            ELSE elem
                        END
                    )
                    FROM jsonb_array_elements(input_data->'zones') AS elem
                ),
                true
            )
        ELSE input_data
    END
WHERE input_data IS NOT NULL
  AND (
    input_data::text LIKE '%ROOTS%' 
    OR input_data::text LIKE '%MIDS%' 
    OR input_data::text LIKE '%ENDS%'
  );

COMMIT;
*/