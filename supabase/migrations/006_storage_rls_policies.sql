-- 006_storage_rls_policies.sql
-- Políticas RLS para permitir operaciones en el bucket de anonimización

-- 1. PERMITIR LA SUBIDA (INSERT)
-- Da permiso a cualquier usuario que haya iniciado sesión ('authenticated')
-- para subir archivos SÓLO al bucket 'originals-for-anonymization'.
CREATE POLICY "Authenticated users can upload to originals"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK ( bucket_id = 'originals-for-anonymization' );

-- 2. PERMITIR LA LECTURA (SELECT) por parte del propietario
-- Necesario para que el sistema pueda verificar el archivo que acaba de subir.
CREATE POLICY "Owner can read their own original files"
ON storage.objects FOR SELECT
TO authenticated
USING ( auth.uid() = owner AND bucket_id = 'originals-for-anonymization' );

-- 3. PERMITIR EL BORRADO (DELETE) por parte del propietario
-- ¡CRÍTICO! Esto es lo que permitirá a nuestra Edge Function
-- eliminar el archivo original después de procesarlo.
CREATE POLICY "Owner can delete their own original files"
ON storage.objects FOR DELETE
TO authenticated
USING ( auth.uid() = owner AND bucket_id = 'originals-for-anonymization' );

-- 4. POLÍTICA ADICIONAL: Permitir UPDATE para el propietario
-- Algunos clientes de storage pueden necesitar actualizar metadata
CREATE POLICY "Owner can update their own original files"
ON storage.objects FOR UPDATE
TO authenticated
USING ( auth.uid() = owner AND bucket_id = 'originals-for-anonymization' );

-- 5. POLÍTICAS PARA EL BUCKET PÚBLICO client-photos
-- Los usuarios autenticados pueden leer cualquier foto anonimizada
CREATE POLICY "Anyone can read anonymized photos"
ON storage.objects FOR SELECT
TO public
USING ( bucket_id = 'client-photos' );

-- Solo el sistema (service_role) puede escribir en el bucket público
-- Las Edge Functions usan service_role, por lo que pueden escribir
-- Los usuarios normales NO pueden escribir directamente aquí