-- Migration: Unified Product Naming System
-- Date: 2025-01-23
-- Description: Normalizes product types and adds computed display_name column

-- Step 1: Create backup of products table
CREATE TABLE IF NOT EXISTS products_backup_20250123 AS 
SELECT * FROM products;

-- Step 2: Normalize product types to Spanish
UPDATE products 
SET type = CASE 
  WHEN LOWER(type) IN ('color', 'tinte') THEN 'Tinte'
  WHEN LOWER(type) IN ('developer', 'oxidante') THEN 'Oxidante'
  WHEN LOWER(type) IN ('bleach', 'decolorante') THEN 'Decolorante'
  WHEN LOWER(type) IN ('treatment', 'tratamiento') THEN 'Tratamiento'
  WHEN LOWER(type) IN ('toner', 'matizador') THEN 'Matizador'
  WHEN LOWER(type) IN ('shampoo', 'champú') THEN 'Champú'
  WHEN LOWER(type) IN ('conditioner', 'acondicionador') THEN 'Acondicionador'
  WHEN LOWER(type) IN ('styling') THEN 'Styling'
  WHEN LOWER(type) IN ('additive', 'aditivo') THEN 'Aditivo'
  WHEN LOWER(type) IN ('pre_pigment', 'pre-pigmentacion') THEN 'Pre-pigmentación'
  ELSE INITCAP(type)
END
WHERE type IS NOT NULL;

-- Step 3: Drop existing display_name column if it's computed (can't update computed columns)
ALTER TABLE products DROP COLUMN IF EXISTS display_name;

-- Step 4: Add new computed display_name column
ALTER TABLE products 
ADD COLUMN display_name TEXT GENERATED ALWAYS AS (
  CASE 
    WHEN type = 'Tinte' AND brand IS NOT NULL AND line IS NOT NULL AND shade IS NOT NULL THEN 
      type || ' ' || brand || ' ' || line || ' ' || shade
    WHEN type = 'Tinte' AND brand IS NOT NULL AND shade IS NOT NULL THEN 
      type || ' ' || brand || ' ' || shade
    WHEN type = 'Oxidante' AND shade IS NOT NULL THEN 
      type || ' ' || shade
    WHEN type = 'Oxidante' AND brand IS NOT NULL AND shade IS NOT NULL THEN 
      type || ' ' || brand || ' ' || shade
    WHEN type = 'Decolorante' AND brand IS NOT NULL AND line IS NOT NULL THEN 
      type || ' ' || brand || ' ' || line
    WHEN type = 'Decolorante' AND brand IS NOT NULL THEN 
      type || ' ' || brand
    WHEN brand IS NOT NULL AND line IS NOT NULL THEN 
      type || ' ' || brand || ' ' || line
    WHEN brand IS NOT NULL THEN 
      type || ' ' || brand
    ELSE 
      COALESCE(name, 'Producto sin nombre')
  END
) STORED;

-- Step 5: Improve product_mappings table
ALTER TABLE product_mappings 
ADD COLUMN IF NOT EXISTS mapping_context JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Step 6: Create indices for better search performance
CREATE INDEX IF NOT EXISTS idx_products_brand ON products(LOWER(brand));
CREATE INDEX IF NOT EXISTS idx_products_type ON products(LOWER(type));
CREATE INDEX IF NOT EXISTS idx_products_shade ON products(LOWER(shade));
CREATE INDEX IF NOT EXISTS idx_products_display_name ON products(LOWER(display_name));

-- Step 7: Create a view for legacy compatibility (optional)
CREATE OR REPLACE VIEW products_legacy AS
SELECT 
  id,
  salon_id,
  brand,
  name,
  line,
  type,
  size_ml,
  stock_ml,
  cost_per_unit,
  sale_price,
  minimum_stock_ml,
  barcode,
  is_active,
  created_at,
  updated_at,
  category,
  supplier,
  notes,
  max_stock,
  last_purchase_date,
  color_code,
  shade,
  display_name
FROM products;

-- Step 8: Update RLS policies if needed (they should work with the new column automatically)

-- Step 9: Add trigger to update last_used_at in product_mappings
CREATE OR REPLACE FUNCTION update_product_mapping_last_used()
RETURNS TRIGGER AS $$
BEGIN
  NEW.last_used_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_product_mapping_last_used_trigger
BEFORE UPDATE ON product_mappings
FOR EACH ROW
EXECUTE FUNCTION update_product_mapping_last_used();

-- Step 10: Add comment to document the change
COMMENT ON COLUMN products.display_name IS 'Auto-generated display name for consistent product naming across the application';