-- Migration: Add missing columns to client_consents table
-- Date: 2025-01-11
-- Purpose: Fix the "Could not find the 'consent_data' column" error

-- Add missing columns to client_consents table
ALTER TABLE client_consents 
ADD COLUMN IF NOT EXISTS consent_data JSONB,
ADD COLUMN IF NOT EXISTS safety_checklist TEXT[],
ADD COLUMN IF NOT EXISTS skip_safety BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS user_agent TEXT;

-- Create index for better performance on consent_data
CREATE INDEX IF NOT EXISTS idx_client_consents_consent_data ON client_consents USING GIN (consent_data);

-- Add comment for documentation
COMMENT ON COLUMN client_consents.consent_data IS 'JSON object containing consent items array';
COMMENT ON COLUMN client_consents.safety_checklist IS 'Array of completed safety checklist items';
COMMENT ON COLUMN client_consents.skip_safety IS 'Whether safety verification was skipped (with legal warning)';
COMMENT ON COLUMN client_consents.user_agent IS 'Browser user agent for audit trail';

-- Verify the columns were added
DO $$
BEGIN
  -- Check if all columns exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'client_consents' AND column_name = 'consent_data') THEN
    RAISE EXCEPTION 'Column consent_data was not created';
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'client_consents' AND column_name = 'safety_checklist') THEN
    RAISE EXCEPTION 'Column safety_checklist was not created';
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'client_consents' AND column_name = 'skip_safety') THEN
    RAISE EXCEPTION 'Column skip_safety was not created';
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'client_consents' AND column_name = 'user_agent') THEN
    RAISE EXCEPTION 'Column user_agent was not created';
  END IF;
  
  RAISE NOTICE 'All columns successfully added to client_consents table';
END $$;