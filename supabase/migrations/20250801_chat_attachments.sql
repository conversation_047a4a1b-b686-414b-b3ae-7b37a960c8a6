-- Chat Attachments Table
-- Esta migración agrega soporte para imágenes y archivos adjuntos en el sistema de chat

-- Tabla de archivos adjuntos
CREATE TABLE IF NOT EXISTS chat_attachments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    message_id UUID NOT NULL REFERENCES chat_messages(id) ON DELETE CASCADE,
    attachment_type TEXT NOT NULL CHECK (attachment_type IN ('image', 'document')),
    file_url TEXT NOT NULL,
    file_name TEXT,
    file_size INTEGER, -- en bytes
    mime_type TEXT,
    width INTEGER, -- para imágenes
    height INTEGER, -- para imágenes
    thumbnail_url TEXT, -- URL de miniatura para preview rápido
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX idx_chat_attachments_message_id ON chat_attachments(message_id);
CREATE INDEX idx_chat_attachments_type ON chat_attachments(attachment_type);

-- RLS (Row Level Security)
ALTER TABLE chat_attachments ENABLE ROW LEVEL SECURITY;

-- Política RLS: los usuarios pueden ver attachments de mensajes en sus conversaciones
CREATE POLICY "Users can view attachments from their salon conversations" ON chat_attachments
    FOR SELECT USING (
        message_id IN (
            SELECT m.id FROM chat_messages m
            JOIN chat_conversations c ON m.conversation_id = c.id
            WHERE c.salon_id IN (
                SELECT salon_id FROM profiles WHERE id = auth.uid()
            )
        )
    );

-- Política RLS: los usuarios pueden crear attachments en sus conversaciones
CREATE POLICY "Users can create attachments in their salon conversations" ON chat_attachments
    FOR INSERT WITH CHECK (
        message_id IN (
            SELECT m.id FROM chat_messages m
            JOIN chat_conversations c ON m.conversation_id = c.id
            WHERE c.salon_id IN (
                SELECT salon_id FROM profiles WHERE id = auth.uid()
            )
        )
    );

-- Agregar columna para indicar si un mensaje tiene attachments (optimización)
ALTER TABLE chat_messages ADD COLUMN IF NOT EXISTS has_attachments BOOLEAN DEFAULT FALSE;

-- Función para actualizar has_attachments automáticamente
CREATE OR REPLACE FUNCTION update_message_has_attachments()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE chat_messages 
        SET has_attachments = TRUE 
        WHERE id = NEW.message_id;
    ELSIF TG_OP = 'DELETE' THEN
        -- Verificar si quedan más attachments
        IF NOT EXISTS (SELECT 1 FROM chat_attachments WHERE message_id = OLD.message_id) THEN
            UPDATE chat_messages 
            SET has_attachments = FALSE 
            WHERE id = OLD.message_id;
        END IF;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers para mantener has_attachments sincronizado
CREATE TRIGGER update_message_attachments_on_insert
    AFTER INSERT ON chat_attachments
    FOR EACH ROW
    EXECUTE FUNCTION update_message_has_attachments();

CREATE TRIGGER update_message_attachments_on_delete
    AFTER DELETE ON chat_attachments
    FOR EACH ROW
    EXECUTE FUNCTION update_message_has_attachments();

-- Vista extendida para mensajes con sus attachments
CREATE OR REPLACE VIEW chat_messages_with_attachments AS
SELECT 
    m.*,
    COALESCE(
        json_agg(
            json_build_object(
                'id', a.id,
                'type', a.attachment_type,
                'url', a.file_url,
                'fileName', a.file_name,
                'fileSize', a.file_size,
                'mimeType', a.mime_type,
                'width', a.width,
                'height', a.height,
                'thumbnailUrl', a.thumbnail_url
            ) ORDER BY a.created_at
        ) FILTER (WHERE a.id IS NOT NULL),
        '[]'::json
    ) as attachments
FROM chat_messages m
LEFT JOIN chat_attachments a ON m.id = a.message_id
GROUP BY m.id;

-- Comentarios para documentación
COMMENT ON TABLE chat_attachments IS 'Archivos adjuntos (imágenes, documentos) en mensajes de chat';
COMMENT ON COLUMN chat_attachments.attachment_type IS 'Tipo de archivo: image para fotos de cabello, document para otros';
COMMENT ON COLUMN chat_attachments.thumbnail_url IS 'URL de versión comprimida para preview rápido en UI';
COMMENT ON COLUMN chat_messages.has_attachments IS 'Flag de optimización para queries rápidas';