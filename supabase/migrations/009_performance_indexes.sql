-- Add indexes to improve performance

-- Index on profiles.id for faster lookups during signup polling
CREATE INDEX IF NOT EXISTS idx_profiles_id ON public.profiles(id);

-- Index on profiles.salon_id for faster R<PERSON> checks
CREATE INDEX IF NOT EXISTS idx_profiles_salon_id ON public.profiles(salon_id);

-- Index on auth.users.email for faster email lookups
CREATE INDEX IF NOT EXISTS idx_auth_users_email ON auth.users(email);

-- Index on ai_analysis_cache for faster cache lookups
CREATE INDEX IF NOT EXISTS idx_ai_cache_lookup ON public.ai_analysis_cache(salon_id, analysis_type, input_hash);

-- Index on ai_analysis_cache.expires_at for cleanup queries
CREATE INDEX IF NOT EXISTS idx_ai_cache_expires ON public.ai_analysis_cache(expires_at);

-- Function to clean up expired AI cache entries
CREATE OR REPLACE FUNCTION public.cleanup_expired_ai_cache()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  DELETE FROM public.ai_analysis_cache
  WHERE expires_at < NOW();
END;
$$;

-- Create a scheduled job to clean up expired cache (requires pg_cron extension)
-- This would need to be configured in Supabase dashboard
-- SELECT cron.schedule('cleanup-expired-ai-cache', '0 2 * * *', 'SELECT public.cleanup_expired_ai_cache();');