-- Migration: Anonymization Buckets and Policies
-- Purpose: Create temporary bucket for original images pending anonymization
-- Date: 2025-01-18

-- Create the temporary bucket for original images
INSERT INTO storage.buckets (id, name, public, avif_autodetection, file_size_limit, allowed_mime_types)
VALUES 
  ('originals-for-anonymization', 'originals-for-anonymization', false, false, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp'])
ON CONFLICT (id) DO UPDATE SET
  file_size_limit = 10485760,
  allowed_mime_types = ARRAY['image/jpeg', 'image/png', 'image/webp'];

-- Storage policies for originals-for-anonymization bucket
-- Users can upload originals to their salon's folder
CREATE POLICY "Users can upload originals for anonymization" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'originals-for-anonymization' AND
    (storage.foldername(name))[1] = auth.salon_id()::text AND
    auth.uid() IS NOT NULL
  );

-- Users can view their salon's originals (needed for Edge Function)
CREATE POLICY "Users can view originals for anonymization" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'originals-for-anonymization' AND
    (storage.foldername(name))[1] = auth.salon_id()::text AND
    auth.uid() IS NOT NULL
  );

-- Users can delete their salon's originals
CREATE POLICY "Users can delete originals for anonymization" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'originals-for-anonymization' AND
    (storage.foldername(name))[1] = auth.salon_id()::text AND
    auth.uid() IS NOT NULL
  );

-- Service role can do everything (needed for Edge Function cleanup)
CREATE POLICY "Service role full access to originals" ON storage.objects
  FOR ALL USING (
    bucket_id = 'originals-for-anonymization' AND
    auth.jwt()->>'role' = 'service_role'
  );

-- Update client-photos bucket to be public for reading
UPDATE storage.buckets 
SET public = true 
WHERE id = 'client-photos';

-- Add public read policy for client-photos (anonymized images)
CREATE POLICY "Public can view anonymized photos" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'client-photos'
  );

-- Scheduled function to clean old files (>24 hours)
-- This acts as a fail-safe in case the Edge Function fails to delete
CREATE OR REPLACE FUNCTION clean_old_originals()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  deleted_count INTEGER := 0;
BEGIN
  -- Delete files older than 24 hours from originals-for-anonymization
  DELETE FROM storage.objects
  WHERE bucket_id = 'originals-for-anonymization'
  AND created_at < NOW() - INTERVAL '24 hours';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  -- Log the cleanup action
  IF deleted_count > 0 THEN
    RAISE NOTICE 'Cleaned % old original files from anonymization bucket', deleted_count;
  END IF;
END;
$$;

-- Create an index for faster cleanup queries
CREATE INDEX IF NOT EXISTS idx_storage_objects_bucket_created 
ON storage.objects(bucket_id, created_at) 
WHERE bucket_id = 'originals-for-anonymization';

-- Note: To schedule the cleanup function, run this in your Supabase dashboard:
-- SELECT cron.schedule(
--   'cleanup-originals',
--   '0 */6 * * *', -- Every 6 hours
--   'SELECT clean_old_originals();'
-- );

COMMENT ON FUNCTION clean_old_originals() IS 
'Fail-safe cleanup function to delete original images older than 24 hours from the anonymization bucket. This ensures compliance with privacy requirements even if the Edge Function fails to delete the original.';