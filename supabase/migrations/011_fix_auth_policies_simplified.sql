-- Simplified auth fix for user registration

-- 1. Drop ALL existing policies on profiles and salons
DO $$ 
DECLARE
    pol RECORD;
BEGIN
    -- Drop all policies on profiles
    FOR pol IN SELECT policyname FROM pg_policies WHERE schemaname = 'public' AND tablename = 'profiles'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON public.profiles', pol.policyname);
    END LOOP;
    
    -- Drop all policies on salons
    FOR pol IN SELECT policyname FROM pg_policies WHERE schemaname = 'public' AND tablename = 'salons'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON public.salons', pol.policyname);
    END LOOP;
END $$;

-- 2. Create super simple policies for testing
-- Service role gets everything
CREATE POLICY "service_role_all_profiles" ON public.profiles
FOR ALL TO service_role USING (true) WITH CHECK (true);

CREATE POLICY "service_role_all_salons" ON public.salons
FOR ALL TO service_role USING (true) WITH CHECK (true);

-- Authenticated users can read their own data
CREATE POLICY "users_read_own_profile" ON public.profiles
FOR SELECT TO authenticated USING (auth.uid() = id);

CREATE POLICY "users_read_own_salon" ON public.salons
FOR SELECT TO authenticated 
USING (EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.salon_id = salons.id
));

-- 3. Recreate the trigger function with extensive logging
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  new_salon_id UUID;
  salon_name TEXT;
  v_error_detail TEXT;
  v_error_hint TEXT;
  v_error_context TEXT;
BEGIN
  -- Extract salon name
  salon_name := COALESCE(
    new.raw_user_meta_data->>'salon_name',
    new.raw_user_meta_data->>'full_name',
    split_part(new.email, '@', 1)
  ) || '''s Salon';
  
  RAISE LOG 'handle_new_user: Starting for user % with email %', new.id, new.email;
  RAISE LOG 'handle_new_user: Salon name will be: %', salon_name;
  
  -- Create salon
  BEGIN
    INSERT INTO public.salons (name, owner_id, settings)
    VALUES (
      salon_name,
      new.id,
      jsonb_build_object(
        'hasCompletedOnboarding', false,
        'skipSafetyVerification', false,
        'currencySymbol', '€',
        'volumeUnit', 'ml',
        'weightUnit', 'g'
      )
    )
    RETURNING id INTO new_salon_id;
    
    RAISE LOG 'handle_new_user: Successfully created salon % for user %', new_salon_id, new.id;
  EXCEPTION
    WHEN OTHERS THEN
      GET STACKED DIAGNOSTICS
        v_error_detail = PG_EXCEPTION_DETAIL,
        v_error_hint = PG_EXCEPTION_HINT,
        v_error_context = PG_EXCEPTION_CONTEXT;
      
      RAISE LOG 'handle_new_user: Failed to create salon. SQLSTATE: %, SQLERRM: %, Detail: %, Hint: %, Context: %', 
        SQLSTATE, SQLERRM, v_error_detail, v_error_hint, v_error_context;
      
      -- Don't re-raise, continue to try profile creation
      RETURN new;
  END;

  -- Create profile
  IF new_salon_id IS NOT NULL THEN
    BEGIN
      INSERT INTO public.profiles (
        id, 
        salon_id, 
        email, 
        full_name, 
        role, 
        permissions, 
        is_active
      )
      VALUES (
        new.id,
        new_salon_id,
        new.email,
        COALESCE(new.raw_user_meta_data->>'full_name', split_part(new.email, '@', 1)),
        'owner',
        ARRAY['VIEW_ALL_CLIENTS', 'VIEW_COSTS', 'MODIFY_PRICES', 'MANAGE_INVENTORY', 'VIEW_REPORTS', 'CREATE_USERS', 'DELETE_DATA']::text[],
        true
      );
      
      RAISE LOG 'handle_new_user: Successfully created profile for user %', new.id;
    EXCEPTION
      WHEN OTHERS THEN
        GET STACKED DIAGNOSTICS
          v_error_detail = PG_EXCEPTION_DETAIL,
          v_error_hint = PG_EXCEPTION_HINT,
          v_error_context = PG_EXCEPTION_CONTEXT;
        
        RAISE LOG 'handle_new_user: Failed to create profile. SQLSTATE: %, SQLERRM: %, Detail: %, Hint: %, Context: %', 
          SQLSTATE, SQLERRM, v_error_detail, v_error_hint, v_error_context;
        
        -- Clean up salon if profile creation failed
        IF new_salon_id IS NOT NULL THEN
          DELETE FROM public.salons WHERE id = new_salon_id;
          RAISE LOG 'handle_new_user: Cleaned up salon % after profile creation failure', new_salon_id;
        END IF;
    END;
  END IF;

  -- Always return new to complete user creation
  RETURN new;
END;
$$;

-- 4. Ensure trigger exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW 
  EXECUTE FUNCTION public.handle_new_user();

-- 5. Grant permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT USAGE ON SCHEMA public TO service_role;
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO authenticated;