-- Migración 013: Optimización de Performance y Seguridad
-- Fecha: 2025-01-11
-- Objetivo: Corregir problemas detectados por Supabase Advisors

-- ============================================
-- 1. OPTIMIZAR POLÍTICAS RLS
-- ============================================

-- Optimizar política de profiles
DROP POLICY IF EXISTS "users_read_own_profile" ON public.profiles;
CREATE POLICY "users_read_own_profile" ON public.profiles
  FOR SELECT
  TO authenticated
  USING ((SELECT auth.uid()) = id);

-- Optimizar política de salons
DROP POLICY IF EXISTS "users_read_own_salon" ON public.salons;
CREATE POLICY "users_read_own_salon" ON public.salons
  FOR SELECT
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = (SELECT auth.uid()) 
    AND profiles.salon_id = salons.id
  ));

-- Optimizar las demás políticas para usar (SELECT auth.uid())
DROP POLICY IF EXISTS "Gestión de clientes por miembros del salón." ON public.clients;
CREATE POLICY "Gestión de clientes por miembros del salón." ON public.clients
  FOR ALL
  TO public
  USING (salon_id = (
    SELECT profiles.salon_id 
    FROM profiles 
    WHERE profiles.id = (SELECT auth.uid())
  ));

DROP POLICY IF EXISTS "Gestión de productos por miembros del salón." ON public.products;
CREATE POLICY "Gestión de productos por miembros del salón." ON public.products
  FOR ALL
  TO public
  USING (salon_id = (
    SELECT profiles.salon_id 
    FROM profiles 
    WHERE profiles.id = (SELECT auth.uid())
  ));

DROP POLICY IF EXISTS "Gestión de movimientos de stock por miembros del salón." ON public.stock_movements;
CREATE POLICY "Gestión de movimientos de stock por miembros del salón." ON public.stock_movements
  FOR ALL
  TO public
  USING (salon_id = (
    SELECT profiles.salon_id 
    FROM profiles 
    WHERE profiles.id = (SELECT auth.uid())
  ));

DROP POLICY IF EXISTS "Gestión de servicios por miembros del salón." ON public.services;
CREATE POLICY "Gestión de servicios por miembros del salón." ON public.services
  FOR ALL
  TO public
  USING (salon_id = (
    SELECT profiles.salon_id 
    FROM profiles 
    WHERE profiles.id = (SELECT auth.uid())
  ));

DROP POLICY IF EXISTS "Gestión de fórmulas por miembros del salón." ON public.formulas;
CREATE POLICY "Gestión de fórmulas por miembros del salón." ON public.formulas
  FOR ALL
  TO public
  USING (salon_id = (
    SELECT profiles.salon_id 
    FROM profiles 
    WHERE profiles.id = (SELECT auth.uid())
  ));

DROP POLICY IF EXISTS "Gestión de consentimientos por miembros del salón." ON public.client_consents;
CREATE POLICY "Gestión de consentimientos por miembros del salón." ON public.client_consents
  FOR ALL
  TO public
  USING (salon_id = (
    SELECT profiles.salon_id 
    FROM profiles 
    WHERE profiles.id = (SELECT auth.uid())
  ));

DROP POLICY IF EXISTS "Gestión de caché AI por miembros del salón." ON public.ai_analysis_cache;
CREATE POLICY "Gestión de caché AI por miembros del salón." ON public.ai_analysis_cache
  FOR ALL
  TO public
  USING (salon_id = (
    SELECT profiles.salon_id 
    FROM profiles 
    WHERE profiles.id = (SELECT auth.uid())
  ));

-- ============================================
-- 2. CORREGIR FUNCIONES VULNERABLES
-- ============================================

-- Corregir delete_old_temp_photos
CREATE OR REPLACE FUNCTION public.delete_old_temp_photos()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Esta función eliminará fotos temporales antiguas del bucket
  -- Por ahora solo es un placeholder
  -- La implementación real requiere acceso a storage que debe hacerse desde el dashboard
  RAISE NOTICE 'Función delete_old_temp_photos ejecutada';
END;
$$;

-- Corregir manual_user_setup
CREATE OR REPLACE FUNCTION public.manual_user_setup(p_user_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_email TEXT;
  v_result JSONB;
  v_salon_id UUID;
  v_existing_profile BOOLEAN;
  v_existing_salon BOOLEAN;
BEGIN
  -- Obtener el email del usuario
  SELECT email INTO v_email FROM auth.users WHERE id = p_user_id;
  
  IF v_email IS NULL THEN
    RETURN jsonb_build_object('success', false, 'error', 'Usuario no encontrado');
  END IF;
  
  -- Verificar si ya existe el perfil
  SELECT EXISTS(SELECT 1 FROM public.profiles WHERE id = p_user_id) INTO v_existing_profile;
  
  IF v_existing_profile THEN
    -- Si ya existe el perfil, obtener el salon_id
    SELECT salon_id INTO v_salon_id FROM public.profiles WHERE id = p_user_id;
    RETURN jsonb_build_object(
      'success', true, 
      'message', 'Perfil ya existe',
      'profile_exists', true,
      'salon_id', v_salon_id
    );
  END IF;
  
  -- Crear salón
  INSERT INTO public.salons (name, owner_id, settings)
  VALUES (
    'Mi Salón',
    p_user_id,
    jsonb_build_object(
      'inventoryLevel', 1,
      'skipSafetyWizard', false,
      'language', 'es',
      'units', 'metric',
      'currency', 'EUR'
    )
  )
  RETURNING id INTO v_salon_id;
  
  -- Crear perfil
  INSERT INTO public.profiles (id, email, salon_id, role, permissions, full_name)
  VALUES (
    p_user_id,
    v_email,
    v_salon_id,
    'owner',
    ARRAY['VIEW_ALL_CLIENTS', 'VIEW_COSTS', 'MODIFY_PRICES', 'MANAGE_INVENTORY', 'VIEW_REPORTS', 'CREATE_USERS', 'DELETE_DATA']::text[],
    'Propietario'
  );
  
  RETURN jsonb_build_object(
    'success', true,
    'message', 'Usuario configurado exitosamente',
    'salon_id', v_salon_id,
    'profile_exists', false
  );
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', SQLERRM,
      'detail', SQLSTATE
    );
END;
$$;

-- Corregir update_updated_at_column
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;

-- Corregir clean_temp_photos (alias de delete_old_temp_photos)
CREATE OR REPLACE FUNCTION public.clean_temp_photos()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Función alias para compatibilidad
  PERFORM public.delete_old_temp_photos();
END;
$$;

-- ============================================
-- 3. CREAR ÍNDICES FALTANTES EN FOREIGN KEYS
-- ============================================

-- Índice para client_consents.service_id
CREATE INDEX IF NOT EXISTS idx_client_consents_service_id 
ON public.client_consents(service_id);

-- Índice para clients.created_by
CREATE INDEX IF NOT EXISTS idx_clients_created_by 
ON public.clients(created_by);

-- Índice para formulas.created_by
CREATE INDEX IF NOT EXISTS idx_formulas_created_by 
ON public.formulas(created_by);

-- Índice para salons.owner_id
CREATE INDEX IF NOT EXISTS idx_salons_owner_id 
ON public.salons(owner_id);

-- Índice para stock_movements.created_by
CREATE INDEX IF NOT EXISTS idx_stock_movements_created_by 
ON public.stock_movements(created_by);

-- ============================================
-- 4. ELIMINAR ÍNDICES NO UTILIZADOS
-- ============================================

-- Estos índices fueron detectados como no utilizados por el advisor
-- Los eliminamos para liberar recursos
DROP INDEX IF EXISTS public.idx_profiles_salon_id;
DROP INDEX IF EXISTS public.idx_stock_movements_product_id;
DROP INDEX IF EXISTS public.idx_services_salon_id;
DROP INDEX IF EXISTS public.idx_services_client_id;
DROP INDEX IF EXISTS public.idx_services_stylist_id;
DROP INDEX IF EXISTS public.idx_formulas_salon_id;
DROP INDEX IF EXISTS public.idx_formulas_service_id;
DROP INDEX IF EXISTS public.idx_client_consents_salon_id;
DROP INDEX IF EXISTS public.idx_client_consents_client_id;
DROP INDEX IF EXISTS public.idx_ai_analysis_cache_salon_id;
DROP INDEX IF EXISTS public.idx_ai_analysis_cache_input_hash;

-- Pero mantenemos el índice compuesto que es más útil
-- Este índice sí es útil para las búsquedas de cache
CREATE INDEX IF NOT EXISTS idx_ai_cache_lookup 
ON public.ai_analysis_cache(salon_id, analysis_type, input_hash);

-- ============================================
-- 5. CREAR TRIGGERS PARA updated_at
-- ============================================

-- Crear triggers para actualizar automáticamente updated_at
CREATE TRIGGER update_salons_updated_at BEFORE UPDATE ON public.salons
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON public.profiles
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON public.clients
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON public.products
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_services_updated_at BEFORE UPDATE ON public.services
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- ============================================
-- 6. AGREGAR CONSTRAINTS DE VALIDACIÓN
-- ============================================

-- Validar que el stock no sea negativo
ALTER TABLE public.products 
ADD CONSTRAINT check_stock_positive 
CHECK (stock_ml >= 0);

-- Validar formato de email (básico)
ALTER TABLE public.clients
ADD CONSTRAINT check_email_format
CHECK (email IS NULL OR email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- Validar que service_date no sea del futuro (con 1 día de margen)
ALTER TABLE public.services
ADD CONSTRAINT check_service_date_not_future
CHECK (service_date IS NULL OR service_date <= NOW() + INTERVAL '1 day');

-- Validar permisos válidos
ALTER TABLE public.profiles
ADD CONSTRAINT check_valid_permissions
CHECK (
  permissions IS NULL OR 
  permissions <@ ARRAY[
    'VIEW_ALL_CLIENTS', 
    'VIEW_COSTS', 
    'MODIFY_PRICES', 
    'MANAGE_INVENTORY', 
    'VIEW_REPORTS', 
    'CREATE_USERS', 
    'DELETE_DATA'
  ]::text[]
);

-- ============================================
-- 7. COMENTARIOS Y DOCUMENTACIÓN
-- ============================================

COMMENT ON FUNCTION public.manual_user_setup IS 'Configura manualmente un usuario nuevo creando su perfil y salón. Usado como fallback cuando el trigger automático falla.';
COMMENT ON FUNCTION public.cleanup_expired_ai_cache IS 'Elimina entradas expiradas del cache de análisis de IA. Debe ser ejecutada periódicamente con pg_cron.';
COMMENT ON FUNCTION public.update_updated_at_column IS 'Trigger function que actualiza automáticamente el campo updated_at cuando se modifica un registro.';