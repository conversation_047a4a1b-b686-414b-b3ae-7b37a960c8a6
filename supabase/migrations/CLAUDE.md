# CLAUDE.md - Supabase Migrations & Database Schema

## 🎯 Propósito

Migraciones de base de datos con Row Level Security (RLS) obligatorio y arquitectura multi-tenant. Cada tabla DEBE tener `salon_id` y políticas RLS que impidan acceso entre salones.

## 🔐 Principio FUNDAMENTAL: Aislamiento por Salón

**NUNCA exponer datos entre salones. SIEMPRE filtrar por `salon_id`.**

```sql
-- ✅ CORRECTO: Query con filtro de salón
SELECT * FROM products WHERE salon_id = auth.salon_id();

-- ❌ INCORRECTO: Query sin filtro (NUNCA hacer esto)
SELECT * FROM products;
```

## 📁 Migraciones Existentes

### Core Schema (001-004)

- `001_initial_schema.sql` - Tablas principales con salon_id
- `002_row_level_security.sql` - Políticas RLS base
- `003_auth_triggers.sql` - Triggers de autenticación
- `004_storage_buckets.sql` - Buckets de almacenamiento

### Security & Performance (005-013)

- `005_anonymization_buckets.sql` - Buckets para datos anonimizados
- `006_storage_rls_policies.sql` - RLS para storage
- `009_performance_indexes.sql` - Índices de performance
- `010_fix_rls_recursion.sql` - Fix recursión RLS
- `011_fix_auth_policies_simplified.sql` - Políticas auth simplificadas
- `012_final_auth_fix.sql` - Fix final autenticación
- `013_performance_security_optimization.sql` - Optimización final

### Business Features (014-017)

- `014_product_improvements.sql` - Mejoras productos
- `015_add_satisfaction_score.sql` - Puntuación satisfacción
- `016_add_developer_volume_to_formulas.sql` - Volumen oxidante
- `017_add_shade_to_products.sql` - Tonos a productos

### Modern Features (2025+)

- `20250123000000_unified_product_naming.sql` - Naming unificado
- `20250131_chat_system.sql` - Sistema de chat
- `20250722163210_create_product_mappings_table.sql` - Mapeo productos
- `20250801_chat_attachments.sql` - Adjuntos chat
- `20250807_ai_cache_system.sql` - Sistema caché AI

## 🏗️ Patrón de Tabla Estándar

### Estructura Obligatoria

```sql
CREATE TABLE example_table (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,

    -- Campos de negocio
    name TEXT NOT NULL,
    data JSONB,

    -- Campos de auditoría (SIEMPRE incluir)
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id)
);

-- Índices obligatorios
CREATE INDEX idx_example_table_salon_id ON example_table(salon_id);
CREATE INDEX idx_example_table_created_at ON example_table(created_at);

-- RLS obligatorio
ALTER TABLE example_table ENABLE ROW LEVEL SECURITY;

-- Política estándar (SELECT)
CREATE POLICY "Users can view own salon data" ON example_table
    FOR SELECT USING (salon_id = auth.salon_id());

-- Política estándar (INSERT)
CREATE POLICY "Users can insert own salon data" ON example_table
    FOR INSERT WITH CHECK (salon_id = auth.salon_id());

-- Política estándar (UPDATE)
CREATE POLICY "Users can update own salon data" ON example_table
    FOR UPDATE USING (salon_id = auth.salon_id());

-- Política estándar (DELETE)
CREATE POLICY "Users can delete own salon data" ON example_table
    FOR DELETE USING (salon_id = auth.salon_id());

-- Trigger para updated_at
CREATE TRIGGER set_updated_at
    BEFORE UPDATE ON example_table
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## 🔑 Helper Functions Críticas

### auth.salon_id() Function

```sql
-- Esta función DEBE existir y retornar el salon_id del usuario actual
CREATE OR REPLACE FUNCTION auth.salon_id()
RETURNS UUID
LANGUAGE sql
STABLE
AS $$
    SELECT salon_id FROM profiles WHERE id = auth.uid();
$$;
```

### update_updated_at_column()

```sql
-- Trigger function para actualizar timestamps automáticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    NEW.updated_at = NOW();
    NEW.updated_by = auth.uid();
    RETURN NEW;
END;
$$;
```

## 📊 Índices de Performance

### Índices Obligatorios

```sql
-- En TODAS las tablas principales
CREATE INDEX idx_table_salon_id ON table_name(salon_id);
CREATE INDEX idx_table_created_at ON table_name(created_at);

-- En tablas con búsquedas frecuentes
CREATE INDEX idx_products_name_gin ON products USING gin(name gin_trgm_ops);
CREATE INDEX idx_clients_name_gin ON clients USING gin(name gin_trgm_ops);

-- En tablas con joins frecuentes
CREATE INDEX idx_services_client_id ON services(client_id, salon_id);
CREATE INDEX idx_formulas_service_id ON formulas(service_id, salon_id);

-- Para queries de reportes
CREATE INDEX idx_services_date_salon ON services(created_at, salon_id)
    WHERE completed = true;
```

### Índices Compuestos Críticos

```sql
-- Para el sistema de inventario
CREATE INDEX idx_products_brand_line_type ON products(brand, line, type, salon_id);

-- Para el sistema de stock
CREATE INDEX idx_stock_movements_product_date ON stock_movements(product_id, created_at, salon_id);

-- Para el sistema de chat
CREATE INDEX idx_conversations_salon_updated ON conversations(salon_id, updated_at DESC);
```

## 🛡️ Políticas RLS Críticas

### Patrón para Tablas Principales

```sql
-- Ejemplo: Tabla de productos
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- SELECT: Solo productos del salón
CREATE POLICY "select_own_salon_products" ON products
    FOR SELECT USING (salon_id = auth.salon_id());

-- INSERT: Solo en el salón del usuario
CREATE POLICY "insert_own_salon_products" ON products
    FOR INSERT WITH CHECK (salon_id = auth.salon_id());

-- UPDATE: Solo productos del salón
CREATE POLICY "update_own_salon_products" ON products
    FOR UPDATE USING (salon_id = auth.salon_id());

-- DELETE: Solo productos del salón
CREATE POLICY "delete_own_salon_products" ON products
    FOR DELETE USING (salon_id = auth.salon_id());
```

### Patrón para Storage

```sql
-- Buckets con aislamiento por salón
CREATE POLICY "salon_photos_select" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'salon-photos' AND
        (storage.foldername(name))[1] = auth.salon_id()::text
    );

CREATE POLICY "salon_photos_insert" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'salon-photos' AND
        (storage.foldername(name))[1] = auth.salon_id()::text
    );
```

## 🚀 Comandos de Migración

### Aplicar Migraciones

```bash
# Aplicar todas las migraciones pendientes
npx supabase db push

# Reset completo (PELIGROSO - solo desarrollo)
npx supabase db reset

# Aplicar migración específica
npx supabase migration up --local-only

# Crear nueva migración
npx supabase migration new "add_new_feature"
```

### Verificar Estado

```bash
# Ver estado de migraciones
npx supabase status

# Ver diferencias con remote
npx supabase db diff

# Generar tipos TypeScript
npx supabase gen types typescript --local > types/database.ts
```

## 🧪 Testing de Migraciones

### Script de Testing Pre-Deploy

```bash
#!/bin/bash
# scripts/test-migrations.sh

echo "🧪 Testing migrations..."

# 1. Reset a estado limpio
npx supabase db reset --local

# 2. Aplicar todas las migraciones
npx supabase db push --local

# 3. Verificar políticas RLS
psql $DATABASE_URL -c "
SELECT schemaname, tablename, rowsecurity
FROM pg_tables
WHERE schemaname = 'public' AND rowsecurity = false;
"

# 4. Verificar índices críticos
psql $DATABASE_URL -c "
SELECT tablename, indexname
FROM pg_indexes
WHERE schemaname = 'public' AND indexname LIKE '%salon_id%';
"

# 5. Test de inserción con aislamiento
echo "Testing salon isolation..."
# Aquí agregar tests específicos

echo "✅ Migration tests completed"
```

## 📝 Plantilla para Nueva Migración

```sql
-- Migration: YYYYMMDD_feature_description.sql
-- Description: Brief description of what this migration does
-- Author: Your name
-- Date: YYYY-MM-DD

-- Create table with standard structure
CREATE TABLE new_table (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,

    -- Business fields
    name TEXT NOT NULL,
    description TEXT,
    data JSONB DEFAULT '{}',

    -- Audit fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id)
);

-- Create indexes
CREATE INDEX idx_new_table_salon_id ON new_table(salon_id);
CREATE INDEX idx_new_table_created_at ON new_table(created_at);
CREATE INDEX idx_new_table_name ON new_table(name) WHERE salon_id IS NOT NULL;

-- Enable RLS
ALTER TABLE new_table ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "select_own_salon_new_table" ON new_table
    FOR SELECT USING (salon_id = auth.salon_id());

CREATE POLICY "insert_own_salon_new_table" ON new_table
    FOR INSERT WITH CHECK (salon_id = auth.salon_id());

CREATE POLICY "update_own_salon_new_table" ON new_table
    FOR UPDATE USING (salon_id = auth.salon_id());

CREATE POLICY "delete_own_salon_new_table" ON new_table
    FOR DELETE USING (salon_id = auth.salon_id());

-- Create trigger for updated_at
CREATE TRIGGER set_new_table_updated_at
    BEFORE UPDATE ON new_table
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions (if needed)
GRANT SELECT, INSERT, UPDATE, DELETE ON new_table TO authenticated;
GRANT USAGE, SELECT ON SEQUENCE new_table_id_seq TO authenticated;

-- Add comments
COMMENT ON TABLE new_table IS 'Description of what this table stores';
COMMENT ON COLUMN new_table.salon_id IS 'Reference to salon for multi-tenant isolation';
```

## 🚨 Errores Críticos a Evitar

### 1. Tabla sin RLS

```sql
-- ❌ NUNCA crear tabla sin RLS
CREATE TABLE bad_table (id UUID, data TEXT);

-- ✅ SIEMPRE habilitar RLS
CREATE TABLE good_table (id UUID, salon_id UUID, data TEXT);
ALTER TABLE good_table ENABLE ROW LEVEL SECURITY;
```

### 2. Política RLS incorrecta

```sql
-- ❌ Política que permite acceso cross-salon
CREATE POLICY "bad_policy" ON table_name
    FOR SELECT USING (true); -- PELIGROSO

-- ✅ Política con filtro de salón
CREATE POLICY "good_policy" ON table_name
    FOR SELECT USING (salon_id = auth.salon_id());
```

### 3. Falta de índices

```sql
-- ❌ Tabla sin índice en salon_id (performance pobre)
CREATE TABLE slow_table (id UUID, salon_id UUID, data TEXT);

-- ✅ Tabla con índices apropiados
CREATE TABLE fast_table (id UUID, salon_id UUID, data TEXT);
CREATE INDEX idx_fast_table_salon_id ON fast_table(salon_id);
```

## 📊 Monitoreo y Alertas

### Queries de Monitoreo

```sql
-- Verificar que todas las tablas tengan RLS
SELECT schemaname, tablename
FROM pg_tables
WHERE schemaname = 'public'
  AND rowsecurity = false;

-- Verificar políticas RLS existentes
SELECT schemaname, tablename, policyname, cmd, qual
FROM pg_policies
WHERE schemaname = 'public';

-- Verificar índices en salon_id
SELECT t.relname as table_name, i.relname as index_name
FROM pg_class t, pg_class i, pg_index ix, pg_attribute a
WHERE t.oid = ix.indrelid
  AND i.oid = ix.indexrelid
  AND a.attrelid = t.oid
  AND a.attnum = ANY(ix.indkey)
  AND t.relkind = 'r'
  AND a.attname = 'salon_id';
```

## 🤖 Agentes Recomendados

### Para este módulo usar:

**data-migration-specialist** - AGENTE PRINCIPAL para migraciones

- Zero-downtime database migrations
- Schema evolution y data transformations
- Multi-tenant migration strategies
- MUST BE USED antes de TODAS las migraciones de producción

**database-architect** - Diseño de schema y optimización

- Schema design con RLS policies
- Índices de performance y optimización
- Query optimization (>500ms queries)
- PROACTIVAMENTE usar para deployments y performance reviews

**security-privacy-auditor** - Auditoría de seguridad

- Auditar políticas RLS y aislamiento multi-tenant
- Verificar que no hay cross-salon data access
- Validar manejo de datos sensibles
- USAR SIEMPRE antes de releases

**deployment-engineer** - Deploy seguro de migraciones

- CI/CD pipeline para migraciones
- Rollback strategies para cambios de schema
- Monitoring post-migración
- MUST BE USED para deployments de producción

### 💡 Ejemplos de Uso

```bash
# Migración zero-downtime
Task: Use data-migration-specialist to create zero-downtime migration for adding new products column

# Optimizar queries lentas
Task: Use database-architect to analyze and optimize slow queries on products table

# Auditar políticas RLS
Task: Use security-privacy-auditor to audit all RLS policies for multi-tenant isolation

# Deploy seguro de migración
Task: Use deployment-engineer to deploy schema changes with rollback capability
```

## 🔌 MCPs Disponibles

### Funciones MCP críticas para migraciones:

**Para gestión de base de datos:**

- `mcp__supabase__list_tables` - Inspeccionar schema actual
- `mcp__supabase__apply_migration` - Aplicar migraciones seguras
- `mcp__supabase__list_migrations` - Estado de migraciones
- `mcp__supabase__execute_sql` - Queries directas para testing
- `mcp__supabase__get_advisors` - Recomendaciones de seguridad/performance

**Para branching y desarrollo:**

- `mcp__supabase__create_branch` - Crear ramas para testing
- `mcp__supabase__merge_branch` - Merge seguro a producción
- `mcp__supabase__list_branches` - Gestión de desarrollo

**Para análisis de código:**

- `mcp__serena__search_for_pattern` - Buscar patterns en migraciones
- `mcp__serena__write_memory` - Documentar cambios de schema

### 📝 Ejemplos MCP Críticos

```bash
# Inspeccionar estado actual
mcp__supabase__list_tables
mcp__supabase__list_migrations
mcp__supabase__get_advisors: "security"

# Crear rama para testing seguro
mcp__supabase__create_branch: "feature-new-schema"

# Aplicar migración con validación
mcp__supabase__apply_migration: {
  "name": "add_product_categories",
  "query": "ALTER TABLE products ADD COLUMN category TEXT;"
}

# Verificar políticas RLS
mcp__supabase__execute_sql: "
SELECT schemaname, tablename, rowsecurity
FROM pg_tables
WHERE schemaname = 'public' AND rowsecurity = false;
"

# Buscar migraciones sin RLS
mcp__serena__search_for_pattern: "CREATE TABLE.*(?!.*ENABLE ROW LEVEL SECURITY)" in "supabase/migrations/"

# Documentar cambios
mcp__serena__write_memory: "schema-changes" "Cambios de schema en products table para v2.2.0"
```

### 🔄 Combinaciones Críticas

**Migración Segura (OBLIGATORIO):**

1. `data-migration-specialist` + `mcp__supabase__create_branch`
2. `security-privacy-auditor` + `mcp__supabase__get_advisors`
3. `deployment-engineer` + `mcp__supabase__merge_branch`

**Análisis de Performance:**

1. `database-architect` + `mcp__supabase__execute_sql`
2. `database-architect` + `mcp__supabase__get_advisors`

**Security Audit:**

1. `security-privacy-auditor` + `mcp__serena__search_for_pattern`
2. `database-architect` + `mcp__supabase__list_tables`

## 📊 Patterns Críticos con Agentes

### 🚀 Zero-Downtime Migration Pattern

```sql
-- Usar data-migration-specialist para este pattern
-- 1. Crear nueva columna nullable
ALTER TABLE products ADD COLUMN new_field TEXT;

-- 2. Poblar datos gradualmente (background job)
-- 3. Aplicar constrains cuando esté poblado
ALTER TABLE products ALTER COLUMN new_field SET NOT NULL;

-- 4. Crear índices concurrentemente
CREATE INDEX CONCURRENTLY idx_products_new_field ON products(new_field);
```

### 🛡️ Security-First Migration Pattern

```sql
-- SIEMPRE usar security-privacy-auditor para validar
-- 1. Crear tabla con RLS desde el inicio
CREATE TABLE new_table (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
    data JSONB NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Habilitar RLS INMEDIATAMENTE
ALTER TABLE new_table ENABLE ROW LEVEL SECURITY;

-- 3. Crear políticas ANTES de usar la tabla
CREATE POLICY "salon_isolation" ON new_table
    FOR ALL USING (salon_id = auth.salon_id());

-- 4. Crear índices con filtro de seguridad
CREATE INDEX idx_new_table_salon_data ON new_table(salon_id, data);
```

### 📊 Performance-Optimized Migration Pattern

```sql
-- Usar database-architect para optimizar
-- 1. Crear índices concurrentemente
CREATE INDEX CONCURRENTLY idx_products_brand_salon
ON products(brand, salon_id) WHERE salon_id IS NOT NULL;

-- 2. Analizar queries frecuentes
EXPLAIN ANALYZE SELECT * FROM products WHERE brand = 'Wella' AND salon_id = $1;

-- 3. Crear índices compuestos para queries complejas
CREATE INDEX CONCURRENTLY idx_products_search
ON products USING gin(name gin_trgm_ops) WHERE salon_id IS NOT NULL;
```

## ⚠️ Checklist de Migración Crítico

### 🔒 Security (OBLIGATORIO con security-privacy-auditor)

- [ ] Todas las tablas tienen `salon_id`
- [ ] RLS habilitado en todas las tablas públicas
- [ ] Políticas RLS filtran por `auth.salon_id()`
- [ ] No hay queries cross-salon posibles

### 🚀 Performance (con database-architect)

- [ ] Índices en `salon_id` para todas las tablas
- [ ] Índices compuestos para queries frecuentes
- [ ] Análisis de explain plan para queries críticas
- [ ] No hay table scans en queries principales

### 🧪 Testing (con data-migration-specialist)

- [ ] Testing en rama antes de producción
- [ ] Validación de data integrity
- [ ] Testing de rollback procedure
- [ ] Load testing con datos reales

### 📊 Monitoring (con deployment-engineer)

- [ ] Alertas de performance post-migración
- [ ] Monitoring de RLS violations
- [ ] Health checks de base de datos
- [ ] Rollback procedure documentado

## 🔗 Archivos Relacionados

- `../../lib/supabase.ts` - Cliente Supabase con helpers
- `../../types/database.ts` - Tipos TypeScript generados
- `../../scripts/apply-migrations.sh` - Script de aplicación
- `../functions/` - Edge Functions que usan estas tablas

---

**⚡ Recuerda:** La base de datos es el corazón del sistema. MUST USE `data-migration-specialist` para TODAS las migraciones y `security-privacy-auditor` para validación. NUNCA comprometer la seguridad RLS por conveniencia.
