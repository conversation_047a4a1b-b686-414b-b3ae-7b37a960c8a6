-- Migration: Private Buckets with Signed URLs for GDPR/CCPA Compliance
-- Purpose: Replace public buckets with private ones using signed URLs
-- Date: 2025-02-08
-- Author: Claude Code

-- ============================================================================
-- STEP 1: Create Private Buckets
-- ============================================================================

-- Create private buckets as replacements for public ones
INSERT INTO storage.buckets (id, name, public, avif_autodetection, file_size_limit, allowed_mime_types)
VALUES 
  ('client-photos-private', 'client-photos-private', false, false, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp']),
  ('service-photos-private', 'service-photos-private', false, false, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp'])
ON CONFLICT (id) DO UPDATE SET
  public = false,
  file_size_limit = 10485760,
  allowed_mime_types = ARRAY['image/jpeg', 'image/png', 'image/webp'];

-- ============================================================================
-- STEP 2: Row Level Security Policies for Private Buckets
-- ============================================================================

-- RLS Policies for client-photos-private
-- Users can upload photos to their salon's folder only
CREATE POLICY "Salon users can upload client photos private" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'client-photos-private' AND
    (storage.foldername(name))[1] = auth.salon_id()::text AND
    auth.uid() IS NOT NULL
  );

-- Users can view photos from their salon only
CREATE POLICY "Salon users can view client photos private" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'client-photos-private' AND
    (storage.foldername(name))[1] = auth.salon_id()::text AND
    auth.uid() IS NOT NULL
  );

-- Users can delete photos from their salon only
CREATE POLICY "Salon users can delete client photos private" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'client-photos-private' AND
    (storage.foldername(name))[1] = auth.salon_id()::text AND
    auth.uid() IS NOT NULL
  );

-- Service role has full access (needed for Edge Functions)
CREATE POLICY "Service role full access client photos private" ON storage.objects
  FOR ALL USING (
    bucket_id = 'client-photos-private' AND
    auth.jwt()->>'role' = 'service_role'
  );

-- RLS Policies for service-photos-private
-- Users can upload photos to their salon's folder only
CREATE POLICY "Salon users can upload service photos private" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'service-photos-private' AND
    (storage.foldername(name))[1] = auth.salon_id()::text AND
    auth.uid() IS NOT NULL
  );

-- Users can view photos from their salon only
CREATE POLICY "Salon users can view service photos private" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'service-photos-private' AND
    (storage.foldername(name))[1] = auth.salon_id()::text AND
    auth.uid() IS NOT NULL
  );

-- Users can delete photos from their salon only
CREATE POLICY "Salon users can delete service photos private" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'service-photos-private' AND
    (storage.foldername(name))[1] = auth.salon_id()::text AND
    auth.uid() IS NOT NULL
  );

-- Service role has full access (needed for Edge Functions)
CREATE POLICY "Service role full access service photos private" ON storage.objects
  FOR ALL USING (
    bucket_id = 'service-photos-private' AND
    auth.jwt()->>'role' = 'service_role'
  );

-- ============================================================================
-- STEP 3: Automatic Data Retention Functions
-- ============================================================================

-- Function to delete photos older than 90 days for GDPR compliance
CREATE OR REPLACE FUNCTION delete_old_photos_gdpr()
RETURNS TABLE(deleted_count INTEGER, bucket_name TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  client_deleted INTEGER := 0;
  service_deleted INTEGER := 0;
BEGIN
  -- Delete old client photos (90 days)
  DELETE FROM storage.objects
  WHERE bucket_id = 'client-photos-private'
  AND created_at < NOW() - INTERVAL '90 days';
  
  GET DIAGNOSTICS client_deleted = ROW_COUNT;
  
  -- Delete old service photos (90 days)
  DELETE FROM storage.objects
  WHERE bucket_id = 'service-photos-private'
  AND created_at < NOW() - INTERVAL '90 days';
  
  GET DIAGNOSTICS service_deleted = ROW_COUNT;
  
  -- Return results
  RETURN QUERY VALUES 
    (client_deleted, 'client-photos-private'),
    (service_deleted, 'service-photos-private');
  
  -- Log the cleanup actions
  IF client_deleted > 0 OR service_deleted > 0 THEN
    RAISE NOTICE 'GDPR Cleanup: Deleted % client photos and % service photos older than 90 days', 
      client_deleted, service_deleted;
  END IF;
END;
$$;

-- Function to delete all photos for a specific salon (right to be forgotten)
CREATE OR REPLACE FUNCTION delete_salon_photos_gdpr(salon_id_to_delete TEXT)
RETURNS TABLE(deleted_count INTEGER, bucket_name TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  client_deleted INTEGER := 0;
  service_deleted INTEGER := 0;
BEGIN
  -- Validate salon_id format (should be UUID)
  IF salon_id_to_delete !~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$' THEN
    RAISE EXCEPTION 'Invalid salon_id format: %', salon_id_to_delete;
  END IF;
  
  -- Delete all client photos for this salon
  DELETE FROM storage.objects
  WHERE bucket_id = 'client-photos-private'
  AND (storage.foldername(name))[1] = salon_id_to_delete;
  
  GET DIAGNOSTICS client_deleted = ROW_COUNT;
  
  -- Delete all service photos for this salon
  DELETE FROM storage.objects
  WHERE bucket_id = 'service-photos-private'
  AND (storage.foldername(name))[1] = salon_id_to_delete;
  
  GET DIAGNOSTICS service_deleted = ROW_COUNT;
  
  -- Return results
  RETURN QUERY VALUES 
    (client_deleted, 'client-photos-private'),
    (service_deleted, 'service-photos-private');
  
  -- Log the deletion
  RAISE NOTICE 'Right to be forgotten: Deleted % client photos and % service photos for salon %', 
    client_deleted, service_deleted, salon_id_to_delete;
END;
$$;

-- ============================================================================
-- STEP 4: Signed URL Helper Functions
-- ============================================================================

-- Function to generate signed URLs with expiration (for Edge Functions)
CREATE OR REPLACE FUNCTION generate_signed_url_info(
  bucket_name TEXT,
  file_path TEXT,
  expires_in_seconds INTEGER DEFAULT 3600
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
BEGIN
  -- Note: This function prepares the data needed for signed URL generation
  -- The actual signed URL generation happens in the Edge Function using the Supabase client
  -- because signed URLs require service role key which shouldn't be exposed in SQL
  
  result := json_build_object(
    'bucket', bucket_name,
    'path', file_path,
    'expires_in', expires_in_seconds,
    'created_at', EXTRACT(EPOCH FROM NOW()),
    'expires_at', EXTRACT(EPOCH FROM NOW() + INTERVAL '1 second' * expires_in_seconds)
  );
  
  RETURN result;
END;
$$;

-- ============================================================================
-- STEP 5: Privacy Audit Table
-- ============================================================================

-- Table to track photo access for GDPR audit trail
CREATE TABLE IF NOT EXISTS photo_access_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  salon_id UUID NOT NULL,
  user_id UUID REFERENCES auth.users(id),
  bucket_name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  action TEXT NOT NULL CHECK (action IN ('upload', 'view', 'delete', 'signed_url_generated')),
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- RLS for photo_access_log
ALTER TABLE photo_access_log ENABLE ROW LEVEL SECURITY;

-- Users can only see their salon's logs
CREATE POLICY "Users can view salon photo access logs" ON photo_access_log
  FOR SELECT USING (salon_id = auth.salon_id());

-- Only service role can insert logs (from Edge Functions)
CREATE POLICY "Service role can insert photo access logs" ON photo_access_log
  FOR INSERT WITH CHECK (auth.jwt()->>'role' = 'service_role');

-- Index for performance
CREATE INDEX IF NOT EXISTS idx_photo_access_log_salon_created 
ON photo_access_log(salon_id, created_at DESC);

-- ============================================================================
-- STEP 6: Privacy Settings Table
-- ============================================================================

-- Table to store user privacy preferences
CREATE TABLE IF NOT EXISTS user_privacy_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID UNIQUE REFERENCES auth.users(id) ON DELETE CASCADE,
  salon_id UUID NOT NULL,
  photo_retention_days INTEGER DEFAULT 90 CHECK (photo_retention_days BETWEEN 1 AND 365),
  allow_ai_analysis BOOLEAN DEFAULT true,
  allow_anonymization BOOLEAN DEFAULT true,
  analytics_consent BOOLEAN DEFAULT false,
  marketing_consent BOOLEAN DEFAULT false,
  data_processing_consent BOOLEAN DEFAULT true,
  consent_date TIMESTAMPTZ DEFAULT NOW(),
  last_updated TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- RLS for user_privacy_settings
ALTER TABLE user_privacy_settings ENABLE ROW LEVEL SECURITY;

-- Users can manage their own privacy settings
CREATE POLICY "Users can manage their privacy settings" ON user_privacy_settings
  FOR ALL USING (
    user_id = auth.uid() AND 
    salon_id = auth.salon_id()
  );

-- Index for performance
CREATE INDEX IF NOT EXISTS idx_user_privacy_settings_user_salon 
ON user_privacy_settings(user_id, salon_id);

-- ============================================================================
-- STEP 7: GDPR Data Retention Enforcement
-- ============================================================================

-- Enhanced function to respect user privacy settings for data retention
CREATE OR REPLACE FUNCTION delete_photos_by_retention_policy()
RETURNS TABLE(deleted_count INTEGER, bucket_name TEXT, retention_reason TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  client_deleted INTEGER := 0;
  service_deleted INTEGER := 0;
  expired_signed_urls INTEGER := 0;
BEGIN
  -- Delete photos based on individual user retention preferences
  DELETE FROM storage.objects so
  USING user_privacy_settings ups
  WHERE so.bucket_id IN ('client-photos-private', 'client-photos')
  AND (storage.foldername(so.name))[1]::uuid IN (
    SELECT salon_id FROM user_privacy_settings 
    WHERE created_at < NOW() - INTERVAL '1 day' * photo_retention_days
  )
  AND so.created_at < NOW() - INTERVAL '1 day' * ups.photo_retention_days;
  
  GET DIAGNOSTICS client_deleted = ROW_COUNT;
  
  -- Delete old service photos (global 90 days max)
  DELETE FROM storage.objects
  WHERE bucket_id IN ('service-photos-private', 'temp-photos')
  AND created_at < NOW() - INTERVAL '90 days';
  
  GET DIAGNOSTICS service_deleted = ROW_COUNT;
  
  -- Clean expired photo access logs (1 year retention for audit)
  DELETE FROM photo_access_log
  WHERE created_at < NOW() - INTERVAL '1 year';
  
  GET DIAGNOSTICS expired_signed_urls = ROW_COUNT;
  
  -- Return results
  RETURN QUERY VALUES 
    (client_deleted, 'client-photos', 'user_retention_policy'),
    (service_deleted, 'service-photos', 'global_90_day_policy'),
    (expired_signed_urls, 'audit_logs', 'audit_retention_policy');
  
  -- Log the cleanup actions with GDPR compliance note
  IF client_deleted > 0 OR service_deleted > 0 THEN
    RAISE NOTICE 'GDPR Compliance: Deleted % client photos, % service photos, % audit logs', 
      client_deleted, service_deleted, expired_signed_urls;
  END IF;
END;
$$;

COMMENT ON FUNCTION delete_photos_by_retention_policy() IS 
'GDPR-compliant data retention: Respects individual user privacy settings for photo retention periods. Runs daily to ensure compliance.';

-- ============================================================================
-- STEP 7.1: Scheduled Jobs Setup
-- ============================================================================

-- Create indexes for better performance on scheduled cleanup
CREATE INDEX IF NOT EXISTS idx_storage_objects_client_private_created 
ON storage.objects(bucket_id, created_at) 
WHERE bucket_id = 'client-photos-private';

CREATE INDEX IF NOT EXISTS idx_storage_objects_service_private_created 
ON storage.objects(bucket_id, created_at) 
WHERE bucket_id = 'service-photos-private';

-- ============================================================================
-- COMMENTS AND DOCUMENTATION
-- ============================================================================

COMMENT ON FUNCTION delete_old_photos_gdpr() IS 
'GDPR compliance function: Automatically deletes photos older than 90 days from private buckets. Should be scheduled to run daily.';

COMMENT ON FUNCTION delete_salon_photos_gdpr(TEXT) IS 
'Right to be forgotten: Deletes all photos for a specific salon. Used when a salon requests data deletion.';

COMMENT ON FUNCTION generate_signed_url_info(TEXT, TEXT, INTEGER) IS 
'Prepares metadata for signed URL generation. Actual signed URLs are generated in Edge Functions using service role.';

COMMENT ON TABLE photo_access_log IS 
'GDPR audit trail: Tracks all photo access events for compliance reporting.';

COMMENT ON TABLE user_privacy_settings IS 
'User privacy preferences: Stores consent and settings for GDPR compliance.';

-- ============================================================================
-- STEP 8: CRITICAL FIX - Secure existing public buckets
-- ============================================================================

-- URGENT: Make client-photos bucket private to prevent data leaks
UPDATE storage.buckets 
SET public = false 
WHERE id = 'client-photos';

-- Remove dangerous public access policy
DROP POLICY IF EXISTS "Public can view anonymized photos" ON storage.objects;

-- Add proper RLS policy for legacy client-photos bucket
CREATE POLICY "Salon users can view legacy client photos" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'client-photos' AND
    (storage.foldername(name))[1] = auth.salon_id()::text AND
    auth.uid() IS NOT NULL
  );

-- Service role access for Edge Functions
CREATE POLICY "Service role full access legacy client photos" ON storage.objects
  FOR ALL USING (
    bucket_id = 'client-photos' AND
    auth.jwt()->>'role' = 'service_role'
  );

-- ============================================================================
-- MIGRATION SUCCESS NOTIFICATION
-- ============================================================================

DO $$
BEGIN
  RAISE NOTICE '✅ Private buckets migration completed successfully!';
  RAISE NOTICE '📋 Created buckets: client-photos-private, service-photos-private';
  RAISE NOTICE '🔒 RLS policies configured for multi-tenant security';
  RAISE NOTICE '🗑️ GDPR retention functions created (90-day auto-cleanup)';
  RAISE NOTICE '📊 Audit logging system configured';
  RAISE NOTICE '⚙️ Privacy settings table ready';
  RAISE NOTICE '';
  RAISE NOTICE '📝 Next steps:';
  RAISE NOTICE '1. Update secure-image-upload.ts to use private buckets';
  RAISE NOTICE '2. Update Edge Functions for signed URL generation';
  RAISE NOTICE '3. Implement signed URL refresh logic in client';
  RAISE NOTICE '4. Schedule GDPR cleanup job: SELECT cron.schedule(''gdpr-cleanup'', ''0 2 * * *'', ''SELECT delete_old_photos_gdpr();'');';
END $$;