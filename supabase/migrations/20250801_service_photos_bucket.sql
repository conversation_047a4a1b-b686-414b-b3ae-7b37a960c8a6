-- Create service-photos bucket for chat images and other service-related content
-- Date: 2025-08-01
-- Purpose: Dedicated bucket for chat images and service documentation

-- Create the service-photos bucket
INSERT INTO storage.buckets (id, name, public, avif_autodetection, file_size_limit, allowed_mime_types)
VALUES (
  'service-photos', 
  'service-photos', 
  false, 
  false, 
  10485760, -- 10MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'application/pdf']
)
ON CONFLICT (id) DO NOTHING;

-- Chat images policies
-- Structure: chat/{salon_id}/{timestamp}-{random}.jpg

-- Users can upload chat images for their salon
CREATE POLICY "Users can upload chat images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'service-photos' AND
    (storage.foldername(name))[1] = 'chat' AND
    (storage.foldername(name))[2] = (SELECT salon_id FROM profiles WHERE id = auth.uid())::text
  );

-- Users can view chat images from their salon
CREATE POLICY "Users can view chat images" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'service-photos' AND
    (storage.foldername(name))[1] = 'chat' AND
    (storage.foldername(name))[2] = (SELECT salon_id FROM profiles WHERE id = auth.uid())::text
  );

-- Users can delete chat images from their salon
CREATE POLICY "Users can delete chat images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'service-photos' AND
    (storage.foldername(name))[1] = 'chat' AND
    (storage.foldername(name))[2] = (SELECT salon_id FROM profiles WHERE id = auth.uid())::text
  );

-- Service documentation policies (for future expansion)
-- Structure: formulas/{salon_id}/{service_id}/*, diagnostics/{salon_id}/{service_id}/*

-- Users can upload service documents for their salon
CREATE POLICY "Users can upload service docs" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'service-photos' AND
    (storage.foldername(name))[1] IN ('formulas', 'diagnostics', 'reports') AND
    (storage.foldername(name))[2] = (SELECT salon_id FROM profiles WHERE id = auth.uid())::text AND
    auth.has_permission('MANAGE_SERVICES')
  );

-- Users can view service documents based on permissions
CREATE POLICY "Users can view service docs" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'service-photos' AND
    (storage.foldername(name))[1] IN ('formulas', 'diagnostics', 'reports') AND
    (storage.foldername(name))[2] = (SELECT salon_id FROM profiles WHERE id = auth.uid())::text AND
    (
      auth.has_permission('VIEW_ALL_SERVICES') OR
      auth.has_permission('VIEW_OWN_SERVICES')
    )
  );

-- Users can delete service documents they created
CREATE POLICY "Users can delete service docs" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'service-photos' AND
    (storage.foldername(name))[1] IN ('formulas', 'diagnostics', 'reports') AND
    (storage.foldername(name))[2] = (SELECT salon_id FROM profiles WHERE id = auth.uid())::text AND
    auth.has_permission('MANAGE_SERVICES')
  );

-- Comment explaining the bucket structure
COMMENT ON TABLE storage.objects IS 'service-photos bucket structure:
- chat/{salon_id}/{timestamp}-{random}.jpg - Chat assistant images
- formulas/{salon_id}/{service_id}/* - Formula documentation
- diagnostics/{salon_id}/{service_id}/* - Diagnostic images and reports
- reports/{salon_id}/* - Generated reports and analytics';