-- Chat System Tables
-- Esta migración crea las tablas necesarias para el sistema de chat contextual

-- Tabla de conversaciones
CREATE TABLE IF NOT EXISTS chat_conversations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    title TEXT NOT NULL DEFAULT 'Nueva conversación',
    context_type TEXT CHECK (context_type IN ('general', 'client', 'service', 'formula', 'inventory')),
    context_id UUID, -- ID del recurso relacionado (client_id, service_id, etc.)
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'archived')),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tabla de mensajes
CREATE TABLE IF NOT EXISTS chat_messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    conversation_id UUID NOT NULL REFERENCES chat_conversations(id) ON DELETE CASCADE,
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    -- Costos de API
    prompt_tokens INTEGER DEFAULT 0,
    completion_tokens INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    cost_usd DECIMAL(10, 6) DEFAULT 0,
    -- Metadata adicional
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tabla de referencias contextuales (opcional, para links profundos)
CREATE TABLE IF NOT EXISTS chat_context_references (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    message_id UUID NOT NULL REFERENCES chat_messages(id) ON DELETE CASCADE,
    reference_type TEXT NOT NULL CHECK (reference_type IN ('client', 'service', 'formula', 'product', 'image')),
    reference_id UUID NOT NULL,
    reference_data JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX idx_chat_conversations_salon_id ON chat_conversations(salon_id);
CREATE INDEX idx_chat_conversations_user_id ON chat_conversations(user_id);
CREATE INDEX idx_chat_conversations_context ON chat_conversations(context_type, context_id) WHERE context_id IS NOT NULL;
CREATE INDEX idx_chat_messages_conversation_id ON chat_messages(conversation_id);
CREATE INDEX idx_chat_messages_created_at ON chat_messages(created_at);
CREATE INDEX idx_chat_context_references_message_id ON chat_context_references(message_id);

-- RLS (Row Level Security)
ALTER TABLE chat_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_context_references ENABLE ROW LEVEL SECURITY;

-- Políticas RLS para chat_conversations
CREATE POLICY "Users can view conversations from their salon" ON chat_conversations
    FOR SELECT USING (
        salon_id IN (
            SELECT salon_id FROM profiles WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can create conversations in their salon" ON chat_conversations
    FOR INSERT WITH CHECK (
        salon_id IN (
            SELECT salon_id FROM profiles WHERE id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own conversations" ON chat_conversations
    FOR UPDATE USING (user_id = auth.uid());

-- Políticas RLS para chat_messages
CREATE POLICY "Users can view messages from their salon conversations" ON chat_messages
    FOR SELECT USING (
        conversation_id IN (
            SELECT id FROM chat_conversations WHERE salon_id IN (
                SELECT salon_id FROM profiles WHERE id = auth.uid()
            )
        )
    );

CREATE POLICY "Users can create messages in their salon conversations" ON chat_messages
    FOR INSERT WITH CHECK (
        conversation_id IN (
            SELECT id FROM chat_conversations WHERE salon_id IN (
                SELECT salon_id FROM profiles WHERE id = auth.uid()
            )
        )
    );

-- Políticas RLS para chat_context_references
CREATE POLICY "Users can view references from their salon" ON chat_context_references
    FOR SELECT USING (
        message_id IN (
            SELECT m.id FROM chat_messages m
            JOIN chat_conversations c ON m.conversation_id = c.id
            WHERE c.salon_id IN (
                SELECT salon_id FROM profiles WHERE id = auth.uid()
            )
        )
    );

CREATE POLICY "Users can create references in their salon" ON chat_context_references
    FOR INSERT WITH CHECK (
        message_id IN (
            SELECT m.id FROM chat_messages m
            JOIN chat_conversations c ON m.conversation_id = c.id
            WHERE c.salon_id IN (
                SELECT salon_id FROM profiles WHERE id = auth.uid()
            )
        )
    );

-- Función para actualizar updated_at automáticamente
CREATE OR REPLACE FUNCTION update_chat_conversation_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_chat_conversations_updated_at
    BEFORE UPDATE ON chat_conversations
    FOR EACH ROW
    EXECUTE FUNCTION update_chat_conversation_updated_at();

-- Función para calcular el costo total de una conversación
CREATE OR REPLACE FUNCTION get_conversation_total_cost(conversation_uuid UUID)
RETURNS DECIMAL AS $$
BEGIN
    RETURN COALESCE(
        (SELECT SUM(cost_usd) FROM chat_messages WHERE conversation_id = conversation_uuid),
        0
    );
END;
$$ LANGUAGE plpgsql;

-- Vista para conversaciones con información agregada
CREATE VIEW chat_conversations_with_stats AS
SELECT 
    c.*,
    COUNT(m.id) as message_count,
    MAX(m.created_at) as last_message_at,
    SUM(m.total_tokens) as total_tokens_used,
    SUM(m.cost_usd) as total_cost_usd
FROM chat_conversations c
LEFT JOIN chat_messages m ON c.id = m.conversation_id
GROUP BY c.id;

-- Comentarios para documentación
COMMENT ON TABLE chat_conversations IS 'Almacena las conversaciones del asistente de chat por salón';
COMMENT ON TABLE chat_messages IS 'Mensajes individuales dentro de cada conversación';
COMMENT ON TABLE chat_context_references IS 'Referencias opcionales a otros recursos del sistema';
COMMENT ON COLUMN chat_conversations.context_type IS 'Tipo de contexto: general, client, service, formula, inventory';
COMMENT ON COLUMN chat_conversations.context_id IS 'ID del recurso relacionado si aplica';
COMMENT ON COLUMN chat_messages.cost_usd IS 'Costo estimado del mensaje en USD para tracking';