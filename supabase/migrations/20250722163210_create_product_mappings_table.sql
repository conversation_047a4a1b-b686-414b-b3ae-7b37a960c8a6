-- Create product_mappings table for learning product associations
create table if not exists product_mappings (
  id uuid primary key default gen_random_uuid(),
  salon_id uuid references salons(id) on delete cascade not null,
  ai_product_name text not null,
  inventory_product_id uuid references products(id) on delete cascade not null,
  confidence integer not null check (confidence between 0 and 100),
  usage_count integer default 1,
  created_at timestamp with time zone default now(),
  updated_at timestamp with time zone default now()
);

-- Create indexes for better performance
create index idx_product_mappings_salon_id on product_mappings(salon_id);
create index idx_product_mappings_ai_name on product_mappings(salon_id, ai_product_name);
create index idx_product_mappings_confidence on product_mappings(confidence desc);

-- Enable RLS
alter table product_mappings enable row level security;

-- Create RLS policies
create policy "Users can view their salon's mappings" on product_mappings
  for select using (
    salon_id in (
      select salon_id from profiles where id = auth.uid()
    )
  );

create policy "Users can create mappings for their salon" on product_mappings
  for insert with check (
    salon_id in (
      select salon_id from profiles where id = auth.uid()
    )
  );

create policy "Users can update their salon's mappings" on product_mappings
  for update using (
    salon_id in (
      select salon_id from profiles where id = auth.uid()
    )
  );

create policy "Users can delete their salon's mappings" on product_mappings
  for delete using (
    salon_id in (
      select salon_id from profiles where id = auth.uid()
    )
  );

-- Create function to update updated_at
create or replace function update_product_mappings_updated_at()
returns trigger as $$
begin
  new.updated_at = now();
  return new;
end;
$$ language plpgsql;

-- Create trigger to update updated_at
create trigger update_product_mappings_updated_at
  before update on product_mappings
  for each row
  execute function update_product_mappings_updated_at();

-- Create function to increment usage count
create or replace function increment_mapping_usage(
  p_salon_id uuid,
  p_ai_product_name text
)
returns void as $$
begin
  update product_mappings
  set usage_count = usage_count + 1,
      updated_at = now()
  where salon_id = p_salon_id
    and ai_product_name = p_ai_product_name;
end;
$$ language plpgsql security definer;