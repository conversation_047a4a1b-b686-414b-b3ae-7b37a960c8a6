-- Migration: AI Cache System for Performance Optimization
-- Date: 2025-08-07
-- Description: Creates cache table for AI responses to achieve 40% hit rate target

-- Create ai_cache table if not exists
CREATE TABLE IF NOT EXISTS public.ai_cache (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  cache_key TEXT NOT NULL UNIQUE,
  result JSONB NOT NULL,
  request_type TEXT NOT NULL CHECK (request_type IN ('diagnosis', 'formula', 'validation', 'chat')),
  salon_id UUID REFERENCES public.salons(id) ON DELETE CASCADE,
  expires_at TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  hit_count INTEGER DEFAULT 0,
  last_accessed TIMESTAMPTZ DEFAULT NOW(),
  confidence_score INTEGER DEFAULT 0,
  model_used TEXT,
  tokens_saved INTEGER DEFAULT 0
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_ai_cache_key ON public.ai_cache(cache_key);
CREATE INDEX IF NOT EXISTS idx_ai_cache_salon ON public.ai_cache(salon_id);
CREATE INDEX IF NOT EXISTS idx_ai_cache_expires ON public.ai_cache(expires_at);
CREATE INDEX IF NOT EXISTS idx_ai_cache_type ON public.ai_cache(request_type);
CREATE INDEX IF NOT EXISTS idx_ai_cache_created ON public.ai_cache(created_at DESC);

-- Create function to clean expired cache entries
CREATE OR REPLACE FUNCTION clean_expired_ai_cache()
RETURNS void AS $$
BEGIN
  DELETE FROM public.ai_cache 
  WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- Create function to update cache hit statistics
CREATE OR REPLACE FUNCTION update_cache_hit(p_cache_key TEXT)
RETURNS void AS $$
BEGIN
  UPDATE public.ai_cache 
  SET 
    hit_count = hit_count + 1,
    last_accessed = NOW()
  WHERE cache_key = p_cache_key;
END;
$$ LANGUAGE plpgsql;

-- Create table for AI usage metrics
CREATE TABLE IF NOT EXISTS public.ai_usage_metrics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  salon_id UUID REFERENCES public.salons(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  model TEXT NOT NULL,
  request_type TEXT NOT NULL,
  total_requests INTEGER DEFAULT 0,
  cache_hits INTEGER DEFAULT 0,
  total_tokens INTEGER DEFAULT 0,
  tokens_saved INTEGER DEFAULT 0,
  total_cost DECIMAL(10,4) DEFAULT 0,
  cost_saved DECIMAL(10,4) DEFAULT 0,
  avg_latency_ms INTEGER DEFAULT 0,
  error_count INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(salon_id, date, model, request_type)
);

-- Create index for metrics queries
CREATE INDEX IF NOT EXISTS idx_ai_metrics_salon_date ON public.ai_usage_metrics(salon_id, date DESC);
CREATE INDEX IF NOT EXISTS idx_ai_metrics_model ON public.ai_usage_metrics(model);

-- Create function to update AI usage metrics
CREATE OR REPLACE FUNCTION update_ai_usage_metrics(
  p_salon_id UUID,
  p_model TEXT,
  p_request_type TEXT,
  p_tokens INTEGER,
  p_cost DECIMAL,
  p_latency_ms INTEGER,
  p_is_cache_hit BOOLEAN,
  p_tokens_saved INTEGER DEFAULT 0
)
RETURNS void AS $$
BEGIN
  INSERT INTO public.ai_usage_metrics (
    salon_id,
    date,
    model,
    request_type,
    total_requests,
    cache_hits,
    total_tokens,
    tokens_saved,
    total_cost,
    cost_saved,
    avg_latency_ms
  ) VALUES (
    p_salon_id,
    CURRENT_DATE,
    p_model,
    p_request_type,
    1,
    CASE WHEN p_is_cache_hit THEN 1 ELSE 0 END,
    CASE WHEN p_is_cache_hit THEN 0 ELSE p_tokens END,
    CASE WHEN p_is_cache_hit THEN p_tokens_saved ELSE 0 END,
    CASE WHEN p_is_cache_hit THEN 0 ELSE p_cost END,
    CASE WHEN p_is_cache_hit THEN p_cost ELSE 0 END,
    p_latency_ms
  )
  ON CONFLICT (salon_id, date, model, request_type) 
  DO UPDATE SET
    total_requests = ai_usage_metrics.total_requests + 1,
    cache_hits = ai_usage_metrics.cache_hits + CASE WHEN p_is_cache_hit THEN 1 ELSE 0 END,
    total_tokens = ai_usage_metrics.total_tokens + CASE WHEN p_is_cache_hit THEN 0 ELSE p_tokens END,
    tokens_saved = ai_usage_metrics.tokens_saved + CASE WHEN p_is_cache_hit THEN p_tokens_saved ELSE 0 END,
    total_cost = ai_usage_metrics.total_cost + CASE WHEN p_is_cache_hit THEN 0 ELSE p_cost END,
    cost_saved = ai_usage_metrics.cost_saved + CASE WHEN p_is_cache_hit THEN p_cost ELSE 0 END,
    avg_latency_ms = (ai_usage_metrics.avg_latency_ms * ai_usage_metrics.total_requests + p_latency_ms) / (ai_usage_metrics.total_requests + 1),
    updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Create view for cache hit rate analysis
CREATE OR REPLACE VIEW ai_cache_analytics AS
SELECT 
  salon_id,
  date,
  SUM(total_requests) as total_requests,
  SUM(cache_hits) as total_cache_hits,
  ROUND(SUM(cache_hits)::NUMERIC / NULLIF(SUM(total_requests), 0) * 100, 2) as cache_hit_rate,
  SUM(total_tokens) as total_tokens_used,
  SUM(tokens_saved) as total_tokens_saved,
  SUM(total_cost) as total_cost,
  SUM(cost_saved) as total_cost_saved,
  ROUND(AVG(avg_latency_ms)) as avg_latency_ms
FROM public.ai_usage_metrics
GROUP BY salon_id, date
ORDER BY date DESC;

-- RLS policies for ai_cache
ALTER TABLE public.ai_cache ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Salons can view their own cache" ON public.ai_cache
  FOR SELECT USING (
    salon_id IN (
      SELECT id FROM public.salons WHERE owner_id = auth.uid()
      UNION
      SELECT salon_id FROM public.employees WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Salons can insert their own cache" ON public.ai_cache
  FOR INSERT WITH CHECK (
    salon_id IN (
      SELECT id FROM public.salons WHERE owner_id = auth.uid()
      UNION
      SELECT salon_id FROM public.employees WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Salons can update their own cache" ON public.ai_cache
  FOR UPDATE USING (
    salon_id IN (
      SELECT id FROM public.salons WHERE owner_id = auth.uid()
      UNION
      SELECT salon_id FROM public.employees WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Salons can delete their own cache" ON public.ai_cache
  FOR DELETE USING (
    salon_id IN (
      SELECT id FROM public.salons WHERE owner_id = auth.uid()
      UNION
      SELECT salon_id FROM public.employees WHERE user_id = auth.uid()
    )
  );

-- RLS policies for ai_usage_metrics
ALTER TABLE public.ai_usage_metrics ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Salons can view their own metrics" ON public.ai_usage_metrics
  FOR SELECT USING (
    salon_id IN (
      SELECT id FROM public.salons WHERE owner_id = auth.uid()
      UNION
      SELECT salon_id FROM public.employees WHERE user_id = auth.uid()
    )
  );

-- Schedule cleanup job (requires pg_cron extension)
-- Note: This needs to be run as superuser or configured separately
-- SELECT cron.schedule('clean-ai-cache', '0 */6 * * *', 'SELECT clean_expired_ai_cache();');

-- Grant permissions
GRANT ALL ON public.ai_cache TO authenticated;
GRANT ALL ON public.ai_usage_metrics TO authenticated;
GRANT SELECT ON ai_cache_analytics TO authenticated;

-- Comments for documentation
COMMENT ON TABLE public.ai_cache IS 'Cache table for AI responses to reduce API calls and costs';
COMMENT ON TABLE public.ai_usage_metrics IS 'Metrics tracking for AI usage, costs, and performance';
COMMENT ON VIEW ai_cache_analytics IS 'Analytics view for cache hit rates and cost savings';