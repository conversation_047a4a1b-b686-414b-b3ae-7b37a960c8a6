-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create salons table (multi-tenancy base)
CREATE TABLE salons (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  owner_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create profiles table (usuarios con permisos)
CREATE TABLE profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  salon_id UUID REFERENCES salons(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  full_name TEXT,
  role TEXT CHECK (role IN ('owner', 'manager', 'stylist', 'apprentice')) DEFAULT 'stylist',
  permissions TEXT[] DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create clients table
CREATE TABLE clients (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  phone TEXT,
  email TEXT,
  birth_date DATE,
  allergies TEXT[] DEFAULT '{}',
  medical_conditions TEXT,
  current_medications TEXT,
  notes TEXT,
  tags TEXT[] DEFAULT '{}',
  is_vip BOOLEAN DEFAULT false,
  created_by UUID REFERENCES profiles(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create products table (inventario)
CREATE TABLE products (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
  brand TEXT NOT NULL,
  name TEXT NOT NULL,
  line TEXT,
  type TEXT CHECK (type IN ('color', 'developer', 'treatment', 'shampoo', 'conditioner', 'styling', 'other')),
  size_ml DECIMAL NOT NULL,
  stock_ml DECIMAL DEFAULT 0,
  cost_per_unit DECIMAL,
  sale_price DECIMAL,
  minimum_stock_ml DECIMAL DEFAULT 0,
  barcode TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create stock_movements table
CREATE TABLE stock_movements (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
  product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  type TEXT CHECK (type IN ('purchase', 'use', 'adjustment', 'return', 'waste')) NOT NULL,
  quantity_ml DECIMAL NOT NULL,
  reference_id UUID, -- Can reference services, adjustments, etc.
  reference_type TEXT, -- 'service', 'adjustment', etc.
  notes TEXT,
  created_by UUID REFERENCES profiles(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create services table (historial de servicios)
CREATE TABLE services (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
  client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
  stylist_id UUID NOT NULL REFERENCES profiles(id),
  service_date TIMESTAMPTZ DEFAULT NOW(),
  service_type TEXT NOT NULL,
  duration_minutes INTEGER,
  price DECIMAL,
  notes TEXT,
  before_photos TEXT[] DEFAULT '{}',
  after_photos TEXT[] DEFAULT '{}',
  ai_analysis JSONB,
  status TEXT CHECK (status IN ('scheduled', 'in_progress', 'completed', 'cancelled')) DEFAULT 'scheduled',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create formulas table
CREATE TABLE formulas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
  service_id UUID REFERENCES services(id) ON DELETE CASCADE,
  name TEXT,
  formula_text TEXT NOT NULL,
  formula_data JSONB NOT NULL, -- Structured formula data
  total_cost DECIMAL,
  processing_time_minutes INTEGER,
  technique TEXT,
  brand TEXT,
  line TEXT,
  created_by UUID REFERENCES profiles(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create client_consents table
CREATE TABLE client_consents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
  client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
  service_id UUID REFERENCES services(id) ON DELETE CASCADE,
  consent_type TEXT CHECK (consent_type IN ('patch_test', 'chemical_process', 'photo_usage', 'data_processing')) NOT NULL,
  consent_text TEXT NOT NULL,
  signature_url TEXT,
  signed_at TIMESTAMPTZ,
  ip_address INET,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create ai_analysis_cache table
CREATE TABLE ai_analysis_cache (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
  analysis_type TEXT NOT NULL,
  input_hash TEXT NOT NULL,
  input_data JSONB,
  result JSONB NOT NULL,
  model_used TEXT,
  tokens_used INTEGER,
  cost_usd DECIMAL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ DEFAULT NOW() + INTERVAL '30 days',
  UNIQUE(salon_id, analysis_type, input_hash)
);

-- Create indexes for performance
CREATE INDEX idx_profiles_salon_id ON profiles(salon_id);
CREATE INDEX idx_clients_salon_id ON clients(salon_id);
CREATE INDEX idx_products_salon_id ON products(salon_id);
CREATE INDEX idx_stock_movements_salon_id ON stock_movements(salon_id);
CREATE INDEX idx_stock_movements_product_id ON stock_movements(product_id);
CREATE INDEX idx_services_salon_id ON services(salon_id);
CREATE INDEX idx_services_client_id ON services(client_id);
CREATE INDEX idx_services_stylist_id ON services(stylist_id);
CREATE INDEX idx_formulas_salon_id ON formulas(salon_id);
CREATE INDEX idx_formulas_service_id ON formulas(service_id);
CREATE INDEX idx_client_consents_salon_id ON client_consents(salon_id);
CREATE INDEX idx_client_consents_client_id ON client_consents(client_id);
CREATE INDEX idx_ai_analysis_cache_salon_id ON ai_analysis_cache(salon_id);
CREATE INDEX idx_ai_analysis_cache_input_hash ON ai_analysis_cache(input_hash);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers
CREATE TRIGGER update_salons_updated_at BEFORE UPDATE ON salons
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON clients
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_services_updated_at BEFORE UPDATE ON services
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();