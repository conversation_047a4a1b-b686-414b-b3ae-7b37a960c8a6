-- =====================================================
-- PROVEN FORMULAS SYSTEM MIGRATION
-- Creates a collective knowledge base for successful formulations
-- =====================================================

-- Create proven_formulas table for storing successful cases
CREATE TABLE IF NOT EXISTS public.proven_formulas (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    scenario_hash text NOT NULL UNIQUE, -- Hash of diagnosis + desired result for fast matching
    
    -- Case details
    diagnosis_summary text NOT NULL, -- Condensed diagnosis key points
    desired_result_summary text NOT NULL, -- Target result description
    
    -- Formula data
    formula jsonb NOT NULL, -- Complete formula with products, ratios, techniques
    brand text NOT NULL,
    line text,
    
    -- Success metrics
    success_count integer DEFAULT 1 NOT NULL, -- Times this formula worked well
    avg_rating numeric(3,2) DEFAULT 0.0 NOT NULL, -- Average rating from stylists (0.0-5.0)
    total_uses integer DEFAULT 1 NOT NULL, -- Total times this formula was used
    
    -- Metadata
    created_at timestamptz DEFAULT now() NOT NULL,
    updated_at timestamptz DEFAULT now() NOT NULL,
    
    -- Constraints
    CONSTRAINT chk_avg_rating CHECK (avg_rating >= 0.0 AND avg_rating <= 5.0),
    CONSTRAINT chk_success_count CHECK (success_count >= 0),
    CONSTRAINT chk_total_uses CHECK (total_uses >= 0)
);

-- Create formula_feedback table for tracking usage outcomes
CREATE TABLE IF NOT EXISTS public.formula_feedback (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    formula_id uuid NOT NULL REFERENCES public.proven_formulas(id) ON DELETE CASCADE,
    salon_id uuid NOT NULL REFERENCES public.salons(id) ON DELETE CASCADE,
    user_id uuid NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    service_id uuid REFERENCES public.services(id) ON DELETE SET NULL,
    
    -- Outcome data
    worked_as_expected boolean NOT NULL,
    actual_result text, -- Description of actual outcome
    rating integer NOT NULL, -- 1-5 star rating
    adjustments_made text, -- What changes were needed
    would_use_again boolean DEFAULT true NOT NULL,
    
    -- Context
    hair_type text, -- Type of hair this was used on
    environmental_factors text, -- Weather, humidity, etc.
    
    -- Timestamps
    created_at timestamptz DEFAULT now() NOT NULL,
    updated_at timestamptz DEFAULT now() NOT NULL,
    
    -- Constraints
    CONSTRAINT chk_rating CHECK (rating >= 1 AND rating <= 5),
    
    -- Unique constraint to prevent duplicate feedback from same user/salon/formula
    UNIQUE(formula_id, salon_id, user_id, service_id)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Primary lookup index for scenario matching
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_proven_formulas_scenario_hash 
ON public.proven_formulas(scenario_hash);

-- Brand and line filtering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_proven_formulas_brand_line 
ON public.proven_formulas(brand, line) WHERE line IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_proven_formulas_brand 
ON public.proven_formulas(brand);

-- Success metrics for ranking
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_proven_formulas_success_metrics 
ON public.proven_formulas(avg_rating DESC, success_count DESC, total_uses DESC);

-- Text search on summaries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_proven_formulas_diagnosis_text 
ON public.proven_formulas USING gin(to_tsvector('english', diagnosis_summary));

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_proven_formulas_result_text 
ON public.proven_formulas USING gin(to_tsvector('english', desired_result_summary));

-- Formula JSONB search
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_proven_formulas_formula_gin 
ON public.proven_formulas USING gin(formula);

-- Feedback table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_formula_feedback_formula_id 
ON public.formula_feedback(formula_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_formula_feedback_salon_id 
ON public.formula_feedback(salon_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_formula_feedback_rating 
ON public.formula_feedback(rating DESC, worked_as_expected);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_formula_feedback_created_at 
ON public.formula_feedback(created_at DESC);

-- =====================================================
-- UTILITY FUNCTIONS
-- =====================================================

-- Function to generate scenario hash for consistent matching
CREATE OR REPLACE FUNCTION public.generate_scenario_hash(
    p_diagnosis_summary text,
    p_desired_result_summary text,
    p_brand text,
    p_line text DEFAULT NULL
) RETURNS text
LANGUAGE plpgsql
IMMUTABLE
AS $$
BEGIN
    RETURN encode(
        digest(
            LOWER(TRIM(p_diagnosis_summary)) || '|' || 
            LOWER(TRIM(p_desired_result_summary)) || '|' || 
            LOWER(TRIM(p_brand)) || '|' || 
            COALESCE(LOWER(TRIM(p_line)), ''),
            'sha256'
        ),
        'hex'
    );
END;
$$;

-- Function to add or update a proven formula
CREATE OR REPLACE FUNCTION public.upsert_proven_formula(
    p_diagnosis_summary text,
    p_desired_result_summary text,
    p_formula jsonb,
    p_brand text,
    p_line text DEFAULT NULL,
    p_initial_rating integer DEFAULT 5
) RETURNS uuid
LANGUAGE plpgsql
AS $$
DECLARE
    v_scenario_hash text;
    v_formula_id uuid;
BEGIN
    -- Generate consistent hash
    v_scenario_hash := public.generate_scenario_hash(
        p_diagnosis_summary, 
        p_desired_result_summary, 
        p_brand, 
        p_line
    );
    
    -- Insert or update formula
    INSERT INTO public.proven_formulas (
        scenario_hash,
        diagnosis_summary,
        desired_result_summary,
        formula,
        brand,
        line,
        success_count,
        avg_rating,
        total_uses
    ) VALUES (
        v_scenario_hash,
        p_diagnosis_summary,
        p_desired_result_summary,
        p_formula,
        p_brand,
        p_line,
        1,
        p_initial_rating::numeric,
        1
    )
    ON CONFLICT (scenario_hash) DO UPDATE SET
        formula = EXCLUDED.formula,
        success_count = public.proven_formulas.success_count + 1,
        total_uses = public.proven_formulas.total_uses + 1,
        updated_at = now()
    RETURNING id INTO v_formula_id;
    
    RETURN v_formula_id;
END;
$$;

-- Function to find similar proven formulas
CREATE OR REPLACE FUNCTION public.find_similar_formulas(
    p_diagnosis_keywords text,
    p_desired_keywords text,
    p_brand text DEFAULT NULL,
    p_min_rating numeric DEFAULT 3.0,
    p_limit integer DEFAULT 10
) RETURNS TABLE (
    id uuid,
    scenario_hash text,
    diagnosis_summary text,
    desired_result_summary text,
    formula jsonb,
    brand text,
    line text,
    success_count integer,
    avg_rating numeric,
    total_uses integer,
    similarity_score numeric
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pf.id,
        pf.scenario_hash,
        pf.diagnosis_summary,
        pf.desired_result_summary,
        pf.formula,
        pf.brand,
        pf.line,
        pf.success_count,
        pf.avg_rating,
        pf.total_uses,
        (
            -- Text similarity scoring
            GREATEST(
                similarity(pf.diagnosis_summary, p_diagnosis_keywords),
                similarity(pf.desired_result_summary, p_desired_keywords)
            ) * 0.7 +
            -- Success rate scoring
            (pf.avg_rating / 5.0) * 0.3
        ) as similarity_score
    FROM public.proven_formulas pf
    WHERE 
        pf.avg_rating >= p_min_rating
        AND (p_brand IS NULL OR pf.brand = p_brand)
        AND (
            pf.diagnosis_summary % p_diagnosis_keywords 
            OR pf.desired_result_summary % p_desired_keywords
            OR to_tsvector('english', pf.diagnosis_summary) @@ plainto_tsquery('english', p_diagnosis_keywords)
            OR to_tsvector('english', pf.desired_result_summary) @@ plainto_tsquery('english', p_desired_keywords)
        )
    ORDER BY similarity_score DESC, pf.avg_rating DESC, pf.success_count DESC
    LIMIT p_limit;
END;
$$;

-- Function to record formula feedback and update metrics
CREATE OR REPLACE FUNCTION public.record_formula_feedback(
    p_formula_id uuid,
    p_salon_id uuid,
    p_user_id uuid,
    p_service_id uuid,
    p_worked_as_expected boolean,
    p_actual_result text,
    p_rating integer,
    p_adjustments_made text DEFAULT NULL,
    p_would_use_again boolean DEFAULT true,
    p_hair_type text DEFAULT NULL,
    p_environmental_factors text DEFAULT NULL
) RETURNS uuid
LANGUAGE plpgsql
AS $$
DECLARE
    v_feedback_id uuid;
    v_new_avg_rating numeric;
    v_new_success_count integer;
BEGIN
    -- Insert feedback record
    INSERT INTO public.formula_feedback (
        formula_id,
        salon_id,
        user_id,
        service_id,
        worked_as_expected,
        actual_result,
        rating,
        adjustments_made,
        would_use_again,
        hair_type,
        environmental_factors
    ) VALUES (
        p_formula_id,
        p_salon_id,
        p_user_id,
        p_service_id,
        p_worked_as_expected,
        p_actual_result,
        p_rating,
        p_adjustments_made,
        p_would_use_again,
        p_hair_type,
        p_environmental_factors
    )
    RETURNING id INTO v_feedback_id;
    
    -- Recalculate formula metrics
    WITH feedback_stats AS (
        SELECT 
            AVG(rating)::numeric(3,2) as avg_rating,
            COUNT(*) FILTER (WHERE worked_as_expected = true) as success_count,
            COUNT(*) as total_uses
        FROM public.formula_feedback 
        WHERE formula_id = p_formula_id
    )
    UPDATE public.proven_formulas 
    SET 
        avg_rating = feedback_stats.avg_rating,
        success_count = feedback_stats.success_count,
        total_uses = feedback_stats.total_uses,
        updated_at = now()
    FROM feedback_stats
    WHERE id = p_formula_id;
    
    RETURN v_feedback_id;
END;
$$;

-- =====================================================
-- ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS on both tables
ALTER TABLE public.proven_formulas ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.formula_feedback ENABLE ROW LEVEL SECURITY;

-- Proven formulas are globally readable but only insertable by authenticated users
CREATE POLICY "proven_formulas_select" ON public.proven_formulas
    FOR SELECT USING (true);

CREATE POLICY "proven_formulas_insert" ON public.proven_formulas
    FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "proven_formulas_update" ON public.proven_formulas
    FOR UPDATE USING (auth.uid() IS NOT NULL);

-- Formula feedback is restricted to salon members
CREATE POLICY "formula_feedback_select" ON public.formula_feedback
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles p 
            WHERE p.id = auth.uid() AND p.salon_id = formula_feedback.salon_id
        )
    );

CREATE POLICY "formula_feedback_insert" ON public.formula_feedback
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.profiles p 
            WHERE p.id = auth.uid() AND p.salon_id = salon_id
        )
        AND user_id = auth.uid()
    );

CREATE POLICY "formula_feedback_update" ON public.formula_feedback
    FOR UPDATE USING (user_id = auth.uid());

-- =====================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Update timestamp trigger for proven_formulas
CREATE OR REPLACE FUNCTION public.update_proven_formulas_updated_at()
RETURNS trigger AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_proven_formulas_updated_at
    BEFORE UPDATE ON public.proven_formulas
    FOR EACH ROW
    EXECUTE FUNCTION public.update_proven_formulas_updated_at();

-- Update timestamp trigger for formula_feedback
CREATE OR REPLACE FUNCTION public.update_formula_feedback_updated_at()
RETURNS trigger AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_formula_feedback_updated_at
    BEFORE UPDATE ON public.formula_feedback
    FOR EACH ROW
    EXECUTE FUNCTION public.update_formula_feedback_updated_at();

-- =====================================================
-- SAMPLE DATA FOR TESTING
-- =====================================================

-- Add pg_trgm extension for similarity search if not exists
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Sample proven formula for testing
DO $$
DECLARE
    v_formula_id uuid;
BEGIN
    -- Only insert if no proven formulas exist
    IF NOT EXISTS (SELECT 1 FROM public.proven_formulas LIMIT 1) THEN
        v_formula_id := public.upsert_proven_formula(
            'Virgin brown hair level 4, medium texture, good condition',
            'Light blonde balayage with warm golden tones',
            '{
                "products": [
                    {"brand": "Wella", "line": "Blondor", "product": "Multi Blonde", "ratio": "30g"},
                    {"brand": "Wella", "line": "Koleston", "product": "20 vol developer", "ratio": "60ml"}
                ],
                "technique": "Hand-painted balayage with foil wrapping",
                "process_time": "45 minutes",
                "toner": {"brand": "Wella", "shade": "T18", "volume": "10 vol", "time": "15 minutes"}
            }'::jsonb,
            'Wella',
            'Blondor',
            5
        );
    END IF;
END $$;