-- Fix RLS infinite recursion issue

-- Drop existing problematic policies
DROP POLICY IF EXISTS "Los miembros pueden ver los perfiles de su salón." ON public.profiles;
DROP POLICY IF EXISTS "Los usuarios pueden actualizar su propio perfil." ON public.profiles;
DROP POLICY IF EXISTS "Los miembros pueden ver la información de su propio salón." ON public.salons;
DROP POLICY IF EXISTS "Los propietarios pueden actualizar su salón." ON public.salons;

-- Create new policies for profiles table
-- Allow users to view their own profile
CREATE POLICY "Users can view own profile" 
ON public.profiles FOR SELECT 
USING (auth.uid() = id);

-- Allow users to view profiles in their salon
CREATE POLICY "Users can view salon profiles" 
ON public.profiles FOR SELECT 
USING (
  salon_id IN (
    SELECT salon_id FROM public.profiles WHERE id = auth.uid()
  )
);

-- Allow users to update their own profile
CREATE POLICY "Users can update own profile" 
ON public.profiles FOR UPDATE 
USING (auth.uid() = id);

-- Allow insert for new users (needed for trigger)
CREATE POLICY "Enable insert for authentication" 
ON public.profiles FOR INSERT 
WITH CHECK (auth.uid() = id);

-- Create new policies for salons table
-- Allow members to view their salon
CREATE POLICY "Members can view their salon" 
ON public.salons FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE profiles.id = auth.uid()
    AND profiles.salon_id = salons.id
  )
);

-- Allow owners to update their salon
CREATE POLICY "Owners can update their salon" 
ON public.salons FOR UPDATE 
USING (owner_id = auth.uid());

-- Allow insert for new salons (needed for trigger)
CREATE POLICY "Enable insert for salon creation" 
ON public.salons FOR INSERT 
WITH CHECK (owner_id = auth.uid());

-- Recreate the trigger function with better error handling
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  new_salon_id UUID;
BEGIN
  -- Create a new salon for the user
  INSERT INTO public.salons (name, owner_id, settings)
  VALUES (
    COALESCE(new.raw_user_meta_data->>'salon_name', new.raw_user_meta_data->>'full_name', new.email) || '''s Salon',
    new.id,
    jsonb_build_object(
      'hasCompletedOnboarding', false,
      'skipSafetyVerification', false
    )
  )
  RETURNING id INTO new_salon_id;

  -- Create the user profile
  INSERT INTO public.profiles (id, salon_id, email, full_name, role, permissions, is_active)
  VALUES (
    new.id,
    new_salon_id,
    new.email,
    new.raw_user_meta_data->>'full_name',
    'owner',
    ARRAY['VIEW_ALL_CLIENTS', 'VIEW_COSTS', 'MODIFY_PRICES', 'MANAGE_INVENTORY', 'VIEW_REPORTS', 'CREATE_USERS', 'DELETE_DATA']::text[],
    true
  );

  RETURN new;
EXCEPTION
  WHEN OTHERS THEN
    -- Log error but don't fail the user creation
    RAISE LOG 'Error in handle_new_user trigger: %', SQLERRM;
    RETURN new;
END;
$$;

-- Ensure trigger exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();