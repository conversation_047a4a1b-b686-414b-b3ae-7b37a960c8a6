-- Create storage buckets
INSERT INTO storage.buckets (id, name, public, avif_autodetection, file_size_limit, allowed_mime_types)
VALUES 
  ('temp-photos', 'temp-photos', false, false, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp']),
  ('client-photos', 'client-photos', false, false, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp']),
  ('signatures', 'signatures', false, false, 1048576, ARRAY['image/png', 'image/svg+xml'])
ON CONFLICT (id) DO NOTHING;

-- Storage policies for temp-photos bucket
-- Users can upload to their salon's folder
CREATE POLICY "Users can upload temp photos" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'temp-photos' AND
    (storage.foldername(name))[1] = auth.salon_id()::text
  );

-- Users can view their salon's temp photos
CREATE POLICY "Users can view temp photos" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'temp-photos' AND
    (storage.foldername(name))[1] = auth.salon_id()::text
  );

-- Users can delete their salon's temp photos
CREATE POLICY "Users can delete temp photos" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'temp-photos' AND
    (storage.foldername(name))[1] = auth.salon_id()::text
  );

-- Storage policies for client-photos bucket
-- Users can upload client photos for their salon
CREATE POLICY "Users can upload client photos" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'client-photos' AND
    (storage.foldername(name))[1] = auth.salon_id()::text
  );

-- Users can view client photos based on client access
CREATE POLICY "Users can view client photos" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'client-photos' AND
    (storage.foldername(name))[1] = auth.salon_id()::text AND
    (
      auth.has_permission('VIEW_ALL_CLIENTS') OR
      EXISTS (
        SELECT 1 FROM clients 
        WHERE clients.id = ((storage.foldername(name))[2])::uuid
        AND clients.created_by = auth.uid()
      )
    )
  );

-- Storage policies for signatures bucket
-- Users can upload signatures for their salon
CREATE POLICY "Users can upload signatures" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'signatures' AND
    (storage.foldername(name))[1] = auth.salon_id()::text
  );

-- Users can view signatures based on client access
CREATE POLICY "Users can view signatures" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'signatures' AND
    (storage.foldername(name))[1] = auth.salon_id()::text AND
    (
      auth.has_permission('VIEW_ALL_CLIENTS') OR
      EXISTS (
        SELECT 1 FROM client_consents cc
        JOIN clients c ON c.id = cc.client_id
        WHERE cc.signature_url LIKE '%' || name || '%'
        AND c.created_by = auth.uid()
      )
    )
  );

-- Function to auto-delete old temp photos (run daily via pg_cron or similar)
CREATE OR REPLACE FUNCTION clean_temp_photos()
RETURNS void AS $$
BEGIN
  DELETE FROM storage.objects
  WHERE bucket_id = 'temp-photos'
  AND created_at < NOW() - INTERVAL '24 hours';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;