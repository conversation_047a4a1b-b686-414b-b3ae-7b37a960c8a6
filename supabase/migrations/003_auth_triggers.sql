-- Function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  new_salon_id UUID;
BEGIN
  -- Check if user is being added to existing salon (has metadata.salon_id)
  IF NEW.raw_user_meta_data->>'salon_id' IS NOT NULL THEN
    -- Employee joining existing salon
    new_salon_id := (NEW.raw_user_meta_data->>'salon_id')::UUID;
    
    -- Create profile for employee
    INSERT INTO public.profiles (id, salon_id, email, full_name, role, permissions)
    VALUES (
      NEW.id,
      new_salon_id,
      NEW.email,
      COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
      COALESCE(NEW.raw_user_meta_data->>'role', 'stylist'),
      COALESCE(
        ARRAY(SELECT jsonb_array_elements_text(NEW.raw_user_meta_data->'permissions')),
        '{}'::TEXT[]
      )
    );
  ELSE
    -- New salon owner registration
    -- Create salon
    INSERT INTO public.salons (name, owner_id)
    VALUES (
      COALESCE(NEW.raw_user_meta_data->>'salon_name', 'Mi Salón'),
      NEW.id
    )
    RETURNING id INTO new_salon_id;
    
    -- Create owner profile with all permissions
    INSERT INTO public.profiles (id, salon_id, email, full_name, role, permissions)
    VALUES (
      NEW.id,
      new_salon_id,
      NEW.email,
      COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
      'owner',
      ARRAY['VIEW_ALL_CLIENTS', 'VIEW_COSTS', 'MODIFY_PRICES', 'MANAGE_INVENTORY', 'VIEW_REPORTS', 'CREATE_USERS', 'DELETE_DATA']
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to call the function on user creation
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to clean up user data on deletion
CREATE OR REPLACE FUNCTION public.handle_user_delete()
RETURNS TRIGGER AS $$
BEGIN
  -- If user is salon owner, optionally transfer ownership or delete salon
  -- For now, we'll let CASCADE handle the deletion
  RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for user deletion
CREATE TRIGGER on_auth_user_deleted
  BEFORE DELETE ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_user_delete();