# Supabase Backend Setup

Este directorio contiene las migraciones SQL necesarias para configurar el backend de Salonier en Supabase.

## Estructura de Archivos

- `migrations/001_initial_schema.sql` - Esquema inicial con todas las tablas
- `migrations/002_row_level_security.sql` - Políticas de seguridad RLS
- `migrations/003_auth_triggers.sql` - Triggers para manejo de usuarios
- `migrations/004_storage_buckets.sql` - Configuración de storage buckets

## Cómo Aplicar las Migraciones

### Opción 1: Usar el Dashboard de Supabase (Recomendado para empezar)

1. Ve a tu proyecto en https://supabase.com/dashboard
2. Navega a "SQL Editor"
3. Ejecuta cada archivo de migración en orden:
   - Primero: `001_initial_schema.sql`
   - Segundo: `002_row_level_security.sql`
   - <PERSON><PERSON><PERSON>: `003_auth_triggers.sql`
   - Cuarto: `004_storage_buckets.sql`

### Opción 2: Usar Supabase CLI

1. Instala Supabase CLI:

```bash
npm install -g supabase
```

2. Vincula tu proyecto:

```bash
supabase link --project-ref ajsamgugqfbttkrlgvbr
```

3. Aplica las migraciones:

```bash
supabase db push
```

## Configuración Post-Migración

### 1. Configurar Authentication

1. Ve a "Authentication" > "Providers" en el dashboard
2. Habilita los proveedores que necesites:
   - Email/Password (recomendado)
   - Google (opcional)
   - Apple (opcional)

### 2. Configurar Storage

Los buckets ya están creados, pero puedes ajustar:

- Límites de tamaño de archivo
- Políticas de auto-eliminación para temp-photos

### 3. Verificar RLS

Asegúrate de que RLS esté habilitado en todas las tablas:

```sql
-- Verificar estado de RLS
SELECT tablename, rowsecurity
FROM pg_tables
WHERE schemaname = 'public';
```

## Arquitectura de Seguridad

### Multi-tenancy

- Cada salón está completamente aislado
- Los usuarios solo pueden ver datos de su salón
- La función `auth.salon_id()` obtiene el salón del usuario actual

### Permisos

- Los propietarios (owners) tienen todos los permisos
- Los empleados tienen permisos específicos configurables
- La función `auth.has_permission()` verifica permisos

### Storage

- **temp-photos**: Fotos temporales, auto-delete en 24h
- **client-photos**: Fotos permanentes de clientes
- **signatures**: Firmas digitales de consentimientos

## Próximos Pasos

1. Crear la Edge Function `salonier-assistant` para IA
2. Migrar los stores existentes para usar Supabase
3. Implementar sincronización offline-first

## Troubleshooting

### Error: "permission denied for schema public"

Asegúrate de ejecutar las migraciones con un usuario que tenga permisos de superusuario.

### Error: "relation does not exist"

Verifica que las migraciones se ejecutaron en el orden correcto.

### Las políticas RLS no funcionan

1. Verifica que RLS esté habilitado en las tablas
2. Revisa que el usuario tenga un perfil creado
3. Verifica los logs de Postgres para más detalles
