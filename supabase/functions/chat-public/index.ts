// Salonier Public Chat API - SIMPLIFIED
// Version: 2.0 - Consolidated for International Public Access Only
// Last Updated: 2025-08-17
// Changes: Simplified after consolidation - only handles public, unauthenticated chat

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';

// CORS headers for international access
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Max-Age': '86400',
};

// Configuration
const OPENAI_API_KEY = Deno.env.get('OPENAI_API_KEY');
const COST_PER_1K_PROMPT = 0.0025;
const COST_PER_1K_COMPLETION = 0.01;

// Rate limiting
const rateLimits = new Map<string, { count: number; resetAt: number }>();
const RATE_LIMIT = 10; // requests per minute
const RATE_WINDOW = 60 * 1000; // 1 minute

interface ChatRequest {
  conversationId: string;
  message: string;
  salonId: string;
  userId: string;
  attachments?: Array<{
    type: 'image' | 'document';
    url: string;
    mimeType?: string;
  }>;
}

const SYSTEM_PROMPT = `You are Salonier Assistant, the world's most respected hair color consultant.

IDENTITY:
- 20+ years of professional colorimetry experience
- Technical consultant for prestigious global brands
- Specialist in color correction and advanced techniques
- International mentor for professional colorists

CAPABILITIES:
- Professional hair image analysis
- Diagnosis of level, tone, and hair condition
- Custom formulation for all hair types
- Market-adapted recommendations

INTERNATIONAL CONSIDERATIONS:
- Respond in the user's language
- Adapt recommendations by region
- Consider different hair types (Asian, European, African, Latin)
- Use metric measurements with conversions when needed
- Consider local product regulations

Always respond as the world's most trusted colorimetry expert.`;

function checkRateLimit(ip: string): boolean {
  const now = Date.now();
  const limit = rateLimits.get(ip);

  if (!limit || now > limit.resetAt) {
    rateLimits.set(ip, { count: 1, resetAt: now + RATE_WINDOW });
    return true;
  }

  if (limit.count >= RATE_LIMIT) {
    return false;
  }

  limit.count++;
  return true;
}

serve(async req => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Get client IP
    const clientIp =
      req.headers.get('x-forwarded-for')?.split(',')[0] ||
      req.headers.get('x-real-ip') ||
      'unknown';

    // Check rate limit
    if (!checkRateLimit(clientIp)) {
      return new Response(
        JSON.stringify({
          error: 'Too many requests. Please wait a moment.',
          retryAfter: 60,
        }),
        {
          status: 429,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json',
            'Retry-After': '60',
          },
        }
      );
    }

    // Validate API key
    if (!OPENAI_API_KEY) {
      // OPENAI_API_KEY not configured
      return new Response(JSON.stringify({ error: 'Chat service not available' }), {
        status: 503,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Parse request
    const data: ChatRequest = await req.json();

    // Validate input
    if (!data.message || !data.salonId || !data.userId) {
      return new Response(JSON.stringify({ error: 'Missing required fields' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Build messages
    const messages: any[] = [{ role: 'system', content: SYSTEM_PROMPT }];

    // Handle user message
    if (data.attachments?.some(a => a.type === 'image')) {
      // Vision API format
      const content: any[] = [{ type: 'text', text: data.message }];

      for (const att of data.attachments.filter(a => a.type === 'image').slice(0, 3)) {
        content.push({
          type: 'image_url',
          image_url: { url: att.url, detail: 'high' },
        });
      }

      messages.push({ role: 'user', content });
    } else {
      messages.push({ role: 'user', content: data.message });
    }

    // Call OpenAI
    const openAIResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o',
        messages,
        max_tokens: 1000,
        temperature: 0.7,
      }),
    });

    if (!openAIResponse.ok) {
      const error = await openAIResponse.text();
      // OpenAI API error occurred

      const errorMessage =
        openAIResponse.status === 429
          ? 'AI service is busy. Please try again.'
          : 'Failed to process request.';

      return new Response(JSON.stringify({ error: errorMessage }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    const result = await openAIResponse.json();
    const usage = result.usage;
    const content = result.choices[0].message.content;

    // Calculate cost
    const cost =
      (usage.prompt_tokens / 1000) * COST_PER_1K_PROMPT +
      (usage.completion_tokens / 1000) * COST_PER_1K_COMPLETION;

    // Return response
    return new Response(
      JSON.stringify({
        content,
        usage: {
          promptTokens: usage.prompt_tokens,
          completionTokens: usage.completion_tokens,
          totalTokens: usage.total_tokens,
          cost,
        },
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    // Chat public error occurred
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
