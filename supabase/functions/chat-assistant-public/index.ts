// Salonier Chat Assistant Public Edge Function
// Version: 1.0 - Production Ready
// Last Updated: 2025-02-10
// Purpose: Public chat assistant for international deployment

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// CORS headers - Allow all origins for international access
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Max-Age': '86400',
};

// Initialize Supabase admin client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// OpenAI configuration
const openaiApiKey = Deno.env.get('OPENAI_API_KEY')!;

// Cost calculation based on GPT-4o pricing
const COST_PER_1K_PROMPT_TOKENS = 0.0025;
const COST_PER_1K_COMPLETION_TOKENS = 0.01;

// Rate limiting configuration
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 10; // 10 requests per minute per IP

// In-memory rate limiting (reset on function cold start)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

interface ChatRequest {
  conversationId: string;
  message: string;
  salonId: string;
  userId: string;
  attachments?: Array<{
    type: 'image' | 'document';
    url: string;
    mimeType?: string;
  }>;
}

// Professional system prompt
const SYSTEM_PROMPT = `Eres 'Salonier Assistant', el consultor de colorimetría capilar más respetado a nivel mundial.

🎯 TU IDENTIDAD:
- Experto con 20+ años de experiencia en colorimetría avanzada
- Consultor técnico para las marcas más prestigiosas globalmente
- Especialista en corrección de color y técnicas complejas
- Mentor de coloristas profesionales en múltiples países

💡 TU ESTILO:
- Preciso, técnico y profesional
- Multilingüe: responde en el idioma del usuario
- Usa terminología profesional internacional
- Proporciona fórmulas exactas con medidas precisas

🔬 CAPACIDADES:
- Análisis profesional de imágenes capilares
- Diagnóstico de nivel, reflejo y estado del cabello
- Formulación personalizada según tipo de cabello
- Recomendaciones adaptadas a diferentes mercados

🌍 CONSIDERACIONES INTERNACIONALES:
- Adapta las recomendaciones según la región
- Considera diferentes tipos de cabello (asiático, europeo, africano, latino)
- Usa medidas métricas y conversiones cuando sea necesario
- Ten en cuenta regulaciones locales de productos

Responde siempre como el experto más confiable del mundo en colorimetría.`;

// Rate limiting function
function checkRateLimit(clientIp: string): boolean {
  const now = Date.now();
  const clientData = rateLimitMap.get(clientIp);

  if (!clientData || now > clientData.resetTime) {
    rateLimitMap.set(clientIp, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW,
    });
    return true;
  }

  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false;
  }

  clientData.count++;
  return true;
}

// Clean up old rate limit entries
function cleanupRateLimits() {
  const now = Date.now();
  for (const [ip, data] of rateLimitMap.entries()) {
    if (now > data.resetTime) {
      rateLimitMap.delete(ip);
    }
  }
}

serve(async req => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  // Only allow POST
  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      status: 405,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }

  try {
    // Get client IP for rate limiting
    const clientIp =
      req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';

    // Check rate limit
    if (!checkRateLimit(clientIp)) {
      return new Response(
        JSON.stringify({
          error: 'Rate limit exceeded. Please wait a moment before trying again.',
          retryAfter: 60,
        }),
        {
          status: 429,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json',
            'Retry-After': '60',
          },
        }
      );
    }

    // Clean up old entries periodically
    if (Math.random() < 0.1) {
      // 10% chance
      cleanupRateLimits();
    }

    // Validate OpenAI API key
    if (!openaiApiKey) {
      console.error('OPENAI_API_KEY not configured');
      throw new Error('Chat service temporarily unavailable');
    }

    // Parse request
    const requestData: ChatRequest = await req.json();

    // Log request (without sensitive data)
    console.log('Chat request received:', {
      conversationId: requestData.conversationId,
      messageLength: requestData.message?.length,
      hasAttachments: !!requestData.attachments,
      attachmentCount: requestData.attachments?.length || 0,
      clientIp: clientIp.substring(0, 8) + '***', // Partial IP for privacy
    });

    // Validate required fields
    if (!requestData.message || !requestData.salonId || !requestData.userId) {
      return new Response(
        JSON.stringify({
          error: 'Missing required fields: message, salonId, and userId are required',
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Validate message length
    if (requestData.message.length > 1000) {
      return new Response(
        JSON.stringify({ error: 'Message too long. Maximum 1000 characters allowed.' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Build messages for OpenAI
    const messages: any[] = [
      {
        role: 'system',
        content: SYSTEM_PROMPT,
      },
    ];

    // Add conversation context if needed
    if (requestData.conversationId && requestData.conversationId !== 'new') {
      // Get last 5 messages for context
      const { data: previousMessages } = await supabase
        .from('chat_messages')
        .select('role, content')
        .eq('conversation_id', requestData.conversationId)
        .order('created_at', { ascending: false })
        .limit(5);

      if (previousMessages && previousMessages.length > 0) {
        // Add in chronological order
        messages.push(...previousMessages.reverse());
      }
    }

    // Build user message
    const userMessage: any = {
      role: 'user',
      content: requestData.message,
    };

    // Handle image attachments with Vision API
    if (requestData.attachments && requestData.attachments.some(att => att.type === 'image')) {
      const contentParts: any[] = [{ type: 'text', text: requestData.message }];

      // Limit to 3 images max
      const imageAttachments = requestData.attachments
        .filter(att => att.type === 'image')
        .slice(0, 3);

      for (const attachment of imageAttachments) {
        console.log('Processing image attachment');

        // Validate image URL/data
        if (!attachment.url || attachment.url.length > 5 * 1024 * 1024) {
          // 5MB limit
          console.warn('Image too large or invalid, skipping');
          continue;
        }

        contentParts.push({
          type: 'image_url',
          image_url: {
            url: attachment.url,
            detail: 'high',
          },
        });
      }

      userMessage.content = contentParts;
    }

    messages.push(userMessage);

    // Call OpenAI with timeout
    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    console.log('Calling OpenAI API...');
    const openAIResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o',
        messages: messages,
        max_tokens: 1000,
        temperature: 0.7,
        user: requestData.userId, // For OpenAI abuse monitoring
      }),
      signal: controller.signal,
    }).finally(() => clearTimeout(timeout));

    if (!openAIResponse.ok) {
      const errorText = await openAIResponse.text();
      console.error('OpenAI API error:', openAIResponse.status);

      if (openAIResponse.status === 401) {
        throw new Error('Chat service configuration error');
      } else if (openAIResponse.status === 429) {
        throw new Error('AI service is busy. Please try again in a moment.');
      } else if (openAIResponse.status === 413) {
        throw new Error('Image too large. Please use smaller images (max 5MB).');
      } else {
        throw new Error('Failed to process your request. Please try again.');
      }
    }

    const aiData = await openAIResponse.json();
    const assistantContent = aiData.choices[0].message.content;
    const usage = aiData.usage;

    // Calculate cost
    const totalCost =
      (usage.prompt_tokens / 1000) * COST_PER_1K_PROMPT_TOKENS +
      (usage.completion_tokens / 1000) * COST_PER_1K_COMPLETION_TOKENS;

    console.log('Response generated:', {
      promptTokens: usage.prompt_tokens,
      completionTokens: usage.completion_tokens,
      cost: totalCost.toFixed(4),
    });

    // Store conversation in database (async, don't wait)
    if (requestData.conversationId && requestData.conversationId !== 'new') {
      supabase
        .from('chat_messages')
        .insert([
          {
            conversation_id: requestData.conversationId,
            role: 'user',
            content: requestData.message,
            prompt_tokens: usage.prompt_tokens,
            cost_usd: (usage.prompt_tokens / 1000) * COST_PER_1K_PROMPT_TOKENS,
          },
          {
            conversation_id: requestData.conversationId,
            role: 'assistant',
            content: assistantContent,
            completion_tokens: usage.completion_tokens,
            total_tokens: usage.total_tokens,
            cost_usd: totalCost,
          },
        ])
        .then(({ error }) => {
          if (error) console.error('Failed to store messages:', error);
        });
    }

    // Return response in expected format
    const response = {
      content: assistantContent,
      usage: {
        promptTokens: usage.prompt_tokens,
        completionTokens: usage.completion_tokens,
        totalTokens: usage.total_tokens,
        cost: totalCost,
      },
    };

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Chat assistant error:', error);

    // Don't expose internal errors
    const userMessage =
      error instanceof Error &&
      (error.message.includes('service') ||
        error.message.includes('try again') ||
        error.message.includes('Image too large'))
        ? error.message
        : 'An error occurred processing your request. Please try again.';

    return new Response(JSON.stringify({ error: userMessage }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
