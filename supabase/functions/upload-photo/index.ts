import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

interface UploadRequest {
  imageBase64: string;
  salonId: string;
  clientId: string;
  photoType: 'before' | 'after' | 'desired';
  usePrivateBucket?: boolean; // New flag for private bucket uploads
}

interface UploadResponse {
  success: boolean;
  publicUrl?: string; // Legacy support
  signedUrl?: string; // New private bucket signed URL
  bucket?: string;
  path?: string;
  expiresAt?: number;
  error?: string;
}

// Initialize Supabase clients
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

// Helper function to log photo access for GDPR audit trail
async function logPhotoAccess(
  supabaseClient: any,
  salonId: string,
  userId: string,
  bucketName: string,
  filePath: string,
  action: string
) {
  try {
    const { error } = await supabaseClient.from('photo_access_log').insert({
      salon_id: salonId,
      user_id: userId,
      bucket_name: bucketName,
      file_path: filePath,
      action: action,
    });

    if (error) {
      // Failed to log photo access - non-critical
    }
  } catch (err) {
    // Error logging photo access - non-critical
  }
}

// Security utilities
const securityUtils = {
  validateInput: (input: any): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    // Required field validation
    if (!input.imageBase64 || typeof input.imageBase64 !== 'string') {
      errors.push('imageBase64 is required and must be a string');
    }
    if (!input.salonId || typeof input.salonId !== 'string') {
      errors.push('salonId is required and must be a string');
    }
    if (!input.clientId || typeof input.clientId !== 'string') {
      errors.push('clientId is required and must be a string');
    }
    if (!input.photoType || !['before', 'after', 'desired'].includes(input.photoType)) {
      errors.push('photoType must be one of: before, after, desired');
    }

    // Image size validation (10MB limit)
    if (input.imageBase64) {
      const sizeBytes = (input.imageBase64.length * 3) / 4;
      const maxSizeBytes = 10 * 1024 * 1024; // 10MB
      
      if (sizeBytes > maxSizeBytes) {
        errors.push(`Image size (${Math.round(sizeBytes / 1024 / 1024)}MB) exceeds maximum allowed (10MB)`);
      }

      // Basic base64 validation
      const base64Clean = input.imageBase64.replace(/^data:image\/[a-z]+;base64,/, '');
      if (!/^[A-Za-z0-9+/]*={0,2}$/.test(base64Clean)) {
        errors.push('Invalid base64 image format');
      }
    }

    // UUID format validation for salonId and clientId
    const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (input.salonId && !uuidPattern.test(input.salonId)) {
      errors.push('Invalid salonId format (must be UUID)');
    }
    if (input.clientId && !uuidPattern.test(input.clientId)) {
      errors.push('Invalid clientId format (must be UUID)');
    }

    return { isValid: errors.length === 0, errors };
  },

  sanitizeError: (error: unknown): string => {
    if (error instanceof Error) {
      // Don't expose internal system errors
      const sensitivePatterns = [
        /database/i, /connection/i, /internal/i,
        /supabase/i, /postgresql/i, /service_role/i
      ];
      
      if (sensitivePatterns.some(pattern => pattern.test(error.message))) {
        return 'An internal error occurred. Please try again.';
      }
      
      return error.message;
    }
    
    return 'An unexpected error occurred.';
  },

  checkRateLimit: (userId: string): boolean => {
    // Simple in-memory rate limiting (5 uploads per minute)
    const now = Date.now();
    const key = `upload_${userId}`;
    
    if (!globalThis.rateLimits) {
      globalThis.rateLimits = new Map();
    }
    
    const userLimits = globalThis.rateLimits.get(key) || { count: 0, resetTime: now + 60000 };
    
    // Reset if window expired
    if (now >= userLimits.resetTime) {
      userLimits.count = 0;
      userLimits.resetTime = now + 60000;
    }
    
    // Check limit
    if (userLimits.count >= 5) {
      return false; // Rate limited
    }
    
    userLimits.count += 1;
    globalThis.rateLimits.set(key, userLimits);
    
    return true;
  }
};

serve(async req => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  // Method validation
  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Method not allowed' 
    }), {
      status: 405,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  try {
    // Parse and validate request
    const requestBody = await req.json() as UploadRequest;
    const {
      imageBase64,
      salonId,
      clientId,
      photoType,
      usePrivateBucket = true, // Default to private bucket for security
    } = requestBody;

    // Input validation
    const validation = securityUtils.validateInput(requestBody);
    if (!validation.isValid) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Validation failed',
        details: validation.errors
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Validate required fields
    if (!imageBase64 || !salonId || !clientId || !photoType) {
      throw new Error('Missing required fields');
    }

    // Processing upload request

    // Validate authentication
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Missing authorization header'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Create authenticated client to verify user
    const supabaseAuth = createClient(supabaseUrl, supabaseAnonKey, {
      global: { headers: { Authorization: authHeader } },
    });

    const {
      data: { user },
      error: authError,
    } = await supabaseAuth.auth.getUser();
    if (authError || !user) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Authentication failed'
      }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Rate limiting check
    if (!securityUtils.checkRateLimit(user.id)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Rate limit exceeded. Please wait before uploading again.'
      }), {
        status: 429,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // User authenticated

    // Convert base64 to buffer
    const base64Clean = imageBase64.replace(/^data:image\/[a-z]+;base64,/, '');
    const binaryString = atob(base64Clean);
    const buffer = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      buffer[i] = binaryString.charCodeAt(i);
    }

    // Buffer processed

    // Generate unique filename
    const timestamp = Date.now();
    const filename = `${photoType}_${timestamp}.jpg`;
    const path = `${salonId}/${clientId}/${filename}`;

    // Use service role for storage operations
    const supabaseService = createClient(supabaseUrl, supabaseServiceKey);

    // Determine bucket based on flag
    const bucketName = usePrivateBucket ? 'client-photos-private' : 'client-photos';
    // Uploading to storage

    // Upload to storage
    const { error: uploadError } = await supabaseService.storage
      .from(bucketName)
      .upload(path, buffer, {
        contentType: 'image/jpeg',
        upsert: false,
      });

    if (uploadError) {
      // Upload failed
      throw new Error(`Failed to upload image: ${uploadError.message}`);
    }

    // Upload successful

    let response: UploadResponse;

    if (usePrivateBucket) {
      // Generate signed URL for private bucket (1 hour expiration)
      const expiresIn = 3600; // 1 hour
      const { data: signedData, error: signedError } = await supabaseService.storage
        .from(bucketName)
        .createSignedUrl(path, expiresIn);

      if (signedError) {
        // Signed URL generation failed
        throw new Error(`Failed to generate signed URL: ${signedError.message}`);
      }

      if (!signedData?.signedUrl) {
        throw new Error('No signed URL returned from Supabase');
      }

      // Signed URL generated

      // Log for GDPR audit trail
      await logPhotoAccess(supabaseService, salonId, user.id, bucketName, path, 'upload');

      response = {
        success: true,
        signedUrl: signedData.signedUrl,
        publicUrl: signedData.signedUrl, // MISLEADING NAME: This is actually a PRIVATE signed URL for backward compatibility
        privateUrl: signedData.signedUrl, // Clear indication this is private
        bucket: bucketName,
        path: path,
        expiresAt: Date.now() + expiresIn * 1000,
      };
    } else {
      // Legacy: Get public URL (deprecated - use private signed URLs instead)
      const {
        data: { publicUrl },
      } = supabaseService.storage.from(bucketName).getPublicUrl(path);

      // Legacy public URL generated (deprecated)

      response = {
        success: true,
        publicUrl,
      };
    }

    return new Response(JSON.stringify(response), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json',
      },
    });
  } catch (error: any) {
    // Upload failed with error
    console.error('Upload photo error:', error);

    const sanitizedError = securityUtils.sanitizeError(error);
    
    const response: UploadResponse = {
      success: false,
      error: sanitizedError,
    };

    // Determine appropriate status code
    let statusCode = 500;
    if (error.message?.includes('Missing authorization') || error.message?.includes('Unauthorized')) {
      statusCode = 401;
    } else if (error.message?.includes('Validation failed') || error.message?.includes('Invalid')) {
      statusCode = 400;
    } else if (error.message?.includes('Rate limit')) {
      statusCode = 429;
    } else if (error.message?.includes('too large') || error.message?.includes('size')) {
      statusCode = 413; // Payload Too Large
    }

    return new Response(JSON.stringify(response), {
      status: statusCode,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json',
      },
    });
  }
});
