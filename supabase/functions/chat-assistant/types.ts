// Types for Chat Assistant

export interface ChatRequest {
  conversationId: string;
  message: string;
  salonId: string;
  userId: string;
  attachments?: Array<{
    type: 'image' | 'document';
    url: string;
    mimeType?: string;
  }>;
}

export interface ConversationState {
  currentFlow?: 'color_service' | 'consultation' | 'analysis' | null;
  flowStep?: 'gathering_info' | 'analyzing' | 'formulating' | 'confirming';

  // Datos del servicio construidos conversacionalmente
  serviceData?: {
    // Cliente
    clientName?: string;
    clientInfo?: string; // Info adicional (edad, estilo, etc)

    // Análisis actual (foto O descripción O ambos)
    currentColor?: {
      level?: number;
      reflect?: string;
      description?: string; // Descripción del usuario
      photoAnalysis?: any; // Si envió foto
      userCorrections?: string[]; // Correcciones/adiciones del usuario
      grayPercentage?: number;
      condition?: string;
    };

    // Objetivo (foto O descripción O ambos)
    desiredColor?: {
      level?: number;
      reflect?: string;
      description?: string;
      photoReference?: any;
      userNotes?: string[]; // "natural", "no muy frío", etc
      technique?: string; // balayage, mechas, etc
    };

    // Preferencias de productos
    preferredBrands?: string[];
    preferredLines?: string[];
    additionalProducts?: string[]; // "Olaplex", "SmartBond", etc
    avoidProducts?: string[]; // Productos a evitar

    // Fórmula generada
    formula?: {
      steps?: any[];
      userModifications?: string[]; // Cambios pedidos por usuario
      finalVersion?: string; // Versión final aprobada
    };
  };

  // Qué estamos esperando (más flexible)
  expecting?: 'any' | 'photo' | 'text' | 'brand_preference' | 'confirmation';

  // Contexto de la conversación
  conversationContext?: {
    lastQuestion?: string;
    pendingClarification?: string;
    mentionedBrands?: string[];
    mentionedProducts?: string[];
  };
}

export type Intent =
  | 'START_COLOR_SERVICE'
  | 'PROVIDE_CLIENT_INFO'
  | 'PROVIDE_CURRENT_PHOTO'
  | 'PROVIDE_CURRENT_DESCRIPTION'
  | 'PROVIDE_DESIRED_PHOTO'
  | 'PROVIDE_DESIRED_DESCRIPTION'
  | 'SPECIFY_BRANDS'
  | 'ADD_CORRECTION'
  | 'REQUEST_FORMULA'
  | 'APPROVE_FORMULA'
  | 'MODIFY_FORMULA'
  | 'SAVE_SERVICE'
  | 'ANSWER_QUESTION'
  | 'CONTINUE_CONVERSATION'
  | 'CANCEL_FLOW';

export interface ExtractedData {
  // Información del cliente
  clientName?: string;
  clientAge?: number;
  clientStyle?: string;

  // Colores y niveles
  level?: number;
  reflect?: string;
  grayPercentage?: number;

  // Productos
  brands?: string[];
  products?: string[];
  additionalProducts?: string[];

  // Técnica
  technique?: string;

  // Descripciones adicionales
  descriptions?: string[];
  corrections?: string[];
  notes?: string[];
}
