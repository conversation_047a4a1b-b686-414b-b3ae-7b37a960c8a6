// Salonier Chat Assistant Edge Function
// Version: 3 - Fixed authentication and response format
// Last Updated: 2025-02-10

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  'Access-Control-Max-Age': '86400',
};

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const openaiApiKey = Deno.env.get('OPENAI_API_KEY')!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Cost calculation based on GPT-4o pricing
const COST_PER_1K_PROMPT_TOKENS = 0.0025;
const COST_PER_1K_COMPLETION_TOKENS = 0.01;

interface ChatRequest {
  conversationId: string;
  message: string;
  salonId: string;
  userId: string;
  attachments?: Array<{
    type: 'image' | 'document';
    url: string;
    mimeType?: string;
  }>;
}

// Simple system prompt
const SYSTEM_PROMPT = `Eres 'Salonier Assistant', un consultor experto en colorimetría capilar.

TU ROL:
- Experto con 20+ años de experiencia en colorimetría
- Especialista en corrección de color y técnicas avanzadas
- Mentor de coloristas profesionales

CAPACIDADES:
- Análisis de imágenes de cabello
- Formulación de tintes personalizados
- Recomendaciones técnicas precisas
- Diagnóstico de problemas capilares

Responde de forma profesional, clara y directa.`;

serve(async req => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Get auth header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    const requestData: ChatRequest = await req.json();
    console.log('Chat request:', {
      conversationId: requestData.conversationId,
      hasMessage: !!requestData.message,
      salonId: requestData.salonId,
      userId: requestData.userId,
      hasAttachments: !!requestData.attachments,
    });

    // Validate request
    if (!requestData.message || !requestData.salonId || !requestData.userId) {
      throw new Error('Missing required parameters');
    }

    // Validate OpenAI API key
    if (!openaiApiKey) {
      console.error('OPENAI_API_KEY not configured');
      throw new Error('Chat assistant not configured properly');
    }

    // Build messages for OpenAI
    const messages: any[] = [
      {
        role: 'system',
        content: SYSTEM_PROMPT,
      },
    ];

    // Build user message
    const userMessage: any = {
      role: 'user',
      content: requestData.message,
    };

    // Handle image attachments
    if (requestData.attachments && requestData.attachments.some(att => att.type === 'image')) {
      const contentParts: any[] = [{ type: 'text', text: requestData.message }];

      for (const attachment of requestData.attachments) {
        if (attachment.type === 'image') {
          contentParts.push({
            type: 'image_url',
            image_url: {
              url: attachment.url,
              detail: 'high',
            },
          });
        }
      }

      userMessage.content = contentParts;
    }

    messages.push(userMessage);

    // Call OpenAI
    console.log('Calling OpenAI...');
    const openAIResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o',
        messages: messages,
        max_tokens: 1000,
        temperature: 0.7,
      }),
    });

    if (!openAIResponse.ok) {
      const errorText = await openAIResponse.text();
      console.error('OpenAI error:', openAIResponse.status, errorText);

      if (openAIResponse.status === 401) {
        throw new Error('Invalid OpenAI API key');
      } else if (openAIResponse.status === 429) {
        throw new Error('Rate limit exceeded. Please try again later.');
      } else {
        throw new Error(`OpenAI error: ${openAIResponse.status}`);
      }
    }

    const aiData = await openAIResponse.json();
    const assistantContent = aiData.choices[0].message.content;
    const usage = aiData.usage;

    console.log('OpenAI response received:', {
      promptTokens: usage?.prompt_tokens,
      completionTokens: usage?.completion_tokens,
    });

    // Calculate cost
    const totalCost =
      (usage.prompt_tokens / 1000) * COST_PER_1K_PROMPT_TOKENS +
      (usage.completion_tokens / 1000) * COST_PER_1K_COMPLETION_TOKENS;

    // Return simple format that chat-store expects
    const response = {
      content: assistantContent,
      usage: {
        promptTokens: usage.prompt_tokens,
        completionTokens: usage.completion_tokens,
        totalTokens: usage.total_tokens,
        cost: totalCost,
      },
    };

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Chat assistant error:', error);

    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : 'Unknown error',
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});
