/**
 * Enhanced Context System for Salonier Chat Assistant
 * Provides intelligent, contextual information for better AI responses
 */

interface StylistProfile {
  id: string;
  experienceLevel: 'junior' | 'mid' | 'senior' | 'expert';
  specialties: string[];
  preferredBrands: string[];
  successPatterns: {
    mostSuccessfulTechniques: string[];
    averageServiceTime: number;
    clientSatisfactionScore: number;
    preferredColorRanges: string[];
  };
  workingStyle: 'conservative' | 'moderate' | 'adventurous';
}

interface SalonContext {
  currentInventory: {
    brands: string[];
    lowStockProducts: Array<{
      name: string;
      brand: string;
      currentStock: number;
      minimumStock: number;
    }>;
    recentlyUsedProducts: string[];
  };
  upcomingAppointments: Array<{
    clientName: string;
    serviceType: string;
    scheduledTime: string;
    notes?: string;
  }>;
  recentServices: Array<{
    clientName: string;
    serviceType: string;
    formula: string;
    result: 'excellent' | 'good' | 'fair' | 'poor';
    date: string;
  }>;
  seasonalTrends: {
    currentSeason: string;
    popularColors: string[];
    trendingTechniques: string[];
  };
}

interface ConversationMemory {
  recentTopics: string[];
  preferredResponseStyle: 'concise' | 'detailed' | 'step-by-step';
  frequentQuestions: string[];
  clientPreferences: Record<string, any>;
  successfulFormulas: Array<{
    formula: string;
    technique: string;
    clientType: string;
    result: string;
  }>;
}

export class EnhancedContextManager {
  /**
   * Build comprehensive context for AI assistant
   */
  static async buildEnhancedContext(
    salonId: string,
    userId: string,
    conversationHistory: any[],
    contextType?: string,
    contextId?: string
  ): Promise<string> {
    const stylistProfile = await this.getStylistProfile(userId);
    const salonContext = await this.getSalonContext(salonId);
    const conversationMemory = this.buildConversationMemory(conversationHistory, userId);
    const specificContext = await this.getSpecificContext(contextType, contextId, salonId);

    return this.buildContextPrompt({
      stylistProfile,
      salonContext,
      conversationMemory,
      specificContext,
    });
  }

  /**
   * Get stylist profile and preferences
   */
  private static async getStylistProfile(userId: string): Promise<StylistProfile> {
    // This would query the database for stylist information
    // For now, return a default profile
    return {
      id: userId,
      experienceLevel: 'senior',
      specialties: ['balayage', 'color-correction', 'highlights'],
      preferredBrands: ['Wella', "L'Oreal Professional"],
      successPatterns: {
        mostSuccessfulTechniques: ['balayage', 'root-touch-up', 'full-color'],
        averageServiceTime: 120,
        clientSatisfactionScore: 4.8,
        preferredColorRanges: ['blonde', 'brunette'],
      },
      workingStyle: 'moderate',
    };
  }

  /**
   * Get current salon context
   */
  private static async getSalonContext(salonId: string): Promise<SalonContext> {
    // This would query the database for salon information
    return {
      currentInventory: {
        brands: ['Wella', "L'Oreal", 'Schwarzkopf'],
        lowStockProducts: [
          {
            name: 'Koleston Perfect 7/1',
            brand: 'Wella',
            currentStock: 2,
            minimumStock: 5,
          },
        ],
        recentlyUsedProducts: ['Koleston Perfect 8/0', 'Blondor Freelights'],
      },
      upcomingAppointments: [
        {
          clientName: 'María García',
          serviceType: 'Balayage',
          scheduledTime: '14:00',
          notes: 'Quiere un rubio natural',
        },
      ],
      recentServices: [
        {
          clientName: 'Ana López',
          serviceType: 'Color correction',
          formula: 'Wella Color Charm 6A + 20vol',
          result: 'excellent',
          date: '2025-01-15',
        },
      ],
      seasonalTrends: {
        currentSeason: 'winter',
        popularColors: ['chocolate brown', 'ash blonde', 'copper red'],
        trendingTechniques: ['money piece', 'face-framing highlights'],
      },
    };
  }

  /**
   * Build conversation memory from history
   */
  private static buildConversationMemory(
    conversationHistory: any[],
    userId: string
  ): ConversationMemory {
    const recentTopics = this.extractTopics(conversationHistory);
    const frequentQuestions = this.getFrequentQuestions(conversationHistory);

    return {
      recentTopics,
      preferredResponseStyle: 'detailed', // Could be learned from user behavior
      frequentQuestions,
      clientPreferences: {},
      successfulFormulas: [],
    };
  }

  /**
   * Get specific context based on conversation type
   */
  private static async getSpecificContext(
    contextType?: string,
    contextId?: string,
    salonId?: string
  ): Promise<any> {
    if (!contextType || !contextId) return null;

    switch (contextType) {
      case 'client':
        return await this.getClientContext(contextId, salonId!);
      case 'service':
        return await this.getServiceContext(contextId, salonId!);
      case 'formula':
        return await this.getFormulaContext(contextId, salonId!);
      default:
        return null;
    }
  }

  /**
   * Extract topics from conversation history
   */
  private static extractTopics(conversationHistory: any[]): string[] {
    const topics = new Set<string>();

    conversationHistory.forEach(msg => {
      const content = msg.content.toLowerCase();

      // Color-related topics
      if (content.includes('rubio') || content.includes('blonde')) topics.add('blonde');
      if (content.includes('moreno') || content.includes('brown')) topics.add('brown');
      if (content.includes('corrección') || content.includes('correction'))
        topics.add('color-correction');
      if (content.includes('balayage')) topics.add('balayage');
      if (content.includes('mechas') || content.includes('highlights')) topics.add('highlights');
      if (content.includes('fórmula') || content.includes('formula')) topics.add('formulas');
    });

    return Array.from(topics);
  }

  /**
   * Get frequent questions from history
   */
  private static getFrequentQuestions(conversationHistory: any[]): string[] {
    // Analyze conversation history to find patterns
    return [
      '¿Qué volumen de peróxido usar?',
      '¿Cómo neutralizar tonos naranjas?',
      'Tiempo de procesamiento recomendado',
    ];
  }

  /**
   * Build the final context prompt
   */
  private static buildContextPrompt(context: {
    stylistProfile: StylistProfile;
    salonContext: SalonContext;
    conversationMemory: ConversationMemory;
    specificContext?: any;
  }): string {
    const { stylistProfile, salonContext, conversationMemory, specificContext } = context;

    let prompt = `CONTEXTO PROFESIONAL AVANZADO:

PERFIL DEL ESTILISTA:
- Nivel de experiencia: ${stylistProfile.experienceLevel}
- Especialidades: ${stylistProfile.specialties.join(', ')}
- Marcas preferidas: ${stylistProfile.preferredBrands.join(', ')}
- Estilo de trabajo: ${stylistProfile.workingStyle}
- Técnicas más exitosas: ${stylistProfile.successPatterns.mostSuccessfulTechniques.join(', ')}
- Puntuación de satisfacción promedio: ${stylistProfile.successPatterns.clientSatisfactionScore}/5

ESTADO ACTUAL DEL SALÓN:
- Marcas disponibles: ${salonContext.currentInventory.brands.join(', ')}`;

    if (salonContext.currentInventory.lowStockProducts.length > 0) {
      prompt += `\n- PRODUCTOS BAJOS EN STOCK: ${salonContext.currentInventory.lowStockProducts
        .map(p => `${p.brand} ${p.name} (${p.currentStock} unidades)`)
        .join(', ')}`;
    }

    if (salonContext.upcomingAppointments.length > 0) {
      prompt += `\n- Próximas citas: ${salonContext.upcomingAppointments
        .map(apt => `${apt.clientName} - ${apt.serviceType} a las ${apt.scheduledTime}`)
        .join(', ')}`;
    }

    prompt += `\n- Tendencias actuales (${salonContext.seasonalTrends.currentSeason}): ${salonContext.seasonalTrends.popularColors.join(', ')}`;

    if (conversationMemory.recentTopics.length > 0) {
      prompt += `\n\nTEMAS RECIENTES EN LA CONVERSACIÓN: ${conversationMemory.recentTopics.join(', ')}`;
    }

    if (specificContext) {
      prompt += `\n\nCONTEXTO ESPECÍFICO:\n${JSON.stringify(specificContext, null, 2)}`;
    }

    prompt += `\n\nINSTRUCCIONES:
- Adapta tus respuestas al nivel de experiencia del estilista (${stylistProfile.experienceLevel})
- Prioriza las marcas disponibles en el salón
- Considera el inventario actual para las recomendaciones
- Mantén un estilo ${conversationMemory.preferredResponseStyle}
- Sugiere alternativas si hay productos con bajo stock`;

    return prompt;
  }

  /**
   * Context getters for specific types
   */
  private static async getClientContext(clientId: string, salonId: string) {
    // Query client information, service history, preferences
    return {
      name: 'Cliente Example',
      lastService: '2025-01-10',
      preferredColors: ['blonde', 'highlights'],
      allergies: [],
      hairType: 'fine',
    };
  }

  private static async getServiceContext(serviceId: string, salonId: string) {
    // Query service details
    return {
      serviceType: 'Balayage',
      date: '2025-01-15',
      duration: 180,
      formula: 'Wella Freelights + 20vol',
    };
  }

  private static async getFormulaContext(formulaId: string, salonId: string) {
    // Query formula details
    return {
      technique: 'Balayage',
      products: ['Wella Freelights', 'Olaplex No.1'],
      processingTime: 45,
      result: 'Level 8 blonde',
    };
  }
}
