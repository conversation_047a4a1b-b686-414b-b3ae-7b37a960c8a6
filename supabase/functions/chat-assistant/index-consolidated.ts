// <PERSON>ier Chat Assistant - Versión Consolidada
// Version: 2.1
// Last Updated: 2025-02-16
// Purpose: ChatGPT especializado en coloración con control total del usuario

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface ChatRequest {
  conversationId: string;
  message: string;
  salonId: string;
  userId: string;
  attachments?: Array<{
    type: 'image' | 'document';
    url: string;
    mimeType?: string;
  }>;
}

interface ConversationState {
  currentFlow?: 'color_service' | 'consultation' | 'analysis' | null;
  flowStep?: 'gathering_info' | 'analyzing' | 'formulating' | 'confirming';

  // Datos del servicio construidos conversacionalmente
  serviceData?: {
    // Cliente
    clientName?: string;
    clientInfo?: string; // Info adicional (edad, estilo, etc)

    // Análisis actual (foto O descripción O ambos)
    currentColor?: {
      level?: number;
      reflect?: string;
      description?: string; // Descripción del usuario
      photoAnalysis?: any; // Si envió foto
      userCorrections?: string[]; // Correcciones/adiciones del usuario
      grayPercentage?: number;
      condition?: string;
    };

    // Objetivo (foto O descripción O ambos)
    desiredColor?: {
      level?: number;
      reflect?: string;
      description?: string;
      photoReference?: any;
      userNotes?: string[]; // "natural", "no muy frío", etc
      technique?: string; // balayage, mechas, etc
    };

    // Preferencias de productos
    preferredBrands?: string[];
    preferredLines?: string[];
    additionalProducts?: string[]; // "Olaplex", "SmartBond", etc
    avoidProducts?: string[]; // Productos a evitar

    // Fórmula generada
    formula?: {
      steps?: any[];
      userModifications?: string[]; // Cambios pedidos por usuario
      finalVersion?: string; // Versión final aprobada
    };
  };

  // Qué estamos esperando (más flexible)
  expecting?: 'any' | 'photo' | 'text' | 'brand_preference' | 'confirmation';

  // Contexto de la conversación
  conversationContext?: {
    lastQuestion?: string;
    pendingClarification?: string;
    mentionedBrands?: string[];
    mentionedProducts?: string[];
  };
}

type Intent =
  | 'START_COLOR_SERVICE'
  | 'PROVIDE_CLIENT_INFO'
  | 'PROVIDE_CURRENT_PHOTO'
  | 'PROVIDE_CURRENT_DESCRIPTION'
  | 'PROVIDE_DESIRED_PHOTO'
  | 'PROVIDE_DESIRED_DESCRIPTION'
  | 'SPECIFY_BRANDS'
  | 'ADD_CORRECTION'
  | 'REQUEST_FORMULA'
  | 'APPROVE_FORMULA'
  | 'MODIFY_FORMULA'
  | 'SAVE_SERVICE'
  | 'ANSWER_QUESTION'
  | 'CONTINUE_CONVERSATION'
  | 'CANCEL_FLOW';

interface ExtractedData {
  // Información del cliente
  clientName?: string;
  clientAge?: number;
  clientStyle?: string;

  // Colores y niveles
  level?: number;
  reflect?: string;
  grayPercentage?: number;

  // Productos
  brands?: string[];
  products?: string[];
  additionalProducts?: string[];

  // Técnica
  technique?: string;

  // Descripciones adicionales
  descriptions?: string[];
  corrections?: string[];
  notes?: string[];
}

// ============================================================================
// DATA EXTRACTION FUNCTIONS
// ============================================================================

// Marcas conocidas de coloración profesional
const KNOWN_BRANDS = [
  // Marcas premium
  'wella',
  'loreal',
  "l'oreal",
  'schwarzkopf',
  'redken',
  'matrix',
  'goldwell',
  'alfaparf',
  'revlon',
  'pravana',
  'joico',
  'kenra',
  'paul mitchell',
  'guy tang',
  'olaplex',

  // Líneas específicas
  'koleston',
  'illumina',
  'color touch',
  'blondor',
  'majirel',
  'inoa',
  'dia light',
  'dia richesse',
  'igora',
  'blondme',
  'colorance',
  'topchic',
  'evolution',
  'chromatics',
  'color sync',

  // Productos de tratamiento
  'olaplex',
  'smartbond',
  'wellaplex',
  'fibreplex',
  'bond ultim8',
  'ph bonder',
  'matrix bond',
];

// Productos adicionales de tratamiento
const TREATMENT_PRODUCTS = [
  'olaplex',
  'smartbond',
  'wellaplex',
  'fibreplex',
  'bond ultim8',
  'ph bonder',
  'plex',
  'bond',
  'protector',
  'tratamiento',
  'keratina',
  'botox',
];

// Técnicas de aplicación
const TECHNIQUES = [
  'balayage',
  'babylights',
  'mechas',
  'reflejos',
  'ombre',
  'sombre',
  'degradado',
  'californiana',
  'tinte completo',
  'retoque raíz',
  'raíces',
  'cobertura canas',
  'corrección color',
];

/**
 * Extrae marcas mencionadas en el texto
 */
function detectBrands(text: string): string[] {
  const lowerText = text.toLowerCase();
  const foundBrands = new Set<string>();

  for (const brand of KNOWN_BRANDS) {
    if (lowerText.includes(brand)) {
      // Capitalizar correctamente
      const properName = brand
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
      foundBrands.add(properName);
    }
  }

  // Detectar formato "con X" o "usando X"
  const brandPatterns = [
    /con\s+(\w+)/gi,
    /usando\s+(\w+)/gi,
    /marca\s+(\w+)/gi,
    /prefiero\s+(\w+)/gi,
    /trabajo\s+con\s+(\w+)/gi,
  ];

  for (const pattern of brandPatterns) {
    const matches = text.matchAll(pattern);
    for (const match of matches) {
      const potentialBrand = match[1].toLowerCase();
      if (KNOWN_BRANDS.some(b => b.includes(potentialBrand))) {
        foundBrands.add(match[1]);
      }
    }
  }

  return Array.from(foundBrands);
}

/**
 * Extrae nivel de profundidad (1-10)
 */
function detectLevel(text: string): number | null {
  const patterns = [
    /nivel\s+(\d+(?:\.\d+)?)/i,
    /altura\s+de?\s+tono\s+(\d+(?:\.\d+)?)/i,
    /(\d+(?:\.\d+)?)\s+(?:de\s+)?(?:altura|nivel)/i,
    /base\s+(\d+(?:\.\d+)?)/i,
    /\b([1-9]|10)(?:\.\d+)?\b.*(?:rubio|castaño|negro|oscuro|claro)/i,
  ];

  for (const pattern of patterns) {
    const match = text.match(pattern);
    if (match) {
      const level = parseFloat(match[1]);
      if (level >= 1 && level <= 10) {
        return level;
      }
    }
  }

  // Detectar por descripción
  const descriptions: Record<string, number> = {
    negro: 1,
    'castaño muy oscuro': 3,
    'castaño oscuro': 4,
    'castaño medio': 5,
    'castaño claro': 6,
    'rubio oscuro': 6,
    'rubio medio': 7,
    rubio: 8,
    'rubio claro': 9,
    'rubio muy claro': 9,
    'rubio platino': 10,
    platino: 10,
  };

  const lowerText = text.toLowerCase();
  for (const [desc, level] of Object.entries(descriptions)) {
    if (lowerText.includes(desc)) {
      return level;
    }
  }

  return null;
}

/**
 * Extrae reflejo/matiz
 */
function detectReflect(text: string): string | null {
  const reflects: Record<string, string[]> = {
    Ceniza: ['ceniza', 'ash', 'gris', 'mate', '.1'],
    Natural: ['natural', 'neutro', '.0'],
    Dorado: ['dorado', 'gold', 'cálido', '.3'],
    Cobrizo: ['cobrizo', 'cobre', 'copper', '.4', '.44'],
    Caoba: ['caoba', 'mahogany', '.5'],
    Rojizo: ['rojizo', 'rojo', 'red', '.6', '.66'],
    'Verde/Mate': ['verde', 'mate', 'khaki', '.2'],
    Violeta: ['violeta', 'violet', 'morado', '.2'],
    Perlado: ['perlado', 'pearl', 'irisado', '.8', '.9'],
  };

  const lowerText = text.toLowerCase();

  for (const [reflect, keywords] of Object.entries(reflects)) {
    if (keywords.some(kw => lowerText.includes(kw))) {
      return reflect;
    }
  }

  return null;
}

/**
 * Extrae productos adicionales de tratamiento
 */
function detectAdditionalProducts(text: string): string[] {
  const lowerText = text.toLowerCase();
  const found = new Set<string>();

  for (const product of TREATMENT_PRODUCTS) {
    if (lowerText.includes(product)) {
      // Capitalizar correctamente productos conocidos
      if (product === 'olaplex') found.add('Olaplex');
      else if (product === 'smartbond') found.add('SmartBond');
      else if (product === 'wellaplex') found.add('Wellaplex');
      else if (product === 'fibreplex') found.add('Fibreplex');
      else found.add(product);
    }
  }

  return Array.from(found);
}

/**
 * Extrae técnica de aplicación
 */
function detectTechnique(text: string): string | null {
  const lowerText = text.toLowerCase();

  for (const technique of TECHNIQUES) {
    if (lowerText.includes(technique)) {
      return technique.charAt(0).toUpperCase() + technique.slice(1);
    }
  }

  return null;
}

/**
 * Extrae porcentaje de canas
 */
function detectGrayPercentage(text: string): number | null {
  const patterns = [
    /(\d+)\s*%?\s*(?:de\s+)?canas/i,
    /canas?\s+(?:al\s+)?(\d+)\s*%/i,
    /(\d+)\s*%?\s*(?:de\s+)?(?:cabello\s+)?blanco/i,
  ];

  for (const pattern of patterns) {
    const match = text.match(pattern);
    if (match) {
      const percentage = parseInt(match[1]);
      if (percentage >= 0 && percentage <= 100) {
        return percentage;
      }
    }
  }

  // Detectar por descripción
  if (/pocas?\s+canas?/i.test(text)) return 10;
  if (/algunas?\s+canas?/i.test(text)) return 20;
  if (/bastantes?\s+canas?/i.test(text)) return 40;
  if (/muchas?\s+canas?/i.test(text)) return 60;
  if (/totalmente\s+(?:blanco|cano)/i.test(text)) return 90;

  return null;
}

/**
 * Extrae nombre del cliente
 */
function detectClientName(text: string): string | null {
  const patterns = [
    /para\s+(?:mi\s+client[ea]\s+)?(\w+)/i,
    /client[ea]\s+(?:se\s+llama\s+)?(\w+)/i,
    /(?:se\s+llama|es)\s+(\w+)/i,
    /servicio\s+(?:para|de)\s+(\w+)/i,
  ];

  for (const pattern of patterns) {
    const match = text.match(pattern);
    if (match) {
      const name = match[1];
      // Verificar que no sea una palabra común
      const commonWords = ['el', 'la', 'un', 'una', 'mi', 'su', 'que', 'para'];
      if (!commonWords.includes(name.toLowerCase()) && name.length > 2) {
        return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase();
      }
    }
  }

  return null;
}

/**
 * Extrae toda la información posible del mensaje
 */
function extractAllData(text: string): ExtractedData {
  const data: ExtractedData = {};

  // Cliente
  const clientName = detectClientName(text);
  if (clientName) data.clientName = clientName;

  // Detectar edad
  const ageMatch = text.match(/(\d+)\s*años/i);
  if (ageMatch) {
    data.clientAge = parseInt(ageMatch[1]);
  }

  // Detectar estilo
  if (/conservador/i.test(text)) data.clientStyle = 'conservador';
  if (/moderno/i.test(text)) data.clientStyle = 'moderno';
  if (/atrevido/i.test(text)) data.clientStyle = 'atrevido';
  if (/clásico/i.test(text)) data.clientStyle = 'clásico';
  if (/natural/i.test(text)) data.clientStyle = 'natural';

  // Color
  const level = detectLevel(text);
  if (level) data.level = level;

  const reflect = detectReflect(text);
  if (reflect) data.reflect = reflect;

  const grayPercentage = detectGrayPercentage(text);
  if (grayPercentage) data.grayPercentage = grayPercentage;

  // Productos
  const brands = detectBrands(text);
  if (brands.length > 0) data.brands = brands;

  const additionalProducts = detectAdditionalProducts(text);
  if (additionalProducts.length > 0) data.additionalProducts = additionalProducts;

  // Técnica
  const technique = detectTechnique(text);
  if (technique) data.technique = technique;

  // Notas y correcciones
  if (text.includes('pero')) {
    const butPart = text.split('pero')[1];
    if (!data.corrections) data.corrections = [];
    data.corrections.push(butPart.trim());
  }

  if (text.includes('también')) {
    const alsoPart = text.split('también')[1];
    if (!data.notes) data.notes = [];
    data.notes.push(alsoPart.trim());
  }

  return data;
}

// ============================================================================
// HANDLER FUNCTIONS
// ============================================================================

async function handleCurrentPhoto(
  request: ChatRequest,
  messages: any[],
  state: ConversationState,
  callOpenAI: Function
): Promise<string> {
  const userMessage: any = {
    role: 'user',
    content: request.message,
  };

  // Add image if present
  if (request.attachments && request.attachments.length > 0) {
    const contentParts: any[] = [{ type: 'text', text: request.message }];

    for (const attachment of request.attachments) {
      if (attachment.type === 'image') {
        contentParts.push({
          type: 'image_url',
          image_url: {
            url: attachment.url,
            detail: 'high',
          },
        });
      }
    }

    userMessage.content = contentParts;
  }

  messages.push(userMessage);
  messages.push({
    role: 'system',
    content: `Analiza el color actual del cabello en la imagen. Identifica:
    - Nivel (1-10)
    - Reflejo/Matiz
    - Porcentaje de canas si las hay
    - Estado del cabello
    - Cualquier característica relevante
    
    El análisis ya está guardado en: ${JSON.stringify(state.serviceData?.currentColor)}.
    Si el usuario menciona correcciones, tenlas en cuenta.
    
    Después de describir el análisis, pregunta por el color objetivo de forma natural.`,
  });

  return await callOpenAI(messages);
}

async function handleCurrentDescription(
  request: ChatRequest,
  messages: any[],
  state: ConversationState,
  extractedData: ExtractedData,
  callOpenAI: Function
): Promise<string> {
  messages.push({
    role: 'user',
    content: request.message,
  });

  const currentColor = state.serviceData?.currentColor;

  messages.push({
    role: 'system',
    content: `El usuario describe el color actual como:
    - Nivel: ${currentColor?.level || 'no especificado'}
    - Reflejo: ${currentColor?.reflect || 'no especificado'}
    - Canas: ${currentColor?.grayPercentage ? currentColor.grayPercentage + '%' : 'no mencionadas'}
    - Descripción: ${currentColor?.description || request.message}
    
    Confirma estos datos y pregunta por el color objetivo.`,
  });

  return await callOpenAI(messages);
}

async function handleDesiredPhoto(
  request: ChatRequest,
  messages: any[],
  state: ConversationState,
  callOpenAI: Function
): Promise<string> {
  const userMessage: any = {
    role: 'user',
    content: request.message,
  };

  // Add image if present
  if (request.attachments && request.attachments.length > 0) {
    const contentParts: any[] = [{ type: 'text', text: request.message }];

    for (const attachment of request.attachments) {
      if (attachment.type === 'image') {
        contentParts.push({
          type: 'image_url',
          image_url: {
            url: attachment.url,
            detail: 'high',
          },
        });
      }
    }

    userMessage.content = contentParts;
  }

  messages.push(userMessage);
  messages.push({
    role: 'system',
    content: `Analiza el color objetivo/deseado en la imagen de referencia. Identifica:
    - Nivel objetivo (1-10)
    - Reflejo/Matiz deseado
    - Técnica sugerida (si aplica)
    
    Color actual del cliente: ${JSON.stringify(state.serviceData?.currentColor)}
    Color objetivo identificado: ${JSON.stringify(state.serviceData?.desiredColor)}
    
    Después del análisis, pregunta con qué marcas prefiere trabajar.`,
  });

  return await callOpenAI(messages);
}

async function handleDesiredDescription(
  request: ChatRequest,
  messages: any[],
  state: ConversationState,
  extractedData: ExtractedData,
  callOpenAI: Function
): Promise<string> {
  messages.push({
    role: 'user',
    content: request.message,
  });

  const desiredColor = state.serviceData?.desiredColor;

  messages.push({
    role: 'system',
    content: `El usuario describe el color objetivo como:
    - Nivel: ${desiredColor?.level || 'no especificado'}
    - Reflejo: ${desiredColor?.reflect || 'no especificado'}
    - Técnica: ${desiredColor?.technique || 'no especificada'}
    - Descripción: ${desiredColor?.description || request.message}
    - Notas: ${desiredColor?.userNotes?.join(', ') || 'ninguna'}
    
    Confirma estos datos y pregunta con qué marcas prefiere trabajar.`,
  });

  return await callOpenAI(messages);
}

async function handleBrandSpecification(
  request: ChatRequest,
  messages: any[],
  state: ConversationState,
  extractedData: ExtractedData,
  callOpenAI: Function
): Promise<string> {
  messages.push({
    role: 'user',
    content: request.message,
  });

  messages.push({
    role: 'system',
    content: `El usuario prefiere trabajar con: ${state.serviceData?.preferredBrands?.join(', ')}.
    ${state.serviceData?.additionalProducts ? 'Productos adicionales: ' + state.serviceData.additionalProducts.join(', ') : ''}
    
    Si ya tienes el análisis actual y objetivo, procede a generar la fórmula con esas marcas.`,
  });

  return await callOpenAI(messages);
}

async function handleCorrection(
  request: ChatRequest,
  messages: any[],
  state: ConversationState,
  extractedData: ExtractedData,
  callOpenAI: Function
): Promise<string> {
  messages.push({
    role: 'user',
    content: request.message,
  });

  messages.push({
    role: 'system',
    content: `El usuario está haciendo una corrección o agregando información.
    Datos actuales del servicio: ${JSON.stringify(state.serviceData)}
    Corrección/Adición: ${request.message}
    
    Confirma que has entendido la corrección y continúa con el siguiente paso necesario.`,
  });

  return await callOpenAI(messages);
}

async function generatePersonalizedFormula(
  state: ConversationState,
  messages: any[],
  callOpenAI: Function
): Promise<string> {
  const serviceData = state.serviceData;

  const prompt = `Genera una fórmula de coloración profesional PASO A PASO:
  
  CLIENTE: ${serviceData?.clientName} ${serviceData?.clientInfo ? '- ' + serviceData.clientInfo : ''}
  
  SITUACIÓN ACTUAL:
  - Nivel: ${serviceData?.currentColor?.level || 'no especificado'}
  - Reflejo: ${serviceData?.currentColor?.reflect || 'no especificado'}
  - Canas: ${serviceData?.currentColor?.grayPercentage ? serviceData.currentColor.grayPercentage + '%' : 'no especificadas'}
  - Estado: ${serviceData?.currentColor?.condition || 'no especificado'}
  - Notas: ${serviceData?.currentColor?.userCorrections?.join(', ') || 'ninguna'}
  
  OBJETIVO:
  - Nivel: ${serviceData?.desiredColor?.level || 'no especificado'}
  - Reflejo: ${serviceData?.desiredColor?.reflect || 'no especificado'}
  - Técnica: ${serviceData?.desiredColor?.technique || 'no especificada'}
  - Notas: ${serviceData?.desiredColor?.userNotes?.join(', ') || 'ninguna'}
  
  PRODUCTOS PREFERIDOS:
  - Marcas: ${serviceData?.preferredBrands?.join(', ') || 'cualquier marca profesional'}
  - Adicionales: ${serviceData?.additionalProducts?.join(', ') || 'ninguno'}
  
  Genera una fórmula detallada con:
  1. Productos específicos de las marcas solicitadas
  2. Proporciones exactas (gramos/ml)
  3. Tiempos de procesamiento
  4. Técnica de aplicación paso a paso
  5. Si pidió productos adicionales (Olaplex, etc), inclúyelos donde corresponda
  
  Formato conversacional y claro. Después de la fórmula, pregunta si quiere ajustar algo.`;

  messages.push({
    role: 'system',
    content: prompt,
  });

  return await callOpenAI(messages);
}

async function handleSaveService(
  request: ChatRequest,
  state: ConversationState,
  supabase: any
): Promise<string> {
  try {
    // Guardar el servicio en la base de datos
    const { data: service, error } = await supabase
      .from('services')
      .insert({
        salon_id: request.salonId,
        client_name: state.serviceData?.clientName,
        service_type: 'color',
        status: 'in_progress',
        current_hair_analysis: state.serviceData?.currentColor,
        desired_outcome: state.serviceData?.desiredColor,
        formula_data: state.serviceData?.formula,
        metadata: {
          created_from: 'chat',
          conversation_id: request.conversationId,
          preferred_brands: state.serviceData?.preferredBrands,
          additional_products: state.serviceData?.additionalProducts,
        },
      })
      .select()
      .single();

    if (error) throw error;

    return `✅ ¡Servicio guardado exitosamente!
    
    **Resumen del servicio:**
    - Cliente: ${state.serviceData?.clientName}
    - ID del servicio: #${service.id.slice(0, 8)}
    - Estado: En progreso
    
    Cuando termines el servicio, puedes enviarme una foto del resultado para documentarlo.
    
    ¿Hay algo más en lo que pueda ayudarte?`;
  } catch (error) {
    console.error('Error saving service:', error);
    return `❌ Hubo un error al guardar el servicio. Por favor, intenta de nuevo o contacta soporte.`;
  }
}

async function handleConversation(
  request: ChatRequest,
  messages: any[],
  state: ConversationState,
  callOpenAI: Function
): Promise<string> {
  const userMessage: any = {
    role: 'user',
    content: request.message,
  };

  // Add image if present
  if (request.attachments && request.attachments.length > 0) {
    const contentParts: any[] = [{ type: 'text', text: request.message }];

    for (const attachment of request.attachments) {
      if (attachment.type === 'image') {
        contentParts.push({
          type: 'image_url',
          image_url: {
            url: attachment.url,
            detail: 'high',
          },
        });
      }
    }

    userMessage.content = contentParts;
  }

  messages.push(userMessage);

  // Add context if in a flow
  if (state.currentFlow) {
    messages.push({
      role: 'system',
      content: `Contexto actual del servicio: ${JSON.stringify(state.serviceData)}.
      Continúa la conversación de forma natural. Si falta información importante, pregúntala.`,
    });
  }

  return await callOpenAI(messages);
}

// ============================================================================
// MAIN CODE
// ============================================================================

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  'Access-Control-Max-Age': '86400',
};

// Initialize clients
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const openaiApiKey = Deno.env.get('OPENAI_API_KEY');

if (!openaiApiKey) {
  console.error('OPENAI_API_KEY is not set');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// System prompt for the assistant
const SYSTEM_PROMPT = `Eres 'Salonier Assistant', un experto colorista y mentor con 20+ años de experiencia.

CAPACIDADES:
1. MODO CONSULTA: Responder preguntas técnicas sobre coloración
2. MODO SERVICIO: Guiar flujos completos de análisis y formulación
3. MODO HÍBRIDO: Mezclar consultas con acciones

PERSONALIDAD:
- Profesional pero cercano
- Detallado y preciso
- Educativo (explicas el por qué)
- Proactivo (sugieres siguientes pasos)

CUANDO ANALICES IMÁGENES:
- Identifica nivel (1-10) y reflejo
- Evalúa estado del cabello
- Sugiere tratamientos necesarios
- Sé específico con productos y tiempos

FORMATO:
- Usa bullets para listas
- Incluye proporciones exactas
- Especifica tiempos de procesamiento
- Advierte sobre riesgos

Recuerda: Puedes tanto responder preguntas como guiar servicios completos paso a paso.`;

// Detect user intent from message with data extraction
function detectIntentWithContext(
  message: string,
  attachments: any[],
  state: ConversationState
): { intent: Intent; extractedData: ExtractedData } {
  const lowerMessage = message.toLowerCase();
  const hasPhoto = attachments && attachments.some(a => a.type === 'image');

  // Extraer toda la información posible del mensaje
  const extractedData = extractAllData(message);

  // Si el usuario quiere cancelar
  if (
    lowerMessage.includes('cancelar') ||
    lowerMessage.includes('salir') ||
    lowerMessage.includes('terminar')
  ) {
    return { intent: 'CANCEL_FLOW', extractedData };
  }

  // Si el usuario quiere guardar el servicio
  if (
    state.serviceData?.formula &&
    (lowerMessage.includes('guardar') ||
      lowerMessage.includes('perfecto') ||
      lowerMessage === 'sí' ||
      lowerMessage === 'si' ||
      lowerMessage.includes('confirmar') ||
      lowerMessage.includes('adelante'))
  ) {
    return { intent: 'SAVE_SERVICE', extractedData };
  }

  // Detectar inicio de servicio de color
  if (
    lowerMessage.includes('servicio') ||
    (lowerMessage.includes('color') &&
      (lowerMessage.includes('hacer') || lowerMessage.includes('crear'))) ||
    lowerMessage.includes('cliente') ||
    extractedData.clientName
  ) {
    return { intent: 'START_COLOR_SERVICE', extractedData };
  }

  // Si hay una foto, determinar qué tipo de análisis es
  if (hasPhoto) {
    // Determinar si es color actual o deseado por contexto
    if (
      !state.serviceData?.currentColor ||
      lowerMessage.includes('actual') ||
      lowerMessage.includes('ahora') ||
      lowerMessage.includes('tiene')
    ) {
      return { intent: 'PROVIDE_CURRENT_PHOTO', extractedData };
    } else if (
      !state.serviceData?.desiredColor ||
      lowerMessage.includes('desead') ||
      lowerMessage.includes('objetivo') ||
      lowerMessage.includes('quiere') ||
      lowerMessage.includes('referencia')
    ) {
      return { intent: 'PROVIDE_DESIRED_PHOTO', extractedData };
    } else if (
      lowerMessage.includes('resultado') ||
      lowerMessage.includes('final') ||
      lowerMessage.includes('quedó')
    ) {
      return { intent: 'PROVIDE_DESIRED_PHOTO', extractedData }; // Reutilizamos para resultado
    }
  }

  // Si menciona marcas específicamente
  if (extractedData.brands && extractedData.brands.length > 0) {
    if (
      state.flowStep === 'formulating' ||
      (state.serviceData?.currentColor && state.serviceData?.desiredColor)
    ) {
      return { intent: 'SPECIFY_BRANDS', extractedData };
    }
  }

  // Si está proporcionando información sobre el color actual
  if (
    !hasPhoto &&
    (extractedData.level || extractedData.reflect) &&
    (!state.serviceData?.currentColor || lowerMessage.includes('actual'))
  ) {
    return { intent: 'PROVIDE_CURRENT_DESCRIPTION', extractedData };
  }

  // Si está proporcionando información sobre el color deseado
  if (
    !hasPhoto &&
    (extractedData.level || extractedData.reflect) &&
    state.serviceData?.currentColor &&
    !state.serviceData?.desiredColor
  ) {
    return { intent: 'PROVIDE_DESIRED_DESCRIPTION', extractedData };
  }

  // Si está haciendo correcciones o agregando información
  if (
    lowerMessage.includes('pero') ||
    lowerMessage.includes('también') ||
    lowerMessage.includes('agreg') ||
    lowerMessage.includes('además') ||
    lowerMessage.includes('cambiar') ||
    lowerMessage.includes('mejor')
  ) {
    return { intent: 'ADD_CORRECTION', extractedData };
  }

  // Si pide la fórmula explícitamente
  if (
    lowerMessage.includes('fórmula') ||
    lowerMessage.includes('mezcla') ||
    lowerMessage.includes('proporcion')
  ) {
    return { intent: 'REQUEST_FORMULA', extractedData };
  }

  // Si está proporcionando información del cliente
  if (extractedData.clientName || extractedData.clientAge || extractedData.clientStyle) {
    return { intent: 'PROVIDE_CLIENT_INFO', extractedData };
  }

  // Por defecto, continuar la conversación
  return { intent: 'CONTINUE_CONVERSATION', extractedData };
}

// Get conversation state
async function getConversationState(conversationId: string): Promise<ConversationState> {
  const { data } = await supabase
    .from('chat_conversations')
    .select('metadata')
    .eq('id', conversationId)
    .single();

  return data?.metadata?.flowState || {};
}

// Merge extracted data into service data
function mergeExtractedData(
  state: ConversationState,
  extractedData: ExtractedData
): ConversationState {
  const newState = { ...state };
  if (!newState.serviceData) {
    newState.serviceData = {};
  }

  // Cliente
  if (extractedData.clientName) {
    newState.serviceData.clientName = extractedData.clientName;
  }
  if (extractedData.clientAge || extractedData.clientStyle) {
    newState.serviceData.clientInfo =
      `${extractedData.clientAge ? extractedData.clientAge + ' años' : ''} ${extractedData.clientStyle || ''}`.trim();
  }

  // Marcas y productos
  if (extractedData.brands) {
    newState.serviceData.preferredBrands = extractedData.brands;
  }
  if (extractedData.additionalProducts) {
    newState.serviceData.additionalProducts = extractedData.additionalProducts;
  }

  // Técnica
  if (extractedData.technique) {
    if (!newState.serviceData.desiredColor) {
      newState.serviceData.desiredColor = {};
    }
    newState.serviceData.desiredColor.technique = extractedData.technique;
  }

  // Color actual o deseado según contexto
  if (extractedData.level || extractedData.reflect || extractedData.grayPercentage) {
    // Determinar si es color actual o deseado
    const isCurrentColor = !newState.serviceData.currentColor || state.expecting === 'photo';

    if (isCurrentColor) {
      if (!newState.serviceData.currentColor) {
        newState.serviceData.currentColor = {};
      }
      if (extractedData.level) newState.serviceData.currentColor.level = extractedData.level;
      if (extractedData.reflect) newState.serviceData.currentColor.reflect = extractedData.reflect;
      if (extractedData.grayPercentage)
        newState.serviceData.currentColor.grayPercentage = extractedData.grayPercentage;
    } else {
      if (!newState.serviceData.desiredColor) {
        newState.serviceData.desiredColor = {};
      }
      if (extractedData.level) newState.serviceData.desiredColor.level = extractedData.level;
      if (extractedData.reflect) newState.serviceData.desiredColor.reflect = extractedData.reflect;
    }
  }

  // Correcciones y notas
  if (extractedData.corrections) {
    if (!newState.serviceData.currentColor) newState.serviceData.currentColor = {};
    if (!newState.serviceData.currentColor.userCorrections) {
      newState.serviceData.currentColor.userCorrections = [];
    }
    newState.serviceData.currentColor.userCorrections.push(...extractedData.corrections);
  }

  if (extractedData.notes) {
    if (!newState.serviceData.desiredColor) newState.serviceData.desiredColor = {};
    if (!newState.serviceData.desiredColor.userNotes) {
      newState.serviceData.desiredColor.userNotes = [];
    }
    newState.serviceData.desiredColor.userNotes.push(...extractedData.notes);
  }

  return newState;
}

// Update conversation state
async function updateConversationState(conversationId: string, state: ConversationState) {
  const { data: conv } = await supabase
    .from('chat_conversations')
    .select('metadata')
    .eq('id', conversationId)
    .single();

  await supabase
    .from('chat_conversations')
    .update({
      metadata: {
        ...(conv?.metadata || {}),
        flowState: state,
      },
    })
    .eq('id', conversationId);
}

// Get recent messages for context
async function getRecentMessages(conversationId: string, limit: number = 5) {
  const { data } = await supabase
    .from('chat_messages')
    .select('role, content')
    .eq('conversation_id', conversationId)
    .order('created_at', { ascending: false })
    .limit(limit);

  return (data || []).reverse();
}

// Helper function to get next question based on state
function getNextQuestion(state: ConversationState): string {
  if (!state.serviceData?.clientName) {
    return '¿Para quién es el servicio?';
  }
  if (!state.serviceData?.currentColor) {
    return '¿Puedes enviarme una foto del cabello actual o describirlo?';
  }
  if (!state.serviceData?.desiredColor) {
    return '¿Cuál es el color objetivo? Puedes enviar una foto de referencia o describirlo';
  }
  if (!state.serviceData?.preferredBrands) {
    return '¿Con qué marca o marcas prefieres trabajar?';
  }
  if (state.serviceData?.formula && !state.serviceData?.formula?.userModifications) {
    return '¿Te parece bien la fórmula o quieres ajustar algo?';
  }
  return '¿Procedemos a guardar el servicio?';
}

// Handler functions specific to this index file
async function handleStartColorService(
  request: ChatRequest,
  messages: any[],
  state: ConversationState,
  extractedData: ExtractedData,
  callOpenAI: Function
): Promise<string> {
  let response = '¡Perfecto! Vamos a crear un servicio de coloración. ';

  if (state.serviceData?.clientName) {
    response += `Para ${state.serviceData.clientName}. `;
    if (state.serviceData.clientInfo) {
      response += `(${state.serviceData.clientInfo}). `;
    }
  }

  response += getNextQuestion(state);

  messages.push({
    role: 'user',
    content: request.message,
  });

  messages.push({
    role: 'system',
    content: `El usuario quiere crear un servicio de color. Ya has extraído: ${JSON.stringify(extractedData)}. Guíalo paso a paso de forma conversacional.`,
  });

  const aiResponse = await callOpenAI(messages);
  return aiResponse;
}

async function handleClientInfo(
  request: ChatRequest,
  messages: any[],
  state: ConversationState,
  extractedData: ExtractedData,
  callOpenAI: Function
): Promise<string> {
  messages.push({
    role: 'user',
    content: request.message,
  });

  let context = `Cliente: ${state.serviceData?.clientName}`;
  if (state.serviceData?.clientInfo) {
    context += `, ${state.serviceData.clientInfo}`;
  }

  messages.push({
    role: 'system',
    content: `${context}. Ahora pregunta por el estado actual del cabello de forma natural.`,
  });

  return await callOpenAI(messages);
}

// Call OpenAI API
async function callOpenAI(messages: any[]): Promise<string> {
  const payload = {
    model: 'gpt-4o',
    messages: messages,
    max_tokens: 1500,
    temperature: 0.7,
  };

  console.log('Calling OpenAI with payload:', {
    model: payload.model,
    messageCount: messages.length,
    hasImages: messages.some(
      m => Array.isArray(m.content) && m.content.some((c: any) => c.type === 'image_url')
    ),
  });

  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${openaiApiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(payload),
  });

  if (!response.ok) {
    const error = await response.text();
    console.error('OpenAI API error:', error);
    throw new Error(`OpenAI API error: ${response.status}`);
  }

  const data = await response.json();
  return data.choices[0].message.content;
}

// Save messages to database
async function saveMessages(request: ChatRequest, assistantResponse: string) {
  // Save user message
  await supabase.from('chat_messages').insert({
    conversation_id: request.conversationId,
    role: 'user',
    content: request.message,
    has_attachments: !!request.attachments && request.attachments.length > 0,
    metadata: {
      user_id: request.userId,
      attachments_count: request.attachments?.length || 0,
    },
  });

  // Save assistant message
  await supabase.from('chat_messages').insert({
    conversation_id: request.conversationId,
    role: 'assistant',
    content: assistantResponse,
    metadata: {
      model: 'gpt-4o',
    },
  });
}

// Main handler
serve(async req => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const requestData: ChatRequest = await req.json();
    console.log('Chat request received:', {
      conversationId: requestData.conversationId,
      hasAttachments: !!requestData.attachments && requestData.attachments?.length > 0,
      messageLength: requestData.message?.length,
    });

    // Log attachments size if present
    if (requestData.attachments && requestData.attachments.length > 0) {
      console.log(
        'Attachments:',
        requestData.attachments.map(a => ({
          type: a.type,
          mimeType: a.mimeType,
          dataSize: a.url?.length || 0,
        }))
      );
    }

    // Validate request
    if (!requestData.message || !requestData.salonId || !requestData.userId) {
      throw new Error('Faltan parámetros requeridos');
    }

    // Validate OpenAI API key
    if (!openaiApiKey) {
      console.error('OpenAI API key not configured');
      return new Response(
        JSON.stringify({
          success: false,
          error:
            'El servicio de IA no está configurado correctamente. Por favor, contacta al administrador.',
        }),
        {
          status: 503,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Get conversation state
    const state = await getConversationState(requestData.conversationId);

    // Detect intent with data extraction
    const { intent, extractedData } = detectIntentWithContext(
      requestData.message,
      requestData.attachments || [],
      state
    );
    console.log('Detected intent:', intent, 'Extracted data:', extractedData);

    // Get recent messages for context
    const recentMessages = await getRecentMessages(requestData.conversationId);

    // Build messages array for OpenAI
    const messages: any[] = [
      {
        role: 'system',
        content: SYSTEM_PROMPT,
      },
    ];

    // Add conversation history
    messages.push(...recentMessages);

    // Handle based on intent
    let assistantResponse = '';
    let newState = mergeExtractedData(state, extractedData); // Primero mezclar datos extraídos

    switch (intent) {
      case 'START_COLOR_SERVICE':
        assistantResponse = await handleStartColorService(
          requestData,
          messages,
          newState,
          extractedData,
          callOpenAI
        );
        newState.currentFlow = 'color_service';
        newState.flowStep = 'gathering_info';
        break;

      case 'PROVIDE_CLIENT_INFO':
        assistantResponse = await handleClientInfo(
          requestData,
          messages,
          newState,
          extractedData,
          callOpenAI
        );
        break;

      case 'PROVIDE_CURRENT_PHOTO':
        assistantResponse = await handleCurrentPhoto(requestData, messages, newState, callOpenAI);
        if (!newState.serviceData?.desiredColor) {
          newState.expecting = 'any'; // Puede ser foto o descripción
        }
        break;

      case 'PROVIDE_CURRENT_DESCRIPTION':
        assistantResponse = await handleCurrentDescription(
          requestData,
          messages,
          newState,
          extractedData,
          callOpenAI
        );
        if (!newState.serviceData?.desiredColor) {
          newState.expecting = 'any';
        }
        break;

      case 'PROVIDE_DESIRED_PHOTO':
        assistantResponse = await handleDesiredPhoto(requestData, messages, newState, callOpenAI);
        newState.flowStep = 'formulating';
        newState.expecting = 'brand_preference';
        break;

      case 'PROVIDE_DESIRED_DESCRIPTION':
        assistantResponse = await handleDesiredDescription(
          requestData,
          messages,
          newState,
          extractedData,
          callOpenAI
        );
        newState.flowStep = 'formulating';
        newState.expecting = 'brand_preference';
        break;

      case 'SPECIFY_BRANDS':
        assistantResponse = await handleBrandSpecification(
          requestData,
          messages,
          newState,
          extractedData,
          callOpenAI
        );
        if (newState.serviceData?.currentColor && newState.serviceData?.desiredColor) {
          // Generar fórmula automáticamente
          const formula = await generatePersonalizedFormula(newState, messages, callOpenAI);
          assistantResponse += '\n\n' + formula;
          newState.flowStep = 'confirming';
        }
        break;

      case 'ADD_CORRECTION':
        assistantResponse = await handleCorrection(
          requestData,
          messages,
          newState,
          extractedData,
          callOpenAI
        );
        break;

      case 'REQUEST_FORMULA':
        assistantResponse = await generatePersonalizedFormula(newState, messages, callOpenAI);
        newState.flowStep = 'confirming';
        break;

      case 'SAVE_SERVICE':
        assistantResponse = await handleSaveService(requestData, newState, supabase);
        newState.currentFlow = null; // Limpiar flujo
        break;

      case 'CONTINUE_CONVERSATION':
      case 'ANSWER_QUESTION':
      default:
        assistantResponse = await handleConversation(requestData, messages, newState, callOpenAI);
        break;
    }

    console.log('Assistant response generated, length:', assistantResponse?.length);

    // Update conversation state if changed
    if (JSON.stringify(newState) !== JSON.stringify(state)) {
      await updateConversationState(requestData.conversationId, newState);
    }

    // Save messages to database
    await saveMessages(requestData, assistantResponse);

    // Return response
    return new Response(
      JSON.stringify({
        success: true,
        content: assistantResponse,
        state: newState,
        usage: {
          model: 'gpt-4o',
          promptTokens: 0, // Will be calculated
          completionTokens: 0,
          totalTokens: 0,
          cost: 0,
        },
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  } catch (error) {
    console.error('Chat assistant error:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido',
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});
