// Data Extractor for Chat Assistant - Transpiled for Deno
// Extrae información estructurada del texto del usuario

// Marcas conocidas de coloración profesional
const KNOWN_BRANDS = [
  // Marcas premium
  'wella',
  'loreal',
  "l'oreal",
  "l'oréal",
  'loréal',
  'schwarzkopf',
  'redken',
  'matrix',
  'goldwell',
  'alfaparf',
  'revlon',
  'pravana',
  'joico',
  'kenra',
  'paul mitchell',
  'guy tang',
  'olaplex',

  // Líneas específicas
  'koleston',
  'illumina',
  'color touch',
  'blondor',
  'majirel',
  'inoa',
  'dia light',
  'dia richesse',
  'igora',
  'blondme',
  'colorance',
  'topchic',
  'evolution',
  'chromatics',
  'color sync',

  // Productos de tratamiento
  'olaplex',
  'smartbond',
  'wellaplex',
  'fibreplex',
  'bond ultim8',
  'ph bonder',
  'matrix bond',
];

// Productos adicionales de tratamiento
const TREATMENT_PRODUCTS = [
  'olaplex',
  'smartbond',
  'wellaplex',
  'fibreplex',
  'bond ultim8',
  'ph bonder',
  'plex',
  'bond',
  'protector',
  'tratamiento',
  'keratina',
  'botox',
];

// Técnicas de aplicación
const TECHNIQUES = [
  'balayage',
  'babylights',
  'mechas',
  'reflejos',
  'ombre',
  'sombre',
  'degradado',
  'californiana',
  'tinte completo',
  'retoque raíz',
  'raíces',
  'cobertura canas',
  'corrección color',
];

/**
 * Extrae marcas mencionadas en el texto
 */
export function detectBrands(text) {
  const lowerText = text.toLowerCase();
  const foundBrands = new Set();

  for (const brand of KNOWN_BRANDS) {
    if (lowerText.includes(brand)) {
      // Capitalizar correctamente
      const properName = brand
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
      foundBrands.add(properName);
    }
  }

  // Detectar formato "con X" o "usando X" - Version compatible con Deno
  const brandPatterns = [
    /con\s+(\w+)/gi,
    /usando\s+(\w+)/gi,
    /marca\s+(\w+)/gi,
    /prefiero\s+(\w+)/gi,
    /trabajo\s+con\s+(\w+)/gi,
  ];

  for (const pattern of brandPatterns) {
    let match;
    while ((match = pattern.exec(text)) !== null) {
      const potentialBrand = match[1].toLowerCase();
      if (KNOWN_BRANDS.some(b => b.includes(potentialBrand))) {
        foundBrands.add(match[1]);
      }
      // Evitar loop infinito con g flag
      if (!pattern.global) break;
    }
    // Reset pattern for next iteration
    pattern.lastIndex = 0;
  }

  return Array.from(foundBrands);
}

/**
 * Extrae nivel de profundidad (1-10)
 */
export function detectLevel(text) {
  const patterns = [
    /nivel\s+(\d+(?:\.\d+)?)/i,
    /altura\s+de?\s+tono\s+(\d+(?:\.\d+)?)/i,
    /(\d+(?:\.\d+)?)\s+(?:de\s+)?(?:altura|nivel)/i,
    /base\s+(\d+(?:\.\d+)?)/i,
    /\b([1-9]|10)(?:\.\d+)?\b.*(?:rubio|castaño|negro|oscuro|claro)/i,
  ];

  for (const pattern of patterns) {
    const match = text.match(pattern);
    if (match) {
      const level = parseFloat(match[1]);
      if (level >= 1 && level <= 10) {
        return level;
      }
    }
  }

  // Detectar por descripción
  const descriptions = {
    negro: 1,
    'castaño muy oscuro': 3,
    'castaño oscuro': 4,
    'castaño medio': 5,
    'castaño claro': 6,
    'rubio oscuro': 6,
    'rubio medio': 7,
    rubio: 8,
    'rubio claro': 9,
    'rubio muy claro': 9,
    'rubio platino': 10,
    platino: 10,
  };

  const lowerText = text.toLowerCase();
  for (const [desc, level] of Object.entries(descriptions)) {
    if (lowerText.includes(desc)) {
      return level;
    }
  }

  return null;
}

/**
 * Extrae reflejo/matiz
 */
export function detectReflect(text) {
  const reflects = {
    Ceniza: ['ceniza', 'ash', 'gris', 'mate', '.1'],
    Natural: ['natural', 'neutro', '.0'],
    Dorado: ['dorado', 'gold', 'cálido', '.3'],
    Cobrizo: ['cobrizo', 'cobre', 'copper', '.4', '.44'],
    Caoba: ['caoba', 'mahogany', '.5'],
    Rojizo: ['rojizo', 'rojo', 'red', '.6', '.66'],
    'Verde/Mate': ['verde', 'mate', 'khaki', '.2'],
    Violeta: ['violeta', 'violet', 'morado', '.2'],
    Perlado: ['perlado', 'pearl', 'irisado', '.8', '.9'],
  };

  const lowerText = text.toLowerCase();

  for (const [reflect, keywords] of Object.entries(reflects)) {
    if (keywords.some(kw => lowerText.includes(kw))) {
      return reflect;
    }
  }

  return null;
}

/**
 * Extrae productos adicionales de tratamiento
 */
export function detectAdditionalProducts(text) {
  const lowerText = text.toLowerCase();
  const found = new Set();

  for (const product of TREATMENT_PRODUCTS) {
    if (lowerText.includes(product)) {
      // Capitalizar correctamente productos conocidos
      if (product === 'olaplex') found.add('Olaplex');
      else if (product === 'smartbond') found.add('SmartBond');
      else if (product === 'wellaplex') found.add('Wellaplex');
      else if (product === 'fibreplex') found.add('Fibreplex');
      else found.add(product);
    }
  }

  return Array.from(found);
}

/**
 * Extrae técnica de aplicación
 */
export function detectTechnique(text) {
  const lowerText = text.toLowerCase();

  for (const technique of TECHNIQUES) {
    if (lowerText.includes(technique)) {
      return technique.charAt(0).toUpperCase() + technique.slice(1);
    }
  }

  return null;
}

/**
 * Extrae porcentaje de canas
 */
export function detectGrayPercentage(text) {
  const patterns = [
    /(\d+)\s*%?\s*(?:de\s+)?canas/i,
    /canas?\s+(?:al\s+)?(\d+)\s*%/i,
    /(\d+)\s*%?\s*(?:de\s+)?(?:cabello\s+)?blanco/i,
  ];

  for (const pattern of patterns) {
    const match = text.match(pattern);
    if (match) {
      const percentage = parseInt(match[1]);
      if (percentage >= 0 && percentage <= 100) {
        return percentage;
      }
    }
  }

  // Detectar por descripción
  if (/pocas?\s+canas?/i.test(text)) return 10;
  if (/algunas?\s+canas?/i.test(text)) return 20;
  if (/bastantes?\s+canas?/i.test(text)) return 40;
  if (/muchas?\s+canas?/i.test(text)) return 60;
  if (/totalmente\s+(?:blanco|cano)/i.test(text)) return 90;

  return null;
}

/**
 * Extrae nombre del cliente
 */
export function detectClientName(text) {
  const patterns = [
    /para\s+(?:mi\s+client[ea]\s+)?(\w+)/i,
    /client[ea]\s+(?:se\s+llama\s+)?(\w+)/i,
    /(?:se\s+llama|es)\s+(\w+)/i,
    /servicio\s+(?:para|de)\s+(\w+)/i,
  ];

  for (const pattern of patterns) {
    const match = text.match(pattern);
    if (match) {
      const name = match[1];
      // Verificar que no sea una palabra común
      const commonWords = ['el', 'la', 'un', 'una', 'mi', 'su', 'que', 'para'];
      if (!commonWords.includes(name.toLowerCase()) && name.length > 2) {
        return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase();
      }
    }
  }

  return null;
}

/**
 * Extrae toda la información posible del mensaje
 */
export function extractAllData(text) {
  const data = {};

  // Cliente
  const clientName = detectClientName(text);
  if (clientName) data.clientName = clientName;

  // Detectar edad
  const ageMatch = text.match(/(\d+)\s*años/i);
  if (ageMatch) {
    data.clientAge = parseInt(ageMatch[1]);
  }

  // Detectar estilo
  if (/conservador/i.test(text)) data.clientStyle = 'conservador';
  if (/moderno/i.test(text)) data.clientStyle = 'moderno';
  if (/atrevido/i.test(text)) data.clientStyle = 'atrevido';
  if (/clásico/i.test(text)) data.clientStyle = 'clásico';
  if (/natural/i.test(text)) data.clientStyle = 'natural';

  // Color
  const level = detectLevel(text);
  if (level) data.level = level;

  const reflect = detectReflect(text);
  if (reflect) data.reflect = reflect;

  const grayPercentage = detectGrayPercentage(text);
  if (grayPercentage) data.grayPercentage = grayPercentage;

  // Productos
  const brands = detectBrands(text);
  if (brands.length > 0) data.brands = brands;

  const additionalProducts = detectAdditionalProducts(text);
  if (additionalProducts.length > 0) data.additionalProducts = additionalProducts;

  // Técnica
  const technique = detectTechnique(text);
  if (technique) data.technique = technique;

  // Notas y correcciones
  if (text.includes('pero')) {
    const butPart = text.split('pero')[1];
    if (!data.corrections) data.corrections = [];
    data.corrections.push(butPart.trim());
  }

  if (text.includes('también')) {
    const alsoPart = text.split('también')[1];
    if (!data.notes) data.notes = [];
    data.notes.push(alsoPart.trim());
  }

  return data;
}
