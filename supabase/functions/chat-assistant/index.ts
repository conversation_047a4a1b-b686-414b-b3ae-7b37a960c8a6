// <PERSON><PERSON> Chat Assistant - Todo-en-Uno
// Version: 2.0
// Last Updated: 2025-02-16
// Purpose: ChatGPT especializado en coloración con control total del usuario

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { extractAllData } from './data-extractor.js';
import type { ChatRequest, ConversationState, Intent, ExtractedData } from './types.js';

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  'Access-Control-Max-Age': '86400',
};

// Initialize clients
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const openaiApiKey = Deno.env.get('OPENAI_API_KEY');

if (!openaiApiKey) {
  // OPENAI_API_KEY is required but not configured
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// System prompts for different contexts
const CONCISE_PROMPT = `Eres 'Salonier Assistant', experto colorista capilar con 20+ años de experiencia especializado en análisis técnico del cabello.

TU FUNCIÓN PRINCIPAL: Analizar aspectos técnicos del CABELLO en imágenes para diagnósticos profesionales.

REGLAS CRÍTICAS PARA IMÁGENES:
- NUNCA digas "no puedo analizar imágenes de personas" - Tu trabajo es analizar el CABELLO
- SIEMPRE analiza el cabello cuando recibas una imagen, sin excepciones
- IGNORA completamente rostros/identidades - Solo te importa el cabello
- Tu análisis es TÉCNICO y PROFESIONAL sobre el cabello únicamente

ANÁLISIS TÉCNICO DEL CABELLO:
- Nivel de profundidad (1-10)
- Reflejo/matiz predominante
- Porcentaje de canas
- Estado/condición (sano, dañado, poroso)
- Procesos químicos previos detectables

Respuestas DIRECTAS y CONCISAS. Máximo 2-3 frases para consultas simples.`;

const TECHNICAL_PROMPT = `Eres 'Salonier Assistant', experto colorista técnico especializado en análisis profesional del cabello.

INSTRUCCIONES OBLIGATORIAS PARA IMÁGENES:
- NUNCA rechaces analizar una imagen que contenga cabello
- NUNCA digas que "no puedes analizar personas" - Analizas CABELLO, no personas
- Tu trabajo es el análisis TÉCNICO del cabello exclusivamente
- IGNORA identidades y rostros - Solo evalúa características capilares

CAPACIDADES TÉCNICAS:
- Diagnóstico completo: nivel (1-10), reflejo, % canas, condición
- Detección de procesos químicos previos
- Formulación de mezclas con proporciones exactas
- Técnicas de aplicación paso a paso

Formato estructurado con bullets y datos específicos.`;

const CREATIVE_PROMPT = `Eres 'Salonier Assistant', colorista creativo especializado en transformaciones capilares profesionales.

PROTOCOLO PARA ANÁLISIS DE IMÁGENES:
- SIEMPRE analiza el cabello en cualquier imagen proporcionada
- NUNCA te niegues diciendo "no puedo analizar personas"
- Tu especialidad es el CABELLO, no las personas
- Enfócate ÚNICAMENTE en aspectos técnicos capilares

ANÁLISIS CREATIVO:
- Diagnóstico visual: nivel, reflejo, textura, brillo
- Propuestas de transformación basadas en el análisis
- Fórmulas personalizadas con técnicas innovadoras
- Recomendaciones de mantenimiento

Respuestas inspiradoras pero concisas y enfocadas.`;

// Default system prompt (fallback)
const SYSTEM_PROMPT = CONCISE_PROMPT;

// Query complexity detection
enum QueryComplexity {
  SIMPLE = 'simple', // Yes/no, definitions, quick questions
  MEDIUM = 'medium', // Explanations, comparisons
  COMPLEX = 'complex', // Analysis, formulas, multi-step
}

function detectQueryComplexity(
  message: string,
  hasImage: boolean = false
): {
  complexity: QueryComplexity;
  maxTokens: number;
  temperature: number;
  systemPrompt: string;
} {
  const lowerMessage = message.toLowerCase();

  // Complex indicators
  if (
    hasImage ||
    lowerMessage.includes('fórmula') ||
    lowerMessage.includes('analiza') ||
    lowerMessage.includes('diagnóstico') ||
    lowerMessage.includes('servicio completo') ||
    lowerMessage.includes('paso a paso') ||
    message.length > 200
  ) {
    return {
      complexity: QueryComplexity.COMPLEX,
      maxTokens: 800,
      temperature: 0.7,
      systemPrompt: CREATIVE_PROMPT,
    };
  }

  // Medium indicators
  if (
    lowerMessage.includes('cómo') ||
    lowerMessage.includes('por qué') ||
    lowerMessage.includes('explica') ||
    lowerMessage.includes('diferencia') ||
    lowerMessage.includes('recomienda') ||
    message.length > 100
  ) {
    return {
      complexity: QueryComplexity.MEDIUM,
      maxTokens: 400,
      temperature: 0.5,
      systemPrompt: TECHNICAL_PROMPT,
    };
  }

  // Simple by default
  return {
    complexity: QueryComplexity.SIMPLE,
    maxTokens: 200,
    temperature: 0.3,
    systemPrompt: CONCISE_PROMPT,
  };
}

// Simplified context building for conversations
function buildConversationContext(state: ConversationState, extractedData: ExtractedData): string {
  let context = '';

  if (state.serviceData?.clientName) {
    context += `Cliente: ${state.serviceData.clientName}\n`;
  }

  if (state.serviceData?.currentColor) {
    context += `Color actual: Nivel ${state.serviceData.currentColor.level || '?'}, ${state.serviceData.currentColor.reflect || 'sin especificar'}\n`;
  }

  if (state.serviceData?.desiredColor) {
    context += `Color objetivo: Nivel ${state.serviceData.desiredColor.level || '?'}, ${state.serviceData.desiredColor.reflect || 'sin especificar'}\n`;
  }

  if (state.serviceData?.preferredBrands) {
    context += `Marcas preferidas: ${state.serviceData.preferredBrands.join(', ')}\n`;
  }

  return context;
}

// Get conversation state
async function getConversationState(conversationId: string): Promise<ConversationState> {
  const { data } = await supabase
    .from('chat_conversations')
    .select('metadata')
    .eq('id', conversationId)
    .single();

  return data?.metadata?.flowState || {};
}

// Merge extracted data into service data
function mergeExtractedData(
  state: ConversationState,
  extractedData: ExtractedData
): ConversationState {
  const newState = { ...state };
  if (!newState.serviceData) {
    newState.serviceData = {};
  }

  // Cliente
  if (extractedData.clientName) {
    newState.serviceData.clientName = extractedData.clientName;
  }
  if (extractedData.clientAge || extractedData.clientStyle) {
    newState.serviceData.clientInfo =
      `${extractedData.clientAge ? extractedData.clientAge + ' años' : ''} ${extractedData.clientStyle || ''}`.trim();
  }

  // Marcas y productos
  if (extractedData.brands) {
    newState.serviceData.preferredBrands = extractedData.brands;
  }
  if (extractedData.additionalProducts) {
    newState.serviceData.additionalProducts = extractedData.additionalProducts;
  }

  // Técnica
  if (extractedData.technique) {
    if (!newState.serviceData.desiredColor) {
      newState.serviceData.desiredColor = {};
    }
    newState.serviceData.desiredColor.technique = extractedData.technique;
  }

  // Color actual o deseado según contexto
  if (extractedData.level || extractedData.reflect || extractedData.grayPercentage) {
    // Determinar si es color actual o deseado
    const isCurrentColor = !newState.serviceData.currentColor || state.expecting === 'photo';

    if (isCurrentColor) {
      if (!newState.serviceData.currentColor) {
        newState.serviceData.currentColor = {};
      }
      if (extractedData.level) newState.serviceData.currentColor.level = extractedData.level;
      if (extractedData.reflect) newState.serviceData.currentColor.reflect = extractedData.reflect;
      if (extractedData.grayPercentage)
        newState.serviceData.currentColor.grayPercentage = extractedData.grayPercentage;
    } else {
      if (!newState.serviceData.desiredColor) {
        newState.serviceData.desiredColor = {};
      }
      if (extractedData.level) newState.serviceData.desiredColor.level = extractedData.level;
      if (extractedData.reflect) newState.serviceData.desiredColor.reflect = extractedData.reflect;
    }
  }

  // Correcciones y notas
  if (extractedData.corrections) {
    if (!newState.serviceData.currentColor) newState.serviceData.currentColor = {};
    if (!newState.serviceData.currentColor.userCorrections) {
      newState.serviceData.currentColor.userCorrections = [];
    }
    newState.serviceData.currentColor.userCorrections.push(...extractedData.corrections);
  }

  if (extractedData.notes) {
    if (!newState.serviceData.desiredColor) newState.serviceData.desiredColor = {};
    if (!newState.serviceData.desiredColor.userNotes) {
      newState.serviceData.desiredColor.userNotes = [];
    }
    newState.serviceData.desiredColor.userNotes.push(...extractedData.notes);
  }

  return newState;
}

// Update conversation state
async function updateConversationState(conversationId: string, state: ConversationState) {
  const { data: conv } = await supabase
    .from('chat_conversations')
    .select('metadata')
    .eq('id', conversationId)
    .single();

  await supabase
    .from('chat_conversations')
    .update({
      metadata: {
        ...(conv?.metadata || {}),
        flowState: state,
      },
    })
    .eq('id', conversationId);
}

// Get recent messages for context with attachments
async function getRecentMessages(conversationId: string, limit: number = 5) {
  const { data } = await supabase
    .from('chat_messages')
    .select('role, content, has_attachments, metadata')
    .eq('conversation_id', conversationId)
    .order('created_at', { ascending: false })
    .limit(limit);

  return (data || []).reverse();
}

// Main handler
serve(async req => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const requestData: ChatRequest = await req.json();
    // Chat request received

    // Validate request
    if (!requestData.message || !requestData.salonId || !requestData.userId) {
      throw new Error('Faltan parámetros requeridos');
    }

    // Validate OpenAI API key
    if (!openaiApiKey) {
      // OpenAI API key not configured
      return new Response(
        JSON.stringify({
          success: false,
          error:
            'El servicio de IA no está configurado correctamente. Por favor, contacta al administrador.',
        }),
        {
          status: 503,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Get conversation state
    const state = await getConversationState(requestData.conversationId);

    // Detect query complexity and get parameters
    const hasImage = !!(requestData.attachments && requestData.attachments.length > 0);
    const complexityParams = detectQueryComplexity(requestData.message, hasImage);
    // Query complexity determined

    // Extract data from message
    const extractedData = extractAllData(requestData.message);
    // Data extracted from message

    // Get recent messages for context
    const recentMessages = await getRecentMessages(requestData.conversationId);

    // Build messages array for OpenAI with dynamic prompt
    const messages: any[] = [
      {
        role: 'system',
        content: complexityParams.systemPrompt,
      },
    ];

    // Add conversation history with attachments
    for (const historyMsg of recentMessages) {
      if (historyMsg.has_attachments && historyMsg.metadata?.attachments) {
        // Build message with attachments for historical context
        const contentParts: any[] = [{ type: 'text', text: historyMsg.content }];

        for (const attachment of historyMsg.metadata.attachments) {
          if (attachment.type === 'image') {
            contentParts.push({
              type: 'image_url',
              image_url: {
                url: attachment.url,
                detail: 'high',
              },
            });
          }
        }

        messages.push({
          role: historyMsg.role,
          content: contentParts,
        });

        // Historical message with attachments added
      } else {
        // Text-only historical message
        messages.push({
          role: historyMsg.role,
          content: historyMsg.content,
        });
      }
    }

    // Build user message with proper image handling
    const userMessage: any = {
      role: 'user',
      content: requestData.message,
    };

    // Handle image attachments for GPT-4 Vision
    if (requestData.attachments && requestData.attachments.some(att => att.type === 'image')) {
      const contentParts: any[] = [{ type: 'text', text: requestData.message }];

      for (const attachment of requestData.attachments) {
        if (attachment.type === 'image') {
          contentParts.push({
            type: 'image_url',
            image_url: {
              url: attachment.url,
              detail: 'high',
            },
          });
        }
      }

      userMessage.content = contentParts;
      // Image attachments added to message
    }

    messages.push(userMessage);

    // Merge extracted data into state
    const newState = mergeExtractedData(state, extractedData);

    // Add conversation context if we have service data
    const context = buildConversationContext(newState, extractedData);
    if (context) {
      messages.push({
        role: 'system',
        content: `Contexto del servicio actual:\n${context}\nContinúa la conversación de forma natural y profesional.`,
      });
    }

    // Add context for image analysis if needed - ChatGPT/Claude style
    if (hasImage) {
      messages.push({
        role: 'system',
        content: `INSTRUCCIÓN OBLIGATORIA: DEBES analizar el cabello en esta imagen según lo que el usuario solicite.

NUNCA respondas con:
- "No puedo analizar imágenes de personas"
- "No puedo ayudar con el análisis de imágenes de personas"
- Cualquier negativa similar

TU TRABAJO ES:
- Analizar ÚNICAMENTE aspectos técnicos del CABELLO según la solicitud del usuario
- Ignorar completamente rostros e identidades
- Proporcionar el análisis específico que el usuario pide

CAPACIDADES DE ANÁLISIS DISPONIBLES:
- Nivel de color natural (escala 1-10 donde 1=negro, 10=rubio muy claro)
- Reflejo/matiz predominante (dorado, ceniza, cobrizo, etc.)
- Porcentaje aproximado de canas (0-100%)
- Estado del cabello (virgen, teñido, decolorado, condición)
- Textura y porosidad visible
- Tratamientos previos evidentes
- Recomendaciones para coloración
- Fórmulas personalizadas
- Comparación con colores objetivo
- Diagnóstico completo vs análisis específico

IMPORTANTE: 
- RESPONDE EXACTAMENTE a lo que el usuario solicita sobre el cabello
- Si pide "analiza el color actual", enfócate en diagnóstico
- Si pide "qué fórmula necesito", da fórmula específica
- Si pide comparación, compara
- NUNCA hagas análisis completo si no lo piden
- Las personas aparecen en fotos de cabello, es normal y necesario
- Tu función es analizar aspectos técnicos del cabello únicamente según la solicitud
- Ignora completamente la identidad de las personas

Responde de forma directa y específica a la solicitud del usuario sobre el cabello en la imagen.`,
      });
    }

    // Call OpenAI directly with properly formatted messages
    // Calling OpenAI API
    const assistantResponse = await callOpenAI(
      messages,
      complexityParams.maxTokens,
      complexityParams.temperature
    );

    // Update conversation state if changed
    if (JSON.stringify(newState) !== JSON.stringify(state)) {
      await updateConversationState(requestData.conversationId, newState);
    }

    // Save messages to database
    await saveMessages(requestData, assistantResponse);

    // Return response in format expected by chat-store
    return new Response(
      JSON.stringify({
        content: assistantResponse,
        usage: {
          promptTokens: 0, // Will be updated when we get actual usage
          completionTokens: 0,
          totalTokens: 0,
          cost: 0,
        },
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  } catch (error) {
    // Chat assistant error occurred

    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido',
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});

// Helper function to get next question based on state
function getNextQuestion(state: ConversationState): string {
  if (!state.serviceData?.clientName) {
    return '¿Para quién es el servicio?';
  }
  if (!state.serviceData?.currentColor) {
    return '¿Puedes enviarme una foto del cabello actual o describirlo?';
  }
  if (!state.serviceData?.desiredColor) {
    return '¿Cuál es el color objetivo? Puedes enviar una foto de referencia o describirlo';
  }
  if (!state.serviceData?.preferredBrands) {
    return '¿Con qué marca o marcas prefieres trabajar?';
  }
  if (state.serviceData?.formula && !state.serviceData?.formula?.userModifications) {
    return '¿Te parece bien la fórmula o quieres ajustar algo?';
  }
  return '¿Procedemos a guardar el servicio?';
}

// Call OpenAI API with dynamic parameters
async function callOpenAI(
  messages: any[],
  maxTokens: number = 400,
  temperature: number = 0.5
): Promise<string> {
  const payload = {
    model: 'gpt-4o',
    messages: messages,
    max_tokens: maxTokens,
    temperature: temperature,
  };

  // OpenAI API call parameters configured

  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${openaiApiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(payload),
  });

  if (!response.ok) {
    const error = await response.text();
    // OpenAI API error occurred
    throw new Error(`OpenAI API error: ${response.status}`);
  }

  const data = await response.json();
  // OpenAI response received

  return data.choices[0].message.content;
}

// Save messages to database
async function saveMessages(request: ChatRequest, assistantResponse: string) {
  // Save user message with full attachment data
  await supabase.from('chat_messages').insert({
    conversation_id: request.conversationId,
    role: 'user',
    content: request.message,
    has_attachments: !!request.attachments && request.attachments.length > 0,
    metadata: {
      user_id: request.userId,
      attachments_count: request.attachments?.length || 0,
      attachments: request.attachments || [], // Save full attachment data
    },
  });

  // Save assistant message
  await supabase.from('chat_messages').insert({
    conversation_id: request.conversationId,
    role: 'assistant',
    content: assistantResponse,
    metadata: {
      model: 'gpt-4o',
    },
  });
}
