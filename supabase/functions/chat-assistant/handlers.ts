// Handler functions for Chat Assistant
import type { ChatRequest, ConversationState, ExtractedData } from './types.ts';

export async function handleCurrentPhoto(
  request: ChatRequest,
  messages: any[],
  state: ConversationState,
  callOpenAI: Function
): Promise<string> {
  const userMessage: any = {
    role: 'user',
    content: request.message,
  };

  // Add image if present
  if (request.attachments && request.attachments.length > 0) {
    const contentParts: any[] = [{ type: 'text', text: request.message }];

    for (const attachment of request.attachments) {
      if (attachment.type === 'image') {
        contentParts.push({
          type: 'image_url',
          image_url: {
            url: attachment.url,
            detail: 'high',
          },
        });
      }
    }

    userMessage.content = contentParts;
  }

  messages.push(userMessage);
  messages.push({
    role: 'system',
    content: `Analiza el color actual del cabello en la imagen. Identifica:
    - Nivel (1-10)
    - Reflejo/Matiz
    - Porcentaje de canas si las hay
    - Estado del cabello
    - Cualquier característica relevante
    
    El análisis ya está guardado en: ${JSON.stringify(state.serviceData?.currentColor)}.
    Si el usuario menciona correcciones, tenlas en cuenta.
    
    Después de describir el análisis, pregunta por el color objetivo de forma natural.`,
  });

  return await callOpenAI(messages);
}

export async function handleCurrentDescription(
  request: ChatRequest,
  messages: any[],
  state: ConversationState,
  extractedData: ExtractedData,
  callOpenAI: Function
): Promise<string> {
  messages.push({
    role: 'user',
    content: request.message,
  });

  const currentColor = state.serviceData?.currentColor;

  messages.push({
    role: 'system',
    content: `El usuario describe el color actual como:
    - Nivel: ${currentColor?.level || 'no especificado'}
    - Reflejo: ${currentColor?.reflect || 'no especificado'}
    - Canas: ${currentColor?.grayPercentage ? currentColor.grayPercentage + '%' : 'no mencionadas'}
    - Descripción: ${currentColor?.description || request.message}
    
    Confirma estos datos y pregunta por el color objetivo.`,
  });

  return await callOpenAI(messages);
}

export async function handleDesiredPhoto(
  request: ChatRequest,
  messages: any[],
  state: ConversationState,
  callOpenAI: Function
): Promise<string> {
  const userMessage: any = {
    role: 'user',
    content: request.message,
  };

  // Add image if present
  if (request.attachments && request.attachments.length > 0) {
    const contentParts: any[] = [{ type: 'text', text: request.message }];

    for (const attachment of request.attachments) {
      if (attachment.type === 'image') {
        contentParts.push({
          type: 'image_url',
          image_url: {
            url: attachment.url,
            detail: 'high',
          },
        });
      }
    }

    userMessage.content = contentParts;
  }

  messages.push(userMessage);
  messages.push({
    role: 'system',
    content: `Analiza el color objetivo/deseado en la imagen de referencia. Identifica:
    - Nivel objetivo (1-10)
    - Reflejo/Matiz deseado
    - Técnica sugerida (si aplica)
    
    Color actual del cliente: ${JSON.stringify(state.serviceData?.currentColor)}
    Color objetivo identificado: ${JSON.stringify(state.serviceData?.desiredColor)}
    
    Después del análisis, pregunta con qué marcas prefiere trabajar.`,
  });

  return await callOpenAI(messages);
}

export async function handleDesiredDescription(
  request: ChatRequest,
  messages: any[],
  state: ConversationState,
  extractedData: ExtractedData,
  callOpenAI: Function
): Promise<string> {
  messages.push({
    role: 'user',
    content: request.message,
  });

  const desiredColor = state.serviceData?.desiredColor;

  messages.push({
    role: 'system',
    content: `El usuario describe el color objetivo como:
    - Nivel: ${desiredColor?.level || 'no especificado'}
    - Reflejo: ${desiredColor?.reflect || 'no especificado'}
    - Técnica: ${desiredColor?.technique || 'no especificada'}
    - Descripción: ${desiredColor?.description || request.message}
    - Notas: ${desiredColor?.userNotes?.join(', ') || 'ninguna'}
    
    Confirma estos datos y pregunta con qué marcas prefiere trabajar.`,
  });

  return await callOpenAI(messages);
}

export async function handleBrandSpecification(
  request: ChatRequest,
  messages: any[],
  state: ConversationState,
  extractedData: ExtractedData,
  callOpenAI: Function
): Promise<string> {
  messages.push({
    role: 'user',
    content: request.message,
  });

  messages.push({
    role: 'system',
    content: `El usuario prefiere trabajar con: ${state.serviceData?.preferredBrands?.join(', ')}.
    ${state.serviceData?.additionalProducts ? 'Productos adicionales: ' + state.serviceData.additionalProducts.join(', ') : ''}
    
    Si ya tienes el análisis actual y objetivo, procede a generar la fórmula con esas marcas.`,
  });

  return await callOpenAI(messages);
}

export async function handleCorrection(
  request: ChatRequest,
  messages: any[],
  state: ConversationState,
  extractedData: ExtractedData,
  callOpenAI: Function
): Promise<string> {
  messages.push({
    role: 'user',
    content: request.message,
  });

  messages.push({
    role: 'system',
    content: `El usuario está haciendo una corrección o agregando información.
    Datos actuales del servicio: ${JSON.stringify(state.serviceData)}
    Corrección/Adición: ${request.message}
    
    Confirma que has entendido la corrección y continúa con el siguiente paso necesario.`,
  });

  return await callOpenAI(messages);
}

export async function generatePersonalizedFormula(
  state: ConversationState,
  messages: any[],
  callOpenAI: Function
): Promise<string> {
  const serviceData = state.serviceData;

  const prompt = `Genera una fórmula de coloración profesional PASO A PASO:
  
  CLIENTE: ${serviceData?.clientName} ${serviceData?.clientInfo ? '- ' + serviceData.clientInfo : ''}
  
  SITUACIÓN ACTUAL:
  - Nivel: ${serviceData?.currentColor?.level || 'no especificado'}
  - Reflejo: ${serviceData?.currentColor?.reflect || 'no especificado'}
  - Canas: ${serviceData?.currentColor?.grayPercentage ? serviceData.currentColor.grayPercentage + '%' : 'no especificadas'}
  - Estado: ${serviceData?.currentColor?.condition || 'no especificado'}
  - Notas: ${serviceData?.currentColor?.userCorrections?.join(', ') || 'ninguna'}
  
  OBJETIVO:
  - Nivel: ${serviceData?.desiredColor?.level || 'no especificado'}
  - Reflejo: ${serviceData?.desiredColor?.reflect || 'no especificado'}
  - Técnica: ${serviceData?.desiredColor?.technique || 'no especificada'}
  - Notas: ${serviceData?.desiredColor?.userNotes?.join(', ') || 'ninguna'}
  
  PRODUCTOS PREFERIDOS:
  - Marcas: ${serviceData?.preferredBrands?.join(', ') || 'cualquier marca profesional'}
  - Adicionales: ${serviceData?.additionalProducts?.join(', ') || 'ninguno'}
  
  Genera una fórmula detallada con:
  1. Productos específicos de las marcas solicitadas
  2. Proporciones exactas (gramos/ml)
  3. Tiempos de procesamiento
  4. Técnica de aplicación paso a paso
  5. Si pidió productos adicionales (Olaplex, etc), inclúyelos donde corresponda
  
  Formato conversacional y claro. Después de la fórmula, pregunta si quiere ajustar algo.`;

  messages.push({
    role: 'system',
    content: prompt,
  });

  return await callOpenAI(messages);
}

export async function handleSaveService(
  request: ChatRequest,
  state: ConversationState,
  supabase: any
): Promise<string> {
  try {
    // Guardar el servicio en la base de datos
    const { data: service, error } = await supabase
      .from('services')
      .insert({
        salon_id: request.salonId,
        client_name: state.serviceData?.clientName,
        service_type: 'color',
        status: 'in_progress',
        current_hair_analysis: state.serviceData?.currentColor,
        desired_outcome: state.serviceData?.desiredColor,
        formula_data: state.serviceData?.formula,
        metadata: {
          created_from: 'chat',
          conversation_id: request.conversationId,
          preferred_brands: state.serviceData?.preferredBrands,
          additional_products: state.serviceData?.additionalProducts,
        },
      })
      .select()
      .single();

    if (error) throw error;

    return `✅ ¡Servicio guardado exitosamente!
    
    **Resumen del servicio:**
    - Cliente: ${state.serviceData?.clientName}
    - ID del servicio: #${service.id.slice(0, 8)}
    - Estado: En progreso
    
    Cuando termines el servicio, puedes enviarme una foto del resultado para documentarlo.
    
    ¿Hay algo más en lo que pueda ayudarte?`;
  } catch (error) {
    console.error('Error saving service:', error);
    return `❌ Hubo un error al guardar el servicio. Por favor, intenta de nuevo o contacta soporte.`;
  }
}

export async function handleConversation(
  request: ChatRequest,
  messages: any[],
  state: ConversationState,
  callOpenAI: Function
): Promise<string> {
  const userMessage: any = {
    role: 'user',
    content: request.message,
  };

  // Add image if present
  if (request.attachments && request.attachments.length > 0) {
    const contentParts: any[] = [{ type: 'text', text: request.message }];

    for (const attachment of request.attachments) {
      if (attachment.type === 'image') {
        contentParts.push({
          type: 'image_url',
          image_url: {
            url: attachment.url,
            detail: 'high',
          },
        });
      }
    }

    userMessage.content = contentParts;
  }

  messages.push(userMessage);

  // Add context if in a flow
  if (state.currentFlow) {
    messages.push({
      role: 'system',
      content: `Contexto actual del servicio: ${JSON.stringify(state.serviceData)}.
      Continúa la conversación de forma natural. Si falta información importante, pregúntala.`,
    });
  }

  return await callOpenAI(messages);
}
