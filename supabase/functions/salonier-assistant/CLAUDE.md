# CLAUDE.md - Salonier Assistant Edge Function

## 🎯 Propósito

Edge Function principal que procesa análisis de cabello y genera fórmulas químicas usando OpenAI GPT-4o Vision. Versión estable: **v41.1** - Sistema 100% funcional con colorimetría centralizada.

## 🚀 Comandos Esenciales

```bash
# Deployment
./scripts/deploy-edge-function.sh
# O alternativamente:
npx supabase functions deploy salonier-assistant

# Testing local
./scripts/test-edge-function-simple.sh
./scripts/test-formula-generation.sh

# Verificar deployment
./scripts/verify-stable-deployment.sh

# Logs en tiempo real
npx supabase functions logs salonier-assistant
```

## ⚠️ CRÍTICO: Variables de Entorno

**ANTES de cada deploy, verificar en Dashboard:**

- `OPENAI_API_KEY` - Obligatorio para función AI
- Ubicación: Dashboard → Edge Functions → salonier-assistant → Manage secrets

## 📊 Límites y Performance

### Costos OpenAI (ESTRICTOS)

- **Latencia máxima:** 3s (P95)
- **Costo por request:** <$0.10 USD
- **Cache hit rate esperado:** >40%
- **Success rate:** >95%

### Sistema de Fallback

1. **Primario:** GPT-4o (análisis completo + vision)
2. **Secundario:** GPT-4o-mini (análisis simplificado)
3. **Terciario:** Respuesta cached predefinida

## 🧪 Validación Química Obligatoria

```typescript
// SIEMPRE validar antes de retornar fórmulas
const validation = await ChemicalValidator.validate(formula);
if (!validation.isValid) {
  throw new Error(`Chemical validation failed: ${validation.errors.join(', ')}`);
}
```

### Reglas de Seguridad

- Detectar pigmentos subyacentes en cabello coloreado
- Validar matriz de compatibilidad entre productos
- Sugerir neutralización para tonos no deseados
- **NUNCA** devolver combinaciones peligrosas

## 🏷️ Brand Expertise Dinámico

Cada marca tiene reglas específicas cargadas dinámicamente:

```typescript
const brandRules = await getBrandExpertise(targetBrand);
const validation = await getBrandValidationRules(targetBrand, targetLine);
```

### Marcas Soportadas

- Wella (Koleston, Illumina, Color Fresh)
- L'Oréal (Majirel, DiaRichesse, Inoa)
- Schwarzkopf (Igora Royal, BlondMe)
- Matrix, Goldwell, Redken

## 🗂️ Estructura de Archivos

```
salonier-assistant/
├── index.ts                    # Entry point principal
├── constants.ts               # Constantes y configuración
├── types.ts                   # Tipos TypeScript
├── utils/
│   ├── ai-router.ts          # Routing inteligente de modelos
│   ├── brand-expertise.ts    # Knowledge base por marca
│   ├── cache-manager.ts      # Sistema de caché con TTL
│   ├── chemical-validator.ts # Validación química
│   ├── colorimetry-rules.ts  # Reglas de colorimetría
│   ├── fallback-system.ts    # Respuestas de emergencia
│   ├── optimized-prompts.ts  # Prompts comprimidos (<300 chars)
│   └── smart-cache.ts        # Caché inteligente por contexto
└── helpers/
    ├── image-validation.ts    # Validación de imágenes
    └── openai-client.ts      # Cliente OpenAI configurado
```

## 🔧 Patrones de Implementación

### Manejo de Errores

```typescript
try {
  const result = await processAIRequest(data);
  return new Response(JSON.stringify(result));
} catch (error) {
  logger.error('AI Processing failed', { error, requestId });
  const fallback = await FallbackSystem.getResponse(error.type);
  return new Response(JSON.stringify(fallback), { status: 200 });
}
```

### Optimización de Prompts

- Prompts comprimidos: 2,087 chars → <300 chars
- Selección de template basada en complejidad
- JSON mode para output estructurado
- Contexto mínimo pero suficiente

### Sistema de Caché

```typescript
const cacheKey = `${imageHash}_${selectedProducts}_${desiredColor}`;
const cached = await PromptCache.get(cacheKey);
if (cached && cached.confidence > 80) {
  return cached;
}
```

## 🏗️ Arquitectura AI

### Flujo de Procesamiento

1. **Validación de entrada** (imagen + datos)
2. **Análisis con GPT-4o Vision** (diagnóstico)
3. **Generación de fórmula** (GPT-4o texto)
4. **Validación química** (safety check)
5. **Brand expertise** (ajustes específicos)
6. **Response optimizado** (estructura JSON)

### Mapeo de Categorías

```typescript
const CATEGORY_MAPPING = {
  // Spanish (UI) → English (database)
  tinte: 'color',
  oxidante: 'developer',
  decolorante: 'bleach',
  matizador: 'toner',
  // ... más mapeos
};
```

## 🚨 Errores Comunes y Soluciones

### 1. OPENAI_API_KEY no configurada

```bash
Error: "Missing OpenAI API key"
Solución: Configurar en Dashboard → Edge Functions → Secrets
```

### 2. Timeout en requests

```bash
Error: "Request timeout"
Solución: Verificar latencia <3s, usar fallback si excede
```

### 3. Validación química falló

```bash
Error: "Chemical validation failed"
Solución: Revisar ChemicalValidator rules, nunca ignorar warnings
```

### 4. Brand expertise no encontrado

```bash
Error: "Brand not supported"
Solución: Agregar marca en brand-expertise.ts
```

## 📝 Checklist Pre-Deploy

- [ ] Variables de entorno configuradas
- [ ] Tests pasando: `npm test`
- [ ] Lint sin errores: `npm run lint:fix`
- [ ] Validación química funcionando
- [ ] Cache system operativo
- [ ] Fallback responses definidas
- [ ] Métricas de latencia <3s
- [ ] Verificar con script: `./scripts/verify-stable-deployment.sh`

## 🤖 Agentes Recomendados

### Para este módulo usar:

**deployment-engineer** - Deploy seguro de Edge Functions

- Deployment con zero-downtime y rollback automático
- Configuración de variables de entorno seguras
- Monitoring y alertas de Edge Functions
- MUST BE USED para TODOS los deployments

**ai-integration-specialist** - Optimización de AI

- Optimización de prompts para reducir latencia <3s
- Reducción de costos OpenAI (target <$0.10 por request)
- Mejora de accuracy en resultados (>95%)
- PROACTIVAMENTE usar cuando trabajar con AI features

**colorimetry-expert** - Validación técnica

- Validar fórmulas químicas generadas por AI
- Revisar terminología profesional en respuestas
- Verificar precision en análisis de color
- PROACTIVAMENTE usar al revisar prompts y outputs

**debug-specialist** - Debugging de Edge Functions

- Root cause analysis de fallos en producción
- Análisis de timeouts y errores de API
- Debugging de memory leaks en funciones
- PROACTIVAMENTE usar para errores en producción

**security-privacy-auditor** - Seguridad en Edge Functions

- Auditar manejo de API keys y secrets
- Validar sanitización de inputs
- Verificar no exposición de datos sensibles
- Usar antes de todos los deployments

### 💡 Ejemplos de Uso

```bash
# Deploy seguro con verificaciones completas
Task: Use deployment-engineer to deploy salonier-assistant with comprehensive health checks

# Optimizar prompts para reducir latencia
Task: Use ai-integration-specialist to optimize hair analysis prompts to achieve <3s response time

# Validar fórmulas químicas en output
Task: Use colorimetry-expert to validate chemical accuracy in formula generation responses

# Debug timeout en production
Task: Use debug-specialist to investigate timeout issues in hair diagnosis processing
```

## 🔌 MCPs Disponibles

### Funciones MCP críticas para Edge Functions:

**Para deployment y monitoring:**

- `mcp__supabase__list_edge_functions` - Verificar funciones deployadas
- `mcp__supabase__deploy_edge_function` - Deploy programático seguro
- `mcp__supabase__get_logs` - Debug en tiempo real
- `mcp__supabase__get_advisors` - Verificaciones post-deploy

**Para análisis de código:**

- `mcp__serena__get_symbols_overview` - Estructura de función
- `mcp__serena__find_symbol` - Localizar helpers específicos
- `mcp__serena__search_for_pattern` - Buscar patterns en prompts

**Para documentación:**

- `mcp__context7__resolve_library_id` - Docs de OpenAI
- `mcp__context7__get_library_docs` - Documentación específica

### 📝 Ejemplos MCP Críticos

```bash
# Deploy y verificación completa
mcp__supabase__deploy_edge_function: {
  "name": "salonier-assistant",
  "files": [{"name": "index.ts", "content": "..."}]
}
mcp__supabase__get_logs: "edge-function"
mcp__supabase__get_advisors: "security"

# Análisis de la función
mcp__serena__get_symbols_overview: "supabase/functions/salonier-assistant/index.ts"
mcp__serena__find_symbol: "processAIRequest"

# Buscar optimizaciones en prompts
mcp__serena__search_for_pattern: "prompt.*optimization" in "supabase/functions/salonier-assistant/"

# Documentación de OpenAI para optimización
mcp__context7__resolve_library_id: "openai"
mcp__context7__get_library_docs: "/openai/openai-node"
```

### 🔄 Combinaciones Críticas

**Deployment Seguro (OBLIGATORIO):**

1. `deployment-engineer` + `mcp__supabase__deploy_edge_function`
2. `security-privacy-auditor` + `mcp__supabase__get_advisors`

**Optimización de AI:**

1. `ai-integration-specialist` + `mcp__context7__get_library_docs`
2. `colorimetry-expert` + `mcp__serena__find_symbol`

**Debugging en Producción:**

1. `debug-specialist` + `mcp__supabase__get_logs`
2. `deployment-engineer` + `mcp__supabase__list_edge_functions`

## 📊 Patterns Críticos con Agentes

### 🚀 Deployment Pattern

```typescript
// SIEMPRE usar deployment-engineer para este pattern
const secureDeployment = async () => {
  // 1. Verificaciones pre-deploy (deployment-engineer)
  await runPreDeployChecks();

  // 2. Deploy con health checks (deployment-engineer)
  const deployment = await deployWithHealthChecks();

  // 3. Verificación post-deploy (security-privacy-auditor)
  await validateSecurityPostDeploy();

  // 4. Monitoring setup (deployment-engineer)
  await setupMonitoring();
};
```

### 🧠 AI Optimization Pattern

```typescript
// Usar ai-integration-specialist para optimizar
const optimizeAICall = async (input: AIInput): Promise<AIOutput> => {
  // 1. Prompt optimization (ai-integration-specialist)
  const optimizedPrompt = await optimizePrompt(input);

  // 2. Model selection (ai-integration-specialist)
  const selectedModel = selectOptimalModel(input.complexity);

  // 3. Call with fallback (ai-integration-specialist)
  const result = await callWithFallback(optimizedPrompt, selectedModel);

  // 4. Validation (colorimetry-expert)
  return await validateResult(result);
};
```

### 🛡️ Security Pattern

```typescript
// Usar security-privacy-auditor para validar
const secureProcessing = async (request: Request): Promise<Response> => {
  // 1. Input validation (security-privacy-auditor)
  const sanitizedInput = sanitizeInput(await request.json());

  // 2. Auth verification (security-privacy-auditor)
  const isAuthorized = await verifyAuth(request);

  // 3. Process with monitoring (debug-specialist)
  const result = await processWithMonitoring(sanitizedInput);

  // 4. Output sanitization (security-privacy-auditor)
  return sanitizeOutput(result);
};
```

## ⚠️ Checklist Crítico Pre-Deploy

### 🔒 Security (OBLIGATORIO con security-privacy-auditor)

- [ ] API keys en variables de entorno (no hardcoded)
- [ ] Input sanitization implementado
- [ ] Output sanitization verificado
- [ ] Error messages no exponen datos internos

### 🚀 Performance (con ai-integration-specialist)

- [ ] Latencia <3s verificada
- [ ] Costo <$0.10 por request
- [ ] Cache hit rate >40%
- [ ] Fallback system funcionando

### 🧪 Testing (con test-runner)

- [ ] Unit tests pasando
- [ ] Integration tests con OpenAI
- [ ] Load testing completado
- [ ] Error scenarios testeados

### 📊 Monitoring (con deployment-engineer)

- [ ] Health checks configurados
- [ ] Alertas de error setup
- [ ] Logging structured habilitado
- [ ] Rollback procedure documentado

## 🔗 Archivos Relacionados

- `../../migrations/` - Schema de base de datos
- `../../../stores/ai-analysis-store.ts` - Estado AI del cliente
- `../../../utils/ai-error-handler.ts` - Manejo de errores AI
- `../../../constants/product-mappings.ts` - Mapeo de productos

## 📚 Documentación Adicional

- [Guía de OpenAI GPT-4 Vision](https://platform.openai.com/docs/guides/vision)
- [Supabase Edge Functions](https://supabase.com/docs/guides/functions)
- [Chemical Validation Rules](./utils/chemical-validator.ts)
- [Brand Expertise System](./utils/brand-expertise.ts)

---

**⚡ Recuerda:** Esta es la función más crítica del sistema. MUST USE `deployment-engineer` para TODOS los deployments y `security-privacy-auditor` para validación. Cualquier cambio debe ser testeado exhaustivamente antes de deploy.
