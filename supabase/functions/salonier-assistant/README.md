# Salonier Assistant - Edge Function

Esta es la función orquestadora central para todas las operaciones de IA en Salonier.

## Tareas Soportadas

### 1. `diagnose_image`

Analiza una foto del cabello actual del cliente.

**Payload:**

```json
{
  "task": "diagnose_image",
  "payload": {
    "imageUrl": "https://..."
  }
}
```

**Respuesta:**

- <PERSON><PERSON><PERSON><PERSON> de<PERSON>lado por zonas (raíces, medios, puntas)
- Niveles de profundidad con decimales
- Detección de procesos químicos previos
- Recomendaciones profesionales

### 2. `analyze_desired_look`

Analiza una foto de referencia del color deseado.

**Payload:**

```json
{
  "task": "analyze_desired_look",
  "payload": {
    "imageUrl": "https://...",
    "currentLevel": 6.5
  }
}
```

**Respuesta:**

- Nivel objetivo detectado
- Técnica probable
- Viabilidad del cambio
- Número de sesiones necesarias

### 3. `generate_formula`

Genera una fórmula profesional de coloración.

**Payload:**

```json
{
  "task": "generate_formula",
  "payload": {
    "diagnosis": {
      /* resultado de diagnose_image */
    },
    "desiredResult": {
      /* resultado de analyze_desired_look */
    },
    "brand": "L'Oréal Professionnel",
    "line": "Dia Light",
    "clientHistory": "Primera decoloración hace 3 meses"
  }
}
```

**Respuesta:**

- Fórmula completa en markdown
- Tiempos de procesamiento por zona
- Técnica de aplicación
- Cuidados post-servicio

### 4. `convert_formula`

Convierte una fórmula entre marcas.

**Payload:**

```json
{
  "task": "convert_formula",
  "payload": {
    "originalBrand": "Wella",
    "originalLine": "Koleston",
    "originalFormula": "8/1 + 6% 1:1",
    "targetBrand": "L'Oréal",
    "targetLine": "Majirel"
  }
}
```

### 5. `parse_product_text`

Extrae información estructurada de texto libre.

**Payload:**

```json
{
  "task": "parse_product_text",
  "payload": {
    "text": "caja de 12 tubos de tinte Wella Koleston 8/1 de 60ml"
  }
}
```

## Configuración

### Variables de Entorno Requeridas:

- `OPENAI_API_KEY`: API key de OpenAI
- `SUPABASE_URL`: URL del proyecto
- `SUPABASE_SERVICE_ROLE_KEY`: Service role key

### Configurar en Supabase:

```bash
supabase secrets set OPENAI_API_KEY=your-api-key
```

## Características

### Caché Inteligente

- Resultados guardados por 30 días
- Reduce costos de API
- Respuestas instantáneas para análisis repetidos

### Seguridad

- Validación JWT obligatoria
- Aislamiento por salón
- Auto-blur de rostros en fotos

### Rate Limiting

- Por implementar: límites por salón
- Tracking de uso y costos

## Despliegue

```bash
# Deploy the function
supabase functions deploy salonier-assistant

# Set secrets
supabase secrets set OPENAI_API_KEY=sk-...
```

## Testing

```bash
# Test locally
supabase functions serve salonier-assistant

# Test request
curl -i --location --request POST \
  'http://localhost:54321/functions/v1/salonier-assistant' \
  --header 'Authorization: Bearer YOUR_ANON_KEY' \
  --header 'Content-Type: application/json' \
  --data '{
    "task": "parse_product_text",
    "payload": {
      "text": "tubo de tinte rubio ceniza 8.1"
    }
  }'
```
