// Salonier Assistant Edge Function - CONSOLIDATED
// Version: 42 (Consolidated)
// Last Updated: 2025-08-17
// Changes: Consolidated 6 Edge Functions into one central AI hub
// - Integrated chat_assistant functionality (from 4 chat functions)
// - Integrated upload_photo functionality
// - Maintained all existing capabilities: diagnose_image, analyze_desired_look, generate_formula
// - Single point of entry for all AI operations with centralized caching and authentication

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';

// Simple logger for Edge Functions
const logger = {
  debug: (message: string, data?: any) => {
    if (Deno.env.get('NODE_ENV') !== 'production') {
      logger.debug(`[DEBUG] ${message}`, data || '');
    }
  },
  info: (message: string, data?: any) => {
    if (Deno.env.get('NODE_ENV') !== 'production') {
      logger.debug(`[INFO] ${message}`, data || '');
    }
  },
  warn: (message: string, data?: any) => {
    console.warn(`[WARN] ${message}`, data || '');
  },
  error: (message: string, error?: any) => {
    logger.error(`[ERROR] ${message}`, error || '');
  }
};
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { PromptTemplates } from './utils/prompt-templates.ts';
import { OptimizedPrompts, PromptCache, FallbackResponses } from './utils/optimized-prompts.ts';
import { AIRouter, AIRequest as AIRouterRequest } from './utils/ai-router.ts';
import { ChemicalValidator } from './utils/chemical-validator.ts';
import {
  validateColorProcess,
  getColorimetryInstructions,
  ColorProcess,
} from './utils/colorimetry-rules.ts';
import {
  getBrandExpertise,
  getBrandValidationRules,
  getBrandExampleFormulas,
} from './utils/brand-expertise.ts';

// Category to type mapping - same as client-side
const CATEGORY_TO_TYPE_MAPPING: Record<string, string> = {
  // Spanish categories (UI) → English types (database)
  tinte: 'color',
  oxidante: 'developer',
  decolorante: 'bleach',
  tratamiento: 'treatment',
  matizador: 'toner',
  aditivo: 'additive',
  'pre-pigmentacion': 'pre_pigment',
  otro: 'other',

  // Additional Spanish variants that might come from AI
  colorante: 'color',
  oxigenada: 'developer',
  'agua oxigenada': 'developer',
  blanqueador: 'bleach',
  acondicionador: 'treatment',
  champú: 'treatment',
  champu: 'treatment',
  mascarilla: 'treatment',
  serum: 'treatment',
  aceite: 'treatment',

  // English types (for backwards compatibility)
  color: 'color',
  developer: 'developer',
  bleach: 'bleach',
  treatment: 'treatment',
  toner: 'toner',
  shampoo: 'treatment',
  conditioner: 'treatment',
  styling: 'treatment',
  additive: 'additive',
  pre_pigment: 'pre_pigment',
  other: 'other',
};

function mapCategoryToType(categoryOrType: string): string {
  const normalized = categoryOrType?.toLowerCase().trim() || '';
  return CATEGORY_TO_TYPE_MAPPING[normalized] || 'other';
}

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  'Access-Control-Max-Age': '86400',
};

interface AIRequest {
  task:
    | 'diagnose_image'
    | 'analyze_desired_look'
    | 'generate_formula'
    | 'convert_formula'
    | 'parse_product_text'
    | 'chat_assistant'
    | 'upload_photo';
  payload: Record<string, any>;
}

interface ChatRequest {
  conversationId: string;
  message: string;
  salonId: string;
  userId: string;
  attachments?: Array<{
    type: 'image' | 'document';
    url: string;
    mimeType?: string;
  }>;
}

interface UploadRequest {
  imageBase64: string;
  salonId: string;
  clientId: string;
  photoType: 'before' | 'after' | 'desired';
  usePrivateBucket?: boolean;
}

interface AIResponse {
  success: boolean;
  data?: any;
  error?: string;
  cached?: boolean;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const openaiApiKey = Deno.env.get('OPENAI_API_KEY');

// Edge Function initialized - logging removed for production security

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Helper to generate cache key
function generateCacheKey(task: string, payload: any): string {
  const normalized = JSON.stringify(payload, Object.keys(payload).sort());
  const encoder = new TextEncoder();
  const data = encoder.encode(`${task}:${normalized}`);
  return btoa(String.fromCharCode(...new Uint8Array(data)));
}

// Helper to check cache
async function checkCache(salonId: string, task: string, inputHash: string): Promise<any | null> {
  const { data, error } = await supabase
    .from('ai_analysis_cache')
    .select('result')
    .eq('salon_id', salonId)
    .eq('analysis_type', task)
    .eq('input_hash', inputHash)
    .gte('expires_at', new Date().toISOString())
    .single();

  if (!error && data) {
    return data.result;
  }
  return null;
}

// Helper to save to cache
async function saveToCache(
  salonId: string,
  task: string,
  inputHash: string,
  inputData: any,
  result: any,
  model: string,
  tokensUsed: number,
  costUsd: number
) {
  await supabase.from('ai_analysis_cache').upsert({
    salon_id: salonId,
    analysis_type: task,
    input_hash: inputHash,
    input_data: inputData,
    result: result,
    model_used: model,
    tokens_used: tokensUsed,
    cost_usd: costUsd,
    created_at: new Date().toISOString(),
    expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
  });
}

// Helper for retry with exponential backoff
async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  initialDelay: number = 1000
): Promise<T> {
  let lastError: any;

  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error: any) {
      lastError = error;

      // Check if it's a rate limit error
      if (error.status === 429 || error.message?.includes('rate limit')) {
        const delay = initialDelay * Math.pow(2, i);
        // Rate limited - retrying with exponential backoff
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        // If it's not a rate limit error, throw immediately
        throw error;
      }
    }
  }

  throw lastError;
}

// Helper to extract JSON from AI response that might include markdown formatting
function extractJsonFromString(text: string): string {
  if (!text || typeof text !== 'string') {
    throw new Error('Input text is empty or not a string');
  }

  // Clean the response by removing common markdown formatting
  let cleanText = text.trim();

  // Remove markdown code blocks if present
  if (cleanText.includes('```json')) {
    // Cleaning markdown JSON block
    cleanText = cleanText.replace(/```json\s*/g, '').replace(/```/g, '');
  } else if (cleanText.includes('```')) {
    // Cleaning generic markdown block
    cleanText = cleanText.replace(/```\s*/g, '');
  }

  // Find JSON object boundaries
  const startIndex = cleanText.indexOf('{');
  const endIndex = cleanText.lastIndexOf('}');

  if (startIndex === -1 || endIndex === -1) {
    throw new Error('No JSON object boundaries found in the string');
  }

  if (endIndex <= startIndex) {
    throw new Error('Invalid JSON object boundaries (end before start)');
  }

  // Extract the JSON substring
  const jsonString = cleanText.substring(startIndex, endIndex + 1);

  // Basic validation: ensure it looks like valid JSON
  if (!jsonString.startsWith('{') || !jsonString.endsWith('}')) {
    throw new Error('Extracted string does not appear to be a valid JSON object');
  }

  // JSON extraction completed

  return jsonString;
}

// Helper to convert image URL to base64
async function urlToBase64(url: string): Promise<string> {
  try {
    // Converting URL to base64

    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
    }

    const arrayBuffer = await response.arrayBuffer();
    const bytes = new Uint8Array(arrayBuffer);

    // Convert to base64
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    const base64 = btoa(binary);

    // Image conversion completed

    return `data:image/jpeg;base64,${base64}`;
  } catch (error) {
    logger.error('Error converting URL to base64:', error);
    throw new Error(`Failed to convert image URL to base64: ${error.message}`);
  }
}

// Helper to ensure user has salon_id (auto-repair legacy profiles)
async function ensureUserHasSalonId(userId: string): Promise<string | null> {
  // Checking user salon_id

  try {
    // First check if user already has salon_id
    const { data: profile } = await supabase
      .from('profiles')
      .select('salon_id')
      .eq('id', userId)
      .single();

    if (profile?.salon_id) {
      // User already has salon_id
      return profile.salon_id;
    }

    // If not, try to find their salon through team members
    // User profile missing salon_id, attempting to repair

    const { data: teamMember } = await supabase
      .from('team_members')
      .select('salon_id')
      .eq('user_id', userId)
      .single();

    if (teamMember?.salon_id) {
      // Update profile with found salon_id
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ salon_id: teamMember.salon_id })
        .eq('id', userId);

      if (updateError) {
        logger.error('Failed to update profile with salon_id:', updateError);
        return null;
      }

      // Profile repaired with salon_id from team_members
      return teamMember.salon_id;
    }

    // As last resort, check if user owns a salon
    const { data: salon } = await supabase
      .from('salons')
      .select('id')
      .eq('owner_id', userId)
      .single();

    if (salon?.id) {
      // Update profile with owned salon_id
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ salon_id: salon.id })
        .eq('id', userId);

      if (updateError) {
        logger.error('Failed to update profile with owned salon_id:', updateError);
        return null;
      }

      // Profile repaired with owned salon_id
      return salon.id;
    }

    logger.error('Could not find salon_id for user');
    return null;
  } catch (error) {
    logger.error('Error ensuring user has salon_id:', error);
    return null;
  }
}

// Updated pricing as of 2025
const MODEL_PRICING = {
  'gpt-4o': { input: 2.5, output: 10.0 }, // per 1M tokens
  'gpt-4o-mini': { input: 0.15, output: 0.6 }, // per 1M tokens
  'gpt-3.5-turbo': { input: 0.5, output: 1.5 }, // per 1M tokens
};

function calculateCost(model: string, inputTokens: number, outputTokens: number): number {
  const pricing = MODEL_PRICING[model as keyof typeof MODEL_PRICING] || MODEL_PRICING['gpt-4o'];
  const inputCost = (inputTokens / 1_000_000) * pricing.input;
  const outputCost = (outputTokens / 1_000_000) * pricing.output;
  return inputCost + outputCost;
}

// Helper function to determine complexity based on context
function determineComplexity(diagnosis?: any): 'simple' | 'medium' | 'complex' {
  // Simple cases: virgin hair, basic touch-ups, no previous treatments
  if (!diagnosis) return 'simple';

  const hasChemicalProcess =
    diagnosis.detectedChemicalProcess &&
    diagnosis.detectedChemicalProcess !== 'Ninguno' &&
    diagnosis.detectedChemicalProcess !== 'None';

  const hasHighDamage =
    diagnosis.overallCondition?.includes('Severo') ||
    diagnosis.overallCondition?.includes('Severe') ||
    diagnosis.damage === 'Severo' ||
    diagnosis.damage === 'Severe';

  const hasMetallicRisk = diagnosis.detectedRisks?.metallic === true;
  const hasHennaRisk = diagnosis.detectedRisks?.henna === true;

  // Complex cases need premium model
  if (hasMetallicRisk || hasHennaRisk || hasHighDamage) {
    return 'complex';
  }

  // Medium complexity for previously treated hair
  if (hasChemicalProcess) {
    return 'medium';
  }

  // Simple for virgin hair or basic services
  return 'simple';
}

// Task handlers
async function diagnoseImage(payload: any, salonId: string): Promise<AIResponse> {
  const { imageUrl, imageBase64, diagnosis } = payload;

  // diagnoseImage called

  // Soportar ambos formatos para compatibilidad
  let imageDataUrl: string;

  if (imageUrl) {
    // Convertir URL pública a base64 para OpenAI
    // Converting public URL to base64 for diagnosis
    imageDataUrl = await urlToBase64(imageUrl);
  } else if (imageBase64) {
    // Mantener compatibilidad con base64
    // Using base64 for backward compatibility

    // Validar que imageBase64 no sea null o undefined
    if (!imageBase64) {
      logger.error('Image base64 is empty or null');
      throw new Error('Image base64 is empty');
    }

    // Validar y limpiar base64
    const cleanBase64 = imageBase64.replace(/\s/g, '').replace(/\n/g, '');

    // Verificar tamaño del base64
    const base64SizeInBytes = (cleanBase64.length * 3) / 4;
    // Base64 size validated

    // Limitar tamaño máximo a ~4MB después de decodificar
    if (base64SizeInBytes > 4 * 1024 * 1024) {
      logger.error(`Image too large: ${(base64SizeInBytes / 1024 / 1024).toFixed(2)} MB`);
      throw new Error('Image too large. Maximum size is 4MB');
    }

    // Validar que sea base64 válido
    try {
      // Verificar que solo tenga caracteres válidos de base64
      if (!/^[A-Za-z0-9+/]*={0,2}$/.test(cleanBase64)) {
        logger.error('Invalid base64 characters detected');
        throw new Error('Invalid base64 format');
      }
      imageDataUrl = `data:image/jpeg;base64,${cleanBase64}`;
    } catch (e) {
      logger.error('Invalid base64:', e);
      throw new Error('Invalid image format');
    }
  } else {
    logger.error('No image provided in payload');
    throw new Error('No image provided');
  }

  // Determine complexity based on image and context
  const complexity = determineComplexity(diagnosis);

  // Use FULL prompt to get ALL required fields including:
  // - elasticity, resistance, cuticleState
  // - grayType, grayPattern
  // - unwantedTone, estimatedLastProcessDate
  // - ALL zone analysis fields for roots, mids, ends
  const prompt = PromptTemplates.getDiagnosisPrompt('full', 'es');

  // Using optimized prompt for diagnosis
  // Using complexity level
  // Prompt optimized

  try {
    // Preparing OpenAI request
    logger.debug('Image data URL length:', imageDataUrl?.length || 0);
    logger.debug('Image data URL prefix:', imageDataUrl?.substring(0, 50) || 'undefined');

    // Validar que imageDataUrl existe y tiene el formato correcto
    if (!imageDataUrl) {
      throw new Error('Image data URL is undefined');
    }

    // Validar que sea data URL después de conversión
    if (!imageDataUrl.startsWith('data:image/')) {
      throw new Error(
        `Invalid image format. Expected data URL after conversion. Got: ${imageDataUrl.substring(0, 30)}`
      );
    }

    // Construir el mensaje de manera segura
    const messageContent = [
      { type: 'text', text: prompt },
      { type: 'image_url', image_url: { url: imageDataUrl, detail: 'high' } },
    ];

    logger.debug('Message content structure:', JSON.stringify(messageContent[1], null, 2));

    // IMPORTANT: Always use vision-capable model for image analysis
    // gpt-3.5-turbo does NOT support images
    // Vision models do NOT support functions/function_call - only response_format
    const requestBody = {
      model: 'gpt-4o-mini', // Always use vision model for images
      messages: [
        {
          role: 'user',
          content: messageContent,
        },
      ],
      max_tokens: 1500,
      temperature: 0.3,
      response_format: { type: 'json_object' },
      // Note: Vision models do NOT support functions/function_call
      // Using only response_format for structured output
    };

    logger.debug('Sending request to OpenAI...');
    logger.debug('OpenAI API key status:', openaiApiKey ? 'Present' : 'Missing');

    if (!openaiApiKey) {
      throw new Error('OpenAI API key is not configured');
    }

    const response = await retryWithBackoff(async () => {
      const headers = {
        Authorization: `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      };
      logger.debug('Request headers prepared, auth header length:', headers.Authorization.length);

      return await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
      });
    });

    // Check HTTP status first
    if (!response.ok) {
      logger.error('OpenAI API request failed:', {
        status: response.status,
        statusText: response.statusText,
      });

      const errorData = await response
        .json()
        .catch(() => ({ error: 'Failed to parse error response' }));
      logger.error('Error response:', errorData);

      if (response.status === 401) {
        throw new Error('OpenAI API key is invalid or missing');
      } else if (response.status === 429) {
        throw new Error('OpenAI API rate limit exceeded');
      } else if (response.status === 400) {
        throw new Error(
          `OpenAI API bad request: ${errorData.error?.message || 'Invalid request format'}`
        );
      } else {
        throw new Error(
          `OpenAI API error: ${response.status} ${errorData.error?.message || response.statusText}`
        );
      }
    }

    const data = await response.json();
    logger.debug('OpenAI response received, has choices:', !!data.choices);

    if (data.error) {
      logger.error('OpenAI API error in response body:', data.error);
      logger.error('Request body image_url was:', messageContent[1]);
      throw new Error(data.error.message || 'OpenAI API error');
    }

    // Validate response structure with detailed logging
    logger.debug('Validating OpenAI response structure...');
    logger.debug('Response keys:', Object.keys(data));
    logger.debug('Has choices:', !!data.choices);

    if (!data.choices) {
      logger.error('OpenAI response missing choices array:', JSON.stringify(data, null, 2));
      throw new Error('OpenAI response missing choices - possible API quota issue');
    }

    if (!Array.isArray(data.choices) || data.choices.length === 0) {
      logger.error('OpenAI choices is not valid array:', data.choices);
      throw new Error('OpenAI response has empty or invalid choices array');
    }

    const choice = data.choices[0];
    logger.debug('First choice keys:', Object.keys(choice));

    if (!choice.message) {
      logger.error('Choice missing message:', choice);
      throw new Error('OpenAI response choice missing message');
    }

    if (!choice.message.content) {
      logger.error('Message missing content:', choice.message);
      logger.debug('Message keys:', Object.keys(choice.message));
      logger.debug('Finish reason:', choice.finish_reason);

      // Check for content moderation or refusal
      if (choice.message.refusal) {
        logger.error('OpenAI refused to process:', choice.message.refusal);
        throw new Error(`OpenAI rechazó procesar la imagen: ${choice.message.refusal}`);
      }

      if (choice.finish_reason === 'content_filter') {
        throw new Error(
          'La imagen fue bloqueada por filtros de contenido. Por favor, usa una foto que muestre solo el cabello.'
        );
      }

      if (choice.finish_reason === 'length') {
        throw new Error('La respuesta fue truncada. Por favor, intenta con una imagen más simple.');
      }

      // Generic error with more context
      throw new Error(
        `OpenAI no pudo procesar la imagen (${choice.finish_reason || 'razón desconocida'}). Por favor, intenta con otra foto del cabello.`
      );
    }

    logger.debug('OpenAI response structure validation passed');

    // Parse AI response with error handling
    let result;
    const aiResponse = data.choices[0].message.content;

    try {
      // Log first 200 chars of response for debugging
      logger.debug('AI response preview:', aiResponse.substring(0, 200) + '...');

      // Try to extract JSON if AI added extra text
      let jsonStr = aiResponse.trim();

      // Remove markdown code blocks if present
      if (jsonStr.includes('```json')) {
        jsonStr = jsonStr.replace(/```json\s*/g, '').replace(/```/g, '');
      }

      // Find JSON object boundaries
      const jsonStart = jsonStr.indexOf('{');
      const jsonEnd = jsonStr.lastIndexOf('}') + 1;

      if (jsonStart !== -1 && jsonEnd > jsonStart) {
        jsonStr = jsonStr.substring(jsonStart, jsonEnd);
      }

      result = JSON.parse(jsonStr);
      logger.debug('Successfully parsed AI response');
    } catch (parseError) {
      logger.error('Failed to parse AI response:', parseError);
      logger.error('Raw AI response:', aiResponse);
      logger.error('Response length:', aiResponse.length);
      logger.error('First 500 chars:', aiResponse.substring(0, 500));
      throw new Error(`Invalid JSON from AI: ${parseError.message}`);
    }

    // Validate required fields in result
    if (!result || typeof result !== 'object') {
      logger.error('AI response is not an object:', result);
      throw new Error('AI response is not a valid object');
    }

    // With full prompt, AI should return complete structure directly
    // Only validate that we have the expected structure
    if (!result.zoneAnalysis || !result.hairThickness || !result.hairDensity) {
      logger.debug('AI returned incomplete structure, this should not happen with full prompt');
      logger.debug('Result keys:', Object.keys(result));
    } else {
      logger.debug('AI returned complete analysis structure');
    }

    // Mapeo de compatibilidad para términos antiguos
    if (result) {
      // Mapear términos generales si vienen con nombres antiguos
      if (result.overallUndertone && !result.overallReflect) {
        result.overallReflect = result.overallUndertone;
        delete result.overallUndertone;
      }
      if (result.averageDepthLevel && !result.averageLevel) {
        result.averageLevel = result.averageDepthLevel;
        delete result.averageDepthLevel;
      }

      // Mapear términos en análisis por zonas
      if (result.zoneAnalysis) {
        ['roots', 'mids', 'ends'].forEach(zone => {
          if (result.zoneAnalysis[zone]) {
            // Mapear depth a level
            if (result.zoneAnalysis[zone].depth && !result.zoneAnalysis[zone].level) {
              result.zoneAnalysis[zone].level = result.zoneAnalysis[zone].depth;
              delete result.zoneAnalysis[zone].depth;
            }
            // Mapear undertone a reflect
            if (result.zoneAnalysis[zone].undertone && !result.zoneAnalysis[zone].reflect) {
              result.zoneAnalysis[zone].reflect = result.zoneAnalysis[zone].undertone;
              delete result.zoneAnalysis[zone].undertone;
            }
          }
        });
      }
    }

    // Log successful analysis
    logger.debug('AI analysis completed successfully:', {
      hasHairThickness: !!result.hairThickness,
      hasHairDensity: !!result.hairDensity,
      hasZoneAnalysis: !!result.zoneAnalysis,
      zoneCount: result.zoneAnalysis ? Object.keys(result.zoneAnalysis).length : 0,
    });

    const inputTokens = data.usage?.prompt_tokens || 0;
    const outputTokens = data.usage?.completion_tokens || 0;
    const totalTokens = data.usage?.total_tokens || 0;
    const costUsd = calculateCost('gpt-4o-mini', inputTokens, outputTokens);

    // Save to cache (usar solo una pequeña parte del base64 para el hash si existe)
    const cachePayload = imageBase64 ? { imageHash: imageBase64.substring(0, 100) } : { imageUrl };
    const inputHash = generateCacheKey('diagnose_image', cachePayload);
    await saveToCache(
      salonId,
      'diagnose_image',
      inputHash,
      payload,
      result,
      'gpt-4o',
      totalTokens,
      costUsd
    );

    return { success: true, data: result };
  } catch (error: any) {
    logger.error('Error in diagnoseImage:', error);
    return { success: false, error: error.message };
  }
}

async function analyzeDesiredLook(payload: any, salonId: string): Promise<AIResponse> {
  const { imageUrl, imageBase64, currentLevel, diagnosis } = payload;

  // Mantener compatibilidad hacia atrás: si no hay diagnosis, usar currentLevel
  const hairContext = diagnosis || { averageLevel: currentLevel || 6 };

  // Extract current level and state for colorimetry validation
  const actualCurrentLevel =
    diagnosis?.level ||
    diagnosis?.averageLevel ||
    diagnosis?.averageDepthLevel ||
    currentLevel ||
    6;
  const currentState =
    diagnosis?.state?.toLowerCase().includes('teñido') ||
    diagnosis?.state?.toLowerCase().includes('colored')
      ? 'colored'
      : diagnosis?.state?.toLowerCase().includes('decolorado') ||
          diagnosis?.state?.toLowerCase().includes('bleached')
        ? 'bleached'
        : 'natural';

  // Soportar ambos formatos para compatibilidad
  let imageDataUrl: string;

  if (imageUrl) {
    // Convertir URL pública a base64 para OpenAI
    logger.debug('Converting public URL to base64 for desired look analysis:', imageUrl);
    imageDataUrl = await urlToBase64(imageUrl);
  } else if (imageBase64) {
    // Mantener compatibilidad con base64
    logger.debug('Using base64 for backward compatibility in desired look');

    // Validar que imageBase64 no sea null o undefined
    if (!imageBase64) {
      throw new Error('Image base64 is empty');
    }

    // Validar y limpiar base64
    const cleanBase64 = imageBase64.replace(/\s/g, '').replace(/\n/g, '');

    // Verificar tamaño del base64
    const base64SizeInBytes = (cleanBase64.length * 3) / 4;
    logger.debug(
      `Base64 size for desired look: ${base64SizeInBytes} bytes (${(base64SizeInBytes / 1024).toFixed(2)} KB)`
    );

    // Limitar tamaño máximo
    if (base64SizeInBytes > 4 * 1024 * 1024) {
      throw new Error('Image too large. Maximum size is 4MB');
    }

    // Validar formato
    if (!/^[A-Za-z0-9+/]*={0,2}$/.test(cleanBase64)) {
      throw new Error('Invalid base64 format');
    }

    imageDataUrl = `data:image/jpeg;base64,${cleanBase64}`;
  } else {
    throw new Error('No image provided');
  }

  // Si tenemos diagnóstico completo, incluir información relevante en el prompt
  const diagnosticContext = hairContext.averageLevel
    ? `
  El análisis del cabello actual muestra:
  - Nivel actual: ${hairContext.averageLevel}
  - Tono actual: ${hairContext.overallTone || 'No especificado'}
  - Estado: ${hairContext.detectedChemicalProcess || 'Natural'}
  - Condición: ${hairContext.overallCondition || 'Buena'}
  ${hairContext.zoneAnalysis?.roots?.grayPercentage ? `- Canas: ${hairContext.zoneAnalysis.roots.grayPercentage}%` : ''}
  
  Considera estos factores al evaluar la viabilidad del color deseado.`
    : '';

  const prompt = `Analiza esta imagen de referencia de color de cabello deseado.
  ${diagnosticContext}
  
  IMPORTANTE - Principios de colorimetría para calcular sesiones:
  - COLOR NO LEVANTA COLOR: Si el cabello tiene color artificial (Teñido/Colored), NO se puede aclarar con tinte
  - Cabello natural: puede aclarar hasta 3 niveles con tinte en una sesión
  - Cabello con color: requiere decapado primero para aclarar cualquier nivel
  - Máximo aclarado por sesión con decoloración: 4 niveles
  - Al oscurecer 3+ niveles: requiere pre-pigmentación
  
  Para calcular estimatedSessions:
  - Si necesita aclarar cabello con color: mínimo 2 sesiones (decapado + decoloración)
  - Si aclara >4 niveles: dividir entre 4 y redondear hacia arriba
  - Si el proceso no es viable: indicar en warnings
  
  Proporciona un análisis en formato JSON con esta estructura exacta:
  {
    "detectedLevel": número decimal del nivel objetivo general,
    "detectedTone": "tono principal detectado",
    "detectedTechnique": "técnica de aplicación detectada",
    "detectedTones": ["lista de tonos presentes"],
    "viabilityScore": 0-100,
    "estimatedSessions": número de sesiones necesarias (basado en principios de colorimetría),
    "requiredProcesses": ["procesos necesarios"],
    "confidence": porcentaje de confianza,
    "warnings": ["advertencias si las hay"],
    "zoneAnalysis": {
      "roots": {
        "level": número decimal del nivel en raíces,
        "tone": "tono detectado en raíces",
        "reflect": "reflejo detectado (Ceniza, Natural, Dorado, etc.)"
      },
      "mids": {
        "level": número decimal del nivel en medios,
        "tone": "tono detectado en medios",
        "reflect": "reflejo detectado"
      },
      "ends": {
        "level": número decimal del nivel en puntas,
        "tone": "tono detectado en puntas",
        "reflect": "reflejo detectado"
      }
    }
  }`;

  try {
    logger.debug('Preparing OpenAI request for desired look...');
    logger.debug('Image data URL length:', imageDataUrl?.length || 0);
    logger.debug('Image data URL prefix:', imageDataUrl?.substring(0, 50) || 'undefined');

    if (!imageDataUrl) {
      throw new Error('Image data URL is undefined');
    }

    // Validar que sea data URL después de conversión
    if (!imageDataUrl.startsWith('data:image/')) {
      throw new Error(
        `Invalid image format. Expected data URL after conversion. Got: ${imageDataUrl.substring(0, 30)}`
      );
    }

    // Validar que la imagen base64 no esté vacía
    if (imageDataUrl === 'data:image/jpeg;base64,') {
      logger.error('Empty image data URL for desired look analysis');
      throw new Error('La imagen está vacía o corrupta');
    }

    // Log del tamaño real del base64
    const base64Content = imageDataUrl.split(',')[1];
    if (base64Content) {
      logger.debug('Base64 content length for desired look:', base64Content.length);
      logger.debug('First 100 chars of base64:', base64Content.substring(0, 100));
    }

    const messageContent = [
      { type: 'text', text: prompt },
      { type: 'image_url', image_url: { url: imageDataUrl, detail: 'high' } },
    ];

    // Note: Vision models do NOT support functions/function_call
    // Using only response_format for structured output
    const requestBody = {
      model: 'gpt-4o',
      messages: [
        {
          role: 'user',
          content: messageContent,
        },
      ],
      max_tokens: 800,
      temperature: 0.3,
      response_format: { type: 'json_object' },
    };

    const response = await retryWithBackoff(async () => {
      return await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${openaiApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });
    });

    // Check HTTP status first
    if (!response.ok) {
      logger.error('OpenAI API request failed for desired look:', {
        status: response.status,
        statusText: response.statusText,
      });

      const errorData = await response
        .json()
        .catch(() => ({ error: 'Failed to parse error response' }));
      logger.error('Error response:', errorData);

      if (response.status === 401) {
        throw new Error('OpenAI API key is invalid or missing');
      } else if (response.status === 429) {
        throw new Error('OpenAI API rate limit exceeded');
      } else if (response.status === 400) {
        throw new Error(
          `OpenAI API bad request: ${errorData.error?.message || 'Invalid request format'}`
        );
      } else {
        throw new Error(
          `OpenAI API error: ${response.status} ${errorData.error?.message || response.statusText}`
        );
      }
    }

    const data = await response.json();
    logger.debug('OpenAI response received for desired look, has choices:', !!data.choices);

    if (data.error) {
      logger.error('OpenAI error for desired look:', {
        error: data.error,
        imageDataUrlLength: imageDataUrl?.length,
        hasBase64Content: imageDataUrl?.includes('base64,'),
      });
      throw new Error(data.error.message);
    }

    // Log para debugging
    logger.debug('Raw AI response for desired look:', data.choices[0].message.content);
    const result = JSON.parse(data.choices[0].message.content);
    logger.debug('Parsed desired look analysis:', {
      detectedLevel: result.detectedLevel,
      detectedTone: result.detectedTone,
      imageAnalysisNotes: result.imageAnalysisNotes,
    });

    const inputTokens = data.usage.prompt_tokens;
    const outputTokens = data.usage.completion_tokens;
    const totalTokens = data.usage.total_tokens;
    const costUsd = calculateCost('gpt-4o', inputTokens, outputTokens);

    // Save to cache
    const cachePayload = imageBase64
      ? { imageHash: imageBase64.substring(0, 100), hairContext }
      : { imageUrl, hairContext };
    const inputHash = generateCacheKey('analyze_desired_look', cachePayload);
    await saveToCache(
      salonId,
      'analyze_desired_look',
      inputHash,
      payload,
      result,
      'gpt-4o',
      totalTokens,
      costUsd
    );

    return { success: true, data: result };
  } catch (error: any) {
    logger.error('Error in analyzeDesiredLook:', error);
    return { success: false, error: error.message };
  }
}

// El resto del código permanece igual...
async function generateFormula(payload: any, salonId: string): Promise<AIResponse> {
  const { diagnosis, desiredResult, brand, line, clientHistory, regionalConfig, inventoryLevel } =
    payload;

  // Determinar configuración regional
  const volumeUnit = regionalConfig?.volumeUnit || 'ml';
  const weightUnit = regionalConfig?.weightUnit || 'g';
  const developerTerm = regionalConfig?.developerTerminology || 'oxidante';
  const colorTerm = regionalConfig?.colorTerminology || 'tinte';
  const maxDeveloperVolume = regionalConfig?.maxDeveloperVolume || 40;
  const currencySymbol = regionalConfig?.currencySymbol || '€';
  const measurementSystem = regionalConfig?.measurementSystem || 'metric';
  const decimalSeparator = regionalConfig?.decimalSeparator || ',';

  // Detectar idioma
  const isEnglish = regionalConfig?.language === 'en';

  // Extraer técnica seleccionada
  const selectedTechnique = desiredResult?.general?.technique || 'full_color';
  const customTechnique = desiredResult?.general?.customTechnique;

  // Obtener instrucciones específicas por técnica
  const getTechniqueInstructions = () => {
    if (selectedTechnique === 'custom' && customTechnique) {
      return isEnglish
        ? `Custom technique described as: "${customTechnique}". Adapt the formula accordingly.`
        : `Técnica personalizada descrita como: "${customTechnique}". Adapta la fórmula según corresponda.`;
    }

    const techniquePrompts = {
      full_color: isEnglish
        ? `- Single formula for complete coverage
- Ensure uniform application from roots to ends
- Consider natural regrowth for maintenance`
        : `- Fórmula única para cobertura completa
- Asegurar aplicación uniforme de raíces a puntas
- Considerar crecimiento natural para mantenimiento`,

      highlights: isEnglish
        ? `- Use foil technique for precision
- Create multiple formulas if needed (base + highlights)
- Thicker consistency to prevent bleeding
- Maximum ${developerTerm} 30 vol for highlights
- Consider placement pattern (full head, partial, face-framing)`
        : `- Usar técnica con papel aluminio para precisión
- Crear múltiples fórmulas si es necesario (base + mechas)
- Consistencia más espesa para evitar sangrado
- Máximo ${developerTerm} 30 vol para mechas
- Considerar patrón de colocación (cabeza completa, parcial, contorno facial)`,

      balayage: isEnglish
        ? `- Free-hand painting technique
- Gradual transition from dark to light
- Use lower ${developerTerm} volume (20 vol max recommended)
- Creamy consistency for controlled application
- Natural, sun-kissed effect
- Consider using clay or cream lightener`
        : `- Técnica de pintado a mano alzada
- Transición gradual de oscuro a claro
- Usar ${developerTerm} de menor volumen (20 vol máximo recomendado)
- Consistencia cremosa para aplicación controlada
- Efecto natural, como besado por el sol
- Considerar usar decolorante en crema o arcilla`,

      ombre: isEnglish
        ? `- Clear horizontal gradient
- Multiple formulas for different zones
- Seamless blending is crucial
- Start application from ends, work up
- Consider toner for perfect transition`
        : `- Degradado horizontal claro
- Múltiples fórmulas para diferentes zonas
- La mezcla perfecta es crucial
- Comenzar aplicación desde puntas, subir gradualmente
- Considerar toner para transición perfecta`,

      babylights: isEnglish
        ? `- Ultra-fine sections (max 1-2mm)
- Low ${developerTerm} volume (10-20 vol)
- Natural, subtle effect
- Longer processing time due to fine sections
- Mimic natural sun-lightened strands`
        : `- Secciones ultrafinas (máx 1-2mm)
- ${developerTerm} de bajo volumen (10-20 vol)
- Efecto natural y sutil
- Mayor tiempo de procesamiento por secciones finas
- Imitar mechones aclarados naturalmente por el sol`,

      color_correction: isEnglish
        ? `- Analyze underlying pigments carefully
- May need pre-pigmentation or color removal
- Multiple steps might be required
- Use appropriate neutralizing tones
- Consider strand test mandatory
- Document each step for future reference`
        : `- Analizar cuidadosamente pigmentos subyacentes
- Puede necesitar pre-pigmentación o remoción de color
- Pueden requerirse múltiples pasos
- Usar tonos neutralizantes apropiados
- Considerar prueba de mechón obligatoria
- Documentar cada paso para referencia futura`,

      foilyage: isEnglish
        ? `- Combine foil and balayage techniques
- Use foil for stronger lift at top sections
- Free-hand painting for natural flow
- Varying ${developerTerm} volumes by section
- Creates maximum dimension`
        : `- Combinar técnicas de aluminio y balayage
- Usar aluminio para mayor aclarado en secciones superiores
- Pintado a mano para flujo natural
- Variar volúmenes de ${developerTerm} por sección
- Crea máxima dimensión`,

      money_piece: isEnglish
        ? `- Focus on face-framing sections
- High contrast for impact
- Protect surrounding hair
- Consider client's skin tone
- Easy maintenance placement`
        : `- Enfoque en secciones que enmarcan el rostro
- Alto contraste para impacto
- Proteger cabello circundante
- Considerar tono de piel del cliente
- Colocación de fácil mantenimiento`,

      chunky_highlights: isEnglish
        ? `- Thick sections (1cm or more)
- Bold contrast recommended
- Strategic placement for maximum effect
- Higher ${developerTerm} volume acceptable (up to 40 vol)
- 90s-inspired dramatic look`
        : `- Secciones gruesas (1cm o más)
- Contraste audaz recomendado
- Colocación estratégica para máximo efecto
- ${developerTerm} de mayor volumen aceptable (hasta 40 vol)
- Look dramático inspirado en los 90s`,

      reverse_balayage: isEnglish
        ? `- Add depth to over-lightened hair
- Use demi-permanent or semi-permanent color
- Focus on roots and mid-lengths
- Create natural shadow root
- Low ${developerTerm} volume or no ${developerTerm}`
        : `- Agregar profundidad a cabello sobre-aclarado
- Usar color demi-permanente o semi-permanente
- Enfoque en raíces y medios
- Crear raíz sombreada natural
- ${developerTerm} de bajo volumen o sin ${developerTerm}`,
    };

    return (
      techniquePrompts[selectedTechnique as keyof typeof techniquePrompts] ||
      techniquePrompts.full_color
    );
  };

  // Crear ejemplos de formato según la región
  const formatExamples =
    measurementSystem === 'metric'
      ? isEnglish
        ? `- Quantities: "40${volumeUnit} of ${colorTerm} 7.1", "60${volumeUnit} of ${developerTerm} 20 vol"
  - Ratios: "1:1${decimalSeparator}5" or "1:2"
  - Weights: "15${weightUnit} of lightening powder"`
        : `- Cantidades: "40${volumeUnit} de ${colorTerm} 7.1", "60${volumeUnit} de ${developerTerm} 20 vol"
  - Proporciones: "1:1${decimalSeparator}5" o "1:2"
  - Pesos: "15${weightUnit} de polvo decolorante"`
      : `- Quantities: "1.35${volumeUnit} of ${colorTerm} 7.1", "2${volumeUnit} of ${developerTerm} 20 vol"
  - Ratios: "1:1.5" or "1:2"
  - Weights: "0.5${weightUnit} of lightening powder"`;

  // Adaptar restricciones según región
  const volumeRestriction =
    maxDeveloperVolume < 40
      ? isEnglish
        ? `IMPORTANT: In this region, the maximum allowed ${developerTerm} volume is ${maxDeveloperVolume} volumes.`
        : `IMPORTANTE: En esta región, el volumen máximo permitido de ${developerTerm} es ${maxDeveloperVolume} volúmenes.`
      : '';

  const techniqueInstructions = getTechniqueInstructions();
  const techniqueName =
    selectedTechnique === 'custom' && customTechnique
      ? customTechnique
      : {
          full_color: isEnglish ? 'Full Color' : 'Tinte Completo',
          highlights: isEnglish ? 'Highlights' : 'Mechas',
          balayage: 'Balayage',
          ombre: 'Ombré',
          babylights: 'Babylights',
          color_correction: isEnglish ? 'Color Correction' : 'Corrección de Color',
          foilyage: 'Foilyage',
          money_piece: 'Money Piece',
          chunky_highlights: isEnglish ? 'Chunky Highlights' : 'Mechas Gruesas',
          reverse_balayage: 'Reverse Balayage',
        }[selectedTechnique] || (isEnglish ? 'Full Color' : 'Tinte Completo');

  // Validate color process according to colorimetry principles
  let colorimetryInstructions = '';
  let colorimetryWarnings: string[] = [];

  try {
    // Extract current and desired levels from diagnosis
    const currentLevel =
      diagnosis?.averageDepthLevel ||
      diagnosis?.level ||
      diagnosis?.zoneAnalysis?.roots?.depth ||
      5; // Default to level 5 if not found

    const desiredLevel =
      desiredResult?.general?.detectedLevel || desiredResult?.general?.targetLevel || currentLevel; // Default to current if not specified

    // Determine current hair state
    const currentState =
      diagnosis?.detectedChemicalProcess?.toLowerCase().includes('color') ||
      diagnosis?.state?.toLowerCase().includes('teñido') ||
      diagnosis?.state?.toLowerCase().includes('colored')
        ? 'colored'
        : diagnosis?.detectedChemicalProcess?.toLowerCase().includes('bleach') ||
            diagnosis?.state?.toLowerCase().includes('decolorado') ||
            diagnosis?.state?.toLowerCase().includes('bleached')
          ? 'bleached'
          : 'natural';

    const colorProcess: ColorProcess = {
      currentLevel: Math.round(currentLevel),
      desiredLevel: Math.round(desiredLevel),
      currentState: currentState as 'natural' | 'colored' | 'bleached',
      hasMetallicSalts: diagnosis?.detectedRisks?.metallic || false,
      hasHenna: diagnosis?.detectedRisks?.henna || false,
    };

    logger.debug('Validating color process:', colorProcess);

    const validation = validateColorProcess(colorProcess, maxDeveloperVolume);
    colorimetryInstructions = getColorimetryInstructions(validation, isEnglish ? 'en' : 'es');
    colorimetryWarnings = validation.warnings;

    logger.debug('Colorimetry validation result:', {
      isViable: validation.isViable,
      requiredProcesses: validation.requiredProcesses,
      recommendedVolume: validation.recommendedDeveloperVolume,
      warnings: validation.warnings.length,
    });
  } catch (error) {
    logger.error('Error in colorimetry validation:', error);
    // Continue without colorimetry validation if there's an error
  }

  // JSON structure definition for the prompt
  const jsonStructure = `interface ProductMix {
  productId: string;
  productName: string;
  brand: string;
  line?: string;
  type: string;
  shade?: string;
  quantity: number;
  unit: 'gr' | 'ml' | 'gotas' | 'pulsaciones';
}

interface ApplicationTechnique {
  name: string;
  description: string;
}

interface FormulationStep {
  stepNumber: number;
  stepTitle: string;
  mix?: ProductMix[];
  technique?: ApplicationTechnique;
  instructions: string;
  processingTime?: number;
}

interface Formulation {
  formulaTitle: string;
  summary: string;
  steps: FormulationStep[];
  totalTime: number;
  warnings?: string[];
}`;

  // Get brand-specific expertise
  let brandExpertiseSection = '';
  let brandValidationRules = null;
  let brandExamples = '';

  if (brand) {
    try {
      logger.debug(`Loading brand expertise for ${brand} ${line || ''}`);

      // Get comprehensive brand expertise
      const brandExpertise = getBrandExpertise(brand, line || '', isEnglish ? 'en' : 'es');

      // Get validation rules for the brand
      brandValidationRules = getBrandValidationRules(brand);

      // Get example formulas for the brand and technique
      brandExamples = getBrandExampleFormulas(
        brand,
        line || '',
        selectedTechnique,
        isEnglish ? 'en' : 'es'
      );

      // Build the brand expertise section for the prompt
      if (isEnglish) {
        brandExpertiseSection = `
**BRAND EXPERTISE - ${brand.toUpperCase()} ${line ? line.toUpperCase() : ''} SPECIALIST:**

${brandExpertise.personality}

**Technical Knowledge:**
${brandExpertise.technicalKnowledge}

**Brand-Specific Rules:**
${brandExpertise.specificRules}

**Mixing Ratios:**
${brandExpertise.mixingRatios}

**Special Products & Additives:**
${brandExpertise.specialProducts}

**Processing Times:**
${brandExpertise.processingTimes}

**Professional Tips:**
${brandExpertise.proTips}

**Nomenclature System:**
${brandExpertise.nomenclature}

${brandExamples ? `**Reference Formulas:**\n${brandExamples}` : ''}

IMPORTANT: As a ${brand} expert, you MUST follow their specific standards and recommendations exactly.
`;
      } else {
        brandExpertiseSection = `
**EXPERTISE DE MARCA - ESPECIALISTA ${brand.toUpperCase()} ${line ? line.toUpperCase() : ''}:**

${brandExpertise.personality}

**Conocimiento Técnico:**
${brandExpertise.technicalKnowledge}

**Reglas Específicas de la Marca:**
${brandExpertise.specificRules}

**Proporciones de Mezcla:**
${brandExpertise.mixingRatios}

**Productos Especiales y Aditivos:**
${brandExpertise.specialProducts}

**Tiempos de Procesamiento:**
${brandExpertise.processingTimes}

**Tips Profesionales:**
${brandExpertise.proTips}

**Sistema de Nomenclatura:**
${brandExpertise.nomenclature}

${brandExamples ? `**Fórmulas de Referencia:**\n${brandExamples}` : ''}

IMPORTANTE: Como experto en ${brand}, DEBES seguir exactamente sus estándares y recomendaciones específicas.
`;
      }

      logger.debug('Brand expertise loaded successfully');
    } catch (error) {
      logger.error('Error loading brand expertise:', error);
      // Continue without brand expertise if there's an error
    }
  }

  // Get inventory products if not in 'solo-formulas' mode
  let inventoryContext = '';
  if (inventoryLevel && inventoryLevel !== 'solo-formulas' && salonId) {
    try {
      logger.debug('Loading inventory products for salon:', salonId);

      // Get products from the salon's inventory
      const { data: products, error } = await supabase
        .from('products')
        .select('brand, line, type, shade, display_name, stock_ml')
        .eq('salon_id', salonId)
        .eq('is_active', true)
        .order('brand', { ascending: true })
        .order('shade', { ascending: true });

      if (!error && products && products.length > 0) {
        logger.debug(`Found ${products.length} products in inventory`);

        // Group products by type for better organization
        const productsByType = products.reduce((acc: any, product) => {
          const type = product.type || 'otro';
          if (!acc[type]) acc[type] = [];

          // Create a simplified product description
          const productDesc =
            product.display_name ||
            `${product.brand} ${product.line || ''} ${product.shade || ''}`.trim();

          acc[type].push(productDesc);
          return acc;
        }, {});

        // Build inventory context with strong directives
        const inventoryLines = [];

        if (isEnglish) {
          inventoryLines.push('\n**CRITICAL INVENTORY INSTRUCTIONS:**');
          inventoryLines.push(
            '1. You MUST use the EXACT product names below, including specific shade/tone numbers'
          );
          inventoryLines.push(
            '2. CORRECT example: "Wella Illumina Color 7.81" NOT "Illumina Color"'
          );
          inventoryLines.push(
            '3. For each product in your formula, include: brand, line, shade/volume, and quantity'
          );
          inventoryLines.push('');
          inventoryLines.push('**AVAILABLE PRODUCTS (USE THESE EXACT NAMES):**');
        } else {
          inventoryLines.push('\n**INSTRUCCIONES CRÍTICAS DE INVENTARIO:**');
          inventoryLines.push(
            '1. DEBES usar los nombres EXACTOS de productos abajo, incluyendo números específicos de tono/matiz'
          );
          inventoryLines.push(
            '2. Ejemplo CORRECTO: "Wella Illumina Color 7.81" NO "Illumina Color"'
          );
          inventoryLines.push(
            '3. Para cada producto en tu fórmula, incluye: marca, línea, tono/volumen y cantidad'
          );
          inventoryLines.push('');
          inventoryLines.push('**PRODUCTOS DISPONIBLES (USA ESTOS NOMBRES EXACTOS):**');
        }

        // Add products by type with emphasis on exact naming
        Object.entries(productsByType).forEach(([type, productList]: [string, any]) => {
          const typeLabel =
            {
              color: isEnglish ? 'Hair Colors (WITH EXACT SHADE)' : 'Tintes (CON TONO EXACTO)',
              developer: isEnglish
                ? 'Developers (WITH EXACT VOLUME)'
                : 'Oxidantes (CON VOLUMEN EXACTO)',
              bleach: isEnglish ? 'Bleaching Products' : 'Decolorantes',
              treatment: isEnglish ? 'Treatments' : 'Tratamientos',
              toner: isEnglish ? 'Toners' : 'Matizadores',
              additive: isEnglish ? 'Additives' : 'Aditivos',
              pre_pigment: isEnglish ? 'Pre-pigmentation' : 'Pre-pigmentación',
              other: isEnglish ? 'Other' : 'Otros',
            }[type] || type;

          inventoryLines.push(`\n${typeLabel}:`);
          // List each product on its own line for clarity
          productList.forEach((product: string) => {
            inventoryLines.push(`  • ${product}`);
          });
        });

        inventoryLines.push('');

        // Add brand-specific inventory guidance if brand is available
        if (brand) {
          if (isEnglish) {
            inventoryLines.push(`**${brand.toUpperCase()} INVENTORY INTEGRATION:**`);
            inventoryLines.push(
              `- As a ${brand} expert, select the most appropriate products from the available inventory`
            );
            inventoryLines.push(`- Apply ${brand}'s specific mixing ratios and recommendations`);
            inventoryLines.push(
              `- If a recommended ${brand} product is not available, suggest the closest alternative and mark it clearly`
            );
          } else {
            inventoryLines.push(`**INTEGRACIÓN DE INVENTARIO ${brand.toUpperCase()}:**`);
            inventoryLines.push(
              `- Como experto en ${brand}, selecciona los productos más apropiados del inventario disponible`
            );
            inventoryLines.push(
              `- Aplica las proporciones de mezcla y recomendaciones específicas de ${brand}`
            );
            inventoryLines.push(
              `- Si un producto recomendado de ${brand} no está disponible, sugiere la alternativa más cercana y márcalo claramente`
            );
          }
          inventoryLines.push('');
        }

        inventoryLines.push(
          isEnglish
            ? 'IMPORTANT: Products NOT listed above must be marked as "(Not in stock)" in your formula.'
            : 'IMPORTANTE: Los productos NO listados arriba deben marcarse como "(No en stock)" en tu fórmula.'
        );

        inventoryContext = inventoryLines.join('\n');
        logger.debug(
          'Inventory context prepared with',
          Object.keys(productsByType).length,
          'product types'
        );
      } else {
        logger.debug('No products found in inventory or error:', error);
      }
    } catch (error) {
      logger.error('Error loading inventory products:', error);
      // Continue without inventory context
    }
  }

  // Create structured prompt that requests JSON output
  const prompt = isEnglish
    ? `You are "Salonier Assistant", a world-renowned Master Colorist expert, specialized in creating precise and safe color formulas.

**MISSION:**
Generate a detailed professional coloring formula based on the client's diagnosis, desired color, and salon preferences.

**GOLDEN RULES:**
1. **MANDATORY OUTPUT FORMAT:** Your response MUST be ONLY a valid JSON object. No introductory text, no markdown, no explanations. IMPORTANT: Do NOT wrap the JSON in code blocks (\`\`\`json) or any markdown formatting. The response must start directly with { and end with }. The JSON must strictly comply with this TypeScript interface:
   \`\`\`typescript
   ${jsonStructure}
   \`\`\`
2. **EXPERT THINKING:** Consider underlying pigments. Adapt formula to ${brand} ${line}. Be explicit about mixing ratios.
3. **SAFETY FIRST:** Add clear warnings if risky.
4. **PROFESSIONAL LANGUAGE:** Use colorist terminology.
5. **PRODUCT SPECIFICITY (CRITICAL):** You MUST be specific with EVERY product:
   - ALWAYS include the exact shade/tone number for colors (e.g., "7.81", "9.60")
   - ALWAYS include the exact volume for developers (e.g., "20 vol", "30 vol")
   - NEVER use generic names like "Illumina Color" without a shade
   - Each product in ProductMix MUST include ALL these fields:
     * brand: Always use "${brand}"
     * line: Use "${line}" if specified
     * type: "Tinte", "Oxidante", "Decolorante", "Tratamiento", etc.
     * shade: The SPECIFIC shade/tone/volume number (MANDATORY)
     * productName: Full product name including shade (e.g., "Wella Illumina Color 7.81")
6. **COLORIMETRY PRINCIPLES (MANDATORY):**
   - COLOR CANNOT LIFT COLOR: If hair has artificial color, you MUST include color removal before lightening
   - DEVELOPER VOLUME RULES:
     * Darkening only: Use 10 volume developer or demi-permanent activator
     * Gray coverage/same level: Use 20 volume
     * Lifting 1-2 levels: Use 20-30 volume
     * Lifting 3+ levels: Requires bleaching, not color
   - PRE-PIGMENTATION: Required when going 3+ levels darker to avoid green/muddy results
   - If the process is not technically viable, include the necessary preparatory steps

**CONTEXT:**
* **Client Diagnosis:** ${JSON.stringify(diagnosis)}
* **Desired Color:** ${JSON.stringify(desiredResult)}
* **Products:** ${brand} ${line}
* **Regional Config:** ${measurementSystem} system, ${volumeUnit}/${weightUnit} units, "${developerTerm}" for developer, "${colorTerm}" for color
* **Technique Requirements:** ${techniqueInstructions}
${colorimetryInstructions}
${brandExpertiseSection}
${inventoryContext}

Generate the JSON formula now.`
    : `Eres "Salonier Assistant", un Maestro Colorista experto de renombre mundial, especializado en crear fórmulas de coloración precisas y seguras.

**MISIÓN:**
Generar una fórmula de coloración profesional detallada basada en el diagnóstico del cliente, color deseado y preferencias del salón.

**REGLAS DE ORO:**
1. **FORMATO OBLIGATORIO:** Tu respuesta DEBE ser SOLO un objeto JSON válido. Sin texto introductorio, sin markdown, sin explicaciones. IMPORTANTE: NO envuelvas el JSON en bloques de código (\`\`\`json) ni ningún formato markdown. La respuesta debe comenzar directamente con { y terminar con }. El JSON debe cumplir estrictamente con esta interfaz TypeScript:
   \`\`\`typescript
   ${jsonStructure}
   \`\`\`
2. **PENSAMIENTO EXPERTO:** Considera pigmentos subyacentes. Adapta fórmula a ${brand} ${line}. Sé explícito con proporciones.
3. **SEGURIDAD PRIMERO:** Añade advertencias claras si hay riesgo.
4. **LENGUAJE PROFESIONAL:** Usa terminología de colorista.
5. **ESPECIFICIDAD DE PRODUCTOS (CRÍTICO):** DEBES ser específico con CADA producto:
   - SIEMPRE incluye el número exacto de tono/matiz para tintes (ej: "7.81", "9.60")
   - SIEMPRE incluye el volumen exacto para oxidantes (ej: "20 vol", "30 vol")
   - NUNCA uses nombres genéricos como "Illumina Color" sin un tono
   - Cada producto en ProductMix DEBE incluir TODOS estos campos:
     * brand: Siempre usa "${brand}"
     * line: Usa "${line}" si está especificada
     * type: "Tinte", "Oxidante", "Decolorante", "Tratamiento", etc.
     * shade: El número ESPECÍFICO de tono/matiz/volumen (OBLIGATORIO)
     * productName: Nombre completo del producto incluyendo tono (ej: "Wella Illumina Color 7.81")
6. **PRINCIPIOS DE COLORIMETRÍA (OBLIGATORIO):**
   - COLOR NO LEVANTA COLOR: Si el cabello tiene color artificial, DEBES incluir decapado antes de aclarar
   - REGLAS DE VOLUMEN DE OXIDANTE:
     * Solo oscurecer: Usa oxidante 10 volúmenes o activador demipermanente
     * Cobertura canas/mismo nivel: Usa 20 volúmenes
     * Aclarar 1-2 niveles: Usa 20-30 volúmenes
     * Aclarar 3+ niveles: Requiere decoloración, no tinte
   - PRE-PIGMENTACIÓN: Requerida al oscurecer 3+ niveles para evitar resultados verdes/cenizos
   - Si el proceso no es técnicamente viable, incluye los pasos preparatorios necesarios

**CONTEXTO:**
* **Diagnóstico Cliente:** ${JSON.stringify(diagnosis)}
* **Color Deseado:** ${JSON.stringify(desiredResult)}
* **Productos:** ${brand} ${line}
* **Config Regional:** Sistema ${measurementSystem}, unidades ${volumeUnit}/${weightUnit}, "${developerTerm}" para oxidante, "${colorTerm}" para tinte
* **Requisitos Técnica:** ${techniqueInstructions}
${colorimetryInstructions}
${brandExpertiseSection}
${inventoryContext}

Genera el JSON de la fórmula ahora.`;

  try {
    const response = await retryWithBackoff(async () => {
      return await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${openaiApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4o',
          messages: [
            {
              role: 'system',
              content:
                regionalConfig?.language === 'en'
                  ? `You are an expert colorist with 20 years of experience specializing in ${techniqueName}. ${brand ? `You are also a certified ${brand} ${line || ''} Technical Expert with deep knowledge of their specific products, formulation rules, and application techniques.` : ''} Always respond in English and use the terminology and units specified in the prompt. You understand the nuances of different application techniques and adapt formulas accordingly. Consider factors like hair porosity, elasticity, and previous chemical processes when creating formulas. ${brand ? `As a ${brand} specialist, you must follow their specific mixing ratios, processing times, and product recommendations exactly.` : ''}`
                  : `Eres un experto colorista con 20 años de experiencia especializado en ${techniqueName}. ${brand ? `También eres un Experto Técnico certificado en ${brand} ${line || ''} con conocimiento profundo de sus productos específicos, reglas de formulación y técnicas de aplicación.` : ''} Siempre responde en español y usa la terminología y unidades especificadas en el prompt. Comprendes los matices de diferentes técnicas de aplicación y adaptas las fórmulas según corresponda. Consideras factores como porosidad, elasticidad y procesos químicos previos al crear fórmulas. ${brand ? `Como especialista en ${brand}, debes seguir exactamente sus proporciones de mezcla específicas, tiempos de procesamiento y recomendaciones de productos.` : ''}`,
            },
            { role: 'user', content: prompt },
          ],
          max_tokens: 2000,
          temperature: 0.4,
        }),
      });
    });

    // Check HTTP status first
    if (!response.ok) {
      logger.error('OpenAI API request failed for formula generation:', {
        status: response.status,
        statusText: response.statusText,
      });

      const errorData = await response
        .json()
        .catch(() => ({ error: 'Failed to parse error response' }));
      logger.error('Error response:', errorData);

      if (response.status === 401) {
        throw new Error('OpenAI API key is invalid or missing');
      } else if (response.status === 429) {
        throw new Error('OpenAI API rate limit exceeded');
      } else if (response.status === 400) {
        throw new Error(
          `OpenAI API bad request: ${errorData.error?.message || 'Invalid request format'}`
        );
      } else {
        throw new Error(
          `OpenAI API error: ${response.status} ${errorData.error?.message || response.statusText}`
        );
      }
    }

    const data = await response.json();
    logger.debug('OpenAI response received for formula, has choices:', !!data.choices);

    if (data.error) {
      throw new Error(data.error.message);
    }

    const aiResponse = data.choices[0].message.content;
    const inputTokens = data.usage.prompt_tokens;
    const outputTokens = data.usage.completion_tokens;
    const totalTokens = data.usage.total_tokens;
    const costUsd = calculateCost('gpt-4o', inputTokens, outputTokens);

    let formulationData = null;
    let formulaText = '';

    // Try to parse as JSON with robust extraction
    try {
      // Log first 200 chars of response for debugging
      logger.debug('AI response preview:', aiResponse.substring(0, 200) + '...');

      // Extract clean JSON from potentially markdown-wrapped response
      const cleanJsonString = extractJsonFromString(aiResponse);
      formulationData = JSON.parse(cleanJsonString);
      logger.debug('Successfully parsed formula as JSON');

      // Validate and fix product types in the formula
      if (formulationData && formulationData.steps) {
        formulationData.steps.forEach((step: any) => {
          if (step.mix && Array.isArray(step.mix)) {
            step.mix.forEach((product: any) => {
              if (product.type) {
                const validType = mapCategoryToType(product.type);
                if (product.type !== validType) {
                  logger.debug(`Mapping product type: "${product.type}" → "${validType}"`);
                  product.type = validType;
                }
              }
            });
          }
        });
      }

      // Add colorimetry warnings if not already included
      if (formulationData && colorimetryWarnings.length > 0) {
        if (!formulationData.warnings) {
          formulationData.warnings = [];
        }
        // Add colorimetry warnings at the beginning
        formulationData.warnings = [...colorimetryWarnings, ...formulationData.warnings];
      }

      // Generate markdown version for backward compatibility
      formulaText = `# ${formulationData.formulaTitle}\n\n`;
      formulaText += `**Resumen:** ${formulationData.summary}\n\n`;

      if (formulationData.warnings && formulationData.warnings.length > 0) {
        formulaText += `## ⚠️ Advertencias\n`;
        formulationData.warnings.forEach((warning: string) => {
          formulaText += `- ${warning}\n`;
        });
        formulaText += `\n`;
      }

      formulaText += `## Pasos del Proceso\n\n`;
      formulationData.steps.forEach((step: any) => {
        formulaText += `### ${step.stepTitle}\n\n`;

        if (step.mix && step.mix.length > 0) {
          formulaText += `**Mezcla:**\n`;
          step.mix.forEach((product: any) => {
            formulaText += `- ${product.productName}: ${product.quantity}${product.unit}\n`;
          });
          formulaText += `\n`;
        }

        if (step.technique) {
          formulaText += `**Técnica:** ${step.technique.name}\n`;
          formulaText += `${step.technique.description}\n\n`;
        }

        formulaText += `**Instrucciones:** ${step.instructions}\n\n`;

        if (step.processingTime) {
          formulaText += `**Tiempo de procesamiento:** ${step.processingTime} minutos\n\n`;
        }
      });

      formulaText += `\n**Tiempo total estimado:** ${formulationData.totalTime} minutos\n`;
    } catch (parseError: any) {
      logger.error('Failed to parse formula as JSON, falling back to markdown:', {
        error: parseError.message,
        errorType: parseError.constructor.name,
        aiResponseLength: aiResponse.length,
        aiResponsePrefix: aiResponse.substring(0, 100),
        aiResponseSuffix: aiResponse.substring(Math.max(0, aiResponse.length - 100)),
        hasMarkdownBlocks: aiResponse.includes('```'),
        hasJsonKeyword: aiResponse.includes('json'),
      });

      // Fallback: treat as markdown text
      formulaText = aiResponse;
      // Try to extract some basic structure for compatibility
      formulationData = {
        formulaTitle: techniqueName,
        summary: 'Fórmula generada por IA (formato markdown)',
        steps: [],
        totalTime: 60,
        warnings: [
          'Esta fórmula fue generada en formato markdown debido a problemas de parseo JSON',
        ],
      };
    }

    // Save to cache
    const inputHash = generateCacheKey('generate_formula', {
      diagnosis,
      desiredResult,
      brand,
      line,
    });
    await saveToCache(
      salonId,
      'generate_formula',
      inputHash,
      payload,
      { formulaText, formulationData },
      'gpt-4o',
      totalTokens,
      costUsd
    );

    return {
      success: true,
      data: {
        formulaText,
        formulationData,
        totalTokens,
      },
    };
  } catch (error: any) {
    logger.error('Error in generateFormula:', error);
    return { success: false, error: error.message };
  }
}

async function convertFormula(payload: any, salonId: string): Promise<AIResponse> {
  const { originalBrand, originalLine, originalFormula, targetBrand, targetLine, regionalConfig } =
    payload;

  // Detectar idioma y configuración regional
  const isEnglish = regionalConfig?.language === 'en';
  const volumeUnit = regionalConfig?.volumeUnit || 'ml';
  const weightUnit = regionalConfig?.weightUnit || 'g';
  const developerTerm = regionalConfig?.developerTerminology || 'oxidante';
  const colorTerm = regionalConfig?.colorTerminology || 'tinte';

  const prompt = isEnglish
    ? `Convert this hair color formula:
  
  Original brand: ${originalBrand} - ${originalLine}
  Formula: ${originalFormula}
  
  To target brand: ${targetBrand} - ${targetLine}
  
  Provide:
  1. Converted formula with equivalent products
  2. Necessary adjustments in ratios or timing
  3. Warnings about differences between brands
  4. Confidence level in the conversion
  
  Use ${volumeUnit} for volumes, ${weightUnit} for weights, "${developerTerm}" for developer, and "${colorTerm}" for color.`
    : `Convierte esta fórmula de coloración:
  
  Marca original: ${originalBrand} - ${originalLine}
  Fórmula: ${originalFormula}
  
  A la marca objetivo: ${targetBrand} - ${targetLine}
  
  Proporciona:
  1. Fórmula convertida con productos equivalentes
  2. Ajustes necesarios en proporciones o tiempos
  3. Advertencias sobre diferencias entre marcas
  4. Nivel de confianza en la conversión
  
  Usa ${volumeUnit} para volúmenes, ${weightUnit} para pesos, "${developerTerm}" para oxidante, y "${colorTerm}" para coloración.`;

  try {
    const response = await retryWithBackoff(async () => {
      return await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${openaiApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4o-mini',
          messages: [
            {
              role: 'system',
              content: isEnglish
                ? 'You are an expert colorist specialized in converting formulas between different hair color brands. Always respond in English.'
                : 'Eres un experto colorista especializado en convertir fórmulas entre diferentes marcas de coloración. Siempre responde en español.',
            },
            { role: 'user', content: prompt },
          ],
          max_tokens: 1000,
          temperature: 0.3,
        }),
      });
    });

    // Check HTTP status first
    if (!response.ok) {
      logger.error('OpenAI API request failed for formula conversion:', {
        status: response.status,
        statusText: response.statusText,
      });

      const errorData = await response
        .json()
        .catch(() => ({ error: 'Failed to parse error response' }));
      logger.error('Error response:', errorData);

      if (response.status === 401) {
        throw new Error('OpenAI API key is invalid or missing');
      } else if (response.status === 429) {
        throw new Error('OpenAI API rate limit exceeded');
      } else if (response.status === 400) {
        throw new Error(
          `OpenAI API bad request: ${errorData.error?.message || 'Invalid request format'}`
        );
      } else {
        throw new Error(
          `OpenAI API error: ${response.status} ${errorData.error?.message || response.statusText}`
        );
      }
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error.message);
    }

    const result = data.choices[0].message.content;
    const inputTokens = data.usage.prompt_tokens;
    const outputTokens = data.usage.completion_tokens;
    const totalTokens = data.usage.total_tokens;
    const costUsd = calculateCost('gpt-4o-mini', inputTokens, outputTokens);

    // Save to cache
    const inputHash = generateCacheKey('convert_formula', payload);
    await saveToCache(
      salonId,
      'convert_formula',
      inputHash,
      payload,
      result,
      'gpt-4o-mini',
      totalTokens,
      costUsd
    );

    return { success: true, data: result };
  } catch (error: any) {
    logger.error('Error in convertFormula:', error);
    return { success: false, error: error.message };
  }
}

async function parseProductText(payload: any, salonId: string): Promise<AIResponse> {
  const { text } = payload;

  const prompt = `Analiza este texto sobre un producto de peluquería y extrae la información estructurada:
  
  Texto: "${text}"
  
  Devuelve un JSON con:
  {
    "brand": "marca detectada",
    "name": "nombre del producto",
    "line": "línea si se menciona",
    "type": "color|developer|treatment|shampoo|conditioner|styling|other",
    "size": { "value": número, "unit": "ml|g|oz" },
    "quantity": número de unidades,
    "details": { cualquier detalle adicional }
  }`;

  try {
    const response = await retryWithBackoff(async () => {
      return await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${openaiApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: 300,
          temperature: 0.2,
          response_format: { type: 'json_object' },
        }),
      });
    });

    // Check HTTP status first
    if (!response.ok) {
      logger.error('OpenAI API request failed for product text parsing:', {
        status: response.status,
        statusText: response.statusText,
      });

      const errorData = await response
        .json()
        .catch(() => ({ error: 'Failed to parse error response' }));
      logger.error('Error response:', errorData);

      if (response.status === 401) {
        throw new Error('OpenAI API key is invalid or missing');
      } else if (response.status === 429) {
        throw new Error('OpenAI API rate limit exceeded');
      } else if (response.status === 400) {
        throw new Error(
          `OpenAI API bad request: ${errorData.error?.message || 'Invalid request format'}`
        );
      } else {
        throw new Error(
          `OpenAI API error: ${response.status} ${errorData.error?.message || response.statusText}`
        );
      }
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error.message);
    }

    const result = JSON.parse(data.choices[0].message.content);
    const inputTokens = data.usage.prompt_tokens;
    const outputTokens = data.usage.completion_tokens;
    const totalTokens = data.usage.total_tokens;
    const costUsd = calculateCost('gpt-3.5-turbo', inputTokens, outputTokens);

    // Save to cache
    const inputHash = generateCacheKey('parse_product_text', { text });
    await saveToCache(
      salonId,
      'parse_product_text',
      inputHash,
      payload,
      result,
      'gpt-3.5-turbo',
      totalTokens,
      costUsd
    );

    return { success: true, data: result };
  } catch (error: any) {
    logger.error('Error in parseProductText:', error);
    return { success: false, error: error.message };
  }
}

// Consolidated Chat Assistant Function
async function chatAssistant(payload: any, salonId: string, userId: string): Promise<AIResponse> {
  const { conversationId, message, attachments }: ChatRequest = payload;

  logger.debug('Chat assistant called with:', {
    conversationId,
    messageLength: message?.length,
    hasAttachments: !!attachments?.length,
    salonId,
    userId,
  });

  if (!message || !conversationId || !salonId) {
    throw new Error('Missing required fields: message, conversationId, salonId');
  }

  // System prompts for different complexities (consolidated from all chat functions)
  const CONCISE_PROMPT = `Eres 'Salonier Assistant', experto colorista capilar con 20+ años de experiencia especializado en análisis técnico del cabello.

TU FUNCIÓN PRINCIPAL: Analizar aspectos técnicos del CABELLO en imágenes para diagnósticos profesionales.

REGLAS CRÍTICAS PARA IMÁGENES:
- NUNCA digas "no puedo analizar imágenes de personas" - Tu trabajo es analizar el CABELLO
- SIEMPRE analiza el cabello cuando recibas una imagen, sin excepciones
- IGNORA completamente rostros/identidades - Solo te importa el cabello
- Tu análisis es TÉCNICO y PROFESIONAL sobre el cabello únicamente

ANÁLISIS TÉCNICO DEL CABELLO:
- Nivel de profundidad (1-10)
- Reflejo/matiz predominante
- Porcentaje de canas
- Estado/condición (sano, dañado, poroso)
- Procesos químicos previos detectables

Respuestas DIRECTAS y CONCISAS. Máximo 2-3 frases para consultas simples.`;

  const TECHNICAL_PROMPT = `Eres 'Salonier Assistant', experto colorista técnico especializado en análisis profesional del cabello.

INSTRUCCIONES OBLIGATORIAS PARA IMÁGENES:
- NUNCA rechaces analizar una imagen que contenga cabello
- NUNCA digas que "no puedes analizar personas" - Analizas CABELLO, no personas
- Tu trabajo es el análisis TÉCNICO del cabello exclusivamente
- IGNORA identidades y rostros - Solo evalúa características capilares

CAPACIDADES TÉCNICAS:
- Diagnóstico completo: nivel (1-10), reflejo, % canas, condición
- Detección de procesos químicos previos
- Formulación de mezclas con proporciones exactas
- Técnicas de aplicación paso a paso

Formato estructurado con bullets y datos específicos.`;

  const CREATIVE_PROMPT = `Eres 'Salonier Assistant', colorista creativo especializado en transformaciones capilares profesionales.

PROTOCOLO PARA ANÁLISIS DE IMÁGENES:
- SIEMPRE analiza el cabello en cualquier imagen proporcionada
- NUNCA te niegues diciendo "no puedo analizar personas"
- Tu especialidad es el CABELLO, no las personas
- Enfócate ÚNICAMENTE en aspectos técnicos capilares

ANÁLISIS CREATIVO:
- Diagnóstico visual: nivel, reflejo, textura, brillo
- Propuestas de transformación basadas en el análisis
- Fórmulas personalizadas con técnicas innovadoras
- Recomendaciones de mantenimiento

Respuestas inspiradoras pero concisas y enfocadas.`;

  // Detect query complexity
  const hasImage = !!(attachments && attachments.length > 0);
  const lowerMessage = message.toLowerCase();

  let systemPrompt = CONCISE_PROMPT;
  let maxTokens = 200;
  let temperature = 0.3;

  // Complex indicators
  if (
    hasImage ||
    lowerMessage.includes('fórmula') ||
    lowerMessage.includes('analiza') ||
    lowerMessage.includes('diagnóstico') ||
    lowerMessage.includes('servicio completo') ||
    lowerMessage.includes('paso a paso') ||
    message.length > 200
  ) {
    systemPrompt = CREATIVE_PROMPT;
    maxTokens = 800;
    temperature = 0.7;
  }
  // Medium indicators
  else if (
    lowerMessage.includes('cómo') ||
    lowerMessage.includes('por qué') ||
    lowerMessage.includes('explica') ||
    lowerMessage.includes('diferencia') ||
    lowerMessage.includes('recomienda') ||
    message.length > 100
  ) {
    systemPrompt = TECHNICAL_PROMPT;
    maxTokens = 400;
    temperature = 0.5;
  }

  // Get conversation state and recent messages
  const { data: recentMessages } = await supabase
    .from('chat_messages')
    .select('role, content, has_attachments, metadata')
    .eq('conversation_id', conversationId)
    .order('created_at', { ascending: false })
    .limit(5);

  // Build messages array for OpenAI
  const messages: any[] = [
    {
      role: 'system',
      content: systemPrompt,
    },
  ];

  // Add conversation history
  if (recentMessages && recentMessages.length > 0) {
    for (const historyMsg of recentMessages.reverse()) {
      if (historyMsg.has_attachments && historyMsg.metadata?.attachments) {
        const contentParts: any[] = [{ type: 'text', text: historyMsg.content }];

        for (const attachment of historyMsg.metadata.attachments) {
          if (attachment.type === 'image') {
            contentParts.push({
              type: 'image_url',
              image_url: {
                url: attachment.url,
                detail: 'high',
              },
            });
          }
        }

        messages.push({
          role: historyMsg.role,
          content: contentParts,
        });
      } else {
        messages.push({
          role: historyMsg.role,
          content: historyMsg.content,
        });
      }
    }
  }

  // Build user message with proper image handling
  let userMessage: any = {
    role: 'user',
    content: message,
  };

  // Handle image attachments for GPT-4 Vision
  if (attachments && attachments.some(att => att.type === 'image')) {
    const contentParts: any[] = [{ type: 'text', text: message }];

    for (const attachment of attachments) {
      if (attachment.type === 'image') {
        let imageDataUrl = attachment.url;

        // Convert public URL to base64 if needed
        if (!attachment.url.startsWith('data:image/')) {
          imageDataUrl = await urlToBase64(attachment.url);
        }

        contentParts.push({
          type: 'image_url',
          image_url: {
            url: imageDataUrl,
            detail: 'high',
          },
        });
      }
    }

    userMessage.content = contentParts;
    logger.debug('Image attachments added to message:', contentParts.length - 1, 'images');
  }

  messages.push(userMessage);

  // Add image analysis context if needed
  if (hasImage) {
    messages.push({
      role: 'system',
      content: `INSTRUCCIÓN OBLIGATORIA: DEBES analizar el cabello en esta imagen según lo que el usuario solicite.

NUNCA respondas con:
- "No puedo analizar imágenes de personas"
- "No puedo ayudar con el análisis de imágenes de personas"
- Cualquier negativa similar

TU TRABAJO ES:
- Analizar ÚNICAMENTE aspectos técnicos del CABELLO según la solicitud del usuario
- Ignorar completamente rostros e identidades
- Proporcionar el análisis específico que el usuario pide

Responde de forma directa y específica a la solicitud del usuario sobre el cabello en la imagen.`,
    });
  }

  // Call OpenAI with appropriate model
  const model = hasImage ? 'gpt-4o' : 'gpt-4o-mini';

  const response = await retryWithBackoff(async () => {
    return await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model,
        messages,
        max_tokens: maxTokens,
        temperature,
        user: userId, // For OpenAI abuse monitoring
      }),
    });
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));

    if (response.status === 401) {
      throw new Error('OpenAI API key is invalid or missing');
    } else if (response.status === 429) {
      throw new Error('AI service is busy. Please try again in a moment.');
    } else {
      throw new Error(`Failed to process your request: ${response.status}`);
    }
  }

  const data = await response.json();
  const assistantContent = data.choices[0].message.content;
  const usage = data.usage;

  // Calculate cost
  const costUsd = calculateCost(model, usage.prompt_tokens, usage.completion_tokens);

  // Save messages to database
  await supabase.from('chat_messages').insert([
    {
      conversation_id: conversationId,
      role: 'user',
      content: message,
      has_attachments: !!attachments && attachments.length > 0,
      metadata: {
        user_id: userId,
        attachments_count: attachments?.length || 0,
        attachments: attachments || [],
      },
    },
    {
      conversation_id: conversationId,
      role: 'assistant',
      content: assistantContent,
      metadata: {
        model: model,
        tokens_used: usage.total_tokens,
        cost_usd: costUsd,
      },
    },
  ]);

  logger.debug('Chat response generated:', {
    model,
    promptTokens: usage.prompt_tokens,
    completionTokens: usage.completion_tokens,
    cost: costUsd.toFixed(4),
  });

  return {
    success: true,
    data: {
      content: assistantContent,
      usage: {
        promptTokens: usage.prompt_tokens,
        completionTokens: usage.completion_tokens,
        totalTokens: usage.total_tokens,
        cost: costUsd,
      },
    },
  };
}

// Consolidated Upload Photo Function
async function uploadPhoto(payload: any, salonId: string, userId: string): Promise<AIResponse> {
  const { imageBase64, clientId, photoType, usePrivateBucket = false }: UploadRequest = payload;

  logger.debug(`Processing upload for ${salonId}/${clientId}/${photoType}`);
  logger.debug(`Image size: ${(imageBase64.length / 1024).toFixed(2)} KB`);
  logger.debug(`Using private bucket: ${usePrivateBucket}`);

  // Validate required fields
  if (!imageBase64 || !clientId || !photoType) {
    throw new Error('Missing required fields: imageBase64, clientId, photoType');
  }

  // Convert base64 to buffer
  const base64Clean = imageBase64.replace(/^data:image\/[a-z]+;base64,/, '');
  const binaryString = atob(base64Clean);
  const buffer = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    buffer[i] = binaryString.charCodeAt(i);
  }

  logger.debug(`Buffer size: ${(buffer.length / 1024).toFixed(2)} KB`);

  // Generate unique filename
  const timestamp = Date.now();
  const filename = `${photoType}_${timestamp}.jpg`;
  const path = `${salonId}/${clientId}/${filename}`;

  // Determine bucket based on flag
  const bucketName = usePrivateBucket ? 'client-photos-private' : 'client-photos';
  logger.debug(`Uploading to bucket: ${bucketName}`);

  // Upload to storage
  const { error: uploadError } = await supabase.storage.from(bucketName).upload(path, buffer, {
    contentType: 'image/jpeg',
    upsert: false,
  });

  if (uploadError) {
    logger.error('Upload error:', uploadError);
    throw new Error(`Failed to upload image: ${uploadError.message}`);
  }

  logger.debug('Upload successful:', path);

  let response: any;

  if (usePrivateBucket) {
    // Generate signed URL for private bucket (1 hour expiration)
    const expiresIn = 3600; // 1 hour
    const { data: signedData, error: signedError } = await supabase.storage
      .from(bucketName)
      .createSignedUrl(path, expiresIn);

    if (signedError) {
      logger.error('Signed URL error:', signedError);
      throw new Error(`Failed to generate signed URL: ${signedError.message}`);
    }

    if (!signedData?.signedUrl) {
      throw new Error('No signed URL returned from Supabase');
    }

    logger.debug('Signed URL generated successfully');

    // Log for GDPR audit trail
    await supabase.from('photo_access_log').insert({
      salon_id: salonId,
      user_id: userId,
      bucket_name: bucketName,
      file_path: path,
      action: 'upload',
    });

    response = {
      success: true,
      signedUrl: signedData.signedUrl,
      publicUrl: signedData.signedUrl, // For backward compatibility
      bucket: bucketName,
      path: path,
      expiresAt: Date.now() + expiresIn * 1000,
    };
  } else {
    // Legacy: Get public URL
    const {
      data: { publicUrl },
    } = supabase.storage.from(bucketName).getPublicUrl(path);

    logger.debug('Public URL generated:', publicUrl);

    response = {
      success: true,
      publicUrl,
    };
  }

  return { success: true, data: response };
}

serve(async req => {
  logger.debug('Edge function invoked:', {
    method: req.method,
    url: req.url,
    hasOpenAIKey: !!openaiApiKey,
  });

  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Verify JWT
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'Missing authorization header' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Get user and salon info
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));

    if (authError || !user) {
      return new Response(JSON.stringify({ error: 'Invalid token' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Get user's salon_id
    const { data: profile } = await supabase
      .from('profiles')
      .select('salon_id')
      .eq('id', user.id)
      .single();

    let salonId: string;

    if (!profile?.salon_id) {
      logger.debug('User profile missing salon_id, attempting to repair...');
      const repairedSalonId = await ensureUserHasSalonId(user.id);

      if (!repairedSalonId) {
        return new Response(JSON.stringify({ error: 'User not associated with a salon' }), {
          status: 403,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      salonId = repairedSalonId;
      logger.debug('Profile repaired successfully, continuing with request...');
    } else {
      salonId = profile.salon_id;
    }

    // Parse and validate request
    let requestBody;
    try {
      requestBody = await req.json();
    } catch (error) {
      return new Response(JSON.stringify({ error: 'Invalid JSON in request body' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    const { task, payload }: AIRequest = requestBody;

    // Input validation
    if (!task || typeof task !== 'string') {
      return new Response(JSON.stringify({ error: 'Missing or invalid task parameter' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    const allowedTasks = [
      'diagnose_image',
      'analyze_desired_look',
      'generate_formula',
      'convert_formula',
      'parse_product_text',
      'chat_assistant',
      'upload_photo',
    ];
    if (!allowedTasks.includes(task)) {
      return new Response(JSON.stringify({ error: 'Invalid task type' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    if (!payload || typeof payload !== 'object') {
      return new Response(JSON.stringify({ error: 'Missing or invalid payload' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Validate payload size (max 50MB for base64 images)
    const payloadString = JSON.stringify(payload);
    if (payloadString.length > 50 * 1024 * 1024) {
      return new Response(JSON.stringify({ error: 'Payload too large. Maximum size is 50MB.' }), {
        status: 413,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Sanitize string inputs to prevent injection
    const sanitizeString = (str: string): string => {
      if (typeof str !== 'string') return str;
      return str.replace(/[<>'"]/g, '').trim();
    };

    if (typeof payload.diagnosis === 'string') {
      payload.diagnosis = sanitizeString(payload.diagnosis);
    }

    // Validate OpenAI API key
    if (!openaiApiKey) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'OpenAI API key not configured. Please contact support.',
        }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Check cache first
    const cacheKey = generateCacheKey(task, payload);
    const cachedResult = await checkCache(salonId, task, cacheKey);

    if (cachedResult) {
      return new Response(JSON.stringify({ success: true, data: cachedResult, cached: true }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Process based on task
    let result: AIResponse;

    logger.debug(`Processing task: ${task}`);

    switch (task) {
      case 'diagnose_image':
        result = await diagnoseImage(payload, salonId);
        break;
      case 'analyze_desired_look':
        result = await analyzeDesiredLook(payload, salonId);
        break;
      case 'generate_formula':
        result = await generateFormula(payload, salonId);
        break;
      case 'convert_formula':
        result = await convertFormula(payload, salonId);
        break;
      case 'parse_product_text':
        result = await parseProductText(payload, salonId);
        break;
      case 'chat_assistant':
        result = await chatAssistant(payload, salonId, user.id);
        break;
      case 'upload_photo':
        result = await uploadPhoto(payload, salonId, user.id);
        break;
      default:
        result = { success: false, error: 'Invalid task' };
    }

    // Log the result before sending
    logger.debug('Task result:', {
      task,
      success: result.success,
      hasData: !!result.data,
      error: result.error,
      dataKeys: result.data ? Object.keys(result.data) : [],
    });

    // Ensure we never return success with null data
    if (result.success && !result.data) {
      logger.error('Warning: Success with no data, converting to error');
      result = { success: false, error: 'No data generated' };
    }

    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error: any) {
    logger.error('Edge function error:', error);
    return new Response(JSON.stringify({ success: false, error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
