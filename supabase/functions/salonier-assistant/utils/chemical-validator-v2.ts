/**
 * Advanced Chemical Validation System V2
 * Implements pigment detection, compatibility matrix, and safety alerts
 */

export interface PigmentAnalysis {
  level: number;
  underlyingPigment: {
    dominant: string;
    secondary: string | null;
    intensity: 'light' | 'medium' | 'heavy';
  };
  neutralizationNeeded: {
    required: boolean;
    neutralizer: string | null;
    amount: 'minimal' | 'moderate' | 'significant';
  };
  expectedLift: {
    natural: number;
    withBleach: number;
    sessions: number;
  };
}

export interface ChemicalCompatibility {
  compatible: boolean;
  risks: Risk[];
  warnings: string[];
  alternatives: Alternative[];
  safetyScore: number; // 0-100
}

export interface Risk {
  type: 'reaction' | 'damage' | 'color' | 'health';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  mitigation: string;
  probability: number; // 0-1
}

export interface Alternative {
  product: string;
  reason: string;
  effectiveness: number; // 0-100
}

export interface NeutralizationFormula {
  base: string;
  neutralizer: string;
  ratio: string;
  processingTime: number;
  expectedResult: string;
}

export class ChemicalValidatorV2 {
  /**
   * Enhanced underlying pigment chart with intensity levels
   */
  private static PIGMENT_CHART = {
    1: {
      dominant: 'dark red-brown',
      secondary: 'red',
      neutralizers: ['green', 'matte green'],
      intensity: { virgin: 'heavy', treated: 'heavy' },
    },
    2: {
      dominant: 'red-brown',
      secondary: 'red-orange',
      neutralizers: ['green', 'ash green'],
      intensity: { virgin: 'heavy', treated: 'medium-heavy' },
    },
    3: {
      dominant: 'red',
      secondary: 'red-orange',
      neutralizers: ['green', 'matte'],
      intensity: { virgin: 'medium-heavy', treated: 'medium' },
    },
    4: {
      dominant: 'red-orange',
      secondary: 'orange',
      neutralizers: ['green-blue', 'ash'],
      intensity: { virgin: 'medium', treated: 'medium' },
    },
    5: {
      dominant: 'orange',
      secondary: 'orange-yellow',
      neutralizers: ['blue', 'ash blue'],
      intensity: { virgin: 'medium', treated: 'light-medium' },
    },
    6: {
      dominant: 'orange-yellow',
      secondary: 'yellow',
      neutralizers: ['blue-violet', 'ash'],
      intensity: { virgin: 'medium', treated: 'light' },
    },
    7: {
      dominant: 'yellow-orange',
      secondary: 'yellow',
      neutralizers: ['violet-blue', 'pearl'],
      intensity: { virgin: 'light-medium', treated: 'light' },
    },
    8: {
      dominant: 'yellow',
      secondary: 'pale yellow',
      neutralizers: ['violet', 'pearl violet'],
      intensity: { virgin: 'light', treated: 'very light' },
    },
    9: {
      dominant: 'pale yellow',
      secondary: null,
      neutralizers: ['violet', 'silver'],
      intensity: { virgin: 'very light', treated: 'minimal' },
    },
    10: {
      dominant: 'none',
      secondary: null,
      neutralizers: [],
      intensity: { virgin: 'none', treated: 'none' },
    },
  };

  /**
   * Chemical incompatibility matrix with detailed reactions
   */
  private static INCOMPATIBILITY_MATRIX = {
    metallic_salts: {
      incompatible: ['peroxide', 'ammonia', 'bleach', 'thioglycolic_acid'],
      reaction: 'Heat generation, smoke, hair breakage',
      severity: 'critical',
      detection: 'Green discoloration when tested with 20vol peroxide',
    },
    henna: {
      incompatible: ['peroxide', 'ammonia', 'lightener', 'permanent_color'],
      reaction: 'Unpredictable color, potential hair damage',
      severity: 'high',
      detection: 'Orange/red coating resistant to lightening',
    },
    keratin_treatment: {
      incompatible: ['high_ammonia', 'bleach_powder', 'high_lift_color'],
      reaction: 'Treatment breakdown, excessive damage',
      severity: 'medium',
      wait_time: '2 weeks minimum',
    },
    japanese_straightening: {
      incompatible: ['color_service', 'bleach', 'perm'],
      reaction: 'Severe breakage, chemical burns possible',
      severity: 'critical',
      wait_time: '3 months minimum',
    },
    relaxer: {
      incompatible: ['same_day_color', 'bleach', 'perm'],
      reaction: 'Scalp irritation, excessive damage',
      severity: 'high',
      wait_time: '2 weeks minimum',
    },
    chlorine: {
      incompatible: ['immediate_lightening', 'high_lift_color'],
      reaction: 'Green tint, uneven results',
      severity: 'low',
      treatment: 'Clarifying treatment first',
    },
  };

  /**
   * Analyze underlying pigments with advanced detection
   */
  static analyzePigments(
    currentLevel: number,
    isVirgin: boolean,
    previousTreatments: string[],
    desiredLevel: number
  ): PigmentAnalysis {
    const level = Math.round(Math.min(Math.max(currentLevel, 1), 10));
    const pigmentData = this.PIGMENT_CHART[level];

    // Determine intensity based on hair history
    const intensity = isVirgin ? pigmentData.intensity.virgin : pigmentData.intensity.treated;

    // Calculate lift requirements
    const levelDifference = desiredLevel - currentLevel;
    const naturalLift = this.calculateNaturalLift(currentLevel, isVirgin, previousTreatments);
    const bleachLift = this.calculateBleachLift(currentLevel, isVirgin);

    // Determine neutralization needs
    const needsNeutralization = this.requiresNeutralization(
      currentLevel,
      desiredLevel,
      pigmentData.dominant
    );

    // Calculate sessions needed
    const sessions = Math.ceil(levelDifference / (isVirgin ? 4 : 3));

    return {
      level: currentLevel,
      underlyingPigment: {
        dominant: pigmentData.dominant,
        secondary: pigmentData.secondary,
        intensity: intensity as any,
      },
      neutralizationNeeded: {
        required: needsNeutralization,
        neutralizer: needsNeutralization ? pigmentData.neutralizers[0] : null,
        amount: this.calculateNeutralizerAmount(intensity),
      },
      expectedLift: {
        natural: naturalLift,
        withBleach: bleachLift,
        sessions: sessions,
      },
    };
  }

  /**
   * Check chemical compatibility with detailed analysis
   */
  static checkCompatibility(
    currentTreatments: string[],
    plannedProducts: string[],
    timeSinceLastTreatment: number // days
  ): ChemicalCompatibility {
    const risks: Risk[] = [];
    const warnings: string[] = [];
    const alternatives: Alternative[] = [];
    let safetyScore = 100;

    // Check each current treatment against planned products
    for (const treatment of currentTreatments) {
      const incompatData = this.INCOMPATIBILITY_MATRIX[treatment];
      if (!incompatData) continue;

      for (const product of plannedProducts) {
        if (incompatData.incompatible.includes(product)) {
          // Found incompatibility
          risks.push({
            type: 'reaction',
            severity: incompatData.severity as any,
            description: incompatData.reaction,
            mitigation: incompatData.wait_time || 'Avoid this combination',
            probability: 0.9,
          });

          safetyScore -=
            incompatData.severity === 'critical'
              ? 50
              : incompatData.severity === 'high'
                ? 30
                : incompatData.severity === 'medium'
                  ? 20
                  : 10;

          // Suggest alternatives
          alternatives.push(this.suggestAlternative(product, treatment));
        }
      }

      // Check timing requirements
      if (incompatData.wait_time) {
        const requiredDays = this.parseWaitTime(incompatData.wait_time);
        if (timeSinceLastTreatment < requiredDays) {
          warnings.push(
            `Wait ${requiredDays - timeSinceLastTreatment} more days after ${treatment}`
          );
          safetyScore -= 15;
        }
      }
    }

    // Additional safety checks
    if (plannedProducts.includes('bleach') && plannedProducts.includes('color')) {
      warnings.push('Bleach and color in same session - ensure proper isolation');
      safetyScore -= 10;
    }

    return {
      compatible: risks.filter(r => r.severity === 'critical').length === 0,
      risks,
      warnings,
      alternatives,
      safetyScore: Math.max(safetyScore, 0),
    };
  }

  /**
   * Generate neutralization formula
   */
  static generateNeutralizationFormula(
    currentLevel: number,
    targetLevel: number,
    targetTone: string,
    brand: string
  ): NeutralizationFormula {
    const pigmentData = this.PIGMENT_CHART[currentLevel];
    const neutralizer = this.selectNeutralizer(
      pigmentData.dominant,
      targetTone,
      pigmentData.neutralizers
    );

    // Calculate ratio based on pigment intensity
    const intensity = pigmentData.intensity.virgin;
    const ratio = intensity === 'heavy' ? '1:1' : intensity === 'medium' ? '3:1' : '5:1'; // Light intensity

    return {
      base: `${brand} ${targetLevel}`,
      neutralizer: `${brand} ${neutralizer}`,
      ratio: ratio,
      processingTime: this.calculateProcessingTime(currentLevel, targetLevel),
      expectedResult: this.predictResult(currentLevel, targetLevel, neutralizer),
    };
  }

  /**
   * Advanced safety alerts system
   */
  static generateSafetyAlerts(
    hairState: any,
    plannedService: any
  ): {
    alerts: Alert[];
    canProceed: boolean;
    requiresPatchTest: boolean;
    requiresStrandTest: boolean;
  } {
    const alerts: Alert[] = [];
    let canProceed = true;
    let requiresPatchTest = false;
    let requiresStrandTest = false;

    // Check for metallic salts
    if (hairState.hasMetallicSalts) {
      alerts.push({
        level: 'critical',
        message: 'METALLIC SALTS DETECTED - Do not proceed with chemical service',
        action: 'Perform metallic salts removal treatment first',
      });
      canProceed = false;
    }

    // Check damage level
    if (hairState.damage === 'severe' && plannedService.includesBleach) {
      alerts.push({
        level: 'high',
        message: 'Severe damage detected - bleaching not recommended',
        action: 'Perform strengthening treatments first',
      });
      requiresStrandTest = true;
    }

    // Check for henna
    if (hairState.hasHenna) {
      alerts.push({
        level: 'high',
        message: 'Henna detected - unpredictable results likely',
        action: 'Strand test mandatory, consider waiting for grow-out',
      });
      requiresStrandTest = true;
    }

    // Allergy history
    if (hairState.allergyHistory) {
      alerts.push({
        level: 'medium',
        message: 'Previous allergic reaction recorded',
        action: 'Patch test required 48 hours before service',
      });
      requiresPatchTest = true;
    }

    // High lift on dark hair
    if (plannedService.liftLevels > 4 && hairState.currentLevel < 5) {
      alerts.push({
        level: 'medium',
        message: 'Attempting 4+ levels of lift on dark hair',
        action: 'Consider multiple sessions for hair health',
      });
    }

    return {
      alerts,
      canProceed,
      requiresPatchTest,
      requiresStrandTest,
    };
  }

  // Helper methods

  private static calculateNaturalLift(
    level: number,
    isVirgin: boolean,
    treatments: string[]
  ): number {
    let baseLift = isVirgin ? 3 : 2;

    if (treatments.includes('previous_bleach')) baseLift -= 1;
    if (level <= 3) baseLift -= 1; // Dark hair lifts less
    if (level >= 8) baseLift += 1; // Light hair lifts easier

    return Math.max(baseLift, 1);
  }

  private static calculateBleachLift(level: number, isVirgin: boolean): number {
    let baseLift = isVirgin ? 7 : 5;

    if (level <= 2) baseLift = 5; // Very dark hair max
    if (level >= 7) baseLift = 9; // Light hair can go platinum

    return baseLift;
  }

  private static requiresNeutralization(current: number, target: number, pigment: string): boolean {
    // Neutralization needed when lifting and targeting cool tones
    const isLifting = target > current;
    const hasWarmPigment = ['orange', 'red', 'yellow'].some(p => pigment.includes(p));

    return isLifting && hasWarmPigment;
  }

  private static calculateNeutralizerAmount(
    intensity: string
  ): 'minimal' | 'moderate' | 'significant' {
    switch (intensity) {
      case 'heavy':
      case 'medium-heavy':
        return 'significant';
      case 'medium':
      case 'light-medium':
        return 'moderate';
      default:
        return 'minimal';
    }
  }

  private static selectNeutralizer(
    pigment: string,
    targetTone: string,
    available: string[]
  ): string {
    // Smart neutralizer selection based on target
    if (targetTone.includes('ash') || targetTone.includes('cenizo')) {
      return available.find(n => n.includes('ash')) || available[0];
    }
    if (targetTone.includes('pearl') || targetTone.includes('iridescent')) {
      return available.find(n => n.includes('pearl')) || available[0];
    }
    return available[0] || 'ash';
  }

  private static calculateProcessingTime(current: number, target: number): number {
    const basetime = 35;
    const liftFactor = (target - current) * 5;
    return Math.min(basetime + liftFactor, 50); // Cap at 50 minutes
  }

  private static predictResult(current: number, target: number, neutralizer: string): string {
    const lift = target - current;

    if (lift > 4) {
      return `Level ${current + 3}-${current + 4} with ${neutralizer} tones (multiple sessions recommended)`;
    }
    if (neutralizer.includes('ash')) {
      return `Level ${target} with cool/ash reflects`;
    }
    if (neutralizer.includes('violet')) {
      return `Level ${target} with pearl/platinum reflects`;
    }
    return `Level ${target} with neutral tones`;
  }

  private static suggestAlternative(product: string, treatment: string): Alternative {
    const alternatives = {
      bleach: {
        product: 'High-lift color or highlights',
        reason: 'Gentler alternative to bleaching',
        effectiveness: 75,
      },
      ammonia: {
        product: 'Ammonia-free color',
        reason: 'Less damaging, suitable after treatments',
        effectiveness: 85,
      },
      peroxide: {
        product: 'Demi-permanent color',
        reason: 'Lower developer, less reaction risk',
        effectiveness: 70,
      },
    };

    return (
      alternatives[product] || {
        product: 'Consultation required',
        reason: 'Complex case needs expert evaluation',
        effectiveness: 50,
      }
    );
  }

  private static parseWaitTime(waitTime: string): number {
    const match = waitTime.match(/(\d+)\s*(week|month|day)/);
    if (!match) return 14; // Default 2 weeks

    const [, amount, unit] = match;
    const days =
      unit === 'month'
        ? parseInt(amount) * 30
        : unit === 'week'
          ? parseInt(amount) * 7
          : parseInt(amount);

    return days;
  }
}

interface Alert {
  level: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  action: string;
}
