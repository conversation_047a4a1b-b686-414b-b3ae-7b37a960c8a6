/**
 * Advanced Model Router V2 with Budget Management
 * Implements intelligent routing, budget limits, and accuracy monitoring
 */

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

export interface TaskComplexity {
  type: 'diagnosis' | 'formula' | 'validation' | 'chat';
  factors: {
    hasChemicalProcess: boolean;
    hasDamage: boolean;
    hasMetallicSalts: boolean;
    hasHenna: boolean;
    isColorCorrection: boolean;
    levelChange: number;
    imageQuality: 'low' | 'medium' | 'high';
    previousFailures: number;
  };
}

export interface ModelConfig {
  name: string;
  cost: number; // per 1k tokens
  speed: number; // avg seconds
  accuracy: number; // 0-1
  maxComplexity: 'simple' | 'medium' | 'complex';
  dailyBudget: number; // USD
  currentSpend: number; // USD today
}

export interface BudgetConfig {
  salonId: string;
  dailyLimit: number;
  monthlyLimit: number;
  alertThreshold: number; // % to trigger alert
  hardStop: boolean; // stop all requests when limit reached
}

export class ModelRouterV2 {
  private static models: Map<string, ModelConfig> = new Map([
    [
      'gpt-3.5-turbo',
      {
        name: 'gpt-3.5-turbo',
        cost: 0.002,
        speed: 1.2,
        accuracy: 0.85,
        maxComplexity: 'simple',
        dailyBudget: 5.0,
        currentSpend: 0,
      },
    ],
    [
      'gpt-4o-mini',
      {
        name: 'gpt-4o-mini',
        cost: 0.015,
        speed: 2.5,
        accuracy: 0.92,
        maxComplexity: 'medium',
        dailyBudget: 10.0,
        currentSpend: 0,
      },
    ],
    [
      'gpt-4o',
      {
        name: 'gpt-4o',
        cost: 0.03,
        speed: 4.5,
        accuracy: 0.98,
        maxComplexity: 'complex',
        dailyBudget: 15.0,
        currentSpend: 0,
      },
    ],
  ]);

  private static complexityWeights = {
    hasChemicalProcess: 0.15,
    hasDamage: 0.2,
    hasMetallicSalts: 0.3,
    hasHenna: 0.25,
    isColorCorrection: 0.25,
    levelChange: 0.1, // multiplied by actual level change
    imageQuality: { low: 0.15, medium: 0.05, high: 0 },
    previousFailures: 0.2, // multiplied by failure count
  };

  /**
   * Calculate task complexity score (0-1)
   */
  static calculateComplexityScore(task: TaskComplexity): number {
    const { factors } = task;
    let score = 0;

    // Boolean factors
    if (factors.hasChemicalProcess) score += this.complexityWeights.hasChemicalProcess;
    if (factors.hasDamage) score += this.complexityWeights.hasDamage;
    if (factors.hasMetallicSalts) score += this.complexityWeights.hasMetallicSalts;
    if (factors.hasHenna) score += this.complexityWeights.hasHenna;
    if (factors.isColorCorrection) score += this.complexityWeights.isColorCorrection;

    // Numeric factors
    score += Math.min(factors.levelChange * 0.05, 0.25); // Cap at 0.25 for 5+ levels
    score += this.complexityWeights.imageQuality[factors.imageQuality];
    score += Math.min(factors.previousFailures * 0.1, 0.3); // Cap at 0.30

    return Math.min(score, 1.0); // Ensure max is 1.0
  }

  /**
   * Determine complexity category from score
   */
  static getComplexityCategory(score: number): 'simple' | 'medium' | 'complex' {
    if (score < 0.3) return 'simple';
    if (score < 0.6) return 'medium';
    return 'complex';
  }

  /**
   * Select optimal model based on complexity and budget
   */
  static async selectModel(
    task: TaskComplexity,
    budgetConfig: BudgetConfig
  ): Promise<{ model: string; reason: string; confidence: number }> {
    const score = this.calculateComplexityScore(task);
    const complexity = this.getComplexityCategory(score);

    // Check budget constraints
    const budgetRemaining = await this.getBudgetRemaining(budgetConfig);

    // If budget is critically low, force cheapest model
    if (budgetRemaining < 0.5 && budgetConfig.hardStop) {
      return {
        model: 'gpt-3.5-turbo',
        reason: 'Budget constraint - using most economical model',
        confidence: 70,
      };
    }

    // Get accuracy history for this type of task
    const accuracyHistory = await this.getAccuracyHistory(budgetConfig.salonId, task.type);

    // Start with ideal model for complexity
    let selectedModel = this.getIdealModel(complexity, accuracyHistory);

    // Adjust based on previous failures
    if (task.factors.previousFailures > 0) {
      selectedModel = this.upgradeModel(selectedModel);
    }

    // Validate budget allows this model
    const modelConfig = this.models.get(selectedModel)!;
    if (modelConfig.currentSpend >= modelConfig.dailyBudget) {
      selectedModel = this.downgradeModel(selectedModel);
    }

    // Calculate confidence based on model match
    const confidence = this.calculateConfidence(selectedModel, complexity, task.factors);

    return {
      model: selectedModel,
      reason: this.getSelectionReason(selectedModel, complexity, score),
      confidence,
    };
  }

  /**
   * Get ideal model for complexity level
   */
  private static getIdealModel(
    complexity: 'simple' | 'medium' | 'complex',
    accuracyHistory: Map<string, number>
  ): string {
    // Check if we have good accuracy with cheaper models
    const gpt35Accuracy = accuracyHistory.get('gpt-3.5-turbo') || 0.85;
    const miniAccuracy = accuracyHistory.get('gpt-4o-mini') || 0.92;

    switch (complexity) {
      case 'simple':
        // Use GPT-3.5 if accuracy is good enough
        return gpt35Accuracy > 0.8 ? 'gpt-3.5-turbo' : 'gpt-4o-mini';

      case 'medium':
        // Use mini if accuracy is good
        return miniAccuracy > 0.88 ? 'gpt-4o-mini' : 'gpt-4o';

      case 'complex':
        // Always use best model for complex tasks
        return 'gpt-4o';

      default:
        return 'gpt-4o-mini';
    }
  }

  /**
   * Upgrade model to next tier
   */
  private static upgradeModel(model: string): string {
    const upgrades = {
      'gpt-3.5-turbo': 'gpt-4o-mini',
      'gpt-4o-mini': 'gpt-4o',
      'gpt-4o': 'gpt-4o', // Already at max
    };
    return upgrades[model] || model;
  }

  /**
   * Downgrade model to cheaper tier
   */
  private static downgradeModel(model: string): string {
    const downgrades = {
      'gpt-4o': 'gpt-4o-mini',
      'gpt-4o-mini': 'gpt-3.5-turbo',
      'gpt-3.5-turbo': 'gpt-3.5-turbo', // Already at min
    };
    return downgrades[model] || model;
  }

  /**
   * Calculate confidence in model selection
   */
  private static calculateConfidence(
    model: string,
    complexity: 'simple' | 'medium' | 'complex',
    factors: TaskComplexity['factors']
  ): number {
    const modelConfig = this.models.get(model)!;
    let confidence = modelConfig.accuracy * 100;

    // Reduce confidence if model doesn't match complexity
    const complexityMatch = {
      simple: { 'gpt-3.5-turbo': 1.0, 'gpt-4o-mini': 0.9, 'gpt-4o': 0.8 },
      medium: { 'gpt-3.5-turbo': 0.7, 'gpt-4o-mini': 1.0, 'gpt-4o': 0.95 },
      complex: { 'gpt-3.5-turbo': 0.5, 'gpt-4o-mini': 0.75, 'gpt-4o': 1.0 },
    };

    confidence *= complexityMatch[complexity][model] || 0.8;

    // Reduce for risky factors
    if (factors.hasMetallicSalts) confidence *= 0.85;
    if (factors.hasHenna) confidence *= 0.88;
    if (factors.levelChange > 4) confidence *= 0.9;

    return Math.round(Math.max(confidence, 50)); // Min 50% confidence
  }

  /**
   * Get human-readable reason for model selection
   */
  private static getSelectionReason(
    model: string,
    complexity: 'simple' | 'medium' | 'complex',
    score: number
  ): string {
    const reasons = {
      'gpt-3.5-turbo': {
        simple: 'Simple task - using fast economical model',
        medium: 'Budget optimized - acceptable accuracy for medium task',
        complex: 'Budget constraint - best available within limits',
      },
      'gpt-4o-mini': {
        simple: 'Enhanced accuracy for better results',
        medium: 'Optimal balance of cost and accuracy',
        complex: 'Budget conscious - good accuracy for complex task',
      },
      'gpt-4o': {
        simple: 'Premium accuracy requested',
        medium: 'High accuracy needed for chemical processes',
        complex: 'Maximum precision for complex color correction',
      },
    };

    return (
      reasons[model]?.[complexity] ||
      `Selected based on complexity score: ${(score * 100).toFixed(0)}%`
    );
  }

  /**
   * Get remaining budget for salon
   */
  private static async getBudgetRemaining(config: BudgetConfig): Promise<number> {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL')!,
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    );

    const today = new Date().toISOString().split('T')[0];

    const { data } = await supabase
      .from('ai_metrics_simple')
      .select('total_cost')
      .eq('salon_id', config.salonId)
      .eq('metric_date', today)
      .single();

    const spent = data?.total_cost || 0;
    return Math.max(config.dailyLimit - spent, 0);
  }

  /**
   * Get historical accuracy for models
   */
  private static async getAccuracyHistory(
    salonId: string,
    taskType: string
  ): Promise<Map<string, number>> {
    // This would query actual accuracy data from database
    // For now, return default values
    return new Map([
      ['gpt-3.5-turbo', 0.85],
      ['gpt-4o-mini', 0.92],
      ['gpt-4o', 0.98],
    ]);
  }

  /**
   * Track model usage and accuracy
   */
  static async trackUsage(
    salonId: string,
    model: string,
    taskType: string,
    tokens: number,
    cost: number,
    success: boolean,
    latencyMs: number
  ): Promise<void> {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL')!,
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    );

    const today = new Date().toISOString().split('T')[0];

    // Update metrics
    await supabase.from('ai_metrics_simple').upsert(
      {
        salon_id: salonId,
        metric_date: today,
        total_requests: 1,
        total_cost: cost,
      },
      {
        onConflict: 'salon_id,metric_date',
        ignoreDuplicates: false,
      }
    );

    // Update model spend tracking
    const modelConfig = this.models.get(model);
    if (modelConfig) {
      modelConfig.currentSpend += cost;
    }
  }

  /**
   * Get budget status and recommendations
   */
  static async getBudgetStatus(config: BudgetConfig): Promise<{
    remaining: number;
    percentUsed: number;
    recommendation: string;
    shouldAlert: boolean;
  }> {
    const remaining = await this.getBudgetRemaining(config);
    const percentUsed = ((config.dailyLimit - remaining) / config.dailyLimit) * 100;

    let recommendation = 'Budget healthy - all models available';
    let shouldAlert = false;

    if (percentUsed > config.alertThreshold) {
      shouldAlert = true;
      recommendation = 'Budget warning - consider using economical models';
    }

    if (percentUsed > 90) {
      recommendation = 'Critical budget - only essential requests';
    }

    if (remaining <= 0) {
      recommendation = 'Budget exhausted - requests blocked';
    }

    return {
      remaining,
      percentUsed,
      recommendation,
      shouldAlert,
    };
  }
}
