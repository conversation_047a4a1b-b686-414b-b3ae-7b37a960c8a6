/**
 * Formula Explainer System for Salonier Assistant
 * Generates clear, educational explanations for every formula decision
 */

import { validateColorProcess, getColorimetryInstructions, ProcessType } from './colorimetry-rules.ts';
import type { ProcessValidation } from './colorimetry-rules.ts';

// Types for formula explanation
export interface FormulaExplanation {
  confidenceScore: number;
  overallStrategy: string;
  levelExplanation: string;
  developerExplanation: string;
  productChoiceExplanation: string;
  timingExplanation: string;
  processSteps: ProcessStepExplanation[];
  riskFactors: RiskFactor[];
  successTips: string[];
  colorimetryReasoning: string;
}

export interface ProcessStepExplanation {
  step: number;
  action: string;
  reasoning: string;
  timing: string;
  warningLevel: 'low' | 'medium' | 'high';
  icon: string;
}

export interface RiskFactor {
  factor: string;
  level: 'low' | 'medium' | 'high';
  explanation: string;
  mitigation: string;
  icon: string;
}

export interface FormulaAnalysis {
  diagnosis: any;
  desiredResult: any;
  formula: any;
  selectedProducts: any[];
  processValidation: ProcessValidation;
  brand: string;
  line: string;
  technique: string;
}

/**
 * Formula Explainer Class
 * Generates comprehensive explanations for hair coloring formulas
 */
export class FormulaExplainer {
  private lang: 'es' | 'en';

  constructor(language: 'es' | 'en' = 'es') {
    this.lang = language;
  }

  /**
   * Generate complete explanation for a formula
   */
  generateExplanation(analysis: FormulaAnalysis): FormulaExplanation {
    const confidenceScore = this.calculateConfidenceScore(analysis);
    
    return {
      confidenceScore,
      overallStrategy: this.explainOverallStrategy(analysis),
      levelExplanation: this.explainLevelChoice(analysis),
      developerExplanation: this.explainDeveloperVolume(analysis),
      productChoiceExplanation: this.explainProductChoice(analysis),
      timingExplanation: this.explainTiming(analysis),
      processSteps: this.generateProcessSteps(analysis),
      riskFactors: this.identifyRiskFactors(analysis),
      successTips: this.generateSuccessTips(analysis),
      colorimetryReasoning: this.explainColorimetryPrinciples(analysis),
    };
  }

  /**
   * Calculate confidence score based on multiple factors
   */
  private calculateConfidenceScore(analysis: FormulaAnalysis): number {
    let score = 100;
    const { diagnosis, desiredResult, processValidation } = analysis;

    // Reduce confidence for unclear diagnosis
    if (diagnosis.overallConfidence < 80) {
      score -= (80 - diagnosis.overallConfidence) * 0.5;
    }

    // Reduce confidence for complex processes
    if (processValidation.requiredProcesses.length > 2) {
      score -= 10;
    }

    // Reduce confidence for high-risk processes
    if (processValidation.warnings.length > 0) {
      score -= processValidation.warnings.length * 5;
    }

    // Reduce confidence for damaged hair
    if (diagnosis.zoneAnalysis?.roots?.damage === 'Severo' || 
        diagnosis.zoneAnalysis?.roots?.damage === 'Severe') {
      score -= 15;
    }

    // Reduce confidence for extreme color changes
    const levelDifference = Math.abs(
      (desiredResult.detectedLevel || 0) - (diagnosis.averageDepthLevel || 0)
    );
    if (levelDifference > 4) {
      score -= 10;
    }

    // Reduce confidence for multiple sessions needed
    if (processValidation.estimatedSessions > 1) {
      score -= 10;
    }

    return Math.max(Math.round(score), 60); // Minimum 60% confidence
  }

  /**
   * Explain the overall strategy behind the formula
   */
  private explainOverallStrategy(analysis: FormulaAnalysis): string {
    const { diagnosis, desiredResult, processValidation } = analysis;
    const currentLevel = diagnosis.averageDepthLevel || 0;
    const desiredLevel = desiredResult.detectedLevel || currentLevel;
    const levelDifference = desiredLevel - currentLevel;

    if (this.lang === 'es') {
      if (levelDifference > 0) {
        return `🎯 **Estrategia de Aclarado**: Elevamos el cabello desde nivel ${currentLevel} a nivel ${desiredLevel}. ${
          processValidation.requiredProcesses.includes(ProcessType.BLEACHING) 
            ? 'Usaremos decoloración para un aclarado seguro y controlado.' 
            : 'Podemos lograrlo directamente con tinte, respetando los límites del cabello.'
        }`;
      } else if (levelDifference < 0) {
        return `🎯 **Estrategia de Oscurecimiento**: Bajamos del nivel ${currentLevel} al ${desiredLevel}. ${
          Math.abs(levelDifference) >= 3 
            ? 'Incluimos pre-pigmentación para evitar tonos verdosos y asegurar un resultado uniforme.' 
            : 'Proceso directo de depósito de color.'
        }`;
      } else {
        return `🎯 **Estrategia de Matización**: Mantenemos el nivel ${currentLevel} pero cambiamos el tono. Focus en neutralizar reflejos no deseados y depositar el tono objetivo.`;
      }
    } else {
      if (levelDifference > 0) {
        return `🎯 **Lightening Strategy**: Lifting hair from level ${currentLevel} to level ${desiredLevel}. ${
          processValidation.requiredProcesses.includes(ProcessType.BLEACHING) 
            ? 'We\'ll use bleaching for safe and controlled lightening.' 
            : 'We can achieve this directly with color, respecting hair limitations.'
        }`;
      } else if (levelDifference < 0) {
        return `🎯 **Darkening Strategy**: Going from level ${currentLevel} to ${desiredLevel}. ${
          Math.abs(levelDifference) >= 3 
            ? 'Including pre-pigmentation to avoid green tones and ensure uniform results.' 
            : 'Direct color deposit process.'
        }`;
      } else {
        return `🎯 **Toning Strategy**: Maintaining level ${currentLevel} while changing tone. Focus on neutralizing unwanted reflects and depositing target tone.`;
      }
    }
  }

  /**
   * Explain level choice reasoning
   */
  private explainLevelChoice(analysis: FormulaAnalysis): string {
    const { diagnosis, desiredResult, formula } = analysis;
    const currentLevel = diagnosis.averageDepthLevel || 0;
    const desiredLevel = desiredResult.detectedLevel || currentLevel;
    
    if (this.lang === 'es') {
      return `📊 **Nivel Elegido**: ${formula.level || desiredLevel}
      
• **Diagnóstico actual**: Nivel ${currentLevel} ${this.getLevelDescription(currentLevel)}
• **Objetivo**: Nivel ${desiredLevel} ${this.getLevelDescription(desiredLevel)}
• **Decisión**: ${this.explainLevelDecision(currentLevel, desiredLevel, analysis)}`;
    } else {
      return `📊 **Chosen Level**: ${formula.level || desiredLevel}
      
• **Current diagnosis**: Level ${currentLevel} ${this.getLevelDescription(currentLevel)}
• **Target**: Level ${desiredLevel} ${this.getLevelDescription(desiredLevel)}
• **Decision**: ${this.explainLevelDecision(currentLevel, desiredLevel, analysis)}`;
    }
  }

  /**
   * Explain developer volume choice
   */
  private explainDeveloperVolume(analysis: FormulaAnalysis): string {
    const { processValidation, formula } = analysis;
    const volume = processValidation.recommendedDeveloperVolume;

    if (this.lang === 'es') {
      return `⚗️ **Oxigenada ${volume} Vol**: ${this.getDeveloperExplanation(volume)}
      
• **Función**: ${this.getDeveloperFunction(volume)}
• **Seguridad**: Volumen óptimo para este proceso sin daño excesivo
• **Resultado**: ${this.getDeveloperResult(volume)}`;
    } else {
      return `⚗️ **${volume} Vol Developer**: ${this.getDeveloperExplanation(volume)}
      
• **Function**: ${this.getDeveloperFunction(volume)}
• **Safety**: Optimal volume for this process without excessive damage
• **Result**: ${this.getDeveloperResult(volume)}`;
    }
  }

  /**
   * Explain product choice reasoning
   */
  private explainProductChoice(analysis: FormulaAnalysis): string {
    const { selectedProducts, brand, line, diagnosis } = analysis;

    if (this.lang === 'es') {
      return `🧪 **Productos ${brand} ${line}**:
      
• **Calidad**: Línea profesional con consistencia de resultados
• **Compatibilidad**: Formulados para trabajar juntos sin reacciones
• **Condición del cabello**: ${this.getProductChoiceReasoning(diagnosis)}
• **Cobertura**: ${this.getCoverageExplanation(diagnosis)}`;
    } else {
      return `🧪 **${brand} ${line} Products**:
      
• **Quality**: Professional line with consistent results
• **Compatibility**: Formulated to work together without reactions
• **Hair condition**: ${this.getProductChoiceReasoning(diagnosis)}
• **Coverage**: ${this.getCoverageExplanation(diagnosis)}`;
    }
  }

  /**
   * Explain timing decisions
   */
  private explainTiming(analysis: FormulaAnalysis): string {
    const { diagnosis, formula, processValidation } = analysis;
    
    if (this.lang === 'es') {
      return `⏱️ **Tiempo de Procesamiento**: ${formula.processingTime || '35-45 minutos'}
      
• **Base**: ${this.getTimingBase(diagnosis)}
• **Ajustes**: ${this.getTimingAdjustments(diagnosis, processValidation)}
• **Monitoreo**: Revisa cada 10 minutos para evaluar progreso`;
    } else {
      return `⏱️ **Processing Time**: ${formula.processingTime || '35-45 minutes'}
      
• **Base**: ${this.getTimingBase(diagnosis)}
• **Adjustments**: ${this.getTimingAdjustments(diagnosis, processValidation)}
• **Monitoring**: Check every 10 minutes to evaluate progress`;
    }
  }

  /**
   * Generate step-by-step process explanations
   */
  private generateProcessSteps(analysis: FormulaAnalysis): ProcessStepExplanation[] {
    const { processValidation } = analysis;
    const steps: ProcessStepExplanation[] = [];

    processValidation.requiredProcesses.forEach((process, index) => {
      switch (process) {
        case ProcessType.COLOR_REMOVAL:
          steps.push({
            step: index + 1,
            action: this.lang === 'es' ? 'Decapado de Color' : 'Color Removal',
            reasoning: this.lang === 'es' 
              ? 'Eliminar pigmentos artificiales que impiden el aclarado deseado'
              : 'Remove artificial pigments that prevent desired lightening',
            timing: this.lang === 'es' ? '20-30 minutos' : '20-30 minutes',
            warningLevel: 'high',
            icon: '🧽'
          });
          break;

        case ProcessType.BLEACHING:
          steps.push({
            step: index + 1,
            action: this.lang === 'es' ? 'Decoloración' : 'Bleaching',
            reasoning: this.lang === 'es'
              ? 'Aclarar el cabello para alcanzar la base necesaria para el color deseado'
              : 'Lighten hair to achieve necessary base for desired color',
            timing: this.lang === 'es' ? '30-50 minutos' : '30-50 minutes',
            warningLevel: 'high',
            icon: '⚡'
          });
          break;

        case ProcessType.PRE_PIGMENTATION:
          steps.push({
            step: index + 1,
            action: this.lang === 'es' ? 'Pre-pigmentación' : 'Pre-pigmentation',
            reasoning: this.lang === 'es'
              ? 'Rellenar la estructura del cabello con pigmentos cálidos para evitar tonos verdosos'
              : 'Fill hair structure with warm pigments to avoid green tones',
            timing: this.lang === 'es' ? '15-20 minutos' : '15-20 minutes',
            warningLevel: 'medium',
            icon: '🎨'
          });
          break;

        case ProcessType.DIRECT_COLOR:
          steps.push({
            step: index + 1,
            action: this.lang === 'es' ? 'Aplicación de Color' : 'Color Application',
            reasoning: this.lang === 'es'
              ? 'Depositar el tono final y conseguir el resultado deseado'
              : 'Deposit final tone and achieve desired result',
            timing: this.lang === 'es' ? '35-45 minutos' : '35-45 minutes',
            warningLevel: 'low',
            icon: '🎯'
          });
          break;

        case ProcessType.TONING:
          steps.push({
            step: index + 1,
            action: this.lang === 'es' ? 'Matización' : 'Toning',
            reasoning: this.lang === 'es'
              ? 'Neutralizar reflejos no deseados y perfeccionar el tono final'
              : 'Neutralize unwanted reflects and perfect final tone',
            timing: this.lang === 'es' ? '10-20 minutos' : '10-20 minutes',
            warningLevel: 'low',
            icon: '✨'
          });
          break;
      }
    });

    return steps;
  }

  /**
   * Identify risk factors and explanations
   */
  private identifyRiskFactors(analysis: FormulaAnalysis): RiskFactor[] {
    const { diagnosis, processValidation } = analysis;
    const risks: RiskFactor[] = [];

    // Hair damage risk
    const damageLevel = diagnosis.zoneAnalysis?.roots?.damage || 'Ninguno';
    if (damageLevel === 'Moderado' || damageLevel === 'Moderate') {
      risks.push({
        factor: this.lang === 'es' ? 'Cabello Previamente Dañado' : 'Previously Damaged Hair',
        level: 'medium',
        explanation: this.lang === 'es'
          ? 'El cabello muestra signos de daño moderado que puede afectar el resultado'
          : 'Hair shows signs of moderate damage that may affect the result',
        mitigation: this.lang === 'es'
          ? 'Usar tratamientos de acondicionamiento antes y después del proceso'
          : 'Use conditioning treatments before and after the process',
        icon: '⚠️'
      });
    }

    if (damageLevel === 'Severo' || damageLevel === 'Severe') {
      risks.push({
        factor: this.lang === 'es' ? 'Cabello Severamente Dañado' : 'Severely Damaged Hair',
        level: 'high',
        explanation: this.lang === 'es'
          ? 'Alto riesgo de rotura o resultado desigual debido al daño severo'
          : 'High risk of breakage or uneven result due to severe damage',
        mitigation: this.lang === 'es'
          ? 'Considerar posponer el servicio y hacer tratamientos reparadores primero'
          : 'Consider postponing service and doing repair treatments first',
        icon: '🚨'
      });
    }

    // Multiple session risk
    if (processValidation.estimatedSessions > 1) {
      risks.push({
        factor: this.lang === 'es' ? 'Proceso de Múltiples Sesiones' : 'Multiple Session Process',
        level: 'medium',
        explanation: this.lang === 'es'
          ? 'El cambio deseado requiere más de una cita para completarse de forma segura'
          : 'Desired change requires more than one appointment to complete safely',
        mitigation: this.lang === 'es'
          ? 'Planificar sesiones con 2-4 semanas de diferencia y tratamientos intermedios'
          : 'Plan sessions 2-4 weeks apart with intermediate treatments',
        icon: '📅'
      });
    }

    // High porosity risk
    const porosity = diagnosis.zoneAnalysis?.roots?.porosity || 'Media';
    if (porosity === 'Alta' || porosity === 'High') {
      risks.push({
        factor: this.lang === 'es' ? 'Porosidad Alta' : 'High Porosity',
        level: 'medium',
        explanation: this.lang === 'es'
          ? 'El cabello absorbe rápidamente pero también pierde color fácilmente'
          : 'Hair absorbs quickly but also loses color easily',
        mitigation: this.lang === 'es'
          ? 'Usar rellenos de porosidad y reducir tiempos de procesamiento'
          : 'Use porosity fillers and reduce processing times',
        icon: '🕳️'
      });
    }

    return risks;
  }

  /**
   * Generate success tips
   */
  private generateSuccessTips(analysis: FormulaAnalysis): string[] {
    const { diagnosis, processValidation } = analysis;
    const tips: string[] = [];

    if (this.lang === 'es') {
      tips.push('🎯 Realiza siempre una prueba de mechón 48 horas antes');
      tips.push('⏰ No dejes el producto más tiempo del recomendado');
      tips.push('🌡️ Mantén temperatura ambiente constante (20-25°C)');
      
      if (diagnosis.zoneAnalysis?.roots?.grayPercentage > 30) {
        tips.push('👩‍🦳 Para buena cobertura de canas, aplica primero en raíces');
      }

      if (processValidation.requiredProcesses.includes(ProcessType.BLEACHING)) {
        tips.push('⚡ Monitorea constantemente durante la decoloración');
        tips.push('💧 Aplica tratamiento reparador inmediatamente después');
      }

      tips.push('📸 Documenta el resultado para futuras referencias');
    } else {
      tips.push('🎯 Always perform strand test 48 hours before');
      tips.push('⏰ Don\'t leave product longer than recommended');
      tips.push('🌡️ Maintain constant room temperature (68-77°F)');
      
      if (diagnosis.zoneAnalysis?.roots?.grayPercentage > 30) {
        tips.push('👩‍🦳 For good gray coverage, apply to roots first');
      }

      if (processValidation.requiredProcesses.includes(ProcessType.BLEACHING)) {
        tips.push('⚡ Monitor constantly during bleaching');
        tips.push('💧 Apply repair treatment immediately after');
      }

      tips.push('📸 Document results for future reference');
    }

    return tips;
  }

  /**
   * Explain colorimetry principles used
   */
  private explainColorimetryPrinciples(analysis: FormulaAnalysis): string {
    const { processValidation } = analysis;
    return getColorimetryInstructions(processValidation, this.lang);
  }

  // Helper methods
  private getLevelDescription(level: number): string {
    if (this.lang === 'es') {
      if (level <= 3) return '(Negro/Castaño Oscuro)';
      if (level <= 5) return '(Castaño Medio)';
      if (level <= 7) return '(Castaño Claro/Rubio Oscuro)';
      if (level <= 9) return '(Rubio Medio/Claro)';
      return '(Rubio Muy Claro)';
    } else {
      if (level <= 3) return '(Black/Dark Brown)';
      if (level <= 5) return '(Medium Brown)';
      if (level <= 7) return '(Light Brown/Dark Blonde)';
      if (level <= 9) return '(Medium/Light Blonde)';
      return '(Very Light Blonde)';
    }
  }

  private explainLevelDecision(current: number, desired: number, analysis: FormulaAnalysis): string {
    const difference = desired - current;
    
    if (this.lang === 'es') {
      if (difference > 0) {
        return `Aclaramos ${difference} ${difference === 1 ? 'nivel' : 'niveles'} respetando los límites del cabello`;
      } else if (difference < 0) {
        return `Oscurecemos ${Math.abs(difference)} ${Math.abs(difference) === 1 ? 'nivel' : 'niveles'} con deposición controlada`;
      } else {
        return 'Mantenemos el nivel base y trabajamos solo el tono';
      }
    } else {
      if (difference > 0) {
        return `Lightening ${difference} ${difference === 1 ? 'level' : 'levels'} respecting hair limits`;
      } else if (difference < 0) {
        return `Darkening ${Math.abs(difference)} ${Math.abs(difference) === 1 ? 'level' : 'levels'} with controlled deposit`;
      } else {
        return 'Maintaining base level and working tone only';
      }
    }
  }

  private getDeveloperExplanation(volume: number): string {
    if (this.lang === 'es') {
      switch (volume) {
        case 10: return 'Deposición suave, ideal para tonos más oscuros';
        case 20: return 'Estándar para cobertura de canas y aclarado moderado';
        case 30: return 'Aclarado medio, balance entre efectividad y cuidado';
        case 40: return 'Máximo aclarado, requiere monitoreo constante';
        default: return `Volumen específico para este proceso (${volume}vol)`;
      }
    } else {
      switch (volume) {
        case 10: return 'Gentle deposit, ideal for darker tones';
        case 20: return 'Standard for gray coverage and moderate lifting';
        case 30: return 'Medium lift, balance between effectiveness and care';
        case 40: return 'Maximum lift, requires constant monitoring';
        default: return `Specific volume for this process (${volume}vol)`;
      }
    }
  }

  private getDeveloperFunction(volume: number): string {
    if (this.lang === 'es') {
      return volume <= 10 ? 'Solo deposición de color' : 
             volume <= 20 ? 'Deposición + aclarado ligero' :
             volume <= 30 ? 'Aclarado moderado' : 'Aclarado intenso';
    } else {
      return volume <= 10 ? 'Color deposit only' : 
             volume <= 20 ? 'Deposit + light lifting' :
             volume <= 30 ? 'Moderate lifting' : 'Intensive lifting';
    }
  }

  private getDeveloperResult(volume: number): string {
    if (this.lang === 'es') {
      return volume <= 10 ? 'Mayor duración del color' : 
             volume <= 20 ? 'Resultado balanceado' :
             volume <= 30 ? 'Aclarado efectivo' : 'Cambio dramático posible';
    } else {
      return volume <= 10 ? 'Longer color duration' : 
             volume <= 20 ? 'Balanced result' :
             volume <= 30 ? 'Effective lightening' : 'Dramatic change possible';
    }
  }

  private getProductChoiceReasoning(diagnosis: any): string {
    const damage = diagnosis.zoneAnalysis?.roots?.damage || 'Ninguno';
    
    if (this.lang === 'es') {
      return damage === 'Severo' || damage === 'Severe' ? 
        'Formulación suave para cabello comprometido' :
        damage === 'Moderado' || damage === 'Moderate' ?
        'Balance entre efectividad y cuidado' :
        'Formulación estándar para cabello sano';
    } else {
      return damage === 'Severe' ? 
        'Gentle formulation for compromised hair' :
        damage === 'Moderate' ?
        'Balance between effectiveness and care' :
        'Standard formulation for healthy hair';
    }
  }

  private getCoverageExplanation(diagnosis: any): string {
    const grayPercentage = diagnosis.zoneAnalysis?.roots?.grayPercentage || 0;
    
    if (this.lang === 'es') {
      return grayPercentage > 50 ? 'Formulación específica para alta cobertura de canas' :
             grayPercentage > 20 ? 'Cobertura media de canas' :
             'Sin necesidad especial de cobertura de canas';
    } else {
      return grayPercentage > 50 ? 'Specific formulation for high gray coverage' :
             grayPercentage > 20 ? 'Medium gray coverage' :
             'No special gray coverage needed';
    }
  }

  private getTimingBase(diagnosis: any): string {
    const porosity = diagnosis.zoneAnalysis?.roots?.porosity || 'Media';
    
    if (this.lang === 'es') {
      return porosity === 'Alta' || porosity === 'High' ? 'Cabello poroso procesa más rápido' :
             porosity === 'Baja' || porosity === 'Low' ? 'Cabello resistente necesita más tiempo' :
             'Tiempo estándar para cabello normal';
    } else {
      return porosity === 'High' ? 'Porous hair processes faster' :
             porosity === 'Low' ? 'Resistant hair needs more time' :
             'Standard time for normal hair';
    }
  }

  private getTimingAdjustments(diagnosis: any, validation: ProcessValidation): string {
    const adjustments: string[] = [];
    
    if (diagnosis.zoneAnalysis?.roots?.resistance === 'Fuerte' || 
        diagnosis.zoneAnalysis?.roots?.resistance === 'Strong') {
      adjustments.push(this.lang === 'es' ? '+5-10 min (cabello resistente)' : '+5-10 min (resistant hair)');
    }

    if (validation.requiredProcesses.includes(ProcessType.BLEACHING)) {
      adjustments.push(this.lang === 'es' ? 'Monitoreo cada 10 min' : 'Check every 10 min');
    }

    return adjustments.join(', ') || (this.lang === 'es' ? 'Sin ajustes necesarios' : 'No adjustments needed');
  }
}

/**
 * Quick explanation generator for simple cases
 */
export function generateQuickExplanation(
  currentLevel: number,
  desiredLevel: number,
  developerVolume: number,
  lang: 'es' | 'en' = 'es'
): string {
  const levelDiff = desiredLevel - currentLevel;
  
  if (lang === 'es') {
    const action = levelDiff > 0 ? 'aclaramos' : levelDiff < 0 ? 'oscurecemos' : 'matizamos';
    return `💡 **Resumen**: ${action} ${Math.abs(levelDiff)} ${Math.abs(levelDiff) === 1 ? 'nivel' : 'niveles'} usando ${developerVolume}vol de oxidante para un resultado controlado y predecible.`;
  } else {
    const action = levelDiff > 0 ? 'lighten' : levelDiff < 0 ? 'darken' : 'tone';
    return `💡 **Summary**: We ${action} ${Math.abs(levelDiff)} ${Math.abs(levelDiff) === 1 ? 'level' : 'levels'} using ${developerVolume}vol developer for a controlled and predictable result.`;
  }
}