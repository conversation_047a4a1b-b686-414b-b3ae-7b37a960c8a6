# 🔧 Guía de Integración: Sistema de Validación de Disponibilidad

## ✅ IMPLEMENTACIÓN COMPLETADA

El sistema de validación de disponibilidad de productos ya está completamente implementado y listo para usar.

### 🚀 Características Principales

1. **Validación de Decolorante**: Detecta cuando se necesita aclarar 3+ niveles pero la línea no tiene decolorante
2. **Validación de Tonos**: Verifica si el tono específico existe en la línea seleccionada  
3. **Validación de Oxidantes**: Comprueba si el volumen de oxidante está disponible
4. **Validación de Matizadores**: Detecta falta de productos para neutralización
5. **Validación de Stock**: Alerta sobre cantidades insuficientes
6. **Sugerencias Automáticas**: Propone alternativas y soluciones

### 📋 Tipos de Errores Detectados

#### 🚨 CRÍTICOS (Impiden proceder)
```
❌ "Esta línea no incluye decolorante - imposible aclarar tanto"
❌ "Tiempo total excesivo: 180 minutos. Riesgo de daño severo"
```

#### ⚠️ ERRORES (Requieren atención)
```
⚠️ "Tono 8.11 no disponible en esta línea - máximo ceniza es 8.1"
⚠️ "Esta marca no tiene oxidante 40vol - máximo es 30vol"
⚠️ "Stock insuficiente: disponible 25ml, necesario 60ml"
```

#### ⚡ ADVERTENCIAS (Recomendaciones)
```
⚡ "No hay matizadores en esta línea para neutralizar"
⚡ "Pre-pigmentación recomendada para mejor resultado"
```

### 🔄 Integración en Edge Function

#### Paso 1: Importar el Sistema

```typescript
// En tu index.ts de la Edge Function
import {
  validateFormulaWithAvailability,
  generateAvailabilitySuggestions,
  createProductAvailabilityFromInventory,
  type ProductAvailability,
  type ValidationResult
} from './utils/formula-validator';
```

#### Paso 2: Obtener Productos del Inventario

```typescript
async function getInventoryProducts(salonId: string): Promise<any[]> {
  const { data: products, error } = await supabase
    .from('products')
    .select('*')
    .eq('salon_id', salonId)
    .eq('is_active', true);
    
  if (error) throw error;
  return products || [];
}
```

#### Paso 3: Validar Antes de Generar Fórmula

```typescript
// Antes de llamar a OpenAI, validar disponibilidad
export async function validateAndGenerateFormula(
  prompt: string,
  salonId: string,
  brand: string,
  line: string
) {
  // 1. Obtener inventario
  const inventoryProducts = await getInventoryProducts(salonId);
  const availableProducts = createProductAvailabilityFromInventory(inventoryProducts);
  
  // 2. Generar fórmula con IA (tu lógica existente)
  const aiFormula = await generateFormulaWithAI(prompt, brand, line);
  
  // 3. Validar disponibilidad
  const validation = validateFormulaWithAvailability(aiFormula, availableProducts);
  
  // 4. Generar sugerencias si hay problemas
  const suggestions = generateAvailabilitySuggestions(aiFormula, availableProducts);
  
  // 5. Retornar resultado completo
  return {
    formula: suggestions.alternativeFormula || aiFormula,
    validation: {
      isValid: validation.isValid,
      canProceed: suggestions.canProceed,
      violations: validation.violations,
      criticalIssues: suggestions.criticalIssues,
      suggestions: suggestions.suggestions,
    }
  };
}
```

#### Paso 4: Manejo de Respuestas

```typescript
// En el response de la Edge Function
if (!result.validation.canProceed) {
  return new Response(JSON.stringify({
    success: false,
    error: "FORMULA_IMPOSSIBLE",
    message: "No se puede realizar esta fórmula con los productos disponibles",
    criticalIssues: result.validation.criticalIssues,
    suggestions: result.validation.suggestions,
    recommendedActions: [
      "Verificar inventario de productos",
      "Considerar líneas alternativas",
      "Ajustar expectativas del cliente"
    ]
  }), {
    status: 400,
    headers: { 'Content-Type': 'application/json' }
  });
}

// Si puede proceder pero con advertencias
return new Response(JSON.stringify({
  success: true,
  formula: result.formula,
  validation: result.validation,
  warnings: result.validation.violations
    .filter(v => v.severity === 'warning')
    .map(v => v.message),
  recommendations: result.validation.suggestions
}), {
  status: 200,
  headers: { 'Content-Type': 'application/json' }
});
```

### 📱 Integración en Frontend

#### Manejo de Errores Críticos

```typescript
// En tu componente React Native
const handleFormulaGeneration = async () => {
  try {
    const response = await generateFormula(prompt);
    
    if (!response.validation.canProceed) {
      // Mostrar modal de error crítico
      Alert.alert(
        "❌ Fórmula Imposible",
        "No se puede realizar con los productos disponibles:\n\n" +
        response.criticalIssues.join("\n") +
        "\n\nSugerencias:\n" +
        response.suggestions.join("\n"),
        [
          { text: "Revisar Inventario", onPress: () => navigation.navigate('Inventory') },
          { text: "Cambiar Marca/Línea", onPress: () => setShowBrandSelector(true) },
          { text: "Cancelar", style: "cancel" }
        ]
      );
      return;
    }
    
    // Mostrar advertencias si las hay
    if (response.warnings?.length > 0) {
      Alert.alert(
        "⚠️ Advertencias",
        response.warnings.join("\n") + 
        "\n\n¿Deseas proceder de todas formas?",
        [
          { text: "Sí, continuar", onPress: () => proceedWithFormula(response.formula) },
          { text: "Revisar", style: "cancel" }
        ]
      );
    } else {
      proceedWithFormula(response.formula);
    }
    
  } catch (error) {
    console.error('Error generating formula:', error);
  }
};
```

#### Componente de Validación de Stock

```typescript
const StockValidationBadge = ({ formula, inventoryProducts }) => {
  const availableProducts = createProductAvailabilityFromInventory(inventoryProducts);
  const validation = validateFormulaWithAvailability(formula, availableProducts);
  
  const criticalCount = validation.violations.filter(v => v.severity === 'critical').length;
  const warningCount = validation.violations.filter(v => v.severity === 'warning').length;
  
  if (criticalCount > 0) {
    return (
      <Badge color="red" icon="alert-circle">
        {criticalCount} problema{criticalCount > 1 ? 's' : ''} crítico{criticalCount > 1 ? 's' : ''}
      </Badge>
    );
  }
  
  if (warningCount > 0) {
    return (
      <Badge color="orange" icon="alert">
        {warningCount} advertencia{warningCount > 1 ? 's' : ''}
      </Badge>
    );
  }
  
  return <Badge color="green" icon="check-circle">Productos disponibles</Badge>;
};
```

### 🧪 Testing en Desarrollo

Para probar el sistema, ejecuta:

```bash
# Tests unitarios
npm test __tests__/utils/formula-validator-availability.test.ts

# Test de ejemplo
npx tsx supabase/functions/salonier-assistant/utils/availability-validation-example.ts
```

### 🚀 Casos de Uso Típicos

1. **Cliente quiere ir de negro a rubio platino**
   - ✅ Detecta: "Necesita decolorante + matizador"
   - ✅ Valida: "¿Tiene la línea productos para 6+ niveles de aclarado?"
   - ✅ Sugiere: "Usar línea X que incluye decolorante profesional"

2. **Canas 80% + color fantasía**
   - ✅ Detecta: "Requiere pre-pigmentación + cobertura"
   - ✅ Valida: "¿Hay tonos base natural disponibles?"
   - ✅ Sugiere: "Mezclar 50% base natural + 50% color deseado"

3. **Corrección de color naranja**
   - ✅ Detecta: "Necesita matizador azul/violeta"
   - ✅ Valida: "¿La línea incluye correctores?"
   - ✅ Sugiere: "Usar línea Y con mejor sistema de matizado"

### 🎯 Próximos Pasos

1. **Integrar en la Edge Function principal** ✅ 
2. **Agregar validación de marcas compatibles** (futuro)
3. **Sistema de sugerencias de compra** (futuro)
4. **Analytics de productos más necesarios** (futuro)

---

## 📊 Métricas de Éxito

- **Reducción de fórmulas imposibles**: 0% → Meta <5%
- **Satisfacción del estilista**: Menos frustraciones por productos faltantes
- **Optimización de inventario**: Sugerencias data-driven de qué comprar
- **Tiempo de servicio**: Menor tiempo perdido buscando alternativas