/**
 * EXAMPLE: Product Availability Validation System
 * 
 * Este archivo muestra cómo implementar y usar el sistema de validación
 * de disponibilidad de productos para evitar fórmulas imposibles.
 */

import { 
  validateFormulaWithAvailability,
  generateAvailabilitySuggestions,
  createProductAvailabilityFromInventory,
  checkProductsAvailability,
  type Formula,
  type ProductAvailability 
} from './formula-validator';

// ========================================
// EJEMPLO 1: Datos de Inventario Simulado
// ========================================

const sampleInventoryProducts = [
  // Wella Koleston Perfect - Línea completa
  {
    id: '1',
    brand: 'Wella',
    line: 'Koleston Perfect',
    category: 'tinte',
    shade: '7.1',
    currentStock: 85,
    isActive: true,
  },
  {
    id: '2', 
    brand: 'Wella',
    line: 'Koleston Perfect', 
    category: 'tinte',
    shade: '8.0',
    currentStock: 92,
    isActive: true,
  },
  {
    id: '3',
    brand: 'Wella',
    line: 'Koleston Perfect',
    category: 'oxidante',
    shade: '20vol',
    currentStock: 250,
    isActive: true,
  },
  {
    id: '4',
    brand: 'Wella', 
    line: 'Koleston Perfect',
    category: 'oxidante',
    shade: '30vol',
    currentStock: 180,
    isActive: true,
  },
  // ❌ PROBLEMA: No tiene decolorante en esta línea
  
  // Wella Blondor - Solo decolorante
  {
    id: '5',
    brand: 'Wella',
    line: 'Blondor',
    category: 'decolorante',
    shade: 'Polvo',
    currentStock: 150,
    isActive: true,
  },
  
  // L'Oréal Majirel - Línea diferente
  {
    id: '6',
    brand: "L'Oréal",
    line: 'Majirel',
    category: 'tinte',
    shade: '8.11',
    currentStock: 75,
    isActive: true,
  },
  {
    id: '7',
    brand: "L'Oréal",
    line: 'Majirel',
    category: 'decolorante',
    shade: 'Platine',
    currentStock: 120,
    isActive: true,
  },
];

// ========================================
// EJEMPLO 2: Fórmula Problemática
// ========================================

const problematicFormula: Formula = {
  brand: 'Wella',
  line: 'Koleston Perfect',
  currentLevel: 4,
  desiredLevel: 9, // ❌ Requiere 5 niveles de aclarado
  currentState: 'colored',
  grayPercentage: 20,
  totalTime: 120,
  steps: [
    {
      id: 'bleach-step',
      title: 'Decoloración',
      type: 'BLEACHING' as any,
      processingTime: 45,
      instructions: 'Aplicar decolorante en medios y puntas',
      ingredients: [
        {
          product: 'Decolorante Koleston',
          amount: 50,
          unit: 'ml',
          type: 'bleach', // ❌ No existe en la línea Koleston
        },
        {
          product: 'Oxidante 40vol',
          amount: 75,
          unit: 'ml',
          type: 'developer',
          volume: 40, // ❌ No disponible, máximo es 30vol
        },
      ],
    },
    {
      id: 'color-step',
      title: 'Coloración',
      type: 'DIRECT_COLOR' as any,
      processingTime: 30,
      instructions: 'Aplicar color en toda la cabeza',
      ingredients: [
        {
          product: 'Koleston 9.11',
          shade: '9.11',
          amount: 60,
          unit: 'ml',
          type: 'color', // ❌ Tono no disponible en inventario
        },
        {
          product: 'Oxidante 20vol',
          amount: 60,
          unit: 'ml',
          type: 'developer',
          volume: 20,
        },
      ],
    },
  ],
};

// ========================================
// EJEMPLO 3: Validación y Detección de Problemas
// ========================================

export function demonstrateAvailabilityValidation() {
  console.log('🔍 DEMOSTRACIÓN: Sistema de Validación de Disponibilidad\n');
  
  // 1. Convertir inventario a formato de disponibilidad
  const availableProducts = createProductAvailabilityFromInventory(sampleInventoryProducts);
  
  console.log('📦 Productos disponibles:');
  availableProducts.forEach(product => {
    console.log(`  • ${product.brand} ${product.line} - ${product.type} ${product.shade || ''}`);
  });
  console.log();
  
  // 2. Validar fórmula problemática
  const validation = validateFormulaWithAvailability(problematicFormula, availableProducts);
  
  console.log('❌ PROBLEMAS DETECTADOS:');
  validation.violations
    .filter(v => v.type === 'availability')
    .forEach((violation, index) => {
      const severityIcon = {
        critical: '🚨',
        error: '⚠️', 
        warning: '⚡'
      }[violation.severity];
      
      console.log(`${index + 1}. ${severityIcon} ${violation.message}`);
      if (violation.suggestion) {
        console.log(`   💡 Sugerencia: ${violation.suggestion}`);
      }
      console.log();
    });
  
  // 3. Generar sugerencias automáticas
  const suggestions = generateAvailabilitySuggestions(problematicFormula, availableProducts);
  
  console.log('🔧 ANÁLISIS DE VIABILIDAD:');
  console.log(`¿Puede proceder? ${suggestions.canProceed ? '✅ SÍ' : '❌ NO'}\n`);
  
  if (suggestions.criticalIssues.length > 0) {
    console.log('🚨 Problemas críticos:');
    suggestions.criticalIssues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue}`);
    });
    console.log();
  }
  
  if (suggestions.suggestions.length > 0) {
    console.log('💡 Sugerencias para proceder:');
    suggestions.suggestions.forEach((suggestion, index) => {
      console.log(`${index + 1}. ${suggestion}`);
    });
    console.log();
  }
  
  // 4. Mostrar fórmula corregida si existe
  if (suggestions.alternativeFormula) {
    console.log('🔄 FÓRMULA ALTERNATIVA SUGERIDA:');
    console.log(`Marca: ${suggestions.alternativeFormula.brand}`);
    console.log(`Línea: ${suggestions.alternativeFormula.line}`);
    console.log('Cambios aplicados:');
    suggestions.alternativeFormula.warnings?.forEach(warning => {
      console.log(`  • ${warning}`);
    });
    console.log();
  }
  
  // 5. Verificación específica de productos
  const requiredProducts = [
    { type: 'bleach' },
    { type: 'developer', volume: 40 },
    { type: 'color', shade: '9.11' },
  ];
  
  const productCheck = checkProductsAvailability(requiredProducts, availableProducts);
  
  console.log('🔍 VERIFICACIÓN ESPECÍFICA DE PRODUCTOS:');
  console.log(`Estado: ${productCheck.available ? '✅ Todos disponibles' : '❌ Productos faltantes'}`);
  
  if (productCheck.missing.length > 0) {
    console.log('\n🚫 Productos faltantes:');
    productCheck.missing.forEach(missing => {
      console.log(`  • ${missing}`);
    });
  }
  
  if (productCheck.alternatives.length > 0) {
    console.log('\n🔄 Alternativas disponibles:');
    productCheck.alternatives.forEach(alt => {
      console.log(`  • ${alt}`);
    });
  }
}

// ========================================
// EJEMPLO 4: Integración con la Edge Function
// ========================================

export interface ValidationRequestPayload {
  formula: Formula;
  inventoryProducts: any[];
  includeAlternatives?: boolean;
}

export interface ValidationResponsePayload {
  isValid: boolean;
  canProceed: boolean;
  violations: Array<{
    type: string;
    severity: string;
    message: string;
    suggestion?: string;
  }>;
  criticalIssues: string[];
  suggestions: string[];
  alternativeFormula?: Formula;
  missingProducts: string[];
  alternatives: string[];
}

/**
 * Función principal para integrar en la Edge Function de Salonier
 */
export function validateFormulaAvailability(
  payload: ValidationRequestPayload
): ValidationResponsePayload {
  const { formula, inventoryProducts, includeAlternatives = true } = payload;
  
  // Convertir productos del inventario
  const availableProducts = createProductAvailabilityFromInventory(inventoryProducts);
  
  // Validar fórmula completa
  const validation = validateFormulaWithAvailability(formula, availableProducts);
  
  // Generar sugerencias si se requieren
  const suggestions = includeAlternatives 
    ? generateAvailabilitySuggestions(formula, availableProducts)
    : { canProceed: true, criticalIssues: [], suggestions: [] };
  
  // Extraer productos específicos requeridos
  const requiredProducts = formula.steps.flatMap(step =>
    step.ingredients.map(ing => ({
      type: ing.type,
      shade: ing.shade,
      volume: ing.volume,
    }))
  );
  
  // Verificar disponibilidad específica
  const productCheck = checkProductsAvailability(requiredProducts, availableProducts);
  
  return {
    isValid: validation.isValid,
    canProceed: suggestions.canProceed,
    violations: validation.violations.map(v => ({
      type: v.type,
      severity: v.severity,
      message: v.message,
      suggestion: v.suggestion,
    })),
    criticalIssues: suggestions.criticalIssues,
    suggestions: suggestions.suggestions,
    alternativeFormula: suggestions.alternativeFormula,
    missingProducts: productCheck.missing,
    alternatives: productCheck.alternatives,
  };
}

// ========================================
// EJEMPLO 5: Casos de Uso Comunes
// ========================================

export const commonValidationScenarios = {
  
  /**
   * CASO 1: Aclarado imposible sin decolorante
   */
  noDecolorante: {
    description: "Cliente quiere ir de nivel 4 a 8 pero la línea no tiene decolorante",
    formula: {
      brand: "Wella",
      line: "Koleston Perfect", // No tiene decolorante
      currentLevel: 4,
      desiredLevel: 8,
    },
    expectedIssue: "La línea Koleston Perfect no incluye decolorante",
    solution: "Cambiar a línea Blondor o usar otra marca"
  },
  
  /**
   * CASO 2: Tono específico no disponible
   */
  tonoNoDisponible: {
    description: "Se requiere un tono ceniza muy específico que no está en inventario",
    requiredShade: "8.11",
    availableShades: ["8.0", "8.1", "7.1"],
    expectedIssue: "Tono 8.11 no disponible en esta línea",
    solution: "Usar mezcla de 8.1 + corrector ceniza"
  },
  
  /**
   * CASO 3: Volumen de oxidante no disponible
   */
  oxidanteNoDisponible: {
    description: "Fórmula requiere 40vol pero la marca solo tiene hasta 30vol",
    requiredVolume: 40,
    maxAvailableVolume: 30,
    expectedIssue: "Esta marca no tiene oxidante 40vol",
    solution: "Usar 30vol con tiempo de procesamiento extendido"
  },
  
  /**
   * CASO 4: Stock insuficiente
   */
  stockInsuficiente: {
    description: "Hay el producto pero no cantidad suficiente",
    required: 60, // ml
    available: 25, // ml
    expectedIssue: "Stock insuficiente",
    solution: "Pedir restock o ajustar cantidades"
  }
};

// Ejecutar demostración si se llama directamente
if (import.meta.main) {
  demonstrateAvailabilityValidation();
}