/**
 * Formula Validator
 * Ensures compliance with colorimetry rules and brand-specific guidelines
 * Provides intelligent auto-correction for detected issues
 */

import { validateColorProcess, ProcessType, COLORIMETRY_PRINCIPLES } from './colorimetry-rules';
import { getBrandValidationRules } from './brand-expertise';

export interface FormulaIngredient {
  product: string;
  shade?: string;
  amount: number;
  unit: 'ml' | 'g' | 'cm' | 'oz';
  type: 'color' | 'developer' | 'additive' | 'bleach' | 'toner';
  volume?: number; // For developers
}

export interface FormulaStep {
  id: string;
  title: string;
  ingredients: FormulaIngredient[];
  processingTime: number; // minutes
  instructions: string;
  type: ProcessType;
}

export interface Formula {
  steps: FormulaStep[];
  currentLevel: number;
  desiredLevel: number;
  currentState: 'natural' | 'colored' | 'bleached' | 'mixed';
  brand: string;
  line: string;
  totalTime: number;
  warnings?: string[];
  grayPercentage?: number;
  availableProducts?: ProductAvailability[];
}

export interface ProductAvailability {
  id: string;
  brand: string;
  line: string;
  type: 'color' | 'developer' | 'bleach' | 'toner' | 'additive' | 'pre-pigment';
  shade?: string;
  volume?: number;
  maxShadeLevel?: number;
  maxDeveloperVolume?: number;
  hasDecolorante?: boolean;
  hasToners?: boolean;
  hasPrePigmentation?: boolean;
  availableShades?: string[];
  availableDeveloperVolumes?: number[];
  stock?: number;
  isActive?: boolean;
}

export interface MixingComponent {
  shade: string;
  percentage: number;
  compatibility: 'perfect' | 'good' | 'acceptable' | 'poor';
  risk?: string;
}

export interface MixingSolution {
  targetShade: string;
  components: MixingComponent[];
  expectedResult: string;
  accuracy: 'exact' | 'very-close' | 'approximate' | 'rough';
  warnings: string[];
  instructions: string;
  alternativeProcess?: string;
  brand: string;
  maxComponents: number;
}

export interface ReflectCompatibility {
  reflect1: string;
  reflect2: string;
  compatible: boolean;
  resultReflect?: string;
  warning?: string;
}

// Reglas de mezcla por marca
export interface BrandMixingRules {
  maxComponents: number;
  maxSpecialMixPercentage: number;
  allowedReflectCombinations: ReflectCompatibility[];
  specialMixProducts: string[];
  restrictions: string[];
}

export interface ValidationResult {
  isValid: boolean;
  violations: ValidationViolation[];
  correctedFormula?: Formula;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  confidence: number; // 0-100
}

export interface ValidationViolation {
  type: 'colorimetry' | 'brand' | 'safety' | 'optimization' | 'availability' | 'mixing';
  severity: 'warning' | 'error' | 'critical';
  message: string;
  step?: string;
  ingredient?: string;
  suggestion?: string;
  autoFixAvailable: boolean;
  alternativeProducts?: string[];
  alternativeLines?: string[];
  alternativeBrands?: string[];
  mixingSolution?: MixingSolution;
}

/**
 * Main Formula Validator Class
 */
export class FormulaValidator {
  private readonly brandRules: Record<string, any>;
  
  constructor(brand: string) {
    this.brandRules = getBrandValidationRules(brand);
  }

  /**
   * Validate complete formula against all rules
   */
  validate(formula: Formula): ValidationResult {
    const violations: ValidationViolation[] = [];
    
    // 1. Product availability validation (CRITICAL - before everything else)
    violations.push(...this.validateProductAvailability(formula));
    
    // 2. Core colorimetry validation
    violations.push(...this.validateColorimetryRules(formula));
    
    // 3. Brand-specific validation
    violations.push(...this.validateBrandRules(formula));
    
    // 4. Safety validation
    violations.push(...this.validateSafety(formula));
    
    // 5. Performance optimization checks
    violations.push(...this.validateOptimization(formula));
    
    // 6. Intelligent mixing validation and suggestions
    violations.push(...this.validateAndSuggestMixing(formula));

    const criticalViolations = violations.filter(v => v.severity === 'critical');
    const errorViolations = violations.filter(v => v.severity === 'error');
    
    const isValid = criticalViolations.length === 0 && errorViolations.length === 0;
    const riskLevel = this.calculateRiskLevel(violations);
    const confidence = this.calculateConfidence(formula, violations);
    
    let correctedFormula: Formula | undefined;
    if (!isValid && violations.some(v => v.autoFixAvailable)) {
      correctedFormula = this.autoCorrect(formula, violations);
    }

    return {
      isValid,
      violations,
      correctedFormula,
      riskLevel,
      confidence,
    };
  }

  /**
   * Validate core colorimetry principles
   */
  private validateColorimetryRules(formula: Formula): ValidationViolation[] {
    const violations: ValidationViolation[] = [];
    const levelDifference = formula.desiredLevel - formula.currentLevel;

    // Rule 1: Color cannot lift color
    if (levelDifference > 0 && formula.currentState === 'colored') {
      const hasColorRemoval = formula.steps.some(step => step.type === ProcessType.COLOR_REMOVAL);
      const hasDirectColorOnly = formula.steps.some(step => 
        step.type === ProcessType.DIRECT_COLOR && 
        !formula.steps.some(s => s.type === ProcessType.BLEACHING)
      );

      if (hasDirectColorOnly && !hasColorRemoval) {
        violations.push({
          type: 'colorimetry',
          severity: 'critical',
          message: 'Color no puede levantar color. Se requiere decapado previo o decoloración.',
          suggestion: 'Agregar paso de decapado antes de la coloración',
          autoFixAvailable: true,
        });
      }
    }

    // Rule 2: Inappropriate developer volume
    for (const step of formula.steps) {
      const developer = step.ingredients.find(ing => ing.type === 'developer');
      if (developer && developer.volume) {
        const expectedVolume = this.calculateExpectedVolume(formula, step.type);
        
        if (developer.volume > expectedVolume + 10) {
          violations.push({
            type: 'colorimetry',
            severity: 'error',
            message: `Volumen de oxidante demasiado alto: ${developer.volume}vol. Recomendado: ${expectedVolume}vol`,
            step: step.id,
            ingredient: developer.product,
            suggestion: `Reducir a ${expectedVolume} volúmenes`,
            autoFixAvailable: true,
          });
        }
      }
    }

    // Rule 3: Missing pre-pigmentation
    if (levelDifference < -COLORIMETRY_PRINCIPLES.PRE_PIGMENT_THRESHOLD) {
      const hasPrePigmentation = formula.steps.some(step => step.type === ProcessType.PRE_PIGMENTATION);
      
      if (!hasPrePigmentation) {
        violations.push({
          type: 'colorimetry',
          severity: 'error',
          message: `Pre-pigmentación requerida al oscurecer ${Math.abs(levelDifference)} niveles`,
          suggestion: 'Agregar paso de pre-pigmentación con pigmento cálido',
          autoFixAvailable: true,
        });
      }
    }

    // Rule 4: Excessive lift in single session
    if (levelDifference > COLORIMETRY_PRINCIPLES.MAX_LIFT_PER_SESSION) {
      violations.push({
        type: 'colorimetry',
        severity: 'critical',
        message: `Aclarado excesivo en una sesión: ${levelDifference} niveles. Máximo seguro: ${COLORIMETRY_PRINCIPLES.MAX_LIFT_PER_SESSION}`,
        suggestion: 'Dividir en múltiples sesiones',
        autoFixAvailable: false,
      });
    }

    // Rule 5: Gray coverage validation
    if (formula.grayPercentage && formula.grayPercentage > 50) {
      const hasNaturalBase = formula.steps.some(step =>
        step.ingredients.some(ing => 
          ing.shade?.includes('/0') || ing.shade?.includes('.0') || ing.shade?.includes('N')
        )
      );

      if (!hasNaturalBase) {
        violations.push({
          type: 'colorimetry',
          severity: 'warning',
          message: 'Más del 50% de canas requiere base natural en la mezcla',
          suggestion: 'Agregar 25-50% de tono natural base',
          autoFixAvailable: true,
        });
      }
    }

    return violations;
  }

  /**
   * Validate brand-specific rules
   */
  private validateBrandRules(formula: Formula): ValidationViolation[] {
    const violations: ValidationViolation[] = [];

    // Check mixing ratios
    for (const step of formula.steps) {
      const colorAmount = step.ingredients
        .filter(ing => ing.type === 'color')
        .reduce((sum, ing) => sum + ing.amount, 0);
      
      const developerAmount = step.ingredients
        .filter(ing => ing.type === 'developer')
        .reduce((sum, ing) => sum + ing.amount, 0);

      if (colorAmount > 0 && developerAmount > 0) {
        const ratio = developerAmount / colorAmount;
        
        if (ratio > this.brandRules.maxDeveloperRatio) {
          violations.push({
            type: 'brand',
            severity: 'error',
            message: `Proporción incorrecta para ${formula.brand}: ${ratio.toFixed(1)}:1. Máximo: ${this.brandRules.maxDeveloperRatio}:1`,
            step: step.id,
            suggestion: `Ajustar a proporción recomendada de ${formula.brand}`,
            autoFixAvailable: true,
          });
        }
      }
    }

    // Brand-specific special products validation
    this.validateSpecialProducts(formula, violations);

    // Developer compatibility
    this.validateDeveloperCompatibility(formula, violations);

    return violations;
  }

  /**
   * Validate safety considerations
   */
  private validateSafety(formula: Formula): ValidationViolation[] {
    const violations: ValidationViolation[] = [];

    // Check for excessive processing times
    if (formula.totalTime > 180) { // 3 hours
      violations.push({
        type: 'safety',
        severity: 'critical',
        message: `Tiempo total excesivo: ${formula.totalTime} minutos. Riesgo de daño severo`,
        suggestion: 'Dividir en múltiples sesiones',
        autoFixAvailable: false,
      });
    }

    // Check for high volume developer on damaged hair
    if (formula.currentState === 'bleached') {
      for (const step of formula.steps) {
        const developer = step.ingredients.find(ing => ing.type === 'developer');
        if (developer && developer.volume && developer.volume > 20) {
          violations.push({
            type: 'safety',
            severity: 'error',
            message: 'Oxidante alto en cabello decolorado puede causar rotura',
            step: step.id,
            suggestion: 'Usar máximo 20 volúmenes en cabello dañado',
            autoFixAvailable: true,
          });
        }
      }
    }

    // Check for incompatible processes
    const hasBleaching = formula.steps.some(step => step.type === ProcessType.BLEACHING);
    const hasColorRemoval = formula.steps.some(step => step.type === ProcessType.COLOR_REMOVAL);
    
    if (hasBleaching && hasColorRemoval) {
      const colorRemovalIndex = formula.steps.findIndex(step => step.type === ProcessType.COLOR_REMOVAL);
      const bleachingIndex = formula.steps.findIndex(step => step.type === ProcessType.BLEACHING);
      
      if (bleachingIndex <= colorRemovalIndex) {
        violations.push({
          type: 'safety',
          severity: 'error',
          message: 'Orden incorrecto: el decapado debe realizarse antes de la decoloración',
          suggestion: 'Reordenar pasos: decapado → evaluación → decoloración',
          autoFixAvailable: true,
        });
      }
    }

    return violations;
  }

  /**
   * Validate optimization opportunities
   */
  private validateOptimization(formula: Formula): ValidationViolation[] {
    const violations: ValidationViolation[] = [];

    // Check for redundant steps
    const stepTypes = formula.steps.map(step => step.type);
    const duplicateTypes = stepTypes.filter((type, index) => stepTypes.indexOf(type) !== index);
    
    if (duplicateTypes.length > 0) {
      violations.push({
        type: 'optimization',
        severity: 'warning',
        message: 'Pasos redundantes detectados',
        suggestion: 'Combinar pasos similares para mayor eficiencia',
        autoFixAvailable: true,
      });
    }

    // Check for inefficient product usage
    for (const step of formula.steps) {
      const totalIngredientAmount = step.ingredients.reduce((sum, ing) => sum + ing.amount, 0);
      
      if (totalIngredientAmount < 30) {
        violations.push({
          type: 'optimization',
          severity: 'warning',
          message: 'Cantidad muy pequeña puede ser difícil de mezclar uniformemente',
          step: step.id,
          suggestion: 'Considerar aumentar proporcionalmente todas las cantidades',
          autoFixAvailable: true,
        });
      }
    }

    return violations;
  }

  /**
   * Auto-correct formula based on violations
   */
  autoCorrect(formula: Formula, violations: ValidationViolation[]): Formula {
    let correctedFormula = { ...formula };
    
    for (const violation of violations.filter(v => v.autoFixAvailable)) {
      switch (violation.type) {
        case 'colorimetry':
          correctedFormula = this.correctColorimetryIssue(correctedFormula, violation);
          break;
        case 'brand':
          correctedFormula = this.correctBrandIssue(correctedFormula, violation);
          break;
        case 'safety':
          correctedFormula = this.correctSafetyIssue(correctedFormula, violation);
          break;
        case 'optimization':
          correctedFormula = this.correctOptimizationIssue(correctedFormula, violation);
          break;
        case 'availability':
          correctedFormula = this.correctAvailabilityIssue(correctedFormula, violation);
          break;
        case 'mixing':
          correctedFormula = this.correctMixingIssue(correctedFormula, violation);
          break;
      }
    }

    // Add correction warnings
    correctedFormula.warnings = [
      ...(correctedFormula.warnings || []),
      'Fórmula auto-corregida para cumplir estándares de seguridad y calidad'
    ];

    return correctedFormula;
  }

  /**
   * Correct colorimetry violations
   */
  private correctColorimetryIssue(formula: Formula, violation: ValidationViolation): Formula {
    const corrected = { ...formula };

    if (violation.message.includes('Color no puede levantar color')) {
      // Add color removal step
      const colorRemovalStep: FormulaStep = {
        id: 'color-removal-auto',
        title: 'Decapado Previo (Auto-añadido)',
        ingredients: [
          {
            product: 'Decapante suave',
            amount: 50,
            unit: 'ml',
            type: 'additive',
          },
        ],
        processingTime: 20,
        instructions: 'Aplicar decapante según instrucciones del fabricante. Evaluar resultado antes de continuar.',
        type: ProcessType.COLOR_REMOVAL,
      };
      
      corrected.steps.unshift(colorRemovalStep);
      corrected.totalTime += 20;
    }

    if (violation.message.includes('Pre-pigmentación requerida')) {
      // Add pre-pigmentation step
      const prePigmentStep: FormulaStep = {
        id: 'pre-pigment-auto',
        title: 'Pre-pigmentación (Auto-añadida)',
        ingredients: [
          {
            product: 'Pigmento cálido',
            shade: `${Math.floor((formula.currentLevel + formula.desiredLevel) / 2)}/43`,
            amount: 30,
            unit: 'ml',
            type: 'color',
          },
          {
            product: 'Oxidante',
            amount: 30,
            unit: 'ml',
            type: 'developer',
            volume: 10,
          },
        ],
        processingTime: 15,
        instructions: 'Aplicar pre-pigmento uniformemente. No enjuagar.',
        type: ProcessType.PRE_PIGMENTATION,
      };

      // Insert before main color step
      const colorStepIndex = corrected.steps.findIndex(step => step.type === ProcessType.DIRECT_COLOR);
      if (colorStepIndex > -1) {
        corrected.steps.splice(colorStepIndex, 0, prePigmentStep);
      } else {
        corrected.steps.push(prePigmentStep);
      }
      
      corrected.totalTime += 15;
    }

    if (violation.message.includes('Volumen de oxidante demasiado alto')) {
      // Correct developer volume
      const stepId = violation.step;
      const step = corrected.steps.find(s => s.id === stepId);
      if (step) {
        const developer = step.ingredients.find(ing => ing.type === 'developer');
        if (developer) {
          const expectedVolume = this.calculateExpectedVolume(formula, step.type);
          developer.volume = expectedVolume;
        }
      }
    }

    return corrected;
  }

  /**
   * Correct brand-specific violations
   */
  private correctBrandIssue(formula: Formula, violation: ValidationViolation): Formula {
    const corrected = { ...formula };

    if (violation.message.includes('Proporción incorrecta')) {
      const stepId = violation.step;
      const step = corrected.steps.find(s => s.id === stepId);
      
      if (step) {
        const colorAmount = step.ingredients
          .filter(ing => ing.type === 'color')
          .reduce((sum, ing) => sum + ing.amount, 0);
        
        const developer = step.ingredients.find(ing => ing.type === 'developer');
        if (developer && colorAmount > 0) {
          // Adjust developer amount to match brand ratio
          developer.amount = colorAmount * this.brandRules.maxDeveloperRatio;
        }
      }
    }

    return corrected;
  }

  /**
   * Correct safety violations
   */
  private correctSafetyIssue(formula: Formula, violation: ValidationViolation): Formula {
    const corrected = { ...formula };

    if (violation.message.includes('cabello decolorado')) {
      const stepId = violation.step;
      const step = corrected.steps.find(s => s.id === stepId);
      
      if (step) {
        const developer = step.ingredients.find(ing => ing.type === 'developer');
        if (developer && developer.volume && developer.volume > 20) {
          developer.volume = 20;
        }
      }
    }

    return corrected;
  }

  /**
   * Correct optimization violations
   */
  private correctOptimizationIssue(formula: Formula, violation: ValidationViolation): Formula {
    const corrected = { ...formula };

    if (violation.message.includes('Cantidad muy pequeña')) {
      const stepId = violation.step;
      const step = corrected.steps.find(s => s.id === stepId);
      
      if (step) {
        // Scale up all ingredients proportionally
        const scaleFactor = 30 / step.ingredients.reduce((sum, ing) => sum + ing.amount, 0);
        step.ingredients.forEach(ing => {
          ing.amount = Math.round(ing.amount * scaleFactor);
        });
      }
    }

    return corrected;
  }

  /**
   * Validate mixing intelligence - suggest blends when exact shades aren't available
   */
  private validateAndSuggestMixing(formula: Formula): ValidationViolation[] {
    const violations: ValidationViolation[] = [];
    const availableProducts = formula.availableProducts || [];
    const brandMixingRules = this.getBrandMixingRules(formula.brand);

    // Check each color ingredient for mixing opportunities
    for (const step of formula.steps) {
      for (const ingredient of step.ingredients) {
        if (ingredient.type === 'color' && ingredient.shade) {
          const isAvailable = this.isShadeDirectlyAvailable(ingredient.shade, availableProducts, formula.brand, formula.line);
          
          if (!isAvailable) {
            // Generate intelligent mixing solution
            const mixingSolution = this.generateMixingSolution(
              ingredient.shade,
              availableProducts,
              formula.brand,
              formula.line,
              brandMixingRules
            );

            if (mixingSolution) {
              violations.push({
                type: 'mixing',
                severity: 'warning',
                message: `Tono ${ingredient.shade} no disponible - se propone mezcla inteligente`,
                step: step.id,
                ingredient: ingredient.product,
                suggestion: `${mixingSolution.expectedResult}: ${mixingSolution.components.map(c => `${c.percentage}% ${c.shade}`).join(' + ')}`,
                autoFixAvailable: true,
                mixingSolution,
              });
            } else {
              // If no mixing solution possible, suggest alternatives
              const alternatives = this.suggestCreativeAlternatives(
                ingredient.shade,
                availableProducts,
                formula.brand,
                formula.line
              );
              
              violations.push({
                type: 'mixing',
                severity: 'error',
                message: `Tono ${ingredient.shade} no disponible y no se puede conseguir por mezcla`,
                step: step.id,
                ingredient: ingredient.product,
                suggestion: alternatives.length > 0 ? `Alternativas: ${alternatives.join(', ')}` : 'Considerar cambiar la fórmula o conseguir el tono específico',
                autoFixAvailable: alternatives.length > 0,
                alternativeProducts: alternatives,
              });
            }
          }
        }
      }
    }

    return violations;
  }

  /**
   * Generate intelligent mixing solution for unavailable shade
   */
  private generateMixingSolution(
    targetShade: string,
    availableProducts: ProductAvailability[],
    brand: string,
    line: string,
    mixingRules: BrandMixingRules
  ): MixingSolution | null {
    const parsedTarget = this.parseShade(targetShade);
    if (!parsedTarget) return null;

    // Get available shades in same brand/line
    const availableShades = this.getAvailableShades(availableProducts, brand, line);
    
    // Find potential mixing combinations
    const combinations = this.findMixingCombinations(
      parsedTarget,
      availableShades,
      mixingRules
    );

    // Select best combination
    const bestCombination = this.selectBestMixingCombination(combinations, parsedTarget);
    
    if (bestCombination) {
      return {
        targetShade,
        components: bestCombination.components,
        expectedResult: bestCombination.expectedResult,
        accuracy: bestCombination.accuracy,
        warnings: bestCombination.warnings,
        instructions: this.generateMixingInstructions(bestCombination, brand),
        alternativeProcess: bestCombination.alternativeProcess,
        brand,
        maxComponents: mixingRules.maxComponents,
      };
    }

    return null;
  }

  /**
   * Parse shade into level and reflects
   */
  private parseShade(shade: string): { level: number; primaryReflect?: string; secondaryReflect?: string } | null {
    // Examples: "7.43", "8/34", "9.03", "6N", "7.4"
    const patterns = [
      /^(\d+)\.(\d)(\d)?$/, // 7.43, 8.3
      /^(\d+)\/(\d)(\d)?$/, // 7/43, 8/3  
      /^(\d+)([A-Z])$/, // 6N, 7A
      /^(\d+)$/, // Just level: 7
    ];

    for (const pattern of patterns) {
      const match = shade.match(pattern);
      if (match) {
        const level = parseInt(match[1]);
        
        if (match[2]) {
          if (isNaN(parseInt(match[2]))) {
            // Letter notation (N, A, etc.)
            return {
              level,
              primaryReflect: match[2],
            };
          } else {
            // Numeric notation
            return {
              level,
              primaryReflect: match[2],
              secondaryReflect: match[3],
            };
          }
        }
        
        return { level };
      }
    }

    return null;
  }

  /**
   * Get available shades from products
   */
  private getAvailableShades(
    availableProducts: ProductAvailability[], 
    brand: string, 
    line: string
  ): string[] {
    return availableProducts
      .filter(p => 
        p.brand.toLowerCase() === brand.toLowerCase() &&
        p.line.toLowerCase() === line.toLowerCase() &&
        p.type === 'color' &&
        p.isActive !== false
      )
      .flatMap(p => p.availableShades || [])
      .filter((shade, index, array) => array.indexOf(shade) === index); // Remove duplicates
  }

  /**
   * Find all possible mixing combinations
   */
  private findMixingCombinations(
    target: { level: number; primaryReflect?: string; secondaryReflect?: string },
    availableShades: string[],
    mixingRules: BrandMixingRules
  ): Array<{
    components: MixingComponent[];
    expectedResult: string;
    accuracy: 'exact' | 'very-close' | 'approximate' | 'rough';
    warnings: string[];
    alternativeProcess?: string;
  }> {
    const combinations: any[] = [];

    // Try two-component mixing first (most common)
    for (let i = 0; i < availableShades.length; i++) {
      for (let j = i + 1; j < availableShades.length; j++) {
        const shade1 = this.parseShade(availableShades[i]);
        const shade2 = this.parseShade(availableShades[j]);
        
        if (!shade1 || !shade2) continue;

        // Check level compatibility (should be same or very close)
        if (Math.abs(shade1.level - target.level) <= 1 && 
            Math.abs(shade2.level - target.level) <= 1) {
          
          const mixingResult = this.calculateTwoShadeMix(
            { shade: availableShades[i], parsed: shade1 },
            { shade: availableShades[j], parsed: shade2 },
            target,
            mixingRules
          );

          if (mixingResult) {
            combinations.push(mixingResult);
          }
        }
      }
    }

    // Try three-component mixing if brand allows it
    if (mixingRules.maxComponents >= 3) {
      // Implementation for 3-component mixing (more complex)
      // This would be added for brands that allow more complex mixtures
    }

    return combinations;
  }

  /**
   * Calculate result of mixing two shades
   */
  private calculateTwoShadeMix(
    shade1: { shade: string; parsed: any },
    shade2: { shade: string; parsed: any },
    target: any,
    mixingRules: BrandMixingRules
  ): any | null {
    // Check reflect compatibility
    const reflectCompatibility = this.checkReflectCompatibility(
      shade1.parsed.primaryReflect,
      shade2.parsed.primaryReflect,
      mixingRules
    );

    if (!reflectCompatibility.compatible) {
      return null; // Incompatible reflects
    }

    // Calculate optimal percentages
    const percentages = this.calculateOptimalPercentages(
      shade1.parsed,
      shade2.parsed,
      target
    );

    if (!percentages) return null;

    const components: MixingComponent[] = [
      {
        shade: shade1.shade,
        percentage: percentages.shade1Percentage,
        compatibility: reflectCompatibility.compatible ? 'good' : 'poor',
      },
      {
        shade: shade2.shade,
        percentage: percentages.shade2Percentage,
        compatibility: reflectCompatibility.compatible ? 'good' : 'poor',
      },
    ];

    // Determine accuracy and warnings
    const accuracy = this.determineAccuracy(shade1.parsed, shade2.parsed, target, percentages);
    const warnings = this.generateMixingWarnings(components, target, reflectCompatibility);

    return {
      components,
      expectedResult: `Aproximado a ${target.level}.${target.primaryReflect || ''}${target.secondaryReflect || ''}`,
      accuracy,
      warnings,
    };
  }

  /**
   * Check if two reflects are compatible for mixing
   */
  private checkReflectCompatibility(
    reflect1?: string,
    reflect2?: string,
    mixingRules?: BrandMixingRules
  ): ReflectCompatibility {
    if (!reflect1 || !reflect2) {
      return { reflect1: reflect1 || '', reflect2: reflect2 || '', compatible: true };
    }

    // Define incompatible combinations
    const incompatibleCombinations = [
      { r1: '1', r2: '3', warning: 'Ceniza + dorado = resultado impredecible' },
      { r1: '1', r2: '4', warning: 'Ceniza + cobrizo = neutralización excesiva' },
      { r1: '2', r2: '6', warning: 'Violeta + rojo = resultado muy oscuro' },
      { r1: '1', r2: '7', warning: 'Ceniza + verde = resultado apagado' },
    ];

    // Check brand-specific rules if available
    if (mixingRules?.allowedReflectCombinations) {
      const ruleMatch = mixingRules.allowedReflectCombinations.find(
        rule => 
          (rule.reflect1 === reflect1 && rule.reflect2 === reflect2) ||
          (rule.reflect1 === reflect2 && rule.reflect2 === reflect1)
      );
      
      if (ruleMatch) {
        return ruleMatch;
      }
    }

    // Check general incompatibilities
    const incompatible = incompatibleCombinations.find(
      combo => 
        (combo.r1 === reflect1 && combo.r2 === reflect2) ||
        (combo.r1 === reflect2 && combo.r2 === reflect1)
    );

    if (incompatible) {
      return {
        reflect1,
        reflect2,
        compatible: false,
        warning: incompatible.warning,
      };
    }

    // Default: compatible
    return {
      reflect1,
      reflect2,
      compatible: true,
      resultReflect: this.predictResultReflect(reflect1, reflect2),
    };
  }

  /**
   * Predict resulting reflect from mixing two reflects
   */
  private predictResultReflect(reflect1: string, reflect2: string): string {
    // Simplified prediction logic
    const reflectMap: Record<string, number> = {
      '0': 0, '1': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9,
      'N': 0, 'A': 1, 'V': 2, 'G': 3, 'C': 4, 'M': 5, 'R': 6,
    };

    const val1 = reflectMap[reflect1] ?? 0;
    const val2 = reflectMap[reflect2] ?? 0;
    const average = Math.round((val1 + val2) / 2);

    return average.toString();
  }

  /**
   * Calculate optimal mixing percentages
   */
  private calculateOptimalPercentages(
    shade1: any,
    shade2: any,
    target: any
  ): { shade1Percentage: number; shade2Percentage: number } | null {
    // If same level, can mix 50/50 as starting point
    if (shade1.level === shade2.level && shade1.level === target.level) {
      return { shade1Percentage: 50, shade2Percentage: 50 };
    }

    // If different levels, calculate based on target level
    if (shade1.level !== shade2.level) {
      // Linear interpolation to get target level
      const levelDiff = shade2.level - shade1.level;
      const targetDiff = target.level - shade1.level;
      
      if (levelDiff === 0) return null; // Same level, shouldn't happen
      
      const shade2Percentage = Math.round((targetDiff / levelDiff) * 100);
      const shade1Percentage = 100 - shade2Percentage;
      
      // Ensure reasonable percentages (10-90%)
      if (shade1Percentage < 10 || shade1Percentage > 90) return null;
      
      return { shade1Percentage, shade2Percentage };
    }

    return { shade1Percentage: 50, shade2Percentage: 50 };
  }

  /**
   * Determine accuracy of mixing result
   */
  private determineAccuracy(
    shade1: any,
    shade2: any,
    target: any,
    percentages: any
  ): 'exact' | 'very-close' | 'approximate' | 'rough' {
    // Same level and compatible reflects = very close
    if (shade1.level === target.level && shade2.level === target.level) {
      if (shade1.primaryReflect === target.primaryReflect || shade2.primaryReflect === target.primaryReflect) {
        return 'very-close';
      }
      return 'approximate';
    }

    // Different levels but close = approximate
    const avgLevel = (shade1.level * percentages.shade1Percentage + shade2.level * percentages.shade2Percentage) / 100;
    if (Math.abs(avgLevel - target.level) <= 0.5) {
      return 'approximate';
    }

    return 'rough';
  }

  /**
   * Generate warnings for mixing
   */
  private generateMixingWarnings(
    components: MixingComponent[],
    target: any,
    reflectCompatibility: ReflectCompatibility
  ): string[] {
    const warnings: string[] = [];

    if (!reflectCompatibility.compatible && reflectCompatibility.warning) {
      warnings.push(reflectCompatibility.warning);
    }

    if (components.some(c => c.percentage < 20)) {
      warnings.push('Porcentajes muy bajos pueden ser difíciles de medir con precisión');
    }

    if (components.some(c => c.percentage > 80)) {
      warnings.push('Un componente domina la mezcla - el resultado será muy similar al tono principal');
    }

    return warnings;
  }

  /**
   * Generate mixing instructions
   */
  private generateMixingInstructions(
    combination: any,
    brand: string
  ): string {
    const instructions = [
      '1. Medir con precisión cada componente',
      `2. Mezclar ${combination.components.map((c: any) => `${c.percentage}% ${c.shade}`).join(' + ')}`,
      '3. Homogeneizar completamente antes de aplicar',
      '4. Realizar prueba mechas si hay dudas sobre el resultado',
    ];

    // Add brand-specific instructions
    const brandInstructions = this.getBrandSpecificMixingInstructions(brand);
    if (brandInstructions.length > 0) {
      instructions.push('\nInstrucciones específicas de ' + brand + ':');
      instructions.push(...brandInstructions);
    }

    return instructions.join('\n');
  }

  /**
   * Get brand-specific mixing instructions
   */
  private getBrandSpecificMixingInstructions(brand: string): string[] {
    const brandInstructions: Record<string, string[]> = {
      'wella': [
        '• Special Mix no debe superar el 25% de la mezcla',
        '• Koleston Perfect acepta hasta 3 tonos en mezcla',
        '• Illumina Color máximo 2 tonos para mantener tecnología',
      ],
      'loreal': [
        '• Majirel: máximo 2 tonos base + corrector',
        '• INOA: evitar mezclar con tonos de cobertura',
        '• Mix correctores hasta 1/4 del tono base',
      ],
      'schwarzkopf': [
        '• Igora Royal: concentrados 0/xx máximo 10%',
        '• Absolutes pueden mezclarse libremente entre sí',
        '• Royal Highlifts no mezclar con tonos oscuros',
      ],
    };

    return brandInstructions[brand.toLowerCase()] || [];
  }

  /**
   * Get brand mixing rules
   */
  private getBrandMixingRules(brand: string): BrandMixingRules {
    const defaultRules: BrandMixingRules = {
      maxComponents: 2,
      maxSpecialMixPercentage: 20,
      allowedReflectCombinations: [],
      specialMixProducts: [],
      restrictions: [],
    };

    const brandRules: Record<string, Partial<BrandMixingRules>> = {
      'wella': {
        maxComponents: 3,
        maxSpecialMixPercentage: 25,
        specialMixProducts: ['Special Mix', 'T-Series'],
        restrictions: ['Illumina Color máximo 2 tonos'],
      },
      'loreal': {
        maxComponents: 2,
        maxSpecialMixPercentage: 25,
        specialMixProducts: ['Mix', '.1', '.2'],
        restrictions: ['INOA no mezclar con cobertura'],
      },
      'schwarzkopf': {
        maxComponents: 2,
        maxSpecialMixPercentage: 10,
        specialMixProducts: ['0/xx concentrados'],
        restrictions: ['Royal Highlifts solo entre sí'],
      },
    };

    return { ...defaultRules, ...brandRules[brand.toLowerCase()] };
  }

  /**
   * Check if shade is directly available
   */
  private isShadeDirectlyAvailable(
    shade: string,
    availableProducts: ProductAvailability[],
    brand: string,
    line: string
  ): boolean {
    return availableProducts.some(p => 
      p.brand.toLowerCase() === brand.toLowerCase() &&
      p.line.toLowerCase() === line.toLowerCase() &&
      p.type === 'color' &&
      p.availableShades?.includes(shade) &&
      p.isActive !== false
    );
  }

  /**
   * Suggest creative alternatives when mixing isn't possible
   */
  private suggestCreativeAlternatives(
    targetShade: string,
    availableProducts: ProductAvailability[],
    brand: string,
    line: string
  ): string[] {
    const alternatives: string[] = [];
    const parsedTarget = this.parseShade(targetShade);
    
    if (!parsedTarget) return alternatives;

    // 1. Suggest using decolorante + toner process
    if (parsedTarget.level >= 7) {
      const hasDecolorante = availableProducts.some(p => 
        p.brand.toLowerCase() === brand.toLowerCase() &&
        p.type === 'bleach' && p.hasDecolorante
      );
      
      const hasToners = availableProducts.some(p => 
        p.brand.toLowerCase() === brand.toLowerCase() &&
        p.type === 'toner' && p.hasToners
      );

      if (hasDecolorante && hasToners) {
        alternatives.push(`Aclarado + matización para conseguir ${targetShade}`);
      }
    }

    // 2. Suggest using cold tones for neutralization
    if (parsedTarget.primaryReflect && ['1', '2', '6'].includes(parsedTarget.primaryReflect)) {
      const coldTones = this.getAvailableShades(availableProducts, brand, line)
        .filter(shade => {
          const parsed = this.parseShade(shade);
          return parsed && parsed.level === parsedTarget.level && 
                 parsed.primaryReflect && ['1', '2', '6'].includes(parsed.primaryReflect);
        });
      
      if (coldTones.length > 0) {
        alternatives.push(`Usar tonos fríos disponibles: ${coldTones.slice(0, 3).join(', ')}`);
      }
    }

    // 3. Suggest level adjustment with similar reflect
    const similarReflectTones = this.getAvailableShades(availableProducts, brand, line)
      .filter(shade => {
        const parsed = this.parseShade(shade);
        return parsed && 
               parsed.primaryReflect === parsedTarget.primaryReflect &&
               Math.abs(parsed.level - parsedTarget.level) <= 1;
      });
    
    if (similarReflectTones.length > 0) {
      alternatives.push(`Nivel similar con mismo reflejo: ${similarReflectTones.slice(0, 2).join(', ')}`);
    }

    // 4. Suggest multi-step process
    if (parsedTarget.level <= 4) {
      alternatives.push('Proceso en 2 pasos: pre-pigmentación + tono final');
    }

    return alternatives;
  }

  /**
   * Select best mixing combination from available options
   */
  private selectBestMixingCombination(
    combinations: any[],
    target: any
  ): any | null {
    if (combinations.length === 0) return null;

    // Sort by accuracy and feasibility
    const sorted = combinations.sort((a, b) => {
      const accuracyScore = { 'exact': 4, 'very-close': 3, 'approximate': 2, 'rough': 1 };
      const aScore = accuracyScore[a.accuracy] || 0;
      const bScore = accuracyScore[b.accuracy] || 0;
      
      if (aScore !== bScore) return bScore - aScore;
      
      // If same accuracy, prefer fewer warnings
      return a.warnings.length - b.warnings.length;
    });

    return sorted[0];
  }

  /**
   * Correct mixing issues by applying suggested mixing solution
   */
  private correctMixingIssue(formula: Formula, violation: ValidationViolation): Formula {
    const corrected = { ...formula };
    
    if (violation.mixingSolution && violation.step) {
      const step = corrected.steps.find(s => s.id === violation.step);
      if (step) {
        const targetIngredient = step.ingredients.find(ing => ing.product === violation.ingredient);
        if (targetIngredient && targetIngredient.shade) {
          // Replace single ingredient with mixing components
          const ingredientIndex = step.ingredients.findIndex(ing => ing === targetIngredient);
          
          // Create new ingredients based on mixing solution
          const mixingIngredients: FormulaIngredient[] = violation.mixingSolution.components.map((component, index) => ({
            product: `${targetIngredient.product} - Componente ${index + 1}`,
            shade: component.shade,
            amount: Math.round((targetIngredient.amount * component.percentage) / 100),
            unit: targetIngredient.unit,
            type: targetIngredient.type,
          }));

          // Replace original ingredient with mixing components
          step.ingredients.splice(ingredientIndex, 1, ...mixingIngredients);
          
          // Update step title and instructions
          step.title += ' (Mezcla inteligente)';
          step.instructions += `\n\nMezcla: ${violation.mixingSolution.instructions}`;
          
          // Add warnings to formula
          corrected.warnings = [
            ...(corrected.warnings || []),
            `Tono ${targetIngredient.shade} reemplazado por mezcla inteligente`,
            ...violation.mixingSolution.warnings,
          ];
        }
      }
    }

    return corrected;
  }

  /**
   * Validate product availability in inventory
   */
  private validateProductAvailability(formula: Formula): ValidationViolation[] {
    const violations: ValidationViolation[] = [];
    const availableProducts = formula.availableProducts || [];
    const levelDifference = formula.desiredLevel - formula.currentLevel;

    // Get brand and line availability data
    const brandProducts = availableProducts.filter(
      p => p.brand.toLowerCase() === formula.brand.toLowerCase()
    );
    const lineProducts = brandProducts.filter(
      p => p.line.toLowerCase() === formula.line.toLowerCase()
    );

    // 1. Validate bleach availability for high lift requirements
    if (levelDifference > 3 || formula.steps.some(step => step.type === ProcessType.BLEACHING)) {
      const hasDecolorante = lineProducts.some(p => 
        p.type === 'bleach' && p.hasDecolorante && p.isActive !== false
      );
      
      if (!hasDecolorante) {
        const alternativeLines = brandProducts
          .filter(p => p.type === 'bleach' && p.hasDecolorante)
          .map(p => p.line)
          .filter((line, index, self) => self.indexOf(line) === index);
        
        const alternativeBrands = availableProducts
          .filter(p => p.type === 'bleach' && p.hasDecolorante)
          .map(p => p.brand)
          .filter((brand, index, self) => self.indexOf(brand) === index);

        violations.push({
          type: 'availability',
          severity: 'critical',
          message: `La línea ${formula.line} no incluye decolorante - imposible aclarar ${levelDifference} niveles`,
          suggestion: alternativeLines.length > 0 
            ? `Usar líneas de ${formula.brand}: ${alternativeLines.join(', ')}`
            : `Cambiar a marcas con decolorante: ${alternativeBrands.slice(0, 3).join(', ')}`,
          autoFixAvailable: alternativeLines.length > 0,
          alternativeLines,
          alternativeBrands,
        });
      }
    }

    // 2. Validate specific shade availability
    for (const step of formula.steps) {
      for (const ingredient of step.ingredients) {
        if (ingredient.type === 'color' && ingredient.shade) {
          const shadeAvailable = lineProducts.some(p => 
            p.type === 'color' && 
            p.availableShades?.includes(ingredient.shade) &&
            p.isActive !== false
          );
          
          if (!shadeAvailable) {
            const maxShadeInLine = Math.max(
              ...lineProducts
                .filter(p => p.type === 'color')
                .flatMap(p => p.availableShades || [])
                .map(shade => {
                  const match = shade.match(/^(\d+)/);
                  return match ? parseInt(match[1]) : 0;
                })
            );

            const requestedLevel = parseInt(ingredient.shade.match(/^(\d+)/)?.[1] || '0');
            const requestedReflect = ingredient.shade.replace(/^\d+\.?/, '');

            let suggestion = '';
            if (requestedLevel > maxShadeInLine) {
              suggestion = `Tono ${ingredient.shade} no disponible - nivel máximo en esta línea es ${maxShadeInLine}`;
            } else if (requestedReflect) {
              suggestion = `Tono ${ingredient.shade} no disponible - verificar matices disponibles en nivel ${requestedLevel}`;
            }

            const alternativeLines = brandProducts
              .filter(p => p.availableShades?.includes(ingredient.shade))
              .map(p => p.line)
              .filter((line, index, self) => self.indexOf(line) === index);

            violations.push({
              type: 'availability',
              severity: 'error',
              message: suggestion,
              step: step.id,
              ingredient: ingredient.product,
              suggestion: alternativeLines.length > 0 
                ? `Disponible en líneas: ${alternativeLines.join(', ')}`
                : `Considerar tono similar disponible en ${formula.line}`,
              autoFixAvailable: false,
              alternativeLines,
            });
          }
        }
      }
    }

    // 3. Validate developer volume availability
    for (const step of formula.steps) {
      const developer = step.ingredients.find(ing => ing.type === 'developer');
      if (developer && developer.volume) {
        const volumeAvailable = lineProducts.some(p => 
          p.type === 'developer' && 
          p.availableDeveloperVolumes?.includes(developer.volume!) &&
          p.isActive !== false
        );
        
        if (!volumeAvailable) {
          const maxVolume = Math.max(
            ...lineProducts
              .filter(p => p.type === 'developer')
              .flatMap(p => p.availableDeveloperVolumes || [])
          );

          const alternativeBrands = availableProducts
            .filter(p => p.availableDeveloperVolumes?.includes(developer.volume!))
            .map(p => p.brand)
            .filter((brand, index, self) => self.indexOf(brand) === index);

          violations.push({
            type: 'availability',
            severity: 'error',
            message: `${formula.brand} no tiene oxidante ${developer.volume}vol - máximo disponible: ${maxVolume}vol`,
            step: step.id,
            ingredient: developer.product,
            suggestion: alternativeBrands.length > 0
              ? `Cambiar a marcas que tengan ${developer.volume}vol: ${alternativeBrands.slice(0, 3).join(', ')}`
              : `Usar ${maxVolume}vol disponible y ajustar proceso`,
            autoFixAvailable: maxVolume > 0,
            alternativeBrands,
          });
        }
      }
    }

    // 4. Validate toner availability for neutralization
    const needsToning = formula.steps.some(step => 
      step.type === ProcessType.TONING || 
      step.ingredients.some(ing => ing.type === 'toner')
    );
    
    if (needsToning) {
      const hasToners = lineProducts.some(p => 
        p.type === 'toner' && p.hasToners && p.isActive !== false
      );
      
      if (!hasToners) {
        const alternativeLines = brandProducts
          .filter(p => p.type === 'toner' && p.hasToners)
          .map(p => p.line)
          .filter((line, index, self) => self.indexOf(line) === index);

        violations.push({
          type: 'availability',
          severity: 'warning',
          message: `No hay matizadores disponibles en la línea ${formula.line}`,
          suggestion: alternativeLines.length > 0
            ? `Usar matizadores de: ${alternativeLines.join(', ')}`
            : 'Considerar otra marca con sistema de matizado',
          autoFixAvailable: alternativeLines.length > 0,
          alternativeLines,
        });
      }
    }

    // 5. Validate pre-pigmentation products
    const needsPrePigmentation = formula.steps.some(step => 
      step.type === ProcessType.PRE_PIGMENTATION
    );
    
    if (needsPrePigmentation) {
      const hasPrePigments = lineProducts.some(p => 
        p.type === 'pre-pigment' && p.hasPrePigmentation && p.isActive !== false
      );
      
      if (!hasPrePigments) {
        violations.push({
          type: 'availability',
          severity: 'warning',
          message: `No hay productos de pre-pigmentación en la línea ${formula.line}`,
          suggestion: 'Usar tonos cálidos de la misma marca como alternativa',
          autoFixAvailable: true,
        });
      }
    }

    // 6. Validate stock levels if available
    for (const step of formula.steps) {
      for (const ingredient of step.ingredients) {
        const matchingProduct = lineProducts.find(p => 
          p.type === ingredient.type &&
          (ingredient.shade ? p.availableShades?.includes(ingredient.shade) : true) &&
          (ingredient.volume ? p.availableDeveloperVolumes?.includes(ingredient.volume) : true)
        );

        if (matchingProduct && typeof matchingProduct.stock === 'number') {
          const requiredAmount = ingredient.amount || 0;
          if (matchingProduct.stock < requiredAmount) {
            violations.push({
              type: 'availability',
              severity: 'warning',
              message: `Stock insuficiente de ${ingredient.product}: disponible ${matchingProduct.stock}ml, necesario ${requiredAmount}ml`,
              step: step.id,
              ingredient: ingredient.product,
              suggestion: 'Verificar stock antes de comenzar el servicio',
              autoFixAvailable: false,
            });
          }
        }
      }
    }

    return violations;
  }

  /**
   * Correct availability violations by suggesting alternatives
   */
  private correctAvailabilityIssue(formula: Formula, violation: ValidationViolation): Formula {
    const corrected = { ...formula };

    // Auto-correct decolorante missing by suggesting alternative line
    if (violation.message.includes('no incluye decolorante') && violation.alternativeLines?.length) {
      corrected.line = violation.alternativeLines[0];
      corrected.warnings = [
        ...(corrected.warnings || []),
        `Línea cambiada a ${violation.alternativeLines[0]} para incluir decolorante`
      ];
    }

    // Auto-correct developer volume
    if (violation.message.includes('oxidante') && violation.step) {
      const step = corrected.steps.find(s => s.id === violation.step);
      if (step) {
        const developer = step.ingredients.find(ing => ing.type === 'developer');
        if (developer) {
          // Extract available volume from error message
          const maxVolumeMatch = violation.message.match(/máximo disponible: (\d+)vol/);
          if (maxVolumeMatch) {
            const maxVolume = parseInt(maxVolumeMatch[1]);
            developer.volume = maxVolume;
            corrected.warnings = [
              ...(corrected.warnings || []),
              `Volumen de oxidante ajustado a ${maxVolume}vol (máximo disponible)`
            ];
          }
        }
      }
    }

    return corrected;
  }

  /**
   * Helper methods
   */
  private calculateExpectedVolume(formula: Formula, stepType: ProcessType): number {
    const levelDifference = formula.desiredLevel - formula.currentLevel;

    switch (stepType) {
      case ProcessType.DIRECT_COLOR:
        if (levelDifference <= 1) return 20;
        if (levelDifference <= 2) return 30;
        return 40;
      case ProcessType.BLEACHING:
        return 30;
      case ProcessType.TONING:
      case ProcessType.PRE_PIGMENTATION:
        return 10;
      default:
        return 20;
    }
  }

  private validateSpecialProducts(formula: Formula, violations: ValidationViolation[]): void {
    // Brand-specific special product validation logic
    // This would be expanded based on specific brand rules
  }

  private validateDeveloperCompatibility(formula: Formula, violations: ValidationViolation[]): void {
    // Check if developer volumes are allowed for the brand
    for (const step of formula.steps) {
      const developer = step.ingredients.find(ing => ing.type === 'developer');
      if (developer && developer.volume) {
        if (!this.brandRules.allowedDeveloperVolumes.includes(developer.volume)) {
          violations.push({
            type: 'brand',
            severity: 'warning',
            message: `Volumen ${developer.volume} no estándar para ${formula.brand}`,
            step: step.id,
            suggestion: `Usar volúmenes estándar: ${this.brandRules.allowedDeveloperVolumes.join(', ')}`,
            autoFixAvailable: true,
          });
        }
      }
    }
  }

  private calculateRiskLevel(violations: ValidationViolation[]): 'low' | 'medium' | 'high' | 'critical' {
    const criticalViolations = violations.filter(v => v.severity === 'critical').length;
    const errorViolations = violations.filter(v => v.severity === 'error').length;
    const warningViolations = violations.filter(v => v.severity === 'warning').length;

    if (criticalViolations > 0) return 'critical';
    if (errorViolations > 2) return 'high';
    if (errorViolations > 0 || warningViolations > 3) return 'medium';
    return 'low';
  }

  private calculateConfidence(formula: Formula, violations: ValidationViolation[]): number {
    let baseConfidence = 100;
    
    violations.forEach(violation => {
      switch (violation.severity) {
        case 'critical':
          baseConfidence -= 30;
          break;
        case 'error':
          baseConfidence -= 15;
          break;
        case 'warning':
          baseConfidence -= 5;
          break;
      }
    });

    // Bonus for proper colorimetry process validation
    const colorProcess = validateColorProcess({
      currentLevel: formula.currentLevel,
      desiredLevel: formula.desiredLevel,
      currentState: formula.currentState,
    });

    if (colorProcess.isViable) {
      baseConfidence += 10;
    }

    return Math.max(0, Math.min(100, baseConfidence));
  }
}

/**
 * Convenience function for quick validation
 */
export function validateFormula(formula: Formula): ValidationResult {
  const validator = new FormulaValidator(formula.brand);
  return validator.validate(formula);
}

/**
 * Convenience function for auto-correction
 */
export function autoCorrectFormula(formula: Formula): Formula {
  const validator = new FormulaValidator(formula.brand);
  const validation = validator.validate(formula);
  
  if (!validation.isValid && validation.correctedFormula) {
    return validation.correctedFormula;
  }
  
  return formula;
}

/**
 * Validate formula with product availability check
 */
export function validateFormulaWithAvailability(
  formula: Formula, 
  availableProducts: ProductAvailability[]
): ValidationResult {
  const formulaWithAvailability = {
    ...formula,
    availableProducts,
  };
  
  const validator = new FormulaValidator(formula.brand);
  return validator.validate(formulaWithAvailability);
}

/**
 * Generate product availability suggestions for impossible formulas
 */
export function generateAvailabilitySuggestions(
  formula: Formula,
  availableProducts: ProductAvailability[]
): {
  canProceed: boolean;
  criticalIssues: string[];
  suggestions: string[];
  alternativeFormula?: Formula;
} {
  const validation = validateFormulaWithAvailability(formula, availableProducts);
  
  const availabilityViolations = validation.violations.filter(v => v.type === 'availability');
  const criticalIssues = availabilityViolations
    .filter(v => v.severity === 'critical')
    .map(v => v.message);
  
  const suggestions = availabilityViolations
    .filter(v => v.suggestion)
    .map(v => v.suggestion!);
  
  const canProceed = criticalIssues.length === 0;
  
  return {
    canProceed,
    criticalIssues,
    suggestions,
    alternativeFormula: validation.correctedFormula,
  };
}

/**
 * Helper function to create ProductAvailability from inventory data
 */
export function createProductAvailabilityFromInventory(inventoryProducts: any[]): ProductAvailability[] {
  return inventoryProducts.map(product => {
    // Determine product type from category
    const typeMapping: Record<string, ProductAvailability['type']> = {
      'tinte': 'color',
      'oxidante': 'developer', 
      'decolorante': 'bleach',
      'matizador': 'toner',
      'aditivo': 'additive',
      'pre-pigmentacion': 'pre-pigment',
    };

    const type = typeMapping[product.category] || 'color';
    
    // Extract available shades for color products
    const availableShades: string[] = [];
    if (type === 'color' && product.shade) {
      availableShades.push(product.shade);
    }
    
    // Extract developer volumes
    const availableDeveloperVolumes: number[] = [];
    if (type === 'developer' && product.shade) {
      const volumeMatch = product.shade.match(/(\d+)\s*vol/);
      if (volumeMatch) {
        availableDeveloperVolumes.push(parseInt(volumeMatch[1]));
      }
    }

    return {
      id: product.id,
      brand: product.brand,
      line: product.line || '',
      type,
      shade: product.shade,
      maxShadeLevel: type === 'color' ? extractMaxLevel(availableShades) : undefined,
      maxDeveloperVolume: type === 'developer' ? Math.max(...availableDeveloperVolumes) : undefined,
      hasDecolorante: type === 'bleach',
      hasToners: type === 'toner',
      hasPrePigmentation: type === 'pre-pigment',
      availableShades: availableShades.length > 0 ? availableShades : undefined,
      availableDeveloperVolumes: availableDeveloperVolumes.length > 0 ? availableDeveloperVolumes : undefined,
      stock: product.currentStock,
      isActive: product.isActive,
    };
  });
}

/**
 * Extract maximum shade level from available shades
 */
function extractMaxLevel(shades: string[]): number {
  return Math.max(
    ...shades.map(shade => {
      const match = shade.match(/^(\d+)/);
      return match ? parseInt(match[1]) : 0;
    })
  );
}

/**
 * Check if specific products are available in inventory
 */
export function checkProductsAvailability(
  requiredProducts: { type: string; shade?: string; volume?: number }[],
  availableProducts: ProductAvailability[]
): { available: boolean; missing: string[]; alternatives: string[] } {
  const missing: string[] = [];
  const alternatives: string[] = [];
  
  for (const required of requiredProducts) {
    const isAvailable = availableProducts.some(available => {
      if (available.type !== required.type) return false;
      
      if (required.shade && !available.availableShades?.includes(required.shade)) {
        return false;
      }
      
      if (required.volume && !available.availableDeveloperVolumes?.includes(required.volume)) {
        return false;
      }
      
      return available.isActive !== false;
    });
    
    if (!isAvailable) {
      const productDesc = `${required.type}${required.shade ? ' ' + required.shade : ''}${required.volume ? ' ' + required.volume + 'vol' : ''}`;
      missing.push(productDesc);
      
      // Find alternatives
      const similarProducts = availableProducts.filter(p => p.type === required.type && p.isActive !== false);
      if (similarProducts.length > 0) {
        alternatives.push(`Para ${productDesc}: ${similarProducts.slice(0, 3).map(p => `${p.brand} ${p.line}`).join(', ')}`);
      }
    }
  }
  
  return {
    available: missing.length === 0,
    missing,
    alternatives,
  };
}

/**
 * Generate intelligent mixing solution for a specific shade
 */
export function generateSmartMixingSolution(
  targetShade: string,
  availableProducts: ProductAvailability[],
  brand: string,
  line: string
): MixingSolution | null {
  const validator = new FormulaValidator(brand);
  const brandMixingRules = (validator as any).getBrandMixingRules(brand);
  
  return (validator as any).generateMixingSolution(
    targetShade,
    availableProducts,
    brand,
    line,
    brandMixingRules
  );
}

/**
 * Check if two color reflects can be mixed together
 */
export function checkReflectMixingCompatibility(
  reflect1: string,
  reflect2: string,
  brand?: string
): {
  compatible: boolean;
  warning?: string;
  expectedResult?: string;
  riskLevel: 'low' | 'medium' | 'high';
} {
  const validator = new FormulaValidator(brand || 'generic');
  const brandRules = (validator as any).getBrandMixingRules(brand || 'generic');
  
  const compatibility = (validator as any).checkReflectCompatibility(
    reflect1,
    reflect2,
    brandRules
  );
  
  let riskLevel: 'low' | 'medium' | 'high' = 'low';
  
  if (!compatibility.compatible) {
    riskLevel = 'high';
  } else if (compatibility.warning) {
    riskLevel = 'medium';
  }
  
  return {
    compatible: compatibility.compatible,
    warning: compatibility.warning,
    expectedResult: compatibility.resultReflect,
    riskLevel,
  };
}

/**
 * Get brand-specific mixing limitations and rules
 */
export function getBrandMixingLimitations(brand: string): {
  maxComponents: number;
  maxSpecialMixPercentage: number;
  restrictions: string[];
  recommendations: string[];
} {
  const validator = new FormulaValidator(brand);
  const rules = (validator as any).getBrandMixingRules(brand);
  const instructions = (validator as any).getBrandSpecificMixingInstructions(brand);
  
  return {
    maxComponents: rules.maxComponents,
    maxSpecialMixPercentage: rules.maxSpecialMixPercentage,
    restrictions: rules.restrictions,
    recommendations: instructions,
  };
}

/**
 * Analyze available shades and suggest possible mixing combinations
 */
export function analyzeAvailableMixingOptions(
  availableProducts: ProductAvailability[],
  brand: string,
  line: string,
  targetLevel?: number
): {
  possibleTargets: string[];
  compatibleCombinations: Array<{
    shade1: string;
    shade2: string;
    compatibilityScore: number;
    expectedResults: string[];
  }>;
  recommendations: string[];
} {
  const validator = new FormulaValidator(brand);
  const availableShades = (validator as any).getAvailableShades(availableProducts, brand, line);
  const brandRules = (validator as any).getBrandMixingRules(brand);
  
  const compatibleCombinations: any[] = [];
  const possibleTargets: string[] = [];
  
  // Analyze all possible two-shade combinations
  for (let i = 0; i < availableShades.length; i++) {
    for (let j = i + 1; j < availableShades.length; j++) {
      const shade1 = (validator as any).parseShade(availableShades[i]);
      const shade2 = (validator as any).parseShade(availableShades[j]);
      
      if (!shade1 || !shade2) continue;
      
      // Filter by target level if specified
      if (targetLevel && 
          Math.abs(shade1.level - targetLevel) > 1 && 
          Math.abs(shade2.level - targetLevel) > 1) {
        continue;
      }
      
      const compatibility = (validator as any).checkReflectCompatibility(
        shade1.primaryReflect,
        shade2.primaryReflect,
        brandRules
      );
      
      if (compatibility.compatible) {
        const compatibilityScore = compatibility.warning ? 0.7 : 1.0;
        
        compatibleCombinations.push({
          shade1: availableShades[i],
          shade2: availableShades[j],
          compatibilityScore,
          expectedResults: [
            `Nivel ${Math.round((shade1.level + shade2.level) / 2)}`,
            `Reflejo ${compatibility.resultReflect || 'mixto'}`,
          ],
        });
        
        // Generate possible target shades
        const avgLevel = Math.round((shade1.level + shade2.level) / 2);
        const resultReflect = compatibility.resultReflect || '0';
        possibleTargets.push(`${avgLevel}.${resultReflect}`);
      }
    }
  }
  
  // Remove duplicate targets
  const uniqueTargets = Array.from(new Set(possibleTargets));
  
  // Generate recommendations
  const recommendations = [
    `Con ${availableShades.length} tonos disponibles, puedes crear aproximadamente ${compatibleCombinations.length} mezclas diferentes`,
    `Las combinaciones más exitosas son entre tonos del mismo nivel`,
    `${brand} permite hasta ${brandRules.maxComponents} componentes por mezcla`,
  ];
  
  if (compatibleCombinations.length === 0) {
    recommendations.push('Considera conseguir tonos con reflejos más compatibles para ampliar opciones de mezcla');
  }
  
  return {
    possibleTargets: uniqueTargets,
    compatibleCombinations: compatibleCombinations.sort((a, b) => b.compatibilityScore - a.compatibilityScore),
    recommendations,
  };
}

/**
 * Calculate exact amounts needed for a mixing formula
 */
export function calculateMixingAmounts(
  mixingSolution: MixingSolution,
  totalAmount: number
): Array<{
  shade: string;
  amount: number;
  unit: string;
  percentage: number;
}> {
  return mixingSolution.components.map(component => ({
    shade: component.shade,
    amount: Math.round((totalAmount * component.percentage) / 100),
    unit: 'ml',
    percentage: component.percentage,
  }));
}

/**
 * Validate a proposed mixing formula before application
 */
export function validateMixingFormula(
  components: Array<{ shade: string; percentage: number }>,
  brand: string
): {
  valid: boolean;
  warnings: string[];
  errors: string[];
  suggestions: string[];
} {
  const warnings: string[] = [];
  const errors: string[] = [];
  const suggestions: string[] = [];
  
  // Check total percentage
  const totalPercentage = components.reduce((sum, comp) => sum + comp.percentage, 0);
  if (Math.abs(totalPercentage - 100) > 1) {
    errors.push(`Porcentaje total incorrecto: ${totalPercentage}% (debe ser 100%)`);
  }
  
  // Check brand limits
  const brandLimits = getBrandMixingLimitations(brand);
  if (components.length > brandLimits.maxComponents) {
    errors.push(`Demasiados componentes: ${components.length} (máximo para ${brand}: ${brandLimits.maxComponents})`);
  }
  
  // Check percentage ranges
  components.forEach(component => {
    if (component.percentage < 10) {
      warnings.push(`${component.shade}: ${component.percentage}% muy bajo, puede ser difícil de medir`);
    }
    if (component.percentage > 90) {
      warnings.push(`${component.shade}: ${component.percentage}% muy alto, dominará la mezcla`);
    }
  });
  
  // Check reflect compatibility
  if (components.length === 2) {
    const shade1 = components[0].shade.match(/\.(\d)/)?.[1];
    const shade2 = components[1].shade.match(/\.(\d)/)?.[1];
    
    if (shade1 && shade2) {
      const compatibility = checkReflectMixingCompatibility(shade1, shade2, brand);
      if (!compatibility.compatible) {
        errors.push(`Reflejos incompatibles: ${shade1} y ${shade2}`);
      } else if (compatibility.warning) {
        warnings.push(compatibility.warning);
      }
    }
  }
  
  // Generate suggestions
  if (components.length === 2 && Math.abs(components[0].percentage - 50) > 25) {
    suggestions.push('Para mejor control del resultado, considera proporciones más equilibradas (60/40 máximo)');
  }
  
  if (warnings.length === 0 && errors.length === 0) {
    suggestions.push('Realiza prueba mecha antes de la aplicación completa');
  }
  
  return {
    valid: errors.length === 0,
    warnings,
    errors,
    suggestions,
  };
}

/**
 * Quick helper to check if a shade can be achieved with available products
 */
export function canAchieveShade(
  targetShade: string,
  availableProducts: ProductAvailability[],
  brand: string,
  line: string
): {
  achievable: boolean;
  method: 'direct' | 'mixing' | 'alternative' | 'impossible';
  solution?: MixingSolution;
  alternatives?: string[];
} {
  const validator = new FormulaValidator(brand);
  
  // Check direct availability first
  const directlyAvailable = (validator as any).isShadeDirectlyAvailable(
    targetShade,
    availableProducts,
    brand,
    line
  );
  
  if (directlyAvailable) {
    return {
      achievable: true,
      method: 'direct',
    };
  }
  
  // Try mixing solution
  const mixingSolution = generateSmartMixingSolution(
    targetShade,
    availableProducts,
    brand,
    line
  );
  
  if (mixingSolution) {
    return {
      achievable: true,
      method: 'mixing',
      solution: mixingSolution,
    };
  }
  
  // Try creative alternatives
  const alternatives = (validator as any).suggestCreativeAlternatives(
    targetShade,
    availableProducts,
    brand,
    line
  );
  
  if (alternatives.length > 0) {
    return {
      achievable: true,
      method: 'alternative',
      alternatives,
    };
  }
  
  return {
    achievable: false,
    method: 'impossible',
  };
}