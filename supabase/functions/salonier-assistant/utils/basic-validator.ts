/**
 * BASIC VALIDATOR - Validaciones críticas simples y rápidas
 * 
 * Máximo 1 segundo de procesamiento, sin dependencias complejas
 * Solo validaciones que pueden prevenir errores graves
 */

interface BasicDiagnosis {
  current_level?: number;
  has_artificial_color?: boolean;
  hair_condition?: string;
  natural_base?: number;
}

interface BasicFormula {
  target_level?: number;
  process_type?: string;
  lightener?: boolean;
  oxidant_volume?: number;
  pre_pigmentation?: boolean;
  products?: Array<{ type?: string; brand?: string }>;
}

/**
 * Validador principal - MÁXIMO 1 segundo
 */
export function validateBasicRules(
  formula: BasicFormula,
  diagnosis: BasicDiagnosis,
  brand?: string
): string[] {
  const startTime = Date.now();
  const warnings: string[] = [];
  const MAX_PROCESSING_TIME = 1000; // 1 segundo límite estricto

  try {
    // Timeout protection - si toma mucho tiempo, salir
    if (Date.now() - startTime > MAX_PROCESSING_TIME) {
      return warnings;
    }

    // 1. "Color no levanta color" - CRÍTICO
    const colorWarning = checkColorLiftingRule(formula, diagnosis);
    if (colorWarning) warnings.push(colorWarning);

    // Timeout check
    if (Date.now() - startTime > MAX_PROCESSING_TIME) {
      return warnings;
    }

    // 2. Volumen de oxidante básico
    const oxidantWarning = checkOxidantVolume(formula, diagnosis);
    if (oxidantWarning) warnings.push(oxidantWarning);

    // Timeout check
    if (Date.now() - startTime > MAX_PROCESSING_TIME) {
      return warnings;
    }

    // 3. Pre-pigmentación necesaria
    const prePigWarning = checkPrePigmentation(formula, diagnosis);
    if (prePigWarning) warnings.push(prePigWarning);

    // Timeout check
    if (Date.now() - startTime > MAX_PROCESSING_TIME) {
      return warnings;
    }

    // 4. Proceso peligroso
    const dangerWarning = checkDangerousProcess(formula, diagnosis);
    if (dangerWarning) warnings.push(dangerWarning);

    // Timeout check
    if (Date.now() - startTime > MAX_PROCESSING_TIME) {
      return warnings;
    }

    // 5. Productos básicos faltantes
    const productWarning = checkMissingProducts(formula);
    if (productWarning) warnings.push(productWarning);

  } catch (error) {
    // En caso de error, devolver warnings actuales sin fallar
    console.warn('Basic validator error:', error);
  }

  return warnings;
}

/**
 * 1. Validación crítica: Color no levanta color
 */
function checkColorLiftingRule(formula: BasicFormula, diagnosis: BasicDiagnosis): string | null {
  const hasArtificialColor = diagnosis.has_artificial_color || false;
  const currentLevel = diagnosis.current_level || 0;
  const targetLevel = formula.target_level || 0;
  const levelDifference = targetLevel - currentLevel;

  // Regla fundamental: Color no levanta color
  if (hasArtificialColor && levelDifference > 0) {
    // Si quiere aclarar cabello con color artificial
    const hasLightener = formula.lightener || false;
    const hasHighVolume = (formula.oxidant_volume || 0) >= 30;

    if (!hasLightener && levelDifference >= 1) {
      return "🚨 CRÍTICO: Color no levanta color. Cabello con color artificial requiere decoloración previa para aclarar.";
    }

    if (levelDifference >= 2 && formula.oxidant_volume && formula.oxidant_volume <= 20) {
      return "⚠️ ADVERTENCIA: Para aclarar cabello con color artificial se necesita oxidante de 30 vol mínimo o decoloración.";
    }
  }

  // Advertencia para aclarados extremos en cabello natural
  if (!hasArtificialColor && levelDifference >= 4 && !formula.lightener) {
    return "⚠️ Para aclarar 4+ niveles en cabello natural se recomienda decoloración para mejores resultados.";
  }

  return null;
}

/**
 * 2. Validación de volumen de oxidante
 */
function checkOxidantVolume(formula: BasicFormula, diagnosis: BasicDiagnosis): string | null {
  const oxidantVolume = formula.oxidant_volume;
  const currentLevel = diagnosis.current_level || 0;
  const targetLevel = formula.target_level || 0;
  const levelDifference = targetLevel - currentLevel;

  if (!oxidantVolume) return null;

  // Reglas básicas de oxidante
  if (levelDifference <= 0 && oxidantVolume > 10) {
    return "⚠️ Para depositar color usa 10 vol. Volúmenes altos pueden dañar sin aclarar.";
  }

  if (levelDifference >= 1 && levelDifference <= 2 && oxidantVolume !== 20) {
    return "⚠️ Para aclarar 1-2 niveles usa 20 vol para mejores resultados.";
  }

  if (levelDifference >= 3 && oxidantVolume < 30) {
    return "⚠️ Para aclarar 3+ niveles necesitas 30 vol o decoloración.";
  }

  return null;
}

/**
 * 3. Validación de pre-pigmentación
 */
function checkPrePigmentation(formula: BasicFormula, diagnosis: BasicDiagnosis): string | null {
  const currentLevel = diagnosis.current_level || 0;
  const targetLevel = formula.target_level || 0;
  const levelDifference = targetLevel - currentLevel;
  const needsPrePig = formula.pre_pigmentation;

  // Si oscurece 3+ niveles y no tiene pre-pigmentación
  if (levelDifference <= -3 && !needsPrePig) {
    return "⚠️ Oscurecer 3+ niveles requiere pre-pigmentación para evitar que se deslave.";
  }

  return null;
}

/**
 * 4. Validación de procesos peligrosos
 */
function checkDangerousProcess(formula: BasicFormula, diagnosis: BasicDiagnosis): string | null {
  const currentLevel = diagnosis.current_level || 0;
  const targetLevel = formula.target_level || 0;
  const levelDifference = targetLevel - currentLevel;
  const hairCondition = diagnosis.hair_condition?.toLowerCase();

  // Aclarar 6+ niveles en una sesión
  if (levelDifference >= 6) {
    return "🚨 PELIGROSO: Aclarar 6+ niveles en una sesión puede causar rotura severa. Considera proceso en etapas.";
  }

  // Cabello dañado + proceso fuerte
  if ((hairCondition?.includes('dañado') || hairCondition?.includes('seco')) && levelDifference >= 4) {
    return "🚨 PELIGROSO: Cabello dañado no debe aclarar 4+ niveles. Riesgo alto de rotura.";
  }

  return null;
}

/**
 * 5. Validación de productos faltantes
 */
function checkMissingProducts(formula: BasicFormula): string | null {
  const products = formula.products || [];
  const needsLightener = formula.lightener || formula.target_level && formula.target_level >= 9;
  
  // Si necesita decolorar pero no hay decolorante
  if (needsLightener) {
    const hasLightener = products.some(p => 
      p.type?.toLowerCase().includes('decolor') || 
      p.type?.toLowerCase().includes('lightener') ||
      p.type?.toLowerCase().includes('polvo')
    );
    
    if (!hasLightener) {
      return "⚠️ Falta decolorante para alcanzar el nivel objetivo. Añade polvo decolorante.";
    }
  }

  return null;
}

/**
 * Función auxiliar: Verificar si el proceso es seguro
 */
export function isProcessSafe(currentLevel: number, targetLevel: number): { safe: boolean; reason?: string } {
  const levelDifference = targetLevel - currentLevel;
  
  // Proceso extremo
  if (levelDifference >= 6) {
    return { 
      safe: false, 
      reason: "Diferencia de 6+ niveles es peligrosa en una sesión" 
    };
  }
  
  // Proceso normal
  if (Math.abs(levelDifference) <= 3) {
    return { safe: true };
  }
  
  // Proceso intermedio - precaución
  return { 
    safe: true, 
    reason: "Proceso fuerte - evaluar condición del cabello" 
  };
}

/**
 * Función auxiliar: Verificar si necesita remoción de color
 */
export function needsColorRemoval(diagnosis: BasicDiagnosis, targetLevel: number): boolean {
  const hasArtificialColor = diagnosis.has_artificial_color || false;
  const currentLevel = diagnosis.current_level || 0;
  const levelDifference = targetLevel - currentLevel;
  
  // Si tiene color artificial y quiere aclarar 2+ niveles
  return hasArtificialColor && levelDifference >= 2;
}

/**
 * Convierte formulationData de IA al formato BasicFormula
 */
function convertFormulationToBasic(formulationData: any, diagnosis?: any, desiredResult?: any): BasicFormula {
  const basicFormula: BasicFormula = {
    products: []
  };

  // Extraer nivel objetivo - priorizar desiredResult
  if (desiredResult?.detectedLevel) {
    basicFormula.target_level = parseInt(desiredResult.detectedLevel);
  } else if (desiredResult?.level) {
    basicFormula.target_level = parseInt(desiredResult.level);
  } else if (diagnosis?.desiredColor?.level) {
    basicFormula.target_level = parseInt(diagnosis.desiredColor.level);
  } else if (diagnosis?.targetLevel) {
    basicFormula.target_level = parseInt(diagnosis.targetLevel);
  }

  // Detectar tipo de proceso basado en el título y pasos
  const formulaTitle = formulationData.formulaTitle?.toLowerCase() || '';
  const summary = formulationData.summary?.toLowerCase() || '';

  if (formulaTitle.includes('decolor') || summary.includes('decolor') ||
      formulaTitle.includes('aclar') || summary.includes('aclar')) {
    basicFormula.process_type = 'lightening';
  } else if (formulaTitle.includes('oscur') || summary.includes('oscur')) {
    basicFormula.process_type = 'darkening';
  } else {
    basicFormula.process_type = 'color_change';
  }

  // Extraer información de los pasos
  if (formulationData.steps && Array.isArray(formulationData.steps)) {
    for (const step of formulationData.steps) {
      if (step.mix && Array.isArray(step.mix)) {
        for (const product of step.mix) {
          // Detectar tipo de producto
          const productType = product.type?.toLowerCase() || '';
          const productName = product.productName?.toLowerCase() || '';

          // Detectar volumen de oxidante
          if (productType.includes('oxidante') || productType.includes('developer') ||
              productName.includes('vol') || productName.includes('oxidante')) {
            const volumeMatch = productName.match(/(\d+)\s*vol/i);
            if (volumeMatch) {
              basicFormula.oxidant_volume = parseInt(volumeMatch[1]);
            }
          }

          // Detectar decolorante
          if (productType.includes('decolor') || productType.includes('lightener') ||
              productType.includes('polvo') || productName.includes('decolor') ||
              productName.includes('lightener') || productName.includes('bleach')) {
            basicFormula.lightener = true;
          }

          // Detectar pre-pigmentación
          if (productName.includes('pre-pig') || productName.includes('prepig') ||
              step.stepTitle?.toLowerCase().includes('pre-pig') ||
              step.stepTitle?.toLowerCase().includes('prepigment')) {
            basicFormula.pre_pigmentation = true;
          }

          basicFormula.products?.push({
            type: productType,
            brand: product.brand
          });
        }
      }
    }
  }

  return basicFormula;
}

/**
 * Convierte diagnosis de IA al formato BasicDiagnosis
 */
function convertDiagnosisToBasic(diagnosis: any): BasicDiagnosis {
  const basicDiagnosis: BasicDiagnosis = {};

  // Extraer nivel actual - múltiples fuentes posibles
  if (diagnosis?.averageDepthLevel) {
    basicDiagnosis.current_level = parseInt(diagnosis.averageDepthLevel);
  } else if (diagnosis?.averageLevel) {
    basicDiagnosis.current_level = parseInt(diagnosis.averageLevel);
  } else if (diagnosis?.currentColor?.level) {
    basicDiagnosis.current_level = parseInt(diagnosis.currentColor.level);
  } else if (diagnosis?.level) {
    basicDiagnosis.current_level = parseInt(diagnosis.level);
  } else if (diagnosis?.zoneAnalysis?.roots?.level) {
    basicDiagnosis.current_level = parseInt(diagnosis.zoneAnalysis.roots.level);
  } else if (diagnosis?.natural_base) {
    basicDiagnosis.current_level = parseInt(diagnosis.natural_base);
  }

  // Detectar color artificial - usar estructura real del diagnóstico
  if (diagnosis?.hasArtificialColor !== undefined) {
    basicDiagnosis.has_artificial_color = diagnosis.hasArtificialColor;
  } else if (diagnosis?.zoneAnalysis?.roots?.state) {
    // Usar el estado de las raíces como indicador principal
    const rootState = diagnosis.zoneAnalysis.roots.state.toLowerCase();
    basicDiagnosis.has_artificial_color = rootState === 'teñido' ||
                                          rootState === 'colored' ||
                                          rootState === 'decolorado' ||
                                          rootState === 'bleached' ||
                                          rootState === 'procesado';
  } else if (diagnosis?.state) {
    // Fallback al estado general
    const state = diagnosis.state.toLowerCase();
    basicDiagnosis.has_artificial_color = state === 'teñido' ||
                                          state === 'colored' ||
                                          state === 'decolorado' ||
                                          state === 'bleached' ||
                                          state === 'procesado';
  } else if (diagnosis?.detectedChemicalProcess) {
    // Usar proceso químico detectado
    const process = diagnosis.detectedChemicalProcess.toLowerCase();
    basicDiagnosis.has_artificial_color = process.includes('color') ||
                                          process.includes('bleach') ||
                                          process.includes('tinte');
  } else if (diagnosis?.lastChemicalProcessType) {
    basicDiagnosis.has_artificial_color = diagnosis.lastChemicalProcessType !== 'none' &&
                                          diagnosis.lastChemicalProcessType !== 'natural';
  }

  // Condición del cabello
  if (diagnosis?.hairCondition) {
    basicDiagnosis.hair_condition = diagnosis.hairCondition;
  } else if (diagnosis?.condition) {
    basicDiagnosis.hair_condition = diagnosis.condition;
  } else if (diagnosis?.zoneAnalysis?.roots?.damage) {
    basicDiagnosis.hair_condition = diagnosis.zoneAnalysis.roots.damage;
  }

  // Base natural
  if (diagnosis?.naturalBase) {
    basicDiagnosis.natural_base = parseInt(diagnosis.naturalBase);
  } else if (diagnosis?.natural_base) {
    basicDiagnosis.natural_base = parseInt(diagnosis.natural_base);
  }

  return basicDiagnosis;
}

/**
 * Función de timeout - procesar con límite de tiempo estricto
 * Ahora acepta también desiredResult como parámetro separado
 */
export function validateWithTimeout(
  formulationData: any,
  diagnosis: any,
  brand?: string,
  timeoutMs: number = 1000,
  desiredResult?: any
): Promise<string[]> {
  return Promise.race([
    Promise.resolve(() => {
      try {
        // Convertir estructuras de IA a formato básico
        const basicFormula = convertFormulationToBasic(formulationData, diagnosis, desiredResult);
        const basicDiagnosis = convertDiagnosisToBasic(diagnosis);

        // Log para debugging
        console.log('🔍 BASIC VALIDATOR EXECUTING:', {
          basicFormula,
          basicDiagnosis,
          originalFormulation: formulationData?.formulaTitle,
          originalDiagnosis: {
            currentLevel: diagnosis?.averageDepthLevel || diagnosis?.averageLevel,
            rootState: diagnosis?.zoneAnalysis?.roots?.state,
            hasArtificial: basicDiagnosis.has_artificial_color
          },
          desiredLevel: desiredResult?.detectedLevel,
          levelDifference: (basicFormula.target_level || 0) - (basicDiagnosis.current_level || 0)
        });

        const warnings = validateBasicRules(basicFormula, basicDiagnosis, brand);

        console.log('🚨 BASIC VALIDATOR RESULTS:', {
          warningCount: warnings.length,
          warnings: warnings,
          formulaTitle: formulationData?.formulaTitle
        });

        return warnings;
      } catch (error) {
        console.warn('Error in validation conversion:', error);
        return [];
      }
    })(),
    new Promise<string[]>((_, reject) => {
      setTimeout(() => reject(new Error('Validation timeout')), timeoutMs);
    })
  ]).catch(() => {
    // Si hay timeout, devolver array vacío
    return [];
  });
}