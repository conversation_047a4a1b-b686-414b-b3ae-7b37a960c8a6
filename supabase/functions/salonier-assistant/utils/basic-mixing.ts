/**
 * BASIC MIXING SYSTEM - SAFE & SIMPLE
 * 
 * Sistema de mezclas básicas para tonos faltantes.
 * Solo mezclas seguras y probadas, con timeouts estrictos.
 * 
 * Reglas básicas:
 * - Misma base numérica (7.x + 7.y)
 * - Reflejos compatibles únicamente
 * - Máximo 2 tonos por mezcla
 * - Ratios 50/50 o simples (60/40, 70/30)
 */

interface MixSuggestion {
  tone1: string;
  tone2: string;
  ratio1: number; // Porcentaje del primer tono (0-100)
  ratio2: number; // Porcentaje del segundo tono (0-100)
  confidence: 'high' | 'medium' | 'low';
  explanation: string;
}

interface CompatibilityResult {
  compatible: boolean;
  reason: string;
}

// Tabla de compatibilidad de reflejos - LOOKUP SIMPLE
const REFLECTION_COMPATIBILITY: Record<string, string[]> = {
  '0': ['0'], // Natural con natural
  '1': ['1', '8'], // Ceniza con ceniza o perla
  '2': ['2'], // Irisado solo
  '3': ['3', '4', '6'], // Dorado con cobrizo y violeta
  '4': ['3', '4'], // Cobrizo con dorado
  '5': ['5'], // Caoba solo
  '6': ['3', '6'], // Violeta con dorado
  '7': ['7'], // Mate solo
  '8': ['1', '8'], // Perla con ceniza
  '9': ['9'], // Especiales solos
};

// Ratios básicos predefinidos - sin cálculos complejos
const BASIC_RATIOS = [
  { ratio1: 50, ratio2: 50, name: '50/50' },
  { ratio1: 60, ratio2: 40, name: '60/40' },
  { ratio1: 70, ratio2: 30, name: '70/30' },
  { ratio1: 40, ratio2: 60, name: '40/60' },
  { ratio1: 30, ratio2: 70, name: '30/70' },
];

/**
 * Extrae base y reflejo de un tono (ej: "7.43" → base: "7", reflejo: "43")
 */
function parseTone(tone: string): { base: string; reflection: string } | null {
  const match = tone.match(/^(\d+)\.?(\d*)$/);
  if (!match) return null;
  
  return {
    base: match[1],
    reflection: match[2] || '0'
  };
}

/**
 * Verifica si dos reflejos son compatibles para mezcla
 */
function areReflectionsCompatible(ref1: string, ref2: string): boolean {
  // Timeout básico - solo lookup
  const startTime = Date.now();
  if (Date.now() - startTime > 50) return false;
  
  // Si son iguales, siempre compatibles
  if (ref1 === ref2) return true;
  
  // Verificar en tabla de compatibilidad
  const compatible1 = REFLECTION_COMPATIBILITY[ref1[0]]?.includes(ref2[0]);
  const compatible2 = REFLECTION_COMPATIBILITY[ref2[0]]?.includes(ref1[0]);
  
  return compatible1 || compatible2 || false;
}

/**
 * Verifica si dos tonos pueden mezclarse de forma segura
 */
export function canMixTones(tone1: string, tone2: string): CompatibilityResult {
  const startTime = Date.now();
  
  try {
    // Timeout estricto
    if (Date.now() - startTime > 100) {
      return {
        compatible: false,
        reason: 'Timeout en análisis'
      };
    }
    
    // Parsear tonos
    const parsed1 = parseTone(tone1);
    const parsed2 = parseTone(tone2);
    
    if (!parsed1 || !parsed2) {
      return {
        compatible: false,
        reason: 'Formato de tono inválido'
      };
    }
    
    // Verificar misma base
    if (parsed1.base !== parsed2.base) {
      return {
        compatible: false,
        reason: `Bases diferentes (${parsed1.base} vs ${parsed2.base})`
      };
    }
    
    // Verificar compatibilidad de reflejos
    if (!areReflectionsCompatible(parsed1.reflection, parsed2.reflection)) {
      return {
        compatible: false,
        reason: `Reflejos incompatibles (${parsed1.reflection} + ${parsed2.reflection})`
      };
    }
    
    return {
      compatible: true,
      reason: 'Mezcla básica viable'
    };
    
  } catch (error) {
    return {
      compatible: false,
      reason: 'Error en análisis de compatibilidad'
    };
  }
}

/**
 * Calcula el ratio más apropiado para aproximar un tono objetivo
 */
function calculateMixRatio(tone1: string, tone2: string, targetTone: string): { ratio1: number; ratio2: number } {
  // Sistema ultra-simple: usar ratios predefinidos
  // En una implementación más avanzada, se haría cálculo basado en numerología colorimétrica
  
  const target = parseTone(targetTone);
  const t1 = parseTone(tone1);
  const t2 = parseTone(tone2);
  
  if (!target || !t1 || !t2) {
    return { ratio1: 50, ratio2: 50 }; // Fallback 50/50
  }
  
  // Si el reflejo objetivo está más cerca de uno de los tonos, dar más peso a ese
  const targetRef = parseInt(target.reflection[0] || '0');
  const ref1 = parseInt(t1.reflection[0] || '0');
  const ref2 = parseInt(t2.reflection[0] || '0');
  
  const diff1 = Math.abs(targetRef - ref1);
  const diff2 = Math.abs(targetRef - ref2);
  
  if (diff1 < diff2) {
    return { ratio1: 70, ratio2: 30 }; // Más del primer tono
  } else if (diff2 < diff1) {
    return { ratio1: 30, ratio2: 70 }; // Más del segundo tono
  }
  
  return { ratio1: 50, ratio2: 50 }; // Equilibrado
}

/**
 * Busca una mezcla básica para aproximar un tono objetivo
 */
export function suggestBasicMix(targetTone: string, availableTones: string[]): MixSuggestion | null {
  const startTime = Date.now();
  
  try {
    // Timeout estricto total
    if (Date.now() - startTime > 250) return null;
    
    const targetParsed = parseTone(targetTone);
    if (!targetParsed) return null;
    
    // Filtrar tonos de la misma base
    const sameBaseTones = availableTones.filter(tone => {
      const parsed = parseTone(tone);
      return parsed && parsed.base === targetParsed.base;
    });
    
    if (sameBaseTones.length < 2) return null;
    
    // Buscar la mejor combinación (solo primeras opciones por timeout)
    for (let i = 0; i < Math.min(sameBaseTones.length, 5); i++) {
      for (let j = i + 1; j < Math.min(sameBaseTones.length, 5); j++) {
        // Check timeout
        if (Date.now() - startTime > 200) break;
        
        const tone1 = sameBaseTones[i];
        const tone2 = sameBaseTones[j];
        
        const compatibility = canMixTones(tone1, tone2);
        if (!compatibility.compatible) continue;
        
        const ratio = calculateMixRatio(tone1, tone2, targetTone);
        
        // Determinar confianza basada en proximidad
        const confidence = getConfidenceLevel(tone1, tone2, targetTone);
        
        return {
          tone1,
          tone2,
          ratio1: ratio.ratio1,
          ratio2: ratio.ratio2,
          confidence,
          explanation: `Mezcla: ${ratio.ratio1}% ${tone1} + ${ratio.ratio2}% ${tone2} para aproximar ${targetTone}`
        };
      }
    }
    
    return null;
    
  } catch (error) {
    return null;
  }
}

/**
 * Determina el nivel de confianza de una mezcla
 */
function getConfidenceLevel(tone1: string, tone2: string, targetTone: string): 'high' | 'medium' | 'low' {
  const target = parseTone(targetTone);
  const t1 = parseTone(tone1);
  const t2 = parseTone(tone2);
  
  if (!target || !t1 || !t2) return 'low';
  
  // Si los reflejos están muy cerca del objetivo
  const targetRef = target.reflection;
  const hasCloseRef = t1.reflection === targetRef || t2.reflection === targetRef;
  
  if (hasCloseRef) return 'high';
  
  // Si los reflejos son adyacentes al objetivo
  const targetNum = parseInt(targetRef[0] || '0');
  const ref1Num = parseInt(t1.reflection[0] || '0');
  const ref2Num = parseInt(t2.reflection[0] || '0');
  
  const isAdjacent = Math.abs(targetNum - ref1Num) <= 1 || Math.abs(targetNum - ref2Num) <= 1;
  
  if (isAdjacent) return 'medium';
  
  return 'low';
}

/**
 * Función principal para obtener sugerencia de mezcla con explicación simple
 */
export function getMixingSuggestion(targetTone: string, availableTones: string[]): string {
  const startTime = Date.now();
  
  try {
    // Timeout total estricto
    if (Date.now() - startTime > 300) {
      return `Timeout: búsqueda de mezcla tomó demasiado tiempo`;
    }
    
    const suggestion = suggestBasicMix(targetTone, availableTones);
    
    if (!suggestion) {
      return `No se encontró mezcla viable para ${targetTone}. Buscar tono exacto recomendado.`;
    }
    
    const confidenceText = {
      'high': 'Recomendada',
      'medium': 'Aceptable',
      'low': 'Experimental'
    }[suggestion.confidence];
    
    return `${confidenceText}: ${suggestion.explanation}`;
    
  } catch (error) {
    return `Error al buscar mezcla para ${targetTone}. Usar tono exacto.`;
  }
}

// Función auxiliar para validar si una mezcla es segura antes de aplicar
export function validateMixSafety(tone1: string, tone2: string): boolean {
  const compatibility = canMixTones(tone1, tone2);
  return compatibility.compatible;
}