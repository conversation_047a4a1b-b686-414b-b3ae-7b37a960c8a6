/**
 * Salonier Assistant Edge Function - Bundled Version
 * Auto-generated from modular source files
 */

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// ============ From types.ts ============
/**
 * Type definitions for Salonier Assistant Edge Function
 */
// Request/Response types
interface AIRequest {
  task:
    | 'diagnose_image'
    | 'analyze_desired_look'
    | 'generate_formula'
    | 'convert_formula'
    | 'parse_product_text'
    | 'analyze_product';
  payload: Record<string, any>;
}
interface AIResponse {
  success: boolean;
  data?: any;
  error?: string;
  cached?: boolean;
  metrics?: {
    tokensUsed?: number;
    cost?: number;
    cacheHit?: boolean;
  };
}
// Cache types
interface CacheResult {
  hit: boolean;
  data?: any;
  savedTokens?: number;
  savedCost?: number;
}
interface CacheMetrics {
  totalHits: number;
  totalMisses: number;
  avgSavingsPerHit: number;
  mostCachedQueries: Array<{
    query: string;
    hits: number;
    lastHit: string;
  }>;
  totalSavedUSD: number;
  totalSavedTokens: number;
}
// Template types
type TemplateType = 'full' | 'optimized' | 'minimal';
interface TemplateContext {
  hasHistory: boolean;
  imageQuality: 'high' | 'medium' | 'low';
  userTier: 'free' | 'pro' | 'enterprise';
}
// Configuration types
interface RegionalConfig {
  volumeUnit: string;
  weightUnit: string;
  developerTerminology: string;
  colorTerminology: string;
  maxDeveloperVolume: number;
  currencySymbol: string;
  measurementSystem: 'metric' | 'imperial';
  decimalSeparator: string;
  thousandsSeparator: string;
  language: 'es' | 'en';
}
interface FormulaConfig {
  diagnosis: any;
  desiredResult: any;
  brand: string;
  line: string;
  clientHistory?: string;
  regionalConfig: RegionalConfig;
  selectedTechnique: string;
  customTechnique?: string;
}
// OpenAI types
interface OpenAIRequestOptions {
  model: 'gpt-4o' | 'gpt-4o-mini' | 'gpt-3.5-turbo';
  maxTokens: number;
  temperature: number;
  responseFormat?: { type: 'json_object' | 'text' };
  imageUrl?: string;
}
interface OpenAIResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}
// ============ From constants.ts ============
/**
 * Constants for Salonier Assistant Edge Function
 */
// Version control for cache invalidation
const PROMPT_VERSION = '1.0.0';
// Cache TTL configurations (in milliseconds)
const CACHE_TTL = {
  diagnosis: 30 * 24 * 60 * 60 * 1000, // 30 days - hair doesn't change quickly
  formula: 7 * 24 * 60 * 60 * 1000, // 7 days - formulas might be adjusted
  desired: 14 * 24 * 60 * 60 * 1000, // 14 days - balance between freshness and efficiency
  conversion: 30 * 24 * 60 * 60 * 1000, // 30 days - conversions are stable
  product: 90 * 24 * 60 * 60 * 1000, // 90 days - product parsing rarely changes
};
// CORS headers
const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  'Access-Control-Max-Age': '86400',
};
// OpenAI model pricing (per 1M tokens) - Updated 2025
const MODEL_PRICING = {
  'gpt-4o': { input: 2.5, output: 10.0 },
  'gpt-4o-mini': { input: 0.15, output: 0.6 },
  'gpt-3.5-turbo': { input: 0.5, output: 1.5 },
};
// Retry configuration
const RETRY_CONFIG = {
  maxAttempts: 3,
  initialDelay: 1000,
  backoffMultiplier: 2,
  maxDelay: 10000,
};
// Image size limits
const IMAGE_LIMITS = {
  maxSizeMB: 4,
  maxSizeBytes: 4 * 1024 * 1024,
  warningThresholdKB: 500,
};
// Template optimization targets
const OPTIMIZATION_TARGETS = {
  tokenReduction: {
    optimized: 0.3, // 30% reduction from full
    minimal: 0.5, // 50% reduction from full
  },
  defaultTemplate: {
    free: 'minimal' as const,
    pro: 'optimized' as const,
    enterprise: 'full' as const,
  },
};
// Response validation
const REQUIRED_FIELDS = {
  diagnosis: ['hairThickness', 'hairDensity', 'zoneAnalysis'],
  desiredLook: ['detectedLevel', 'detectedTone', 'detectedTechnique'],
  formula: ['formulaText'],
  conversion: ['convertedFormula'],
  product: ['brand', 'name', 'type'],
  analyze_product: ['brand', 'productType', 'category'],
};
// Error messages
const ERROR_MESSAGES = {
  noImage: 'No image provided',
  imageTooLarge: 'Image too large. Maximum size is 4MB',
  invalidBase64: 'Invalid image format',
  missingApiKey: 'OpenAI API key not configured',
  invalidResponse: 'Invalid response from OpenAI',
  timeout: 'Request timeout after 30 seconds',
  rateLimited: 'Rate limit exceeded. Please try again later.',
};
// ============ From helpers/image-validation.ts ============
/**
 * Image Validation Helper for Salonier Assistant
 */
interface ImageValidationResult {
  isValid: boolean;
  imageDataUrl: string;
  sizeKB: number;
  quality: 'high' | 'medium' | 'low';
  error?: string;
}
/**
 * Validates and prepares image for AI analysis
 */
function validateAndPrepareImage(payload: any): ImageValidationResult {
  const { imageUrl, imageBase64 } = payload;

  // Check if any image is provided
  if (!imageUrl && !imageBase64) {
    return {
      isValid: false,
      imageDataUrl: '',
      sizeKB: 0,
      quality: 'low',
      error: ERROR_MESSAGES.noImage,
    };
  }
  let imageDataUrl: string;
  let sizeKB = 0;
  // Handle base64 format
  if (imageBase64) {
    try {
      // Clean and validate base64
      const cleanBase64 = imageBase64.replace(/\s/g, '').replace(/\n/g, '');

      // Validate base64 format
      if (!/^[A-Za-z0-9+/]*={0,2}$/.test(cleanBase64)) {
        return {
          isValid: false,
          imageDataUrl: '',
          sizeKB: 0,
          quality: 'low',
          error: ERROR_MESSAGES.invalidBase64,
        };
      }
      // Calculate size
      const base64SizeInBytes = (cleanBase64.length * 3) / 4;
      sizeKB = base64SizeInBytes / 1024;
      // Check size limit
      if (base64SizeInBytes > IMAGE_LIMITS.maxSizeBytes) {
        return {
          isValid: false,
          imageDataUrl: '',
          sizeKB,
          quality: 'low',
          error: ERROR_MESSAGES.imageTooLarge,
        };
      }
      imageDataUrl = `data:image/jpeg;base64,${cleanBase64}`;
    } catch (e) {
      console.error('Invalid base64:', e);
      return {
        isValid: false,
        imageDataUrl: '',
        sizeKB: 0,
        quality: 'low',
        error: ERROR_MESSAGES.invalidBase64,
      };
    }
  } else {
    // Use URL directly
    imageDataUrl = imageUrl;
    // For URLs, we can't easily calculate size, so estimate based on typical sizes
    sizeKB = 500; // Default estimate
  }
  // Determine image quality based on size
  let quality: 'high' | 'medium' | 'low';
  if (sizeKB < 100) {
    quality = 'low'; // Very small images are usually low quality
  } else if (sizeKB < IMAGE_LIMITS.warningThresholdKB) {
    quality = 'medium';
  } else {
    quality = 'high';
  }
  // Log size info for monitoring
  console.log(`Image validation: ${sizeKB.toFixed(2)} KB, quality: ${quality}`);
  return {
    isValid: true,
    imageDataUrl,
    sizeKB,
    quality,
  };
}
/**
 * Estimates image quality based on various factors
 */
function estimateImageQuality(
  sizeKB: number,
  dimensions?: { width: number; height: number }
): 'high' | 'medium' | 'low' {
  // Size-based quality estimation
  if (sizeKB < 50) return 'low';
  if (sizeKB > 1000) return 'high';

  // If we have dimensions, use them for better estimation
  if (dimensions) {
    const pixels = dimensions.width * dimensions.height;
    const compressionRatio = (sizeKB * 1024) / pixels;

    // Higher compression ratio means lower quality
    if (compressionRatio < 0.1) return 'low';
    if (compressionRatio > 0.3) return 'high';
  }

  return 'medium';
}
/**
 * Prepares image data URL ensuring proper format
 */
function prepareImageDataUrl(base64: string, mimeType: string = 'image/jpeg'): string {
  // Remove any existing data URL prefix
  const cleanBase64 = base64.replace(/^data:image\/[a-z]+;base64,/, '');

  // Return properly formatted data URL
  return `data:${mimeType};base64,${cleanBase64}`;
}
/**
 * Validates if string is valid base64
 */
function isValidBase64(str: string): boolean {
  try {
    // Check if string matches base64 pattern
    if (!/^[A-Za-z0-9+/]*={0,2}$/.test(str)) {
      return false;
    }

    // Try to decode to verify it's valid
    atob(str);
    return true;
  } catch (e) {
    return false;
  }
}
/**
 * Extract image metadata from base64 if possible
 */
function extractImageMetadata(base64: string): {
  format?: string;
  sizeKB: number;
  isValid: boolean;
} {
  try {
    const sizeKB = (base64.length * 3) / 4 / 1024;

    // Try to detect format from base64 header
    let format: string | undefined;
    if (base64.startsWith('/9j/')) format = 'jpeg';
    else if (base64.startsWith('iVBORw')) format = 'png';
    else if (base64.startsWith('R0lGOD')) format = 'gif';

    return {
      format,
      sizeKB,
      isValid: true,
    };
  } catch (e) {
    return {
      sizeKB: 0,
      isValid: false,
    };
  }
}
// ============ From helpers/openai-client.ts ============
/**
 * OpenAI Client Helper with Retry Logic
 */
class OpenAIClient {
  private static apiKey: string;
  /**
   * Initialize with API key
   */
  static initialize(apiKey: string) {
    this.apiKey = apiKey;
  }
  /**
   * Call OpenAI with automatic retry and error handling
   */
  static async callWithRetry(
    prompt: string,
    options: OpenAIRequestOptions
  ): Promise<{
    response: any;
    tokensUsed: number;
    cost: number;
  }> {
    if (!this.apiKey) {
      throw new Error(ERROR_MESSAGES.missingApiKey);
    }
    let lastError: any;

    for (let attempt = 1; attempt <= RETRY_CONFIG.maxAttempts; attempt++) {
      try {
        console.log(`OpenAI API call attempt ${attempt}/${RETRY_CONFIG.maxAttempts}`);

        const response = await this.makeRequest(prompt, options);

        // Validate response
        if (!response.choices?.[0]?.message?.content) {
          throw new Error(ERROR_MESSAGES.invalidResponse);
        }
        // Parse JSON if expected
        let parsedContent = response.choices[0].message.content;
        if (options.responseFormat?.type === 'json_object') {
          try {
            parsedContent = JSON.parse(parsedContent);
          } catch (e) {
            console.error('Failed to parse JSON response:', e);
            throw new Error('Invalid JSON in AI response');
          }
        }
        // Calculate cost
        const cost = this.calculateCost(
          options.model,
          response.usage.prompt_tokens,
          response.usage.completion_tokens
        );
        return {
          response: parsedContent,
          tokensUsed: response.usage.total_tokens,
          cost,
        };
      } catch (error: any) {
        lastError = error;
        console.error(`OpenAI API error (attempt ${attempt}):`, error.message);
        // Handle specific error types
        if (error.status === 429 || error.message?.includes('rate limit')) {
          // Rate limit - exponential backoff
          const delay = Math.min(
            RETRY_CONFIG.initialDelay * Math.pow(RETRY_CONFIG.backoffMultiplier, attempt - 1),
            RETRY_CONFIG.maxDelay
          );
          console.log(`Rate limited. Waiting ${delay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
        // Don't retry on certain errors
        if (
          error.status === 401 || // Invalid API key
          error.status === 400 || // Bad request
          error.message?.includes('Invalid API key')
        ) {
          throw error;
        }
        // For other errors, retry with delay
        if (attempt < RETRY_CONFIG.maxAttempts) {
          const delay = RETRY_CONFIG.initialDelay * attempt;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    // All retries failed
    throw lastError || new Error('Max retry attempts reached');
  }
  /**
   * Make the actual API request
   */
  private static async makeRequest(
    prompt: string,
    options: OpenAIRequestOptions
  ): Promise<OpenAIResponse> {
    const messages = [
      {
        role: 'user',
        content: options.imageUrl
          ? [
              { type: 'text', text: prompt },
              { type: 'image_url', image_url: { url: options.imageUrl } },
            ]
          : prompt,
      },
    ];
    const requestBody: any = {
      model: options.model,
      messages,
      max_tokens: options.maxTokens,
      temperature: options.temperature,
    };
    // Add response format if specified
    if (options.responseFormat) {
      requestBody.response_format = options.responseFormat;
    }
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      if (response.status === 401) {
        throw new Error(ERROR_MESSAGES.missingApiKey);
      } else if (response.status === 429) {
        throw new Error(ERROR_MESSAGES.rateLimited);
      } else {
        throw {
          status: response.status,
          message: errorData.error?.message || response.statusText,
        };
      }
    }
    return await response.json();
  }
  /**
   * Calculate cost for the API call
   */
  private static calculateCost(model: string, inputTokens: number, outputTokens: number): number {
    const pricing = MODEL_PRICING[model as keyof typeof MODEL_PRICING] || MODEL_PRICING['gpt-4o'];
    const inputCost = (inputTokens / 1_000_000) * pricing.input;
    const outputCost = (outputTokens / 1_000_000) * pricing.output;
    return inputCost + outputCost;
  }
  /**
   * Estimate tokens for a prompt (rough estimate)
   */
  static estimateTokens(text: string): number {
    // Rough estimate: 1 token ≈ 4 characters for English
    // Adjust for Spanish/special characters
    const charCount = text.length;
    const wordCount = text.split(/\s+/).length;

    // Use a weighted average
    const charEstimate = charCount / 4;
    const wordEstimate = wordCount * 1.3;

    return Math.ceil((charEstimate + wordEstimate) / 2);
  }
  /**
   * Validate API key format
   */
  static isValidApiKey(apiKey: string): boolean {
    // OpenAI API keys start with 'sk-' and are 51 characters long
    return apiKey.startsWith('sk-') && apiKey.length > 40;
  }
}
// ============ From utils/response-validator.ts ============
/**
 * Response Validator for AI responses
 */
/**
 * Validates AI response has required fields
 */
function validateAIResponse(
  taskType: string,
  response: any
): { isValid: boolean; missingFields: string[] } {
  const requiredFields = REQUIRED_FIELDS[taskType as keyof typeof REQUIRED_FIELDS] || [];
  const missingFields: string[] = [];
  for (const field of requiredFields) {
    if (!hasNestedProperty(response, field)) {
      missingFields.push(field);
    }
  }
  return {
    isValid: missingFields.length === 0,
    missingFields,
  };
}
/**
 * Check if object has nested property (supports dot notation)
 */
function hasNestedProperty(obj: any, path: string): boolean {
  const parts = path.split('.');
  let current = obj;
  for (const part of parts) {
    if (current === null || current === undefined || !(part in current)) {
      return false;
    }
    current = current[part];
  }
  return true;
}
/**
 * Ensures response maintains backward compatibility
 */
function ensureBackwardCompatibility(taskType: string, response: any): any {
  // Ensure all expected fields exist, even if null
  switch (taskType) {
    case 'diagnose_image':
      return {
        hairThickness: response.hairThickness || 'Medio',
        hairDensity: response.hairDensity || 'Media',
        overallTone: response.overallTone || '',
        overallUndertone: response.overallUndertone || 'Neutro',
        averageDepthLevel: response.averageDepthLevel || 5,
        zoneAnalysis: ensureZoneAnalysis(response.zoneAnalysis),
        detectedChemicalProcess: response.detectedChemicalProcess || null,
        estimatedLastProcessDate: response.estimatedLastProcessDate || null,
        detectedRisks: response.detectedRisks || {
          metallic: false,
          henna: false,
          damaged: false,
          overProcessed: false,
          incompatibleProducts: false,
        },
        serviceComplexity: response.serviceComplexity || 'medium',
        estimatedTime: response.estimatedTime || 120,
        overallCondition: response.overallCondition || '',
        recommendations: response.recommendations || [],
        overallConfidence: response.overallConfidence || 80,
        ...response, // Include any additional fields
      };
    case 'analyze_desired_look':
      return {
        detectedLevel: response.detectedLevel || 7,
        detectedTone: response.detectedTone || '',
        detectedTechnique: response.detectedTechnique || 'full_color',
        detectedTones: response.detectedTones || [],
        viabilityScore: response.viabilityScore || 50,
        estimatedSessions: response.estimatedSessions || 1,
        requiredProcesses: response.requiredProcesses || [],
        confidence: response.confidence || 80,
        warnings: response.warnings || [],
        ...response,
      };
    default:
      return response;
  }
}
/**
 * Ensure zone analysis has all required fields
 */
function ensureZoneAnalysis(zoneAnalysis: any): any {
  const defaultZone = {
    depth: 5,
    tone: '',
    undertone: 'Neutro',
    percentage: 33,
    state: 'Natural',
    unwantedTone: null,
    grayPercentage: 0,
    grayType: null,
    grayPattern: null,
    cuticleState: 'Cerrada',
    damage: 'Ninguno',
    elasticity: 'Buena',
    porosity: 'Media',
    resistance: 'Media',
  };
  return {
    roots: { ...defaultZone, ...zoneAnalysis?.roots },
    mids: { ...defaultZone, ...zoneAnalysis?.mids },
    ends: { ...defaultZone, ...zoneAnalysis?.ends },
  };
}
/**
 * Sanitize response to remove any sensitive data
 */
function sanitizeResponse(response: any): any {
  // Remove any fields that might contain sensitive data
  const sanitized = { ...response };

  // Remove internal fields
  delete sanitized._raw;
  delete sanitized._debug;
  delete sanitized._internal;

  return sanitized;
}
// ============ From utils/cache-manager.ts ============
/**
 * Cache Manager with Metrics for Salonier Assistant
 */
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
class CacheManager {
  private static metrics: CacheMetrics = {
    totalHits: 0,
    totalMisses: 0,
    avgSavingsPerHit: 0,
    mostCachedQueries: [],
    totalSavedUSD: 0,
    totalSavedTokens: 0,
  };

  private static supabase: any;
  private static metricsLastSaved = 0;
  private static readonly METRICS_SAVE_INTERVAL = 5 * 60 * 1000; // 5 minutes
  /**
   * Initialize cache manager with Supabase client
   */
  static initialize(supabaseUrl: string, supabaseKey: string) {
    this.supabase = createClient(supabaseUrl, supabaseKey);
    this.loadMetrics();
  }
  /**
   * Generate cache key with version
   */
  static generateCacheKey(task: string, payload: any): string {
    const normalized = JSON.stringify(
      {
        ...payload,
        _version: PROMPT_VERSION,
        _task: task,
      },
      Object.keys(payload).sort()
    );

    const encoder = new TextEncoder();
    const data = encoder.encode(normalized);
    return btoa(String.fromCharCode(...new Uint8Array(data)));
  }
  /**
   * Check cache with metrics tracking
   */
  static async checkCache(salonId: string, task: string, inputHash: string): Promise<CacheResult> {
    try {
      const { data, error } = await this.supabase
        .from('ai_analysis_cache')
        .select('result, tokens_used, cost_usd, model_used')
        .eq('salon_id', salonId)
        .eq('analysis_type', task)
        .eq('input_hash', inputHash)
        .gte('expires_at', new Date().toISOString())
        .single();
      if (!error && data) {
        // Cache hit - update metrics
        this.metrics.totalHits++;
        const savedTokens = data.tokens_used || 0;
        const savedCost = data.cost_usd || 0;

        this.metrics.totalSavedTokens += savedTokens;
        this.metrics.totalSavedUSD += savedCost;
        this.metrics.avgSavingsPerHit = this.metrics.totalSavedUSD / this.metrics.totalHits;
        // Update most cached queries
        this.updateMostCachedQueries(task);

        // Save metrics periodically
        this.saveMetricsIfNeeded();
        return {
          hit: true,
          data: data.result,
          savedTokens,
          savedCost,
        };
      }
      // Cache miss
      this.metrics.totalMisses++;
      this.saveMetricsIfNeeded();

      return { hit: false };
    } catch (error) {
      console.error('Cache check error:', error);
      return { hit: false };
    }
  }
  /**
   * Save to cache with TTL based on task type
   */
  static async saveToCache(
    salonId: string,
    task: string,
    inputHash: string,
    inputData: any,
    result: any,
    model: string,
    tokensUsed: number,
    costUsd: number
  ): Promise<void> {
    try {
      const ttl = CACHE_TTL[task as keyof typeof CACHE_TTL] || CACHE_TTL.formula;
      const expiresAt = new Date(Date.now() + ttl);
      await this.supabase.from('ai_analysis_cache').upsert({
        salon_id: salonId,
        analysis_type: task,
        input_hash: inputHash,
        input_data: inputData,
        result: result,
        model_used: model,
        tokens_used: tokensUsed,
        cost_usd: costUsd,
        prompt_version: PROMPT_VERSION,
        created_at: new Date().toISOString(),
        expires_at: expiresAt.toISOString(),
      });
    } catch (error) {
      console.error('Cache save error:', error);
    }
  }
  /**
   * Calculate cost savings
   */
  static calculateSavings(tokensUsed: number, model: string): number {
    const pricing = MODEL_PRICING[model as keyof typeof MODEL_PRICING] || MODEL_PRICING['gpt-4o'];
    // Assume 70/30 split for input/output tokens (typical for our use case)
    const inputTokens = Math.floor(tokensUsed * 0.7);
    const outputTokens = tokensUsed - inputTokens;

    const inputCost = (inputTokens / 1_000_000) * pricing.input;
    const outputCost = (outputTokens / 1_000_000) * pricing.output;

    return inputCost + outputCost;
  }
  /**
   * Get current metrics
   */
  static getMetrics(): CacheMetrics {
    const hitRate =
      this.metrics.totalHits + this.metrics.totalMisses > 0
        ? (this.metrics.totalHits / (this.metrics.totalHits + this.metrics.totalMisses)) * 100
        : 0;
    return {
      ...this.metrics,
      hitRate: Math.round(hitRate * 10) / 10, // Round to 1 decimal
    } as CacheMetrics & { hitRate: number };
  }
  /**
   * Clear old cache entries
   */
  static async clearExpiredCache(salonId?: string): Promise<number> {
    try {
      let query = this.supabase
        .from('ai_analysis_cache')
        .delete()
        .lt('expires_at', new Date().toISOString());

      if (salonId) {
        query = query.eq('salon_id', salonId);
      }
      const { data, error } = await query.select('id');

      if (error) throw error;

      return data?.length || 0;
    } catch (error) {
      console.error('Clear cache error:', error);
      return 0;
    }
  }
  /**
   * Update most cached queries tracking
   */
  private static updateMostCachedQueries(task: string) {
    const existing = this.metrics.mostCachedQueries.find(q => q.query === task);

    if (existing) {
      existing.hits++;
      existing.lastHit = new Date().toISOString();
    } else {
      this.metrics.mostCachedQueries.push({
        query: task,
        hits: 1,
        lastHit: new Date().toISOString(),
      });
    }
    // Keep only top 10 most cached
    this.metrics.mostCachedQueries.sort((a, b) => b.hits - a.hits);
    this.metrics.mostCachedQueries = this.metrics.mostCachedQueries.slice(0, 10);
  }
  /**
   * Load metrics from database
   */
  private static async loadMetrics() {
    try {
      const { data } = await this.supabase.from('cache_metrics').select('*').single();

      if (data) {
        this.metrics = data.metrics;
      }
    } catch (error) {
      // Metrics table might not exist or be empty - that's ok
      console.log('No existing metrics found, starting fresh');
    }
  }
  /**
   * Save metrics if needed
   */
  private static async saveMetricsIfNeeded() {
    const now = Date.now();
    if (now - this.metricsLastSaved < this.METRICS_SAVE_INTERVAL) {
      return;
    }
    try {
      await this.supabase.from('cache_metrics').upsert({
        id: 1, // Single row for global metrics
        metrics: this.metrics,
        updated_at: new Date().toISOString(),
      });

      this.metricsLastSaved = now;
    } catch (error) {
      console.error('Save metrics error:', error);
    }
  }
  /**
   * Get cache statistics for a specific salon
   */
  static async getSalonCacheStats(salonId: string): Promise<{
    totalEntries: number;
    totalSizeMB: number;
    oldestEntry: string | null;
    newestEntry: string | null;
  }> {
    try {
      const { data, error } = await this.supabase
        .from('ai_analysis_cache')
        .select('created_at, input_data, result')
        .eq('salon_id', salonId)
        .order('created_at', { ascending: true });
      if (error) throw error;
      const totalEntries = data?.length || 0;
      const totalSizeBytes =
        data?.reduce((sum, entry) => {
          const size =
            JSON.stringify(entry.input_data).length + JSON.stringify(entry.result).length;
          return sum + size;
        }, 0) || 0;
      return {
        totalEntries,
        totalSizeMB: Math.round((totalSizeBytes / 1024 / 1024) * 100) / 100,
        oldestEntry: data?.[0]?.created_at || null,
        newestEntry: data?.[data.length - 1]?.created_at || null,
      };
    } catch (error) {
      console.error('Get salon cache stats error:', error);
      return {
        totalEntries: 0,
        totalSizeMB: 0,
        oldestEntry: null,
        newestEntry: null,
      };
    }
  }
}
// ============ From utils/prompt-templates.ts ============
/**
 * Prompt Templates System for Salonier Assistant
 * Manages all AI prompts with optimization levels
 */
class PromptTemplates {
  /**
   * Selects optimal template based on context
   */
  static selectOptimalTemplate(context: TemplateContext): TemplateType {
    // Enterprise always gets full analysis
    if (context.userTier === 'enterprise') return 'full';

    // High quality images can use optimized templates
    if (context.imageQuality === 'high') {
      return context.userTier === 'pro' ? 'optimized' : 'minimal';
    }

    // Low quality images need more detailed prompts
    if (context.imageQuality === 'low') {
      return context.userTier === 'pro' ? 'full' : 'optimized';
    }

    // Default based on tier
    return OPTIMIZATION_TARGETS.defaultTemplate[context.userTier];
  }
  /**
   * Get diagnosis prompt with optimization level
   */
  static getDiagnosisPrompt(template: TemplateType = 'full', lang: 'es' | 'en' = 'es'): string {
    const prompts = {
      es: {
        full: `Eres un experto colorista profesional analizando el cabello de un cliente.
  
Analiza la imagen proporcionada y devuelve un análisis COMPLETO en formato JSON con EXACTAMENTE esta estructura:
{
  "hairThickness": "Fino|Medio|Grueso",
  "hairDensity": "Baja|Media|Alta",
  "overallTone": "nombre del tono general",
  "overallUndertone": "Frío|Cálido|Neutro",
  "averageDepthLevel": número decimal (1-10),
  "zoneAnalysis": {
    "roots": {
      "depth": número decimal (1-10),
      "tone": "tono específico",
      "undertone": "Frío|Cálido|Neutro",
      "percentage": número (0-100),
      "state": "Natural|Procesado|Decolorado|Teñido",
      "unwantedTone": "Verde|Naranja|Amarillo|Rojo|Violeta|null",
      "grayPercentage": número (0-100),
      "grayType": "Blanco|Gris|Mixto|null",
      "grayPattern": "Uniforme|Localizado|Disperso|null",
      "cuticleState": "Cerrada|Abierta|Dañada",
      "damage": "Ninguno|Leve|Moderado|Severo",
      "elasticity": "Buena|Regular|Mala",
      "porosity": "Baja|Media|Alta",
      "resistance": "Fuerte|Media|Débil"
    },
    "mids": { /* misma estructura */ },
    "ends": { /* misma estructura */ }
  },
  "detectedChemicalProcess": "Coloración|Decoloración|Permanente|Alisado|Ninguno|null",
  "estimatedLastProcessDate": "texto descriptivo",
  "detectedRisks": { 
    "metallic": boolean, 
    "henna": boolean, 
    "damaged": boolean,
    "overProcessed": boolean,
    "incompatibleProducts": boolean
  },
  "serviceComplexity": "simple|medium|complex",
  "estimatedTime": minutos estimados,
  "overallCondition": "descripción detallada",
  "recommendations": ["lista de al menos 3 recomendaciones específicas"],
  "overallConfidence": porcentaje (0-100)
}
IMPORTANTE: Analiza CADA zona por separado con TODOS los campos.`,
        optimized: `Experto colorista analizando cabello. Devuelve JSON:
{
  "hairThickness": "Fino|Medio|Grueso",
  "hairDensity": "Baja|Media|Alta", 
  "overallTone": "tono",
  "overallUndertone": "Frío|Cálido|Neutro",
  "averageDepthLevel": 1-10,
  "zoneAnalysis": {
    "roots": {
      "depth": 1-10,
      "tone": "tono",
      "state": "Natural|Procesado|Decolorado|Teñido",
      "grayPercentage": 0-100,
      "damage": "Ninguno|Leve|Moderado|Severo",
      "porosity": "Baja|Media|Alta"
    },
    "mids": { /* igual */ },
    "ends": { /* igual */ }
  },
  "detectedChemicalProcess": "tipo o null",
  "detectedRisks": { "metallic": bool, "henna": bool, "damaged": bool },
  "serviceComplexity": "simple|medium|complex",
  "overallCondition": "descripción",
  "recommendations": ["3 recomendaciones"],
  "overallConfidence": 0-100
}`,
        minimal: `Analiza cabello, JSON exacto:
{"hairThickness":"Fino|Medio|Grueso","hairDensity":"Baja|Media|Alta","overallTone":"tono","averageDepthLevel":1-10,"zoneAnalysis":{"roots":{"depth":1-10,"tone":"tono","state":"Natural|Procesado|Decolorado|Teñido","grayPercentage":0-100},"mids":{},"ends":{}},"serviceComplexity":"simple|medium|complex","overallCondition":"desc","recommendations":["3 items"]}`,
      },

      en: {
        full: `You are an expert colorist analyzing a client's hair.
Analyze the image and return a COMPLETE JSON analysis with EXACTLY this structure:
{
  "hairThickness": "Fine|Medium|Thick",
  "hairDensity": "Low|Medium|High",
  "overallTone": "overall tone name",
  "overallUndertone": "Cool|Warm|Neutral",
  "averageDepthLevel": decimal number (1-10),
  "zoneAnalysis": {
    "roots": {
      "depth": decimal (1-10),
      "tone": "specific tone",
      "undertone": "Cool|Warm|Neutral",
      "percentage": number (0-100),
      "state": "Natural|Processed|Bleached|Colored",
      "unwantedTone": "Green|Orange|Yellow|Red|Violet|null",
      "grayPercentage": number (0-100),
      "grayType": "White|Gray|Mixed|null",
      "grayPattern": "Uniform|Localized|Scattered|null",
      "cuticleState": "Closed|Open|Damaged",
      "damage": "None|Light|Moderate|Severe",
      "elasticity": "Good|Fair|Poor",
      "porosity": "Low|Medium|High",
      "resistance": "Strong|Medium|Weak"
    },
    "mids": { /* same structure */ },
    "ends": { /* same structure */ }
  },
  "detectedChemicalProcess": "Coloring|Bleaching|Perm|Straightening|None|null",
  "estimatedLastProcessDate": "descriptive text",
  "detectedRisks": {
    "metallic": boolean,
    "henna": boolean,
    "damaged": boolean,
    "overProcessed": boolean,
    "incompatibleProducts": boolean
  },
  "serviceComplexity": "simple|medium|complex",
  "estimatedTime": estimated minutes,
  "overallCondition": "detailed description",
  "recommendations": ["list of at least 3 specific recommendations"],
  "overallConfidence": percentage (0-100)
}`,
        optimized: `Hair expert analysis. Return JSON:
{
  "hairThickness": "Fine|Medium|Thick",
  "hairDensity": "Low|Medium|High",
  "overallTone": "tone",
  "overallUndertone": "Cool|Warm|Neutral",
  "averageDepthLevel": 1-10,
  "zoneAnalysis": {
    "roots": {
      "depth": 1-10,
      "tone": "tone",
      "state": "Natural|Processed|Bleached|Colored",
      "grayPercentage": 0-100,
      "damage": "None|Light|Moderate|Severe",
      "porosity": "Low|Medium|High"
    },
    "mids": { /* same */ },
    "ends": { /* same */ }
  },
  "detectedChemicalProcess": "type or null",
  "detectedRisks": { "metallic": bool, "henna": bool, "damaged": bool },
  "serviceComplexity": "simple|medium|complex",
  "overallCondition": "description",
  "recommendations": ["3 recommendations"],
  "overallConfidence": 0-100
}`,
        minimal: `Analyze hair, exact JSON:
{"hairThickness":"Fine|Medium|Thick","hairDensity":"Low|Medium|High","overallTone":"tone","averageDepthLevel":1-10,"zoneAnalysis":{"roots":{"depth":1-10,"tone":"tone","state":"Natural|Processed|Bleached|Colored","grayPercentage":0-100},"mids":{},"ends":{}},"serviceComplexity":"simple|medium|complex","overallCondition":"desc","recommendations":["3 items"]}`,
      },
    };
    return prompts[lang][template];
  }
  /**
   * Get desired look analysis prompt
   */
  static getDesiredLookPrompt(
    currentLevel: number,
    template: TemplateType = 'full',
    lang: 'es' | 'en' = 'es'
  ): string {
    const prompts = {
      es: {
        full: `Analiza esta imagen de referencia de color de cabello deseado.
  
Considerando que el nivel actual del cliente es ${currentLevel}, proporciona un análisis en formato JSON:
{
  "detectedLevel": número decimal del nivel objetivo,
  "detectedTone": "tono principal detectado",
  "detectedTechnique": "técnica de aplicación detectada",
  "detectedTones": ["lista de tonos presentes"],
  "viabilityScore": 0-100,
  "estimatedSessions": número de sesiones necesarias,
  "requiredProcesses": ["procesos necesarios"],
  "confidence": porcentaje de confianza,
  "warnings": ["advertencias si las hay"]
}`,
        optimized: `Analiza color deseado. Nivel actual: ${currentLevel}. JSON:
{
  "detectedLevel": nivel objetivo,
  "detectedTone": "tono principal",
  "detectedTechnique": "técnica",
  "detectedTones": ["tonos"],
  "viabilityScore": 0-100,
  "estimatedSessions": número,
  "requiredProcesses": ["procesos"],
  "confidence": 0-100
}`,
        minimal: `Color deseado, nivel actual:${currentLevel}. JSON:
{"detectedLevel":num,"detectedTone":"tono","detectedTechnique":"técnica","detectedTones":["array"],"viabilityScore":0-100,"estimatedSessions":num,"requiredProcesses":["array"],"confidence":0-100}`,
      },

      en: {
        full: `Analyze this desired hair color reference image.
Considering the client's current level is ${currentLevel}, provide a JSON analysis:
{
  "detectedLevel": decimal number of target level,
  "detectedTone": "main detected tone",
  "detectedTechnique": "detected application technique",
  "detectedTones": ["list of present tones"],
  "viabilityScore": 0-100,
  "estimatedSessions": number of sessions needed,
  "requiredProcesses": ["required processes"],
  "confidence": confidence percentage,
  "warnings": ["warnings if any"]
}`,
        optimized: `Analyze desired color. Current level: ${currentLevel}. JSON:
{
  "detectedLevel": target level,
  "detectedTone": "main tone",
  "detectedTechnique": "technique",
  "detectedTones": ["tones"],
  "viabilityScore": 0-100,
  "estimatedSessions": number,
  "requiredProcesses": ["processes"],
  "confidence": 0-100
}`,
        minimal: `Desired color, current:${currentLevel}. JSON:
{"detectedLevel":num,"detectedTone":"tone","detectedTechnique":"technique","detectedTones":["array"],"viabilityScore":0-100,"estimatedSessions":num,"requiredProcesses":["array"],"confidence":0-100}`,
      },
    };
    return prompts[lang][template];
  }
  /**
   * Get technique-specific instructions
   */
  static getTechniqueInstructions(
    technique: string,
    config: RegionalConfig,
    template: TemplateType = 'full'
  ): string {
    const lang = config.language;
    const developerTerm = config.developerTerminology;

    // Full instructions by technique
    const fullInstructions = {
      full_color: {
        es: `- Fórmula única para cobertura completa
- Asegurar aplicación uniforme de raíces a puntas
- Considerar crecimiento natural para mantenimiento`,
        en: `- Single formula for complete coverage
- Ensure uniform application from roots to ends
- Consider natural regrowth for maintenance`,
      },
      highlights: {
        es: `- Usar técnica con papel aluminio para precisión
- Crear múltiples fórmulas si es necesario (base + mechas)
- Consistencia más espesa para evitar sangrado
- Máximo ${developerTerm} 30 vol para mechas
- Considerar patrón de colocación (cabeza completa, parcial, contorno facial)`,
        en: `- Use foil technique for precision
- Create multiple formulas if needed (base + highlights)
- Thicker consistency to prevent bleeding
- Maximum ${developerTerm} 30 vol for highlights
- Consider placement pattern (full head, partial, face-framing)`,
      },
      balayage: {
        es: `- Técnica de pintado a mano alzada
- Transición gradual de oscuro a claro
- Usar ${developerTerm} de menor volumen (20 vol máximo recomendado)
- Consistencia cremosa para aplicación controlada
- Efecto natural, como besado por el sol
- Considerar usar decolorante en crema o arcilla`,
        en: `- Free-hand painting technique
- Gradual transition from dark to light
- Use lower ${developerTerm} volume (20 vol max recommended)
- Creamy consistency for controlled application
- Natural, sun-kissed effect
- Consider using clay or cream lightener`,
      },
      // ... más técnicas
    };
    if (template === 'minimal') {
      // Ultra-compressed version
      return lang === 'es' ? `${technique}: fórmula específica` : `${technique}: specific formula`;
    }
    if (template === 'optimized') {
      // Compressed but clear
      const key = Object.keys(fullInstructions).find(k => k === technique) || 'full_color';
      const instructions = fullInstructions[key as keyof typeof fullInstructions][lang];
      return instructions.split('\n').slice(0, 3).join('; ');
    }
    // Full version
    const key = Object.keys(fullInstructions).find(k => k === technique) || 'full_color';
    return fullInstructions[key as keyof typeof fullInstructions][lang] || '';
  }
  /**
   * Get formula generation prompt
   */
  static getFormulaPrompt(config: FormulaConfig, template: TemplateType = 'full'): string {
    const lang = config.regionalConfig.language;
    const technique = config.selectedTechnique;
    const techniqueInstructions = this.getTechniqueInstructions(
      technique,
      config.regionalConfig,
      template
    );
    if (template === 'minimal') {
      return lang === 'es'
        ? `Colorista experto. ${technique}. Crear fórmula profesional con ${config.brand} ${config.line}. Diagnóstico: nivel ${config.diagnosis?.averageDepthLevel}. Resultado deseado: nivel ${config.desiredResult?.level}. Devuelve markdown con fórmula exacta, tiempos, aplicación.`
        : `Expert colorist. ${technique}. Create professional formula with ${config.brand} ${config.line}. Diagnosis: level ${config.diagnosis?.averageDepthLevel}. Desired: level ${config.desiredResult?.level}. Return markdown with exact formula, timing, application.`;
    }
    if (template === 'optimized') {
      return lang === 'es'
        ? `Eres un maestro colorista creando una fórmula para ${technique}.
Marca: ${config.brand} ${config.line}
Diagnóstico: ${JSON.stringify(config.diagnosis).slice(0, 200)}...
Resultado deseado: ${JSON.stringify(config.desiredResult).slice(0, 200)}...
${techniqueInstructions}
Genera fórmula detallada con:
- Productos específicos y proporciones
- Tiempos por zona
- Técnica de aplicación
- Cuidados post-servicio
Formato: Markdown profesional.`
        : `You are a master colorist creating a formula for ${technique}.
Brand: ${config.brand} ${config.line}
Diagnosis: ${JSON.stringify(config.diagnosis).slice(0, 200)}...
Desired result: ${JSON.stringify(config.desiredResult).slice(0, 200)}...
${techniqueInstructions}
Generate detailed formula with:
- Specific products and ratios
- Timing by zone
- Application technique
- Post-service care
Format: Professional markdown.`;
    }
    // Return full prompt (existing implementation)
    return this.getFullFormulaPrompt(config, lang);
  }
  /**
   * Get full formula prompt (existing implementation)
   */
  private static getFullFormulaPrompt(config: FormulaConfig, lang: 'es' | 'en'): string {
    const rc = config.regionalConfig;
    const technique = config.selectedTechnique;
    const techniqueInstructions = this.getTechniqueInstructions(technique, rc, 'full');

    // Get technique name
    const techniqueNames = {
      full_color: lang === 'en' ? 'Full Color' : 'Tinte Completo',
      highlights: lang === 'en' ? 'Highlights' : 'Mechas',
      balayage: 'Balayage',
      ombre: 'Ombré',
      babylights: 'Babylights',
      color_correction: lang === 'en' ? 'Color Correction' : 'Corrección de Color',
      foilyage: 'Foilyage',
      money_piece: 'Money Piece',
      chunky_highlights: lang === 'en' ? 'Chunky Highlights' : 'Mechas Gruesas',
      reverse_balayage: 'Reverse Balayage',
    };

    const techniqueName =
      config.selectedTechnique === 'custom' && config.customTechnique
        ? config.customTechnique
        : techniqueNames[technique as keyof typeof techniqueNames] || techniqueNames.full_color;
    // Format examples
    const formatExamples =
      rc.measurementSystem === 'metric'
        ? lang === 'en'
          ? `- Quantities: "40${rc.volumeUnit} of ${rc.colorTerminology} 7.1", "60${rc.volumeUnit} of ${rc.developerTerminology} 20 vol"\n  - Ratios: "1:1${rc.decimalSeparator}5" or "1:2"\n  - Weights: "15${rc.weightUnit} of lightening powder"`
          : `- Cantidades: "40${rc.volumeUnit} de ${rc.colorTerminology} 7.1", "60${rc.volumeUnit} de ${rc.developerTerminology} 20 vol"\n  - Proporciones: "1:1${rc.decimalSeparator}5" o "1:2"\n  - Pesos: "15${rc.weightUnit} de polvo decolorante"`
        : `- Quantities: "1.35${rc.volumeUnit} of ${rc.colorTerminology} 7.1", "2${rc.volumeUnit} of ${rc.developerTerminology} 20 vol"\n  - Ratios: "1:1.5" or "1:2"\n  - Weights: "0.5${rc.weightUnit} of lightening powder"`;

    // Volume restriction
    const volumeRestriction =
      rc.maxDeveloperVolume < 40
        ? lang === 'en'
          ? `IMPORTANT: In this region, the maximum allowed ${rc.developerTerminology} volume is ${rc.maxDeveloperVolume} volumes.`
          : `IMPORTANTE: En esta región, el volumen máximo permitido de ${rc.developerTerminology} es ${rc.maxDeveloperVolume} volúmenes.`
        : '';
    if (lang === 'en') {
      return `You are a master colorist creating a professional formula for a ${techniqueName} service.
REGIONAL CONFIGURATION:
- Measurement system: ${rc.measurementSystem}
- Volume unit: ${rc.volumeUnit}
- Weight unit: ${rc.weightUnit}
- Term for developer/oxidant: ${rc.developerTerminology}
- Term for color: ${rc.colorTerminology}
- Decimal separator: ${rc.decimalSeparator}
${volumeRestriction}
QUANTITY FORMAT:
${formatExamples}
TECHNIQUE-SPECIFIC REQUIREMENTS:
${techniqueInstructions}
Current diagnosis: ${JSON.stringify(config.diagnosis)}
Desired result: ${JSON.stringify(config.desiredResult)}
Brand: ${config.brand}
Line: ${config.line}
Client history: ${config.clientHistory || 'First time'}
Generate a detailed and professional formula including:
- Preparation steps specific to ${techniqueName}
- Specific formula(s) with exact proportions using ${rc.volumeUnit} and ${rc.weightUnit} units
- For techniques requiring multiple formulas (highlights, ombre, etc.), provide all necessary formulas
- Use the term "${rc.developerTerminology}" for developer/oxidant
- Use the term "${rc.colorTerminology}" for hair color
- Processing times by zone considering the specific technique
- Detailed application technique for ${techniqueName}
- Post-service care specific to this technique
- If including estimated costs, use the ${rc.currencySymbol} symbol
IMPORTANT: 
- Use EXACTLY the units and terminology specified above
- Follow the technique-specific requirements carefully
- Adapt formulation consistency and volumes based on the technique
Format: Professional markdown with clear sections.`;
    } else {
      return `Eres un maestro colorista creando una fórmula profesional para un servicio de ${techniqueName}.
CONFIGURACIÓN REGIONAL:
- Sistema de medidas: ${rc.measurementSystem}
- Unidad de volumen: ${rc.volumeUnit}
- Unidad de peso: ${rc.weightUnit}
- Término para oxidante/revelador: ${rc.developerTerminology}
- Término para coloración: ${rc.colorTerminology}
- Separador decimal: ${rc.decimalSeparator}
${volumeRestriction}
FORMATO DE CANTIDADES:
${formatExamples}
REQUISITOS ESPECÍFICOS DE LA TÉCNICA:
${techniqueInstructions}
Diagnóstico actual: ${JSON.stringify(config.diagnosis)}
Resultado deseado: ${JSON.stringify(config.desiredResult)}
Marca: ${config.brand}
Línea: ${config.line}
Historial del cliente: ${config.clientHistory || 'Primera vez'}
Genera una fórmula detallada y profesional que incluya:
- Pasos de preparación específicos para ${techniqueName}
- Fórmula(s) específica(s) con proporciones exactas usando las unidades ${rc.volumeUnit} y ${rc.weightUnit}
- Para técnicas que requieren múltiples fórmulas (mechas, ombré, etc.), proporcionar todas las fórmulas necesarias
- Usa el término "${rc.developerTerminology}" para el oxidante/revelador
- Usa el término "${rc.colorTerminology}" para la coloración
- Tiempos de procesamiento por zona considerando la técnica específica
- Técnica de aplicación detallada para ${techniqueName}
- Cuidados post-servicio específicos para esta técnica
- Si incluyes costos estimados, usa el símbolo ${rc.currencySymbol}
IMPORTANTE: 
- Usa EXACTAMENTE las unidades y terminología especificadas arriba
- Sigue cuidadosamente los requisitos específicos de la técnica
- Adapta la consistencia y volúmenes de la formulación según la técnica
Formato: Markdown profesional con secciones claras.`;
    }
  }
  /**
   * Get product analysis prompt for inventory
   */
  static getProductAnalysisPrompt(productName: string, lang: 'es' | 'en' = 'es'): string {
    const prompts = {
      es: `Eres un experto en productos profesionales de coloración capilar. Analiza el siguiente nombre de producto y extrae toda la información posible.
Producto: "${productName}"
Debes devolver un JSON con EXACTAMENTE esta estructura:
{
  "brand": "marca del producto",
  "productType": "tipo de producto (Tinte/Oxidante/Decolorante/Tratamiento/Otro)",
  "line": "línea o colección del producto (si se puede identificar)",
  "productName": "nombre específico del producto limpio",
  "tone": "tono o referencia (ej: 8/0, 9.1, etc) si aplica",
  "category": "tinte|oxidante|decolorante|tratamiento|otro",
  "packageSize": número (tamaño típico del envase en ml o g),
  "unit": "ml|g|unidad",
  "barcode": "código de barras si es conocido (opcional)",
  "additionalInfo": "cualquier información adicional relevante"
}
IMPORTANTE:
- Si no puedes determinar algún campo, usa null
- Para el packageSize, usa el tamaño estándar más común para ese tipo de producto
- Identifica correctamente si es un tinte (con tono/referencia) o un oxidante (con volúmenes)
- Las marcas profesionales comunes incluyen: Wella, L'Oréal, Schwarzkopf, Revlon, Matrix, Redken, etc.
- Los oxidantes suelen venir en volúmenes de 10, 20, 30, 40
- Los tintes profesionales suelen venir en 60ml, 90ml o 100ml
- Reconoce nomenclaturas de tonos: X/Y, X.Y, XX/YY`,
      en: `You are an expert in professional hair coloring products. Analyze the following product name and extract all possible information.
Product: "${productName}"
Return a JSON with EXACTLY this structure:
{
  "brand": "product brand",
  "productType": "product type (Color/Developer/Bleach/Treatment/Other)",
  "line": "product line or collection (if identifiable)",
  "productName": "specific clean product name",
  "tone": "tone or reference (e.g., 8/0, 9.1, etc) if applicable",
  "category": "tinte|oxidante|decolorante|tratamiento|otro",
  "packageSize": number (typical package size in ml or g),
  "unit": "ml|g|unidad",
  "barcode": "barcode if known (optional)",
  "additionalInfo": "any additional relevant information"
}
IMPORTANT:
- If you cannot determine a field, use null
- For packageSize, use the most common standard size for that product type
- Correctly identify if it's a color (with tone/reference) or developer (with volumes)
- Common professional brands include: Wella, L'Oréal, Schwarzkopf, Revlon, Matrix, Redken, etc.
- Developers usually come in volumes of 10, 20, 30, 40
- Professional colors usually come in 60ml, 90ml, or 100ml
- Recognize tone nomenclatures: X/Y, X.Y, XX/YY`,
    };
    return prompts[lang];
  }
  /**
   * Calculate token savings estimate
   */
  static estimateTokenSavings(fullTemplate: string, optimizedTemplate: string): number {
    // Rough estimate: 1 token ≈ 4 characters
    const fullTokens = Math.ceil(fullTemplate.length / 4);
    const optimizedTokens = Math.ceil(optimizedTemplate.length / 4);
    return ((fullTokens - optimizedTokens) / fullTokens) * 100;
  }
}
// ============ From index.ts ============
/**
 * Salonier Assistant Edge Function - Optimized v2
 *
 * Major improvements:
 * - Modular architecture with separate concerns
 * - Template-based prompts with optimization levels
 * - Enhanced caching with metrics
 * - 40-50% reduction in code size
 */
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
// Import types and constants
// Import utilities
// Import helpers
// Initialize services
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const openaiApiKey = Deno.env.get('OPENAI_API_KEY');
console.log('Edge Function v2 initialization:', {
  version: PROMPT_VERSION,
  hasSupabaseUrl: !!supabaseUrl,
  hasSupabaseKey: !!supabaseServiceKey,
  hasOpenAIKey: !!openaiApiKey,
});
const supabase = createClient(supabaseUrl, supabaseServiceKey);
CacheManager.initialize(supabaseUrl, supabaseServiceKey);
OpenAIClient.initialize(openaiApiKey!);
// Task handlers - Optimized
async function diagnoseImage(payload: any, salonId: string): Promise<AIResponse> {
  try {
    // 1. Validate and prepare image
    const imageValidation = validateAndPrepareImage(payload);
    if (!imageValidation.isValid) {
      return { success: false, error: imageValidation.error };
    }
    // 2. Check cache
    const cacheKey = CacheManager.generateCacheKey('diagnose_image', payload);
    const cached = await CacheManager.checkCache(salonId, 'diagnose_image', cacheKey);

    if (cached.hit) {
      console.log(
        `Cache hit! Saved ${cached.savedTokens} tokens ($${cached.savedCost?.toFixed(4)})`
      );
      return {
        success: true,
        data: cached.data,
        cached: true,
        metrics: {
          tokensUsed: 0,
          cost: 0,
          cacheHit: true,
        },
      };
    }
    // 3. Determine template context
    const context: TemplateContext = {
      hasHistory: false, // Could be enhanced with actual history check
      imageQuality: imageValidation.quality,
      userTier: 'pro', // Could be fetched from salon settings
    };
    // 4. Select optimal template
    const templateType = PromptTemplates.selectOptimalTemplate(context);
    const prompt = PromptTemplates.getDiagnosisPrompt(templateType, 'es');
    console.log(`Using ${templateType} template for diagnosis`);
    // 5. Call OpenAI
    const { response, tokensUsed, cost } = await OpenAIClient.callWithRetry(prompt, {
      model: 'gpt-4o',
      maxTokens: templateType === 'minimal' ? 800 : 1500,
      temperature: 0.3,
      responseFormat: { type: 'json_object' },
      imageUrl: imageValidation.imageDataUrl,
    });
    // 6. Validate response
    const validation = validateAIResponse('diagnose_image', response);
    if (!validation.isValid) {
      console.error('Invalid AI response, missing fields:', validation.missingFields);
      throw new Error('Incomplete AI response');
    }
    // 7. Ensure backward compatibility
    const compatibleResponse = ensureBackwardCompatibility('diagnose_image', response);
    // 8. Save to cache
    await CacheManager.saveToCache(
      salonId,
      'diagnose_image',
      cacheKey,
      payload,
      compatibleResponse,
      'gpt-4o',
      tokensUsed,
      cost
    );
    return {
      success: true,
      data: compatibleResponse,
      metrics: {
        tokensUsed,
        cost,
        cacheHit: false,
      },
    };
  } catch (error: any) {
    console.error('Error in diagnoseImage:', error);
    return { success: false, error: error.message };
  }
}
async function analyzeDesiredLook(payload: any, salonId: string): Promise<AIResponse> {
  try {
    const { currentLevel } = payload;

    // 1. Validate image
    const imageValidation = validateAndPrepareImage(payload);
    if (!imageValidation.isValid) {
      return { success: false, error: imageValidation.error };
    }
    // 2. Check cache
    const cacheKey = CacheManager.generateCacheKey('analyze_desired_look', payload);
    const cached = await CacheManager.checkCache(salonId, 'analyze_desired_look', cacheKey);

    if (cached.hit) {
      return {
        success: true,
        data: cached.data,
        cached: true,
        metrics: {
          tokensUsed: 0,
          cost: 0,
          cacheHit: true,
        },
      };
    }
    // 3. Get optimized prompt
    const context: TemplateContext = {
      hasHistory: false,
      imageQuality: imageValidation.quality,
      userTier: 'pro',
    };

    const templateType = PromptTemplates.selectOptimalTemplate(context);
    const prompt = PromptTemplates.getDesiredLookPrompt(currentLevel, templateType, 'es');
    // 4. Call OpenAI
    const { response, tokensUsed, cost } = await OpenAIClient.callWithRetry(prompt, {
      model: 'gpt-4o',
      maxTokens: templateType === 'minimal' ? 500 : 800,
      temperature: 0.3,
      responseFormat: { type: 'json_object' },
      imageUrl: imageValidation.imageDataUrl,
    });
    // 5. Validate and save
    const compatibleResponse = ensureBackwardCompatibility('analyze_desired_look', response);

    await CacheManager.saveToCache(
      salonId,
      'analyze_desired_look',
      cacheKey,
      payload,
      compatibleResponse,
      'gpt-4o',
      tokensUsed,
      cost
    );
    return {
      success: true,
      data: compatibleResponse,
      metrics: { tokensUsed, cost, cacheHit: false },
    };
  } catch (error: any) {
    console.error('Error in analyzeDesiredLook:', error);
    return { success: false, error: error.message };
  }
}
async function generateFormula(payload: any, salonId: string): Promise<AIResponse> {
  try {
    const { diagnosis, desiredResult, brand, line, clientHistory, regionalConfig } = payload;

    // 1. Check cache
    const cacheKey = CacheManager.generateCacheKey('generate_formula', payload);
    const cached = await CacheManager.checkCache(salonId, 'generate_formula', cacheKey);

    if (cached.hit) {
      return {
        success: true,
        data: cached.data,
        cached: true,
        metrics: { tokensUsed: 0, cost: 0, cacheHit: true },
      };
    }
    // 2. Build formula config
    const formulaConfig = {
      diagnosis,
      desiredResult,
      brand,
      line,
      clientHistory,
      regionalConfig: regionalConfig || {
        language: 'es',
        volumeUnit: 'ml',
        weightUnit: 'g',
        developerTerminology: 'oxidante',
        colorTerminology: 'tinte',
        maxDeveloperVolume: 40,
        currencySymbol: '€',
        measurementSystem: 'metric',
        decimalSeparator: ',',
        thousandsSeparator: '.',
      },
      selectedTechnique: desiredResult?.general?.technique || 'full_color',
      customTechnique: desiredResult?.general?.customTechnique,
    };
    // 3. Get optimized prompt
    const context: TemplateContext = {
      hasHistory: !!clientHistory,
      imageQuality: 'high', // Formulas don't depend on image quality
      userTier: 'pro',
    };

    const templateType = PromptTemplates.selectOptimalTemplate(context);
    const prompt = PromptTemplates.getFormulaPrompt(formulaConfig, templateType);
    // 4. Call OpenAI
    const { response, tokensUsed, cost } = await OpenAIClient.callWithRetry(prompt, {
      model: 'gpt-4o',
      maxTokens: templateType === 'minimal' ? 1000 : 2000,
      temperature: 0.4,
    });
    // 5. Structure response
    const formulaData = {
      formulaText: response,
      formulaData: {
        steps: [],
        products: [],
        processingTimes: {},
        technique: formulaConfig.selectedTechnique,
      },
      totalTokens: tokensUsed,
    };
    // 6. Save to cache
    await CacheManager.saveToCache(
      salonId,
      'generate_formula',
      cacheKey,
      payload,
      formulaData,
      'gpt-4o',
      tokensUsed,
      cost
    );
    return {
      success: true,
      data: formulaData,
      metrics: { tokensUsed, cost, cacheHit: false },
    };
  } catch (error: any) {
    console.error('Error in generateFormula:', error);
    return { success: false, error: error.message };
  }
}
// Task handler - Product Analysis for Inventory
async function analyzeProduct(payload: any, salonId: string): Promise<AIResponse> {
  try {
    const { productName, language = 'es' } = payload;

    if (!productName || productName.trim().length < 3) {
      return {
        success: false,
        error: language === 'es' ? 'Nombre de producto muy corto' : 'Product name too short',
      };
    }
    // 1. Check cache
    const cacheKey = CacheManager.generateCacheKey('analyze_product', payload);
    const cached = await CacheManager.checkCache(salonId, 'analyze_product', cacheKey);

    if (cached.hit) {
      console.log(
        `Cache hit for product analysis! Saved ${cached.savedTokens} tokens ($${cached.savedCost?.toFixed(4)})`
      );
      return {
        success: true,
        data: cached.data,
        cached: true,
        metrics: {
          tokensUsed: 0,
          cost: 0,
          cacheHit: true,
        },
      };
    }
    // 2. Get product analysis prompt
    const prompt = PromptTemplates.getProductAnalysisPrompt(productName, language);
    // 3. Call OpenAI with GPT-4o-mini for cost efficiency
    const { response, tokensUsed, cost } = await OpenAIClient.callWithRetry(prompt, {
      model: 'gpt-4o-mini',
      maxTokens: 500,
      temperature: 0.3,
      responseFormat: { type: 'json_object' },
    });
    // 4. Validate response
    const validation = validateAIResponse('analyze_product', response);
    if (!validation.isValid) {
      console.error('Invalid product analysis response:', validation.missingFields);
      throw new Error('Incomplete product information');
    }
    // 5. Save to cache (90 days for product info)
    await CacheManager.saveToCache(
      salonId,
      'analyze_product',
      cacheKey,
      payload,
      response,
      'gpt-4o-mini',
      tokensUsed,
      cost
    );
    return {
      success: true,
      data: response,
      metrics: { tokensUsed, cost, cacheHit: false },
    };
  } catch (error: any) {
    console.error('Error in analyzeProduct:', error);
    return { success: false, error: error.message };
  }
}
// Main handler
serve(async req => {
  console.log('Edge function invoked:', {
    method: req.method,
    url: req.url,
    version: PROMPT_VERSION,
  });

  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: CORS_HEADERS });
  }
  try {
    // Verify JWT
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'Missing authorization header' }), {
        status: 401,
        headers: { ...CORS_HEADERS, 'Content-Type': 'application/json' },
      });
    }
    // Get user and salon info
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));

    if (authError || !user) {
      return new Response(JSON.stringify({ error: 'Invalid token' }), {
        status: 401,
        headers: { ...CORS_HEADERS, 'Content-Type': 'application/json' },
      });
    }
    // Get user's salon_id
    const { data: profile } = await supabase
      .from('profiles')
      .select('salon_id')
      .eq('id', user.id)
      .single();
    if (!profile?.salon_id) {
      return new Response(JSON.stringify({ error: 'User not associated with a salon' }), {
        status: 403,
        headers: { ...CORS_HEADERS, 'Content-Type': 'application/json' },
      });
    }
    const salonId = profile.salon_id;
    // Parse request
    const { task, payload }: AIRequest = await req.json();
    // Validate OpenAI API key
    if (!openaiApiKey) {
      return new Response(
        JSON.stringify({
          success: false,
          error: ERROR_MESSAGES.missingApiKey,
        }),
        { status: 500, headers: { ...CORS_HEADERS, 'Content-Type': 'application/json' } }
      );
    }
    // Process based on task
    let result: AIResponse;

    console.log(`Processing task: ${task}`);

    switch (task) {
      case 'diagnose_image':
        result = await diagnoseImage(payload, salonId);
        break;
      case 'analyze_desired_look':
        result = await analyzeDesiredLook(payload, salonId);
        break;
      case 'generate_formula':
        result = await generateFormula(payload, salonId);
        break;
      case 'analyze_product':
        result = await analyzeProduct(payload, salonId);
        break;
      default:
        result = { success: false, error: 'Invalid task' };
    }
    // Log metrics
    if (result.metrics) {
      console.log('Request metrics:', {
        task,
        cacheHit: result.metrics.cacheHit,
        tokensUsed: result.metrics.tokensUsed,
        cost: result.metrics.cost?.toFixed(4),
      });
    }
    // Get and log cache metrics periodically
    const metrics = CacheManager.getMetrics();
    if (Math.random() < 0.1) {
      // Log 10% of the time
      console.log('Cache metrics:', {
        hitRate: `${(metrics as any).hitRate}%`,
        totalSaved: `$${metrics.totalSavedUSD.toFixed(2)}`,
        totalHits: metrics.totalHits,
      });
    }
    return new Response(JSON.stringify(result), {
      headers: { ...CORS_HEADERS, 'Content-Type': 'application/json' },
    });
  } catch (error: any) {
    console.error('Edge function error:', error);
    return new Response(JSON.stringify({ success: false, error: error.message }), {
      status: 500,
      headers: { ...CORS_HEADERS, 'Content-Type': 'application/json' },
    });
  }
});
