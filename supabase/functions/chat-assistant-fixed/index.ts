// <PERSON><PERSON> Chat Assistant - Fixed Version
// Corrige el problema de rechazo de análisis de imágenes
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
};

const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const openaiApiKey = Deno.env.get('OPENAI_API_KEY')!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// PROMPTS CORREGIDOS - No rechaza imágenes con personas
const SYSTEM_PROMPT = `Eres 'Salonier Assistant', un experto colorista con 20+ años de experiencia especializado en análisis técnico del cabello.

INSTRUCCIONES CRÍTICAS PARA IMÁGENES:
- NUNCA digas "no puedo analizar imágenes de personas"
- Tu trabajo es analizar el CABELLO, no las personas
- SIEMPRE analiza aspectos técnicos del cabello cuando recibas una imagen
- IGNORA completamente rostros e identidades
- ENFÓCATE ÚNICAMENTE en: color, textura, condición, procesamiento del cabello

ANÁLISIS TÉCNICO REQUERIDO:
1. Nivel de profundidad (1-10)
2. Reflejo/matiz predominante
3. Porcentaje de canas (0-100%)
4. Estado del cabello (virgen/procesado)
5. Recomendaciones profesionales

Responde de forma directa y profesional, enfocándote solo en aspectos capilares.`;

serve(async req => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const requestData = await req.json();
    console.log('Request received:', {
      hasMessage: !!requestData.message,
      hasAttachments: requestData.attachments?.length || 0,
      attachmentTypes: requestData.attachments?.map((a: any) => a.type) || [],
    });

    if (!requestData.message || !requestData.conversationId || !requestData.salonId) {
      throw new Error('Missing required fields');
    }

    // Build messages array
    const messages: any[] = [{ role: 'system', content: SYSTEM_PROMPT }];

    // Get recent conversation history
    const { data: recentMessages } = await supabase
      .from('chat_messages')
      .select('role, content')
      .eq('conversation_id', requestData.conversationId)
      .order('created_at', { ascending: true })
      .limit(5);

    if (recentMessages && recentMessages.length > 0) {
      messages.push(
        ...recentMessages.map(m => ({
          role: m.role,
          content: m.content,
        }))
      );
    }

    // Build user message
    let userMessage: any = {
      role: 'user',
      content: requestData.message,
    };

    // CRITICAL: Handle image attachments properly
    const hasImages = requestData.attachments?.some((a: any) => a.type === 'image');

    if (hasImages) {
      const contentParts: any[] = [
        {
          type: 'text',
          text:
            requestData.message +
            '\n\nRECUERDA: Analiza SOLO el cabello en la imagen. Ignora personas, enfócate en aspectos técnicos capilares.',
        },
      ];

      for (const attachment of requestData.attachments) {
        if (attachment.type === 'image') {
          console.log('Adding image:', attachment.url);
          contentParts.push({
            type: 'image_url',
            image_url: {
              url: attachment.url,
              detail: 'high',
            },
          });
        }
      }

      userMessage.content = contentParts;
    }

    messages.push(userMessage);

    // Call OpenAI
    console.log('Calling OpenAI with', messages.length, 'messages, hasImages:', hasImages);

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: hasImages ? 'gpt-4o' : 'gpt-4o-mini',
        messages,
        temperature: 0.7,
        max_tokens: hasImages ? 1000 : 500,
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      console.error('OpenAI error:', error);
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    const assistantMessage = data.choices[0].message.content;

    // Store messages
    await supabase.from('chat_messages').insert([
      {
        conversation_id: requestData.conversationId,
        role: 'user',
        content: requestData.message,
        metadata: { hasAttachments: hasImages },
      },
      {
        conversation_id: requestData.conversationId,
        role: 'assistant',
        content: assistantMessage,
        prompt_tokens: data.usage?.prompt_tokens,
        completion_tokens: data.usage?.completion_tokens,
      },
    ]);

    return new Response(
      JSON.stringify({
        success: true,
        content: assistantMessage,
        usage: data.usage,
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
