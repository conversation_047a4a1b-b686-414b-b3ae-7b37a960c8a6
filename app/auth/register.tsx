import React, { useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
} from 'react-native';
import { logger } from '@/utils/logger';
import { Link, router } from 'expo-router';
import { Mail, Lock, Eye, EyeOff, User } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { commonStyles } from '@/styles/commonStyles';
import { useAuthStore } from '@/stores/auth-store';

export default function RegisterScreen() {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const { signUp } = useAuthStore();

  const handleRegister = async () => {
    if (!name || !email || !password || !confirmPassword) {
      setError('Por favor, completa todos los campos');
      return;
    }

    if (password !== confirmPassword) {
      setError('Las contraseñas no coinciden');
      return;
    }

    if (password.length < 6) {
      setError('La contraseña debe tener al menos 6 caracteres');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Registrar con Supabase
      await signUp(email, password, name);

      // After successful signup, check if user is authenticated
      const currentUser = useAuthStore.getState().user;

      if (currentUser) {
        // New users (owners) always go through onboarding
        router.replace('/onboarding/welcome');
      } else {
        // If for some reason the user is not authenticated, redirect to login
        router.replace('/auth/login');
      }
    } catch (err: unknown) {
      logger.error('Registration error', 'RegisterScreen', err);

      // Manejar errores específicos de Supabase
      const errorMessage = err instanceof Error ? err.message : String(err);
      const errorCode =
        err instanceof Error && 'code' in err ? (err as Error & { code: string }).code : null;

      if (
        errorMessage.includes('User already registered') ||
        errorMessage.includes('already registered')
      ) {
        setError('Este email ya está registrado');
      } else if (errorMessage.includes('Invalid email')) {
        setError('Por favor, introduce un email válido');
      } else if (errorMessage.includes('Weak password')) {
        setError('La contraseña es demasiado débil. Usa al menos 6 caracteres.');
      } else if (errorMessage.includes('Network request failed')) {
        setError('Error de conexión. Por favor, verifica tu internet.');
      } else if (errorMessage.includes('Invalid API key')) {
        setError(
          '⚠️ Supabase no está configurado. Por favor, sigue las instrucciones en el archivo .env.local'
        );
      } else if (errorMessage.includes('Failed to fetch')) {
        setError('⚠️ No se puede conectar con Supabase. Verifica tu configuración en .env.local');
      } else if (errorMessage.includes('No se pudo crear el perfil')) {
        setError(
          'Se creó tu cuenta pero hubo un problema al configurar tu perfil. Redirigiendo al login...'
        );
        // Redirect to login after a delay
        setTimeout(() => {
          router.replace('/auth/login');
        }, 2500);
      } else if (errorCode === '42P17' || errorMessage.includes('infinite recursion')) {
        setError('Error de configuración del servidor. Por favor, contacta al soporte técnico.');
        logger.error('RLS configuration error detected', 'RegisterScreen', err);
      } else {
        // Log the full error for debugging
        logger.error('Unhandled registration error', 'RegisterScreen', err);
        setError('Error al crear tu cuenta. Por favor, intenta de nuevo.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const linkStyle = Platform.select({
    web: { textDecoration: 'none' } as const,
    default: {},
  }) as React.CSSProperties;

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.logoContainer}>
          <View style={styles.logoCircle}>
            <Text style={styles.logoText}>S</Text>
          </View>
          <Text style={styles.appName}>Salonier</Text>
          <Text style={styles.appTagline}>Tu asistente inteligente para coloración capilar</Text>
        </View>

        <View style={styles.formContainer}>
          <Text style={styles.formTitle}>Crear cuenta</Text>
          <Text style={styles.formSubtitle}>Únete a la revolución del color</Text>

          {error ? <Text style={styles.errorText}>{error}</Text> : null}

          <View style={styles.inputContainer}>
            <User size={20} color={Colors.light.gray} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder="Nombre completo"
              placeholderTextColor={Colors.light.gray}
              value={name}
              onChangeText={setName}
            />
          </View>

          <View style={styles.inputContainer}>
            <Mail size={20} color={Colors.light.gray} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder="Email"
              placeholderTextColor={Colors.light.gray}
              value={email}
              onChangeText={setEmail}
              autoCapitalize="none"
              keyboardType="email-address"
            />
          </View>

          <View style={styles.inputContainer}>
            <Lock size={20} color={Colors.light.gray} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder="Contraseña"
              placeholderTextColor={Colors.light.gray}
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!showPassword}
            />
            <TouchableOpacity style={styles.eyeIcon} onPress={() => setShowPassword(!showPassword)}>
              {showPassword ? (
                <EyeOff size={20} color={Colors.light.gray} />
              ) : (
                <Eye size={20} color={Colors.light.gray} />
              )}
            </TouchableOpacity>
          </View>

          <View style={styles.inputContainer}>
            <Lock size={20} color={Colors.light.gray} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder="Confirmar contraseña"
              placeholderTextColor={Colors.light.gray}
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry={!showConfirmPassword}
            />
            <TouchableOpacity
              style={styles.eyeIcon}
              onPress={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? (
                <EyeOff size={20} color={Colors.light.gray} />
              ) : (
                <Eye size={20} color={Colors.light.gray} />
              )}
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={[styles.registerButton, isLoading && styles.registerButtonDisabled]}
            onPress={handleRegister}
            disabled={isLoading}
          >
            <Text style={styles.registerButtonText}>
              {isLoading ? 'Configurando tu salón...' : 'Crear Cuenta'}
            </Text>
          </TouchableOpacity>

          <View style={styles.loginContainer}>
            <Text style={styles.loginText}>¿Ya tienes una cuenta? </Text>
            <Link href="/auth/login" style={linkStyle}>
              <Text style={styles.loginLink}>Inicia sesión</Text>
            </Link>
          </View>
        </View>

        <Text style={styles.termsText}>
          Al registrarte, aceptas nuestros Términos de Servicio y Política de Privacidad
        </Text>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    ...commonStyles.flex1,
    backgroundColor: Colors.light.background,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 24,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoCircle: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: Colors.light.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  logoText: {
    fontSize: 32,
    fontWeight: '700',
    color: Colors.light.textLight,
  },
  appName: {
    fontSize: 28,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 8,
  },
  appTagline: {
    fontSize: 16,
    color: Colors.light.gray,
    textAlign: 'center',
    lineHeight: 22,
  },
  formContainer: {
    backgroundColor: Colors.light.background,
    borderRadius: 24,
    padding: 24,
    marginBottom: 20,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  formTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  formSubtitle: {
    fontSize: 16,
    color: Colors.light.gray,
    textAlign: 'center',
    marginBottom: 32,
  },
  errorText: {
    color: Colors.light.error,
    marginBottom: 16,
    textAlign: 'center',
    fontSize: 14,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 12,
    marginBottom: 16,
    height: 56,
    paddingHorizontal: 16,
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: Colors.light.text,
  },
  eyeIcon: {
    padding: 4,
  },
  registerButton: {
    backgroundColor: Colors.light.primary,
    borderRadius: 16,
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  registerButtonDisabled: {
    opacity: 0.7,
  },
  registerButtonText: {
    color: Colors.light.textLight,
    fontSize: 16,
    fontWeight: '700',
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  loginText: {
    color: Colors.light.gray,
    fontSize: 14,
  },
  loginLink: {
    color: Colors.light.primary,
    fontWeight: '600',
    fontSize: 14,
  },
  termsText: {
    textAlign: 'center',
    fontSize: 12,
    color: Colors.light.gray,
    lineHeight: 16,
  },
});
