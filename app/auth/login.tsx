import React, { useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
} from 'react-native';
import { logger } from '@/utils/logger';
import { Link, router } from 'expo-router';
import { Mail, Lock, Eye, EyeOff } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { commonStyles } from '@/styles/commonStyles';
import { useAuthStore } from '@/stores/auth-store';
import { useSalonConfigStore } from '@/stores/salon-config-store';

export default function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const { signIn } = useAuthStore();
  const { hasCompletedOnboarding } = useSalonConfigStore();

  const handleLogin = async () => {
    if (!email || !password) {
      setError('Por favor, introduce tu email y contraseña');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Usar el nuevo método signIn que conecta con Supabase
      await signIn(email, password);

      // Check if user has completed onboarding
      // Solo los propietarios necesitan pasar por el onboarding
      const currentUser = useAuthStore.getState().user;

      if (currentUser?.isOwner && !hasCompletedOnboarding) {
        router.replace('/onboarding/welcome');
      } else {
        router.replace('/(tabs)');
      }
    } catch (err: unknown) {
      // Manejar errores específicos de Supabase
      const errorMessage = err instanceof Error ? err.message : String(err);

      if (errorMessage.includes('Invalid login credentials')) {
        setError('Email o contraseña incorrectos');
      } else if (errorMessage.includes('Email not confirmed')) {
        setError('Por favor, confirma tu email antes de iniciar sesión');
      } else if (errorMessage.includes('Network request failed')) {
        setError('Error de conexión. Por favor, verifica tu internet.');
      } else if (errorMessage.includes('Invalid API key')) {
        setError(
          '⚠️ Supabase no está configurado. Por favor, sigue las instrucciones en el archivo .local'
        );
      } else if (errorMessage.includes('Failed to fetch')) {
        setError('⚠️ No se puede conectar con Supabase. Verifica tu configuración en .local');
      } else {
        setError('Error al iniciar sesión. Por favor, intenta de nuevo.');
      }
      logger.error('Login error', 'LoginScreen', err);
    } finally {
      setIsLoading(false);
    }
  };

  const linkStyle = Platform.select({
    web: { textDecoration: 'none' } as const,
    default: {},
  }) as React.CSSProperties;

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.logoContainer}>
          <View style={styles.logoCircle}>
            <Text style={styles.logoText}>S</Text>
          </View>
          <Text style={styles.appName}>Salonier</Text>
          <Text style={styles.appTagline}>Tu asistente inteligente para coloración capilar</Text>
        </View>

        <View style={styles.formContainer}>
          <Text style={styles.formTitle}>Bienvenido de vuelta</Text>
          <Text style={styles.formSubtitle}>Inicia sesión en tu cuenta</Text>

          {error ? <Text style={styles.errorText}>{error}</Text> : null}

          <View style={styles.inputContainer}>
            <Mail size={20} color={Colors.light.gray} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder="Email"
              placeholderTextColor={Colors.light.gray}
              value={email}
              onChangeText={setEmail}
              autoCapitalize="none"
              keyboardType="email-address"
            />
          </View>

          <View style={styles.inputContainer}>
            <Lock size={20} color={Colors.light.gray} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder="Contraseña"
              placeholderTextColor={Colors.light.gray}
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!showPassword}
            />
            <TouchableOpacity style={styles.eyeIcon} onPress={() => setShowPassword(!showPassword)}>
              {showPassword ? (
                <EyeOff size={20} color={Colors.light.gray} />
              ) : (
                <Eye size={20} color={Colors.light.gray} />
              )}
            </TouchableOpacity>
          </View>

          <TouchableOpacity style={styles.forgotPassword}>
            <Text style={styles.forgotPasswordText}>¿Olvidaste tu contraseña?</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.loginButton, isLoading && styles.loginButtonDisabled]}
            onPress={handleLogin}
            disabled={isLoading}
          >
            <Text style={styles.loginButtonText}>
              {isLoading ? 'Iniciando sesión...' : 'Iniciar Sesión'}
            </Text>
          </TouchableOpacity>

          <View style={styles.registerContainer}>
            <Text style={styles.registerText}>¿No tienes una cuenta? </Text>
            <Link href="/auth/register" style={linkStyle}>
              <Text style={styles.registerLink}>Regístrate</Text>
            </Link>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    ...commonStyles.flex1,
    backgroundColor: Colors.light.background,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 24,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 48,
  },
  logoCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.light.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  logoText: {
    fontSize: 36,
    fontWeight: '700',
    color: Colors.light.textLight,
  },
  appName: {
    fontSize: 32,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 8,
  },
  appTagline: {
    fontSize: 16,
    color: Colors.light.gray,
    textAlign: 'center',
    lineHeight: 22,
  },
  formContainer: {
    backgroundColor: Colors.light.background,
    borderRadius: 24,
    padding: 24,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  formTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  formSubtitle: {
    fontSize: 16,
    color: Colors.light.gray,
    textAlign: 'center',
    marginBottom: 32,
  },
  errorText: {
    color: Colors.light.error,
    marginBottom: 16,
    textAlign: 'center',
    fontSize: 14,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 12,
    marginBottom: 16,
    height: 56,
    paddingHorizontal: 16,
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: Colors.light.text,
  },
  eyeIcon: {
    padding: 4,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: 24,
  },
  forgotPasswordText: {
    color: Colors.light.primary,
    fontSize: 14,
    fontWeight: '600',
  },
  loginButton: {
    backgroundColor: Colors.light.primary,
    borderRadius: 16,
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  loginButtonDisabled: {
    opacity: 0.7,
  },
  loginButtonText: {
    color: Colors.light.textLight,
    fontSize: 16,
    fontWeight: '700',
  },
  registerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  registerText: {
    color: Colors.light.gray,
    fontSize: 14,
  },
  registerLink: {
    color: Colors.light.primary,
    fontWeight: '600',
    fontSize: 14,
  },
});
