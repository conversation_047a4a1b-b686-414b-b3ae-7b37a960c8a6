import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  TextInput,
  FlatList,
  Platform,
  Alert,
} from 'react-native';
import { Link, router, useFocusEffect } from 'expo-router';
import { Search, Plus, Shield, TrendingUp, AlertTriangle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { commonStyles } from '@/styles/commonStyles';
import { spacing, radius } from '@/constants/theme';
import { ErrorState, SkeletonTemplates, AnimatedView } from '@/components/base';
import { useClientStore, Client } from '@/stores/client-store';
import { useClientHistoryStore } from '@/stores/client-history-store';
import { useActiveClientStore } from '@/stores/active-client-store';
import { usePermissions } from '@/hooks/usePermissions';
import ClientListItem from '@/components/client/ClientListItem';

export default function ClientsScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterMode, setFilterMode] = useState<'all' | 'warnings' | 'recommendations'>('all');

  // FlatList ref for scroll to top on focus
  const flatListRef = useRef<FlatList>(null);

  const {
    clients,
    isLoading,
    error,
    loadClients,
    deleteClient: deleteClientFromStore,
  } = useClientStore();

  const {
    getWarningsForClient,
    getRecommendationsForClient,
    getClientProfile,
    initializeClientProfile,
  } = useClientHistoryStore();
  const { setActiveClient } = useActiveClientStore();
  const { can } = usePermissions();

  // Scroll to top when screen receives focus
  useFocusEffect(
    useCallback(() => {
      flatListRef.current?.scrollToOffset({ offset: 0, animated: false });
      return () => {};
    }, [])
  );

  // Load clients on mount
  useEffect(() => {
    loadClients();
  }, [loadClients]);

  // Initialize client profiles on mount
  useEffect(() => {
    clients.forEach(client => {
      initializeClientProfile(client.id);
    });
  }, [clients, initializeClientProfile]);

  const getFilteredClients = () => {
    let filtered = clients.filter(
      client =>
        client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        client.email.toLowerCase().includes(searchQuery.toLowerCase())
    );

    if (filterMode === 'warnings') {
      filtered = filtered.filter(client => getWarningsForClient(client.id).length > 0);
    } else if (filterMode === 'recommendations') {
      filtered = filtered.filter(client => getRecommendationsForClient(client.id).length > 0);
    }

    return filtered;
  };

  const filteredClients = getFilteredClients();

  const deleteClient = useCallback(
    (id: string, name: string) => {
      Alert.alert(
        'Eliminar Cliente',
        `¿Estás seguro de que quieres eliminar a "${name}"? Esta acción no se puede deshacer.`,
        [
          { text: 'Cancelar', style: 'cancel' },
          {
            text: 'Eliminar',
            style: 'destructive',
            onPress: () => deleteClientFromStore(id),
          },
        ]
      );
    },
    [deleteClientFromStore]
  );

  const handleViewClient = useCallback(
    (client: Client) => {
      setActiveClient(client);
      router.push(`/client/${client.id}`);
    },
    [setActiveClient]
  );

  const renderClientCard = useCallback(
    ({ item }: { item: Client }) => {
      const warnings = getWarningsForClient(item.id);
      const recommendations = getRecommendationsForClient(item.id);
      const profile = getClientProfile(item.id);

      return (
        <AnimatedView animation="fadeIn" duration={300}>
          <ClientListItem
            client={item}
            warnings={warnings}
            recommendations={recommendations}
            profile={profile}
            canDelete={can.deleteData}
            onDelete={deleteClient}
            onView={handleViewClient}
          />
        </AnimatedView>
      );
    },
    [
      getWarningsForClient,
      getRecommendationsForClient,
      getClientProfile,
      can.deleteData,
      deleteClient,
      handleViewClient,
    ]
  );

  const linkStyle = Platform.select({
    web: { textDecoration: 'none' } as const,
    default: {},
  });

  // Show loading state
  if (isLoading && clients.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.content}>
          <View style={styles.searchContainer}>
            <Search size={20} color={Colors.light.gray} style={styles.searchIcon} />
            <TextInput
              style={[styles.searchInput, commonStyles.opacity50]}
              placeholder="Buscar por nombre o email..."
              placeholderTextColor={Colors.light.gray}
              editable={false}
            />
            <View style={[styles.addButton, commonStyles.opacity50]}>
              <Plus size={20} color={Colors.light.primary} />
            </View>
          </View>

          <View style={styles.stats}>
            <View style={[styles.statCard, commonStyles.opacity50]}>
              <Shield size={16} color={Colors.light.primary} />
              <Text style={styles.statNumber}>-</Text>
              <Text style={styles.statLabel}>Clientes</Text>
            </View>
            <View style={[styles.statCard, commonStyles.opacity50]}>
              <TrendingUp size={16} color={Colors.light.secondary} />
              <Text style={styles.statNumber}>-</Text>
              <Text style={styles.statLabel}>Satisfacción</Text>
            </View>
          </View>

          <View style={styles.skeletonContainer}>
            {Array.from({ length: 5 }).map((_, index) => (
              <View key={index} style={styles.skeletonItem}>
                <SkeletonTemplates.ListItem />
              </View>
            ))}
          </View>
        </View>
      </View>
    );
  }

  // Show error state
  if (error && clients.length === 0) {
    return (
      <ErrorState
        message={error.message || 'No se pudieron cargar los clientes'}
        onRetry={loadClients}
      />
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.searchContainer}>
          <Search size={20} color={Colors.light.gray} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Buscar por nombre o email..."
            placeholderTextColor={Colors.light.gray}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          <Link href="/client/new" style={linkStyle}>
            <View style={styles.addButton}>
              <Plus size={20} color={Colors.light.primary} />
            </View>
          </Link>
        </View>

        <View style={styles.filtersContainer}>
          <TouchableOpacity
            style={[styles.filterButton, filterMode === 'all' && styles.activeFilterButton]}
            onPress={() => setFilterMode('all')}
          >
            <Text style={[styles.filterText, filterMode === 'all' && styles.activeFilterText]}>
              Todos ({clients.length})
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.filterButton, filterMode === 'warnings' && styles.activeFilterButton]}
            onPress={() => setFilterMode('warnings')}
          >
            <AlertTriangle
              size={14}
              color={filterMode === 'warnings' ? Colors.light.textLight : Colors.light.error}
            />
            <Text style={[styles.filterText, filterMode === 'warnings' && styles.activeFilterText]}>
              Con Alertas ({clients.filter(c => getWarningsForClient(c.id).length > 0).length})
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterButton,
              filterMode === 'recommendations' && styles.activeFilterButton,
            ]}
            onPress={() => setFilterMode('recommendations')}
          >
            <TrendingUp
              size={14}
              color={
                filterMode === 'recommendations' ? Colors.light.textLight : Colors.light.accent
              }
            />
            <Text
              style={[
                styles.filterText,
                filterMode === 'recommendations' && styles.activeFilterText,
              ]}
            >
              Con Recomendaciones
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.statsContainer}>
          <Text style={styles.statsText}>
            {filteredClients.length} cliente
            {filteredClients.length !== 1 ? 's' : ''} encontrado
            {filteredClients.length !== 1 ? 's' : ''}
          </Text>
        </View>

        <FlatList
          ref={flatListRef}
          data={filteredClients}
          keyExtractor={item => item.id}
          renderItem={renderClientCard}
          initialNumToRender={10}
          maxToRenderPerBatch={10}
          windowSize={10}
          removeClippedSubviews={true}
          ListEmptyComponent={
            <View style={styles.emptyState}>
              <Shield size={48} color={Colors.light.gray} />
              <Text style={styles.emptyStateTitle}>
                {filterMode === 'all'
                  ? 'No se encontraron clientes'
                  : filterMode === 'warnings'
                    ? 'No hay clientes con alertas'
                    : 'No hay clientes con recomendaciones'}
              </Text>
              <Text style={styles.emptyStateText}>
                {filterMode === 'all'
                  ? 'Comienza añadiendo tu primer cliente'
                  : filterMode === 'warnings'
                    ? 'Todos tus clientes están sin alertas activas'
                    : 'No hay recomendaciones pendientes'}
              </Text>
              {filterMode === 'all' && (
                <Link href="/client/new" style={linkStyle}>
                  <View style={styles.emptyStateButton}>
                    <Plus
                      size={20}
                      color={Colors.light.textLight}
                      style={styles.emptyStateButtonIcon}
                    />
                    <Text style={styles.emptyStateButtonText}>Añadir Cliente</Text>
                  </View>
                </Link>
              )}
            </View>
          }
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  content: {
    flex: 1,
    padding: 20,
    paddingTop: 10,
  },
  stats: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  statCard: {
    flex: 1,
    backgroundColor: Colors.light.card,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.light.text,
    marginVertical: 4,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.card,
    borderRadius: 12,
    paddingHorizontal: 16,
    height: 48,
    marginBottom: 16,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.light.text,
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: radius.full,
    backgroundColor: Colors.light.primary + '15',
    justifyContent: 'center',
    alignItems: 'center',
  },
  filtersContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 8,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: radius.full,
    backgroundColor: Colors.light.card,
    gap: 6,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  activeFilterButton: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  filterText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontWeight: '500',
  },
  activeFilterText: {
    color: Colors.light.textLight,
  },
  statsContainer: {
    marginBottom: 16,
  },
  statsText: {
    fontSize: 16,
    color: Colors.light.text,
    fontWeight: '600',
  },
  listContainer: {
    flexGrow: 1,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.text,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    color: Colors.light.gray,
    marginBottom: 32,
    textAlign: 'center',
    lineHeight: 22,
  },
  emptyStateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
    gap: 8,
  },
  emptyStateButtonIcon: {},
  emptyStateButtonText: {
    color: Colors.light.textLight,
    fontWeight: '700',
    fontSize: 16,
  },
  skeletonContainer: {
    paddingHorizontal: spacing.md,
    marginTop: spacing.md,
  },
  skeletonItem: {
    marginBottom: 8,
  },
});
