import React from 'react';
import { Tabs } from 'expo-router';
import { Home, Users, Package, Settings, MessageCircle } from 'lucide-react-native';
import { Platform } from 'react-native';

import Colors from '@/constants/colors';
import { shadows, typography, layout } from '@/constants/theme';

export default function TabLayout() {
  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors.light.primary,
        tabBarInactiveTintColor: Colors.light.tabIconDefault,
        headerShown: true,
        tabBarStyle: {
          backgroundColor: Colors.light.surface,
          borderTopWidth: 0,
          height: layout.tabBarHeight,
          paddingBottom: Platform.OS === 'ios' ? 20 : 15,
          paddingTop: 10,
          ...shadows.md,
          shadowOffset: { width: 0, height: -2 },
        },
        tabBarItemStyle: {
          paddingVertical: 5,
        },
        tabBarLabelStyle: {
          fontSize: typography.sizes.xs,
          fontWeight: typography.weights.medium,
        },
        headerStyle: {
          backgroundColor: Colors.light.background,
          elevation: 0,
          shadowOpacity: 0,
          borderBottomWidth: 1,
          borderBottomColor: Colors.light.border,
        },
        headerTitleStyle: {
          fontWeight: typography.weights.bold,
          fontSize: typography.sizes['2xl'],
          color: Colors.light.text,
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Inicio',
          tabBarIcon: ({ color, size }) => <Home size={size} color={color} />,
          headerTitle: 'Salonier',
          tabBarAccessibilityLabel: 'Ir a pantalla de inicio',
        }}
      />
      <Tabs.Screen
        name="clients"
        options={{
          title: 'Clientes',
          tabBarIcon: ({ color, size }) => <Users size={size} color={color} />,
          headerTitle: 'Mis Clientes',
          tabBarAccessibilityLabel: 'Ir a lista de clientes',
        }}
      />
      <Tabs.Screen
        name="inventory"
        options={{
          title: 'Inventario',
          tabBarIcon: ({ color, size }) => <Package size={size} color={color} />,
          headerTitle: 'Mi Inventario',
          tabBarAccessibilityLabel: 'Ir a inventario de productos',
        }}
      />
      <Tabs.Screen
        name="assistant"
        options={{
          title: 'Asistente',
          tabBarIcon: ({ color, size }) => <MessageCircle size={size} color={color} />,
          headerTitle: 'Asistente Salonier',
          tabBarAccessibilityLabel: 'Ir al asistente de colorimetría',
        }}
      />
      <Tabs.Screen
        name="settings"
        options={{
          title: 'Ajustes',
          tabBarIcon: ({ color, size }) => <Settings size={size} color={color} />,
          headerTitle: 'Configuración',
          tabBarAccessibilityLabel: 'Ir a configuración de la aplicación',
        }}
      />
    </Tabs>
  );
}
