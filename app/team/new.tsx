import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Switch,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { router } from 'expo-router';
import { Plus, X } from 'lucide-react-native';
import { BaseHeader } from '@/components/base';
import Colors from '@/constants/colors';
import { typography, spacing, radius } from '@/constants/theme';
import { useTeamStore } from '@/stores/team-store';
import { useAuthStore } from '@/stores/auth-store';
import { TeamMember } from '@/types/team';
import { booleanPermissionsToArray } from '@/utils/permission-helpers';

export default function NewTeamMemberScreen() {
  const { addMember } = useTeamStore();
  const { user } = useAuthStore();

  // Form state
  const [name, setName] = useState('');
  const [role, setRole] = useState<TeamMember['role']>('Asistente');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [licenseNumber, setLicenseNumber] = useState('');
  const [specializations, setSpecializations] = useState<string[]>([]);
  const [newSpecialization, setNewSpecialization] = useState('');
  const [activeStatus, setActiveStatus] = useState(true);

  // Permissions
  const [canCreateServices, setCanCreateServices] = useState(true);
  const [canAccessInventory, setCanAccessInventory] = useState(false);
  const [canViewReports, setCanViewReports] = useState(false);
  const [canManageClients, setCanManageClients] = useState(true);

  const roles: TeamMember['role'][] = [
    'Colorista',
    'Asistente',
    'Estilista',
    'Recepcionista',
    'Manager',
  ];

  const handleAddSpecialization = () => {
    if (newSpecialization.trim() && !specializations.includes(newSpecialization.trim())) {
      setSpecializations([...specializations, newSpecialization.trim()]);
      setNewSpecialization('');
    }
  };

  const handleRemoveSpecialization = (spec: string) => {
    setSpecializations(specializations.filter(s => s !== spec));
  };

  const handleSave = () => {
    if (!name.trim()) {
      Alert.alert('Error', 'Por favor ingresa el nombre del miembro del equipo');
      return;
    }

    addMember({
      name: name.trim(),
      role,
      email: email.trim() || '',
      phone: phone.trim() || undefined,
      licenseNumber: licenseNumber.trim() || undefined,
      specializations: specializations.length > 0 ? specializations : undefined,
      status: activeStatus ? 'active' : 'inactive',
      permissions: booleanPermissionsToArray({
        canCreateServices,
        canAccessInventory,
        canViewReports,
        canManageClients,
      }),
      salonId: user?.salonId || '',
    });

    Alert.alert('Éxito', 'Miembro del equipo añadido correctamente', [
      { text: 'OK', onPress: () => router.back() },
    ]);
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <BaseHeader title="Nuevo Miembro" onBack={() => router.back()} />

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* Basic Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Información Básica</Text>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Nombre Completo *</Text>
            <TextInput
              style={styles.textInput}
              value={name}
              onChangeText={setName}
              placeholder="Nombre del miembro del equipo"
              placeholderTextColor={Colors.light.gray}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Rol *</Text>
            <View style={styles.roleSelector}>
              {roles.map(r => (
                <TouchableOpacity
                  key={r}
                  style={[styles.roleOption, role === r && styles.roleOptionSelected]}
                  onPress={() => setRole(r)}
                >
                  <Text
                    style={[styles.roleOptionText, role === r && styles.roleOptionTextSelected]}
                  >
                    {r}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Estado</Text>
            <View style={styles.switchContainer}>
              <Text style={styles.switchLabel}>{activeStatus ? 'Activo' : 'Inactivo'}</Text>
              <Switch
                value={activeStatus}
                onValueChange={setActiveStatus}
                trackColor={{
                  false: Colors.light.lightGray,
                  true: Colors.light.success,
                }}
                thumbColor={Colors.light.textLight}
              />
            </View>
          </View>
        </View>

        {/* Contact Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Información de Contacto</Text>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Email</Text>
            <TextInput
              style={styles.textInput}
              value={email}
              onChangeText={setEmail}
              placeholder="<EMAIL>"
              placeholderTextColor={Colors.light.gray}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Teléfono</Text>
            <TextInput
              style={styles.textInput}
              value={phone}
              onChangeText={setPhone}
              placeholder="+34 600 000 000"
              placeholderTextColor={Colors.light.gray}
              keyboardType="phone-pad"
            />
          </View>
        </View>

        {/* Professional Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Información Profesional</Text>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Número de Licencia</Text>
            <TextInput
              style={styles.textInput}
              value={licenseNumber}
              onChangeText={setLicenseNumber}
              placeholder="Número de licencia profesional"
              placeholderTextColor={Colors.light.gray}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Especializaciones</Text>
            <View style={styles.tagsContainer}>
              {specializations.map((spec, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>{spec}</Text>
                  <TouchableOpacity onPress={() => handleRemoveSpecialization(spec)}>
                    <X size={14} color={Colors.light.gray} />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
            <View style={styles.addItemContainer}>
              <TextInput
                style={styles.addItemInput}
                placeholder="Añadir especialización..."
                placeholderTextColor={Colors.light.gray}
                value={newSpecialization}
                onChangeText={setNewSpecialization}
                onSubmitEditing={handleAddSpecialization}
              />
              <TouchableOpacity style={styles.addItemButton} onPress={handleAddSpecialization}>
                <Plus size={20} color={Colors.light.primary} />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Permissions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Permisos</Text>

          <View style={styles.permissionItem}>
            <View style={styles.permissionInfo}>
              <Text style={styles.permissionLabel}>Crear Servicios</Text>
              <Text style={styles.permissionDescription}>
                Puede crear nuevos servicios de coloración
              </Text>
            </View>
            <Switch
              value={canCreateServices}
              onValueChange={setCanCreateServices}
              trackColor={{
                false: Colors.light.lightGray,
                true: Colors.light.primary,
              }}
              thumbColor={Colors.light.textLight}
            />
          </View>

          <View style={styles.permissionItem}>
            <View style={styles.permissionInfo}>
              <Text style={styles.permissionLabel}>Acceso al Inventario</Text>
              <Text style={styles.permissionDescription}>
                Puede ver y gestionar el inventario de productos
              </Text>
            </View>
            <Switch
              value={canAccessInventory}
              onValueChange={setCanAccessInventory}
              trackColor={{
                false: Colors.light.lightGray,
                true: Colors.light.primary,
              }}
              thumbColor={Colors.light.textLight}
            />
          </View>

          <View style={styles.permissionItem}>
            <View style={styles.permissionInfo}>
              <Text style={styles.permissionLabel}>Ver Reportes</Text>
              <Text style={styles.permissionDescription}>
                Puede acceder a reportes y estadísticas
              </Text>
            </View>
            <Switch
              value={canViewReports}
              onValueChange={setCanViewReports}
              trackColor={{
                false: Colors.light.lightGray,
                true: Colors.light.primary,
              }}
              thumbColor={Colors.light.textLight}
            />
          </View>

          <View style={styles.permissionItem}>
            <View style={styles.permissionInfo}>
              <Text style={styles.permissionLabel}>Gestionar Clientes</Text>
              <Text style={styles.permissionDescription}>
                Puede añadir y editar información de clientes
              </Text>
            </View>
            <Switch
              value={canManageClients}
              onValueChange={setCanManageClients}
              trackColor={{
                false: Colors.light.lightGray,
                true: Colors.light.primary,
              }}
              thumbColor={Colors.light.textLight}
            />
          </View>
        </View>

        {/* Save Button */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
            <Text style={styles.saveButtonText}>Añadir Miembro</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  content: {
    flex: 1,
  },
  section: {
    padding: spacing.lg,
    backgroundColor: Colors.light.background,
    marginBottom: spacing.md,
  },
  sectionTitle: {
    ...typography.h4,
    color: Colors.light.text,
    marginBottom: spacing.lg,
  },
  inputGroup: {
    marginBottom: spacing.lg,
  },
  inputLabel: {
    ...typography.caption,
    color: Colors.light.gray,
    marginBottom: spacing.sm,
  },
  textInput: {
    ...typography.body,
    color: Colors.light.text,
    borderWidth: 1,
    borderColor: Colors.light.lightGray,
    borderRadius: radius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    backgroundColor: Colors.light.background,
  },
  roleSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  roleOption: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: radius.full,
    borderWidth: 1,
    borderColor: Colors.light.lightGray,
    backgroundColor: Colors.light.background,
  },
  roleOptionSelected: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  roleOptionText: {
    ...typography.body,
    color: Colors.light.text,
  },
  roleOptionTextSelected: {
    color: Colors.light.textLight,
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacing.sm,
  },
  switchLabel: {
    ...typography.body,
    color: Colors.light.text,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
    marginBottom: spacing.md,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: radius.full,
    gap: spacing.xs,
  },
  tagText: {
    ...typography.caption,
    color: Colors.light.text,
  },
  addItemContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  addItemInput: {
    flex: 1,
    ...typography.body,
    color: Colors.light.text,
    borderWidth: 1,
    borderColor: Colors.light.lightGray,
    borderRadius: radius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: Colors.light.background,
  },
  addItemButton: {
    backgroundColor: Colors.light.primary,
    borderRadius: radius.md,
    padding: spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },
  permissionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.lightGray,
  },
  permissionInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  permissionLabel: {
    ...typography.body,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  permissionDescription: {
    ...typography.caption,
    color: Colors.light.gray,
  },
  buttonContainer: {
    padding: spacing.lg,
    paddingBottom: spacing.xl,
  },
  saveButton: {
    backgroundColor: Colors.light.primary,
    paddingVertical: spacing.md,
    borderRadius: radius.md,
    alignItems: 'center',
  },
  saveButtonText: {
    ...typography.buttonMedium,
    color: Colors.light.textLight,
  },
});
