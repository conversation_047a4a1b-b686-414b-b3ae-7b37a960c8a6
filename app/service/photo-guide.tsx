import React from 'react';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Image } from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { ChevronLeft, Camera, Info } from 'lucide-react-native';
import Colors from '@/constants/colors';

const PHOTO_GUIDE_SECTIONS = [
  {
    id: 'top',
    title: 'Vista Superior',
    description: 'Toma la foto desde arriba para capturar la corona y el crecimiento del cabello',
    imageUrl:
      'https://images.unsplash.com/photo-1560869713-7d0b29837c64?w=400&h=600&fit=crop&crop=top',
    tips: [
      'Mantén la cámara directamente sobre la cabeza',
      'Asegúrate de que toda la corona sea visible',
      'Evita sombras en el área central',
    ],
  },
  {
    id: 'sides',
    title: 'Vista Lateral',
    description: 'Captura el perfil para analizar la textura y el estado del cabello',
    imageUrl:
      'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400&h=600&fit=crop&crop=center',
    tips: [
      'Toma fotos de ambos lados',
      'Mantén el cabello suelto y natural',
      'Enfoca en la línea del cabello y las puntas',
    ],
  },
  {
    id: 'back',
    title: 'Vista Posterior',
    description: 'Documenta la parte trasera para un análisis completo del color y daño',
    imageUrl:
      'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=600&fit=crop&crop=center',
    tips: [
      'Captura desde la nuca hasta las puntas',
      'Asegúrate de que el cabello esté bien iluminado',
      'Incluye diferentes secciones si hay variaciones de color',
    ],
  },
];

export default function PhotoGuideScreen() {
  const { clientId } = useLocalSearchParams();

  const handleContinue = () => {
    router.push(clientId ? `/service/new?clientId=${clientId}` : '/service/new');
  };

  const handleBack = () => {
    router.back();
  };

  const renderPhotoSection = (section: (typeof PHOTO_GUIDE_SECTIONS)[0]) => (
    <View key={section.id} style={styles.sectionContainer}>
      <Text style={styles.sectionTitle}>{section.title}</Text>

      <View style={styles.imageContainer}>
        <Image source={{ uri: section.imageUrl }} style={styles.guideImage} resizeMode="cover" />
        <View style={styles.imageOverlay}>
          <Camera size={24} color={Colors.light.textLight} />
        </View>
      </View>

      <Text style={styles.sectionDescription}>{section.description}</Text>

      <View style={styles.tipsContainer}>
        <View style={styles.tipsHeader}>
          <Info size={16} color={Colors.light.primary} />
          <Text style={styles.tipsTitle}>Consejos:</Text>
        </View>
        {section.tips.map((tip, index) => (
          <Text key={index} style={styles.tipItem}>
            • {tip}
          </Text>
        ))}
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <ChevronLeft size={24} color={Colors.light.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Consulta de Coloración</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <Text style={styles.mainTitle}>Captura de Imágenes del Cabello</Text>
          <Text style={styles.mainDescription}>
            Sigue esta guía para capturar 3-5 imágenes del cabello del cliente para un análisis de
            color preciso.
          </Text>

          <View style={styles.importantNote}>
            <View style={styles.noteHeader}>
              <Info size={20} color={Colors.light.primary} />
              <Text style={styles.noteTitle}>Importante para la Privacidad</Text>
            </View>
            <Text style={styles.noteText}>
              • Las imágenes se procesan con difuminado facial automático{'\n'}• Se eliminan
              inmediatamente después del análisis{'\n'}• No se almacenan fotos permanentemente
            </Text>
          </View>

          {PHOTO_GUIDE_SECTIONS.map(renderPhotoSection)}

          <View style={styles.qualityTips}>
            <Text style={styles.qualityTitle}>Para mejores resultados:</Text>
            <View style={styles.qualityList}>
              <Text style={styles.qualityItem}>✓ Usa luz natural o iluminación uniforme</Text>
              <Text style={styles.qualityItem}>✓ Mantén el cabello limpio y desenredado</Text>
              <Text style={styles.qualityItem}>✓ Evita filtros o efectos de cámara</Text>
              <Text style={styles.qualityItem}>✓ Toma múltiples fotos de cada ángulo</Text>
              <Text style={styles.qualityItem}>
                ✓ Asegúrate de que las imágenes estén enfocadas
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>

      <View style={styles.bottomContainer}>
        <TouchableOpacity style={styles.continueButton} onPress={handleContinue}>
          <Text style={styles.continueButtonText}>Comenzar Análisis</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 15,
    backgroundColor: Colors.light.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
  },
  placeholder: {
    width: 34,
  },
  scrollContainer: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  mainTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 8,
    lineHeight: 34,
  },
  mainDescription: {
    fontSize: 16,
    color: Colors.light.gray,
    lineHeight: 22,
    marginBottom: 24,
  },
  importantNote: {
    backgroundColor: Colors.light.primary + '10',
    borderRadius: 12,
    padding: 16,
    marginBottom: 32,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.primary,
  },
  noteHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  noteTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.primary,
  },
  noteText: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
  },
  sectionContainer: {
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 16,
  },
  imageContainer: {
    position: 'relative',
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
    aspectRatio: 2 / 3,
  },
  guideImage: {
    width: '100%',
    height: '100%',
  },
  imageOverlay: {
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: Colors.common.black + '99',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sectionDescription: {
    fontSize: 16,
    color: Colors.light.text,
    lineHeight: 22,
    marginBottom: 16,
  },
  tipsContainer: {
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 8,
    padding: 12,
  },
  tipsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 6,
  },
  tipsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.primary,
  },
  tipItem: {
    fontSize: 14,
    color: Colors.light.gray,
    lineHeight: 20,
    marginBottom: 4,
  },
  qualityTips: {
    backgroundColor: Colors.light.success + '10',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  qualityTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.success,
    marginBottom: 12,
  },
  qualityList: {
    gap: 8,
  },
  qualityItem: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
  },
  bottomContainer: {
    backgroundColor: Colors.light.background,
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  continueButton: {
    backgroundColor: Colors.light.primary,
    borderRadius: 16,
    paddingVertical: 16,
    alignItems: 'center',
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  continueButtonText: {
    color: Colors.light.textLight,
    fontSize: 16,
    fontWeight: '700',
  },
});
