import React, { useEffect, useState, useCallback } from 'react';
import { logger } from '@/utils/logger';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  Image,
  Platform,
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { ChevronLeft, Calendar, Star, Palette, Clock } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { Database } from '@/types/database';
import { typography, spacing, radius, shadows } from '@/constants/theme';
import { BaseCard } from '@/components/base';
import { useClientHistoryStore } from '@/stores/client-history-store';

export default function ServiceDetailScreen() {
  const { serviceId } = useLocalSearchParams();
  const { getServiceDetails } = useClientHistoryStore();

  const [service, setService] = useState<Database['public']['Tables']['services']['Row'] | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadServiceData = useCallback(async () => {
    if (!serviceId) {
      setError('ID de servicio no válido');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      const serviceData = await getServiceDetails(serviceId as string);
      setService(serviceData);
    } catch (err) {
      logger.error('Error loading service', 'ServiceDetailScreen', err);
      setError('Error al cargar el servicio');
    } finally {
      setIsLoading(false);
    }
  }, [serviceId, getServiceDetails]);

  useEffect(() => {
    loadServiceData();
  }, [loadServiceData]);

  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ChevronLeft size={24} color={Colors.light.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Cargando...</Text>
        </View>
      </View>
    );
  }

  if (error || !service) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ChevronLeft size={24} color={Colors.light.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{error || 'Servicio no encontrado'}</Text>
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error || 'No se pudo cargar el servicio'}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadServiceData}>
            <Text style={styles.retryButtonText}>Reintentar</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ChevronLeft size={24} color={Colors.light.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Detalle del Servicio</Text>
      </View>

      {/* Service Overview Card */}
      <BaseCard style={styles.overviewCard}>
        <View style={styles.cardHeader}>
          <Text style={styles.serviceType}>{service.serviceType || 'Coloración'}</Text>
          {service.satisfactionScore && (
            <View style={styles.satisfactionBadge}>
              <Star size={16} color={Colors.light.warning} fill={Colors.light.warning} />
              <Text style={styles.satisfactionText}>{service.satisfactionScore}/5</Text>
            </View>
          )}
        </View>

        <View style={styles.infoRow}>
          <Calendar size={16} color={Colors.light.gray} />
          <Text style={styles.infoText}>
            {service.serviceDate
              ? new Date(service.serviceDate).toLocaleDateString('es-ES', {
                  day: 'numeric',
                  month: 'long',
                  year: 'numeric',
                })
              : 'Fecha no disponible'}
          </Text>
        </View>

        {service.formula?.processingTime && (
          <View style={styles.infoRow}>
            <Clock size={16} color={Colors.light.gray} />
            <Text style={styles.infoText}>{service.formula.processingTime} minutos</Text>
          </View>
        )}

        {service.formula?.technique && (
          <View style={styles.infoRow}>
            <Palette size={16} color={Colors.light.gray} />
            <Text style={styles.infoText}>{service.formula.technique}</Text>
          </View>
        )}

        {service.stylist && (
          <View style={styles.infoRow}>
            <Text style={styles.labelText}>Estilista: </Text>
            <Text style={styles.infoText}>{service.stylist.name}</Text>
          </View>
        )}
      </BaseCard>

      {/* Formula Card */}
      {service.formula && (
        <BaseCard style={styles.formulaCard}>
          <Text style={styles.sectionTitle}>Fórmula Aplicada</Text>
          <View style={styles.formulaContent}>
            <Text style={styles.formulaText}>{service.formula.formulaText}</Text>

            {service.formula.brand && (
              <View style={styles.formulaDetail}>
                <Text style={styles.formulaLabel}>Marca: </Text>
                <Text style={styles.formulaValue}>{service.formula.brand}</Text>
              </View>
            )}

            {service.formula.line && (
              <View style={styles.formulaDetail}>
                <Text style={styles.formulaLabel}>Línea: </Text>
                <Text style={styles.formulaValue}>{service.formula.line}</Text>
              </View>
            )}

            {service.formula.developerVolume && (
              <View style={styles.formulaDetail}>
                <Text style={styles.formulaLabel}>Volumen oxidante: </Text>
                <Text style={styles.formulaValue}>{service.formula.developerVolume} vol</Text>
              </View>
            )}
          </View>
        </BaseCard>
      )}

      {/* Notes Card */}
      {service.notes && (
        <BaseCard style={styles.notesCard}>
          <Text style={styles.sectionTitle}>Notas del Servicio</Text>
          <Text style={styles.notesText}>{service.notes}</Text>
        </BaseCard>
      )}

      {/* Photos Section */}
      {(service.beforePhotos?.length > 0 || service.afterPhotos?.length > 0) && (
        <BaseCard style={styles.photosCard}>
          <Text style={styles.sectionTitle}>Fotos del Servicio</Text>

          {service.beforePhotos?.length > 0 && (
            <View style={styles.photoSection}>
              <Text style={styles.photoLabel}>Antes:</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                {service.beforePhotos.map((photo: string, index: number) => (
                  <Image key={`before-${index}`} source={{ uri: photo }} style={styles.photo} />
                ))}
              </ScrollView>
            </View>
          )}

          {service.afterPhotos?.length > 0 && (
            <View style={styles.photoSection}>
              <Text style={styles.photoLabel}>Después:</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                {service.afterPhotos.map((photo: string, index: number) => (
                  <Image key={`after-${index}`} source={{ uri: photo }} style={styles.photo} />
                ))}
              </ScrollView>
            </View>
          )}
        </BaseCard>
      )}

      {/* Client Info */}
      {service.client && (
        <BaseCard style={styles.clientCard}>
          <Text style={styles.sectionTitle}>Información del Cliente</Text>
          <Text style={styles.clientName}>{service.client.name}</Text>
          {service.client.phone && (
            <Text style={styles.clientInfo}>Tel: {service.client.phone}</Text>
          )}
          {service.client.email && (
            <Text style={styles.clientInfo}>Email: {service.client.email}</Text>
          )}
        </BaseCard>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    backgroundColor: Colors.light.card,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: spacing.md,
    paddingHorizontal: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    ...shadows.sm,
  },
  backButton: {
    padding: spacing.xs,
    marginRight: spacing.md,
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  overviewCard: {
    margin: spacing.md,
    padding: spacing.lg,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  serviceType: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.primary,
  },
  satisfactionBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.warning + '20',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: radius.md,
    gap: spacing.xs,
  },
  satisfactionText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.warning,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    marginBottom: spacing.sm,
  },
  infoText: {
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  formulaCard: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.md,
    padding: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.md,
  },
  formulaContent: {
    backgroundColor: Colors.light.background,
    padding: spacing.md,
    borderRadius: radius.sm,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  formulaText: {
    fontSize: typography.sizes.base,
    color: Colors.light.text,
    lineHeight: 22,
  },
  notesCard: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.md,
    padding: spacing.lg,
  },
  notesText: {
    fontSize: typography.sizes.base,
    color: Colors.light.text,
    lineHeight: 22,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  errorText: {
    fontSize: typography.sizes.base,
    color: Colors.light.error,
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: Colors.light.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: radius.md,
  },
  retryButtonText: {
    color: Colors.light.textLight,
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
  },
  labelText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.gray,
    fontWeight: typography.weights.medium,
  },
  formulaDetail: {
    flexDirection: 'row',
    marginTop: spacing.sm,
  },
  formulaLabel: {
    fontSize: typography.sizes.sm,
    color: Colors.light.gray,
    fontWeight: typography.weights.medium,
  },
  formulaValue: {
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
    flex: 1,
  },
  photosCard: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.md,
    padding: spacing.lg,
  },
  photoSection: {
    marginBottom: spacing.md,
  },
  photoLabel: {
    fontSize: typography.sizes.sm,
    color: Colors.light.gray,
    fontWeight: typography.weights.medium,
    marginBottom: spacing.sm,
  },
  photo: {
    width: 150,
    height: 150,
    borderRadius: radius.sm,
    marginRight: spacing.sm,
  },
  clientCard: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.md,
    padding: spacing.lg,
  },
  clientName: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  clientInfo: {
    fontSize: typography.sizes.sm,
    color: Colors.light.gray,
    marginBottom: spacing.xs,
  },
});
