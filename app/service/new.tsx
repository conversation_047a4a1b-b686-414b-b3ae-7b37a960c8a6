import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, View, Alert } from 'react-native';
import { logger } from '@/utils/logger';
import { useLocalSearchParams, router } from 'expo-router';
import Colors from '@/constants/colors';
import { useClientStore } from '@/stores/client-store';
import { useClientHistoryStore } from '@/stores/client-history-store';
import { useAIAnalysisStore } from '@/stores/ai-analysis-store';
import { useDashboardStore } from '@/stores/dashboard-store';
import { useSyncQueueStore } from '@/stores/sync-queue-store';
import Toast from '@/components/Toast';

// Import our new components and hooks
import { useServiceFlow, STEPS } from '@/src/service/hooks/useServiceFlow';
import { useServicePersistence } from '@/src/service/hooks/useServicePersistence';
import { useServiceDraftStore } from '@/stores/service-draft-store';
import { ServiceHeader } from '@/src/service/components/ServiceHeader';
import { StepIndicator } from '@/src/service/components/StepIndicator';
import { ServiceBreadcrumbs } from '@/src/service/components/ServiceBreadcrumbs';
import { DiagnosisStep } from '@/src/service/components/DiagnosisStep';
import { DesiredColorStep } from '@/src/service/components/DesiredColorStep';
import { FormulationStep } from '@/src/service/components/FormulationStep';
import { CompletionStep } from '@/src/service/components/CompletionStep';

export default function NewServiceScreen() {
  const { clientId, restoreDraft } = useLocalSearchParams();
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [lastSaved, setLastSaved] = useState<Date | undefined>();
  const [isSaving, setIsSaving] = useState(false);
  const [_autoSaveTimer, _setAutoSaveTimer] = useState<number | null>(null);
  const navigationTimerRef = useRef<number | null>(null);
  const autoSaveIntervalRef = useRef<number | null>(null);
  const savingTimeoutRef = useRef<number | null>(null);
  const isSavingRef = useRef(false);
  const serviceDataRef = useRef(serviceData);
  const currentStepRef = useRef(currentStep);

  // Helper functions for breadcrumbs
  const getCompletedSteps = () => {
    const completed = [];
    for (let i = 0; i < currentStep; i++) {
      completed.push(i);
    }
    return completed;
  };

  const getStepSubtitle = (stepId: string): string | undefined => {
    switch (stepId) {
      case 'diagnosis':
        return serviceData.client?.name || 'Cliente no seleccionado';
      case 'desired':
        return serviceData.desiredAnalysisResult?.general?.overallLevel
          ? `Nivel ${serviceData.desiredAnalysisResult.general.overallLevel}`
          : undefined;
      case 'formulation':
        return serviceData.formula ? 'Fórmula generada' : undefined;
      case 'completion':
        return 'Finalizar servicio';
      default:
        return undefined;
    }
  };

  // Cleanup timers on unmount
  useEffect(() => {
    return () => {
      if (navigationTimerRef.current) {
        clearTimeout(navigationTimerRef.current);
      }
      if (_autoSaveTimer) {
        clearTimeout(_autoSaveTimer);
      }
      if (autoSaveIntervalRef.current) {
        clearInterval(autoSaveIntervalRef.current);
      }
    };
  }, [_autoSaveTimer]);

  // Use our new hooks
  const {
    currentStep,
    serviceData,
    updateServiceData,
    goToNextStep,
    goToPreviousStep,
    goToStep,
    canNavigateToStep,
  } = useServiceFlow();

  const { saveServiceDraft, loadServiceDraft, deleteServiceDraft } = useServicePersistence();

  // Client Store
  const { getClient } = useClientStore();

  // Client History Store
  const { saveCompletedService } = useClientHistoryStore();

  // AI Analysis Store (for passing to components)
  const { analysisResult, clearAnalysis } = useAIAnalysisStore();

  // Dashboard Store (for refreshing metrics)
  const { refreshMetrics } = useDashboardStore();

  // Clear any previous AI analysis results when starting a new service
  useEffect(() => {
    clearAnalysis();
  }, [clearAnalysis]);

  // Load client data on mount
  useEffect(() => {
    const loadClientData = async () => {
      if (clientId && typeof clientId === 'string') {
        try {
          const clientData = await getClient(clientId);
          if (clientData) {
            updateServiceData({
              client: clientData,
              clientId: clientId,
            });

            // Try to load draft if requested
            if (restoreDraft === 'true') {
              const draft = loadServiceDraft(clientId);
              if (draft) {
                // Restore service data from draft - only restore compatible data
                const restoredData: Record<string, unknown> = {};

                if (draft.diagnosisData) {
                  Object.assign(restoredData, draft.diagnosisData);
                }
                if (draft.desiredData) {
                  Object.assign(restoredData, draft.desiredData);
                }
                if (draft.formulationData) {
                  Object.assign(restoredData, draft.formulationData);
                }
                if (draft.resultData) {
                  Object.assign(restoredData, draft.resultData);
                }

                updateServiceData(restoredData);
                showToastMessage('Borrador restaurado correctamente');
              }
            }
          }
        } catch (error) {
          logger.error('Error loading client', 'NewServiceScreen', error);
          showToastMessage('Error al cargar datos del cliente');
        }
      }
    };

    loadClientData();
  }, [clientId, restoreDraft, getClient, loadServiceDraft, updateServiceData]);

  // Clean up timers and intervals on unmount
  useEffect(() => {
    return () => {
      if (_autoSaveTimer) {
        clearTimeout(_autoSaveTimer);
      }
      if (autoSaveIntervalRef.current) {
        clearInterval(autoSaveIntervalRef.current);
        autoSaveIntervalRef.current = null;
      }
      if (savingTimeoutRef.current) {
        clearTimeout(savingTimeoutRef.current);
        savingTimeoutRef.current = null;
      }
    };
  }, [_autoSaveTimer]);

  // Sync state with refs to avoid dependency loops
  useEffect(() => {
    isSavingRef.current = isSaving;
    serviceDataRef.current = serviceData;
    currentStepRef.current = currentStep;
  }, [isSaving, serviceData, currentStep]);

  // Auto-save draft periodically - FIXED: No dependencies to prevent constant re-execution
  useEffect(() => {
    // Clear existing interval first
    if (autoSaveIntervalRef.current) {
      clearInterval(autoSaveIntervalRef.current);
      autoSaveIntervalRef.current = null;
    }

    // Clear any existing saving timeout
    if (savingTimeoutRef.current) {
      clearTimeout(savingTimeoutRef.current);
      savingTimeoutRef.current = null;
    }

    // Start auto-save interval (will check conditions inside the interval)
    autoSaveIntervalRef.current = setInterval(() => {
      // DEBUG: Log interval execution
      // console.log('[DEBUG] Auto-save interval executing at', new Date().toLocaleTimeString());

      // Get current state from refs to avoid stale closures
      const currentServiceData = serviceDataRef.current;
      const currentStepValue = currentStepRef.current;

      // Check ALL loading/processing states before auto-saving to avoid interference
      const aiStore = useAIAnalysisStore.getState();
      const syncStore = useSyncQueueStore.getState();
      const clientHistoryStore = useClientHistoryStore.getState();

      const isSystemBusy =
        // AI processing states
        aiStore.isAnalyzing ||
        aiStore.isAnalyzingDesiredPhoto ||
        // Sync and data states
        syncStore.isSyncing ||
        clientHistoryStore.isLoading ||
        // Local saving state - use ref to avoid dependency loop
        isSavingRef.current;

      // Only auto-save when system is truly idle and we have valid data
      if (!isSystemBusy && currentServiceData?.clientId && currentServiceData?.client) {
        // console.log('[DEBUG] Starting auto-save at', new Date().toLocaleTimeString());
        setIsSaving(true);

        // Call saveServiceDraft directly using the store to avoid hook dependencies
        const { saveDraft, createDraftFromServiceState } = useServiceDraftStore.getState();

        const serviceState = {
          diagnosisMethod: currentServiceData.diagnosisMethod,
          hairPhotos: currentServiceData.hairPhotos,
          hairThickness: currentServiceData.hairThickness,
          hairDensity: currentServiceData.hairDensity,
          overallTone: currentServiceData.overallTone,
          overallUndertone: currentServiceData.overallUndertone,
          lastChemicalProcessType: currentServiceData.lastChemicalProcessType,
          lastChemicalProcessDate: currentServiceData.lastChemicalProcessDate,
          diagnosisNotes: currentServiceData.diagnosisNotes,
          zoneColorAnalysis: currentServiceData.zoneColorAnalysis,
          zonePhysicalAnalysis: currentServiceData.zonePhysicalAnalysis,
          desiredMethod: currentServiceData.desiredMethod,
          desiredPhotos: currentServiceData.desiredPhotos,
          desiredAnalysisResult: currentServiceData.desiredAnalysisResult,
          desiredNotes: currentServiceData.desiredNotes,
          selectedBrand: currentServiceData.selectedBrand,
          selectedLine: currentServiceData.selectedLine,
          formula: currentServiceData.formula,
          isFormulaFromAI: currentServiceData.isFormulaFromAI,
          formulaCost: currentServiceData.formulaCost,
          viabilityAnalysis: currentServiceData.viabilityAnalysis,
          stockValidation: currentServiceData.stockValidation,
          resultImage: currentServiceData.resultImage,
          clientSatisfaction: currentServiceData.clientSatisfaction,
          resultNotes: currentServiceData.resultNotes,
        };

        const draft = createDraftFromServiceState(
          currentServiceData.clientId,
          currentServiceData.client.name,
          currentStepValue,
          serviceState
        );

        saveDraft(draft);

        // Clear any existing saving timeout
        if (savingTimeoutRef.current) {
          clearTimeout(savingTimeoutRef.current);
          savingTimeoutRef.current = null;
        }

        // Set a robust timeout with safety mechanism
        savingTimeoutRef.current = setTimeout(() => {
          // console.log('[DEBUG] Finishing auto-save at', new Date().toLocaleTimeString());
          setIsSaving(false);
          setLastSaved(new Date());
          savingTimeoutRef.current = null;
        }, 800);

        // Safety timeout to force reset if something goes wrong
        setTimeout(() => {
          if (isSavingRef.current) {
            // console.log(
            //   '[DEBUG] Safety timeout: Force finishing auto-save at',
            //   new Date().toLocaleTimeString()
            // );
            setIsSaving(false);
            if (savingTimeoutRef.current) {
              clearTimeout(savingTimeoutRef.current);
              savingTimeoutRef.current = null;
            }
          }
        }, 2000);
      }
    }, 20000); // Auto-save every 20 seconds

    return () => {
      if (autoSaveIntervalRef.current) {
        clearInterval(autoSaveIntervalRef.current);
        autoSaveIntervalRef.current = null;
      }
      if (savingTimeoutRef.current) {
        clearTimeout(savingTimeoutRef.current);
        savingTimeoutRef.current = null;
      }
    };
  }, []); // No dependencies - interval runs independently

  const showToastMessage = (message: string) => {
    setToastMessage(message);
    setShowToast(true);
  };

  const performAutoSave = () => {
    if (serviceData.clientId) {
      setIsSaving(true);
      saveServiceDraft(serviceData, currentStep);

      // Show saving state for at least 800ms for better UX
      setTimeout(() => {
        setIsSaving(false);
        setLastSaved(new Date());
      }, 800);
    }
  };

  const handleSaveDraft = (_silent = false) => {
    performAutoSave();
  };

  const handleFinishService = async () => {
    try {
      // Add service to client history
      if (serviceData.clientId && serviceData.client) {
        // Save the completed service to database
        await saveCompletedService({
          clientId: serviceData.clientId,
          clientName: serviceData.client.name,
          serviceType: 'color_service',
          formula: serviceData.formula,
          formulaData: serviceData.formulationData,
          technique: serviceData.applicationTechnique,
          processingTime: serviceData.processingTime,
          developerVolume: serviceData.developerVolume,
          satisfaction: serviceData.clientSatisfaction,
          notes: serviceData.resultNotes,
          beforePhotos: serviceData.diagnosisImage ? [serviceData.diagnosisImage] : [],
          afterPhotos: serviceData.resultImage ? [serviceData.resultImage] : [],
          aiAnalysis: analysisResult || null,
        });

        // Delete draft after successful completion
        deleteServiceDraft(serviceData.clientId);

        // Stop auto-save timer since service is completed
        if (autoSaveIntervalRef.current) {
          clearInterval(autoSaveIntervalRef.current);
          autoSaveIntervalRef.current = null;
        }

        // Small delay to ensure Zustand persists the deletion to AsyncStorage
        await new Promise(resolve => setTimeout(resolve, 100));

        // Refresh dashboard metrics
        await refreshMetrics();

        showToastMessage('Servicio completado y guardado');

        // Navigate to dashboard after a short delay
        navigationTimerRef.current = setTimeout(() => {
          router.replace('/(tabs)');
        }, 1500);
      }
    } catch (error) {
      logger.error('Error finishing service', 'NewServiceScreen', error);
      Alert.alert('Error', 'No se pudo completar el servicio. Por favor intenta nuevamente.');
    }
  };

  const handleStepNavigation = (stepIndex: number) => {
    if (canNavigateToStep(stepIndex)) {
      goToStep(stepIndex);
    } else {
      Alert.alert('Paso no disponible', 'Completa los pasos anteriores antes de continuar.', [
        { text: 'OK' },
      ]);
    }
  };

  // Silent save for AI analysis results
  const handleSaveDraftSilent = () => {
    handleSaveDraft(true);
  };

  // Render the appropriate step component
  const renderCurrentStep = () => {
    const commonProps = {
      data: serviceData,
      onUpdate: (updates: Record<string, unknown>) => {
        updateServiceData(updates);

        // DISABLED: Auto-save timer to prevent conflicts with interval-based auto-save
        // The interval-based auto-save (every 20 seconds) handles all automatic saving
        // Manual saves are still available through onSave and onSaveSilent
      },
      onNext: goToNextStep,
      onBack: goToPreviousStep,
      onSave: handleSaveDraft,
      onSaveSilent: handleSaveDraftSilent,
    };

    switch (currentStep) {
      case 0:
        return <DiagnosisStep {...commonProps} />;
      case 1:
        return <DesiredColorStep {...commonProps} />;
      case 2:
        return <FormulationStep {...commonProps} analysisResult={analysisResult} />;
      case 3:
        return <CompletionStep {...commonProps} onNext={handleFinishService} />;
      default:
        return <DiagnosisStep {...commonProps} />;
    }
  };

  return (
    <View style={styles.container}>
      <ServiceHeader
        currentStep={currentStep}
        clientName={serviceData.client?.name}
        lastSaved={lastSaved}
        isSaving={isSaving}
        onBack={goToPreviousStep}
      />

      <ServiceBreadcrumbs
        steps={STEPS.map(step => ({
          ...step,
          subtitle: getStepSubtitle(step.id),
        }))}
        currentStep={currentStep}
        completedSteps={getCompletedSteps()}
        onStepPress={handleStepNavigation}
        canNavigate={(stepIndex: number) => Boolean(canNavigateToStep(stepIndex))}
      />

      <StepIndicator
        steps={STEPS}
        currentStep={currentStep}
        onStepPress={handleStepNavigation}
        canNavigateToStep={(stepIndex: number) => Boolean(canNavigateToStep(stepIndex))}
      />

      {renderCurrentStep()}

      {showToast && <Toast message={toastMessage} onHide={() => setShowToast(false)} />}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
});
