import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ScrollView } from 'react-native';
import { router } from 'expo-router';
import Colors from '@/constants/colors';
import OnboardingLayout from '@/components/onboarding/OnboardingLayout';
import { Ionicons } from '@expo/vector-icons';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { getCountryList, detectUserCountry, getRegionalConfig } from '@/utils/regionalConfig';
import { CountryCode } from '@/types/regional';

export default function WelcomeScreen() {
  const { updateCountry } = useSalonConfigStore();
  const [selectedCountry, setSelectedCountry] = useState(detectUserCountry());
  const [showCountryPicker, setShowCountryPicker] = useState(false);
  const countries = getCountryList();

  const handleContinue = () => {
    updateCountry(selectedCountry as CountryCode);
    router.push('/onboarding/workspace');
  };

  const handleSkip = () => {
    Alert.alert(
      'Saltar configuración',
      '¿Estás seguro de que quieres saltar la configuración inicial? Podrás configurar estos ajustes más tarde desde la pantalla de configuración.',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Saltar',
          style: 'destructive',
          onPress: () => {
            useSalonConfigStore.getState().setHasCompletedOnboarding(true);
            router.replace('/(tabs)');
          },
        },
      ]
    );
  };

  return (
    <OnboardingLayout
      currentStep={1}
      totalSteps={4}
      title="¡Bienvenido a Salonier!"
      subtitle="Tu asistente inteligente de coloración capilar"
      showBackButton={false}
    >
      <View style={styles.container}>
        <View style={styles.logoContainer}>
          <View style={styles.logoPlaceholder}>
            <Ionicons name="color-palette" size={80} color={Colors.light.primary} />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Configura tu región</Text>
          <Text style={styles.sectionDescription}>
            Esto nos ayudará a personalizar la aplicación con tu moneda, sistema de medidas y marcas
            locales
          </Text>

          <TouchableOpacity
            style={styles.pickerContainer}
            onPress={() => setShowCountryPicker(!showCountryPicker)}
          >
            <Text style={styles.pickerText}>
              {countries.find(c => c.value === selectedCountry)?.label || 'Seleccionar país'}
            </Text>
            <Ionicons name="chevron-down" size={20} color={Colors.light.textSecondary} />
          </TouchableOpacity>

          {showCountryPicker && (
            <ScrollView style={styles.pickerDropdown} showsVerticalScrollIndicator={false}>
              {countries.map(country => (
                <TouchableOpacity
                  key={country.value}
                  style={[
                    styles.pickerOption,
                    selectedCountry === country.value && styles.pickerOptionSelected,
                  ]}
                  onPress={() => {
                    setSelectedCountry(country.value);
                    setShowCountryPicker(false);
                  }}
                >
                  <Text
                    style={[
                      styles.pickerOptionText,
                      selectedCountry === country.value && styles.pickerOptionTextSelected,
                    ]}
                  >
                    {country.label}
                  </Text>
                  {selectedCountry === country.value && (
                    <Ionicons name="checkmark" size={20} color={Colors.light.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          )}

          <View style={styles.configPreview}>
            {(() => {
              const config = getRegionalConfig(selectedCountry);
              return (
                <>
                  <View style={styles.configItem}>
                    <Ionicons name="cash-outline" size={20} color={Colors.light.textSecondary} />
                    <Text style={styles.configLabel}>Moneda:</Text>
                    <Text style={styles.configValue}>
                      {config.currencySymbol} ({config.currency})
                    </Text>
                  </View>
                  <View style={styles.configItem}>
                    <Ionicons name="scale-outline" size={20} color={Colors.light.textSecondary} />
                    <Text style={styles.configLabel}>Sistema:</Text>
                    <Text style={styles.configValue}>
                      {config.measurementSystem === 'metric'
                        ? 'Métrico (ml, g)'
                        : 'Imperial (oz, fl oz)'}
                    </Text>
                  </View>
                  <View style={styles.configItem}>
                    <Ionicons
                      name="language-outline"
                      size={20}
                      color={Colors.light.textSecondary}
                    />
                    <Text style={styles.configLabel}>Idioma:</Text>
                    <Text style={styles.configValue}>{config.language}</Text>
                  </View>
                </>
              );
            })()}
          </View>
        </View>

        <View style={styles.footer}>
          <TouchableOpacity style={styles.primaryButton} onPress={handleContinue}>
            <Text style={styles.primaryButtonText}>Continuar</Text>
            <Ionicons name="arrow-forward" size={20} color={Colors.light.textLight} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
            <Text style={styles.skipButtonText}>Configurar más tarde</Text>
          </TouchableOpacity>
        </View>
      </View>
    </OnboardingLayout>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoPlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: Colors.light.surface,
    alignItems: 'center',
    justifyContent: 'center',
  },
  section: {
    marginBottom: 40,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 20,
    lineHeight: 20,
  },
  pickerContainer: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    marginBottom: 20,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 15,
    justifyContent: 'space-between',
  },
  pickerText: {
    fontSize: 16,
    color: Colors.light.text,
    flex: 1,
  },
  pickerDropdown: {
    backgroundColor: Colors.light.card,
    borderRadius: 12,
    marginTop: -10,
    marginBottom: 20,
    maxHeight: 300,
    shadowColor: Colors.common.shadowColor,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  pickerOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  pickerOptionSelected: {
    backgroundColor: `${Colors.light.primary}10`,
  },
  pickerOptionText: {
    fontSize: 16,
    color: Colors.light.text,
    flex: 1,
  },
  pickerOptionTextSelected: {
    color: Colors.light.primary,
    fontWeight: '600',
  },
  configPreview: {
    backgroundColor: Colors.light.card,
    borderRadius: 12,
    padding: 16,
    gap: 12,
  },
  configItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  configLabel: {
    flex: 1,
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  configValue: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.text,
  },
  footer: {
    marginTop: 'auto',
    gap: 12,
  },
  primaryButton: {
    backgroundColor: Colors.light.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 8,
  },
  primaryButtonText: {
    color: Colors.light.textLight,
    fontSize: 16,
    fontWeight: '600',
  },
  skipButton: {
    alignItems: 'center',
    padding: 12,
  },
  skipButtonText: {
    color: Colors.light.textSecondary,
    fontSize: 14,
  },
});
