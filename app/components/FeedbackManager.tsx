import React, { useEffect, useState, useCallback } from 'react';
import { AppState } from 'react-native';
import { useFormulaFeedbackStore } from '@/stores/formula-feedback-store';
import { useServiceDraftStore } from '@/stores/service-draft-store';
import FormulaFeedback from './FormulaFeedback';
import { logger } from '@/utils/logger';

interface FeedbackManagerProps {
  children: React.ReactNode;
}

const FEEDBACK_DELAY_MINUTES = 30;
const CHECK_INTERVAL_MS = 30000; // Check every 30 seconds

/**
 * FeedbackManager handles automatic formula feedback requests
 * - Monitors completed services with formulas
 * - Schedules feedback requests 30 minutes after completion
 * - Shows feedback modal when appropriate
 * - Handles app state changes to ensure proper timing
 */
export const FeedbackManager: React.FC<FeedbackManagerProps> = ({ children }) => {
  const {
    pendingRequests,
    getActiveFeedbackRequests,
    scheduleFeedbackRequest,
    dismissFeedbackRequest: _dismissFeedbackRequest,
  } = useFormulaFeedbackStore();

  const { drafts } = useServiceDraftStore();

  const [currentFeedbackRequest, setCurrentFeedbackRequest] = useState<any>(null);
  const [isCheckingForRequests, setIsCheckingForRequests] = useState(false);

  // Check for completed services that need feedback scheduling
  const checkForNewFeedbackOpportunities = useCallback(() => {
    const completedDrafts = drafts.filter(
      draft =>
        draft.currentStep >= 3 && // Completion step
        draft.resultData &&
        draft.formulationData?.formula &&
        draft.formulationData?.isFormulaFromAI // Only AI-generated formulas
    );

    completedDrafts.forEach(draft => {
      // Check if we already have a pending request for this service
      const existingRequest = pendingRequests.find(req => req.service_id === draft.id);

      if (!existingRequest && draft.resultData) {
        // Calculate when to show feedback (30 minutes after completion)
        const completionTime = new Date(draft.lastSaved);
        const feedbackTime = new Date(
          completionTime.getTime() + FEEDBACK_DELAY_MINUTES * 60 * 1000
        );

        // Create a summary of the formula for display
        const formulaSummary = createFormulaSummary(draft);

        scheduleFeedbackRequest({
          formula_id: draft.formulationData?.formula || 'unknown',
          service_id: draft.id,
          client_name: draft.clientName,
          formula_summary: formulaSummary,
          scheduled_time: feedbackTime.toISOString(),
        });

        logger.info('Feedback request scheduled for completed service', 'FeedbackManager', {
          serviceId: draft.id,
          clientName: draft.clientName,
          scheduledTime: feedbackTime.toISOString(),
        });
      }
    });
  }, [drafts, pendingRequests, scheduleFeedbackRequest]);

  // Check for active feedback requests that should be shown
  const checkForActiveFeedbackRequests = useCallback(() => {
    if (isCheckingForRequests) return;

    setIsCheckingForRequests(true);

    try {
      const activeRequests = getActiveFeedbackRequests();

      if (activeRequests.length > 0 && !currentFeedbackRequest) {
        // Show the oldest request first
        const oldestRequest = activeRequests.sort(
          (a, b) => new Date(a.scheduled_time).getTime() - new Date(b.scheduled_time).getTime()
        )[0];

        setCurrentFeedbackRequest(oldestRequest);

        logger.info('Showing feedback request', 'FeedbackManager', {
          requestId: oldestRequest.id,
          clientName: oldestRequest.client_name,
        });
      }
    } catch (error) {
      logger.error('Error checking for active feedback requests', 'FeedbackManager', { error });
    } finally {
      setIsCheckingForRequests(false);
    }
  }, [getActiveFeedbackRequests, currentFeedbackRequest, isCheckingForRequests]);

  // Handle app state changes to ensure proper timing
  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'active') {
        // App became active, check for any pending requests
        checkForActiveFeedbackRequests();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [checkForActiveFeedbackRequests]);

  // Periodic checks for new opportunities and active requests
  useEffect(() => {
    const interval = setInterval(() => {
      checkForNewFeedbackOpportunities();
      checkForActiveFeedbackRequests();
    }, CHECK_INTERVAL_MS);

    // Initial check
    checkForNewFeedbackOpportunities();
    checkForActiveFeedbackRequests();

    return () => clearInterval(interval);
  }, [checkForNewFeedbackOpportunities, checkForActiveFeedbackRequests]);

  const handleCloseFeedback = () => {
    setCurrentFeedbackRequest(null);
  };

  return (
    <>
      {children}

      {/* Feedback modal */}
      {currentFeedbackRequest && (
        <FormulaFeedback
          visible={!!currentFeedbackRequest}
          onClose={handleCloseFeedback}
          request={currentFeedbackRequest}
        />
      )}
    </>
  );
};

/**
 * Creates a user-friendly summary of the formula for display
 */
function createFormulaSummary(draft: any): string {
  const { formulationData, diagnosisData, desiredData } = draft;

  if (!formulationData) return 'Fórmula desconocida';

  const brand = formulationData.selectedBrand || 'Marca desconocida';
  const line = formulationData.selectedLine || '';

  // Try to extract key information from the diagnosis and desired result
  const baseColor = diagnosisData?.overallTone || '';
  const targetColor = desiredData?.desiredAnalysisResult?.detectedTone || '';

  let summary = `${brand}`;
  if (line) summary += ` ${line}`;

  if (baseColor && targetColor) {
    summary += ` - De ${baseColor} a ${targetColor}`;
  } else if (targetColor) {
    summary += ` - Hacia ${targetColor}`;
  }

  return summary;
}

export default FeedbackManager;
