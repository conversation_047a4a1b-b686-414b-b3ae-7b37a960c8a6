import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Star, TrendingUp, Users, Award } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius } from '@/constants/theme';
import {
  useFormulaFeedback,
  isFormulaProven,
  getFeedbackQuality,
  formatFeedbackStats,
} from '@/hooks/useFormulaFeedback';

interface FormulaFeedbackIndicatorProps {
  formulaId: string;
  compact?: boolean;
  onPress?: () => void;
  showDetails?: boolean;
}

/**
 * Compact indicator showing formula feedback stats
 * Can be used in formula lists, cards, or detail views
 */
export const FormulaFeedbackIndicator: React.FC<FormulaFeedbackIndicatorProps> = ({
  formulaId,
  compact = false,
  onPress,
  showDetails = true,
}) => {
  const { getFormulaStats } = useFormulaFeedback();
  const stats = getFormulaStats(formulaId);

  // Don't render if no feedback exists
  if (stats.totalUses === 0) {
    return null;
  }

  const isProven = isFormulaProven(stats);
  const quality = getFeedbackQuality(stats);
  const formattedStats = formatFeedbackStats(stats);

  const getQualityColor = () => {
    switch (quality) {
      case 'excellent':
        return Colors.light.success;
      case 'good':
        return Colors.light.primary;
      case 'fair':
        return Colors.light.warning;
      case 'poor':
        return Colors.light.error;
      default:
        return Colors.light.textSecondary;
    }
  };

  const getQualityIcon = () => {
    if (isProven) {
      return <Award size={16} color={Colors.light.success} />;
    }

    switch (quality) {
      case 'excellent':
      case 'good':
        return <Star size={16} color={getQualityColor()} fill={getQualityColor()} />;
      case 'fair':
        return <TrendingUp size={16} color={getQualityColor()} />;
      case 'poor':
        return <Users size={16} color={getQualityColor()} />;
      default:
        return <Star size={16} color={Colors.light.textSecondary} />;
    }
  };

  if (compact) {
    return (
      <TouchableOpacity
        style={[
          styles.compactIndicator,
          { backgroundColor: isProven ? Colors.light.successTransparent15 : Colors.light.surface },
        ]}
        onPress={onPress}
        disabled={!onPress}
      >
        <View style={styles.compactContent}>
          {getQualityIcon()}
          <Text style={[styles.compactRating, { color: getQualityColor() }]}>
            {formattedStats.rating}
          </Text>
          <Text style={styles.compactUses}>({stats.totalUses})</Text>
        </View>
        {isProven && (
          <View style={styles.provenBadge}>
            <Text style={styles.provenText}>PROBADA</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={[
        styles.fullIndicator,
        {
          borderLeftColor: getQualityColor(),
          backgroundColor: isProven ? Colors.light.successTransparent15 : Colors.light.surface,
        },
      ]}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          {getQualityIcon()}
          <Text style={styles.title}>Feedback de estilistas</Text>
          {isProven && (
            <View style={styles.provenBadge}>
              <Text style={styles.provenText}>PROBADA</Text>
            </View>
          )}
        </View>
      </View>

      {showDetails && (
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Star size={14} color={Colors.light.warning} fill={Colors.light.warning} />
            <Text style={styles.statValue}>{formattedStats.rating}</Text>
            <Text style={styles.statLabel}>calificación</Text>
          </View>

          <View style={styles.statItem}>
            <TrendingUp size={14} color={Colors.light.success} />
            <Text style={styles.statValue}>{formattedStats.successRate}</Text>
            <Text style={styles.statLabel}>éxito</Text>
          </View>

          <View style={styles.statItem}>
            <Users size={14} color={Colors.light.primary} />
            <Text style={styles.statValue}>{formattedStats.uses}</Text>
            <Text style={styles.statLabel}>usos</Text>
          </View>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  compactIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: radius.sm,
    alignSelf: 'flex-start',
  },
  compactContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  compactRating: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
  },
  compactUses: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
  },

  fullIndicator: {
    backgroundColor: Colors.light.surface,
    borderRadius: radius.md,
    borderLeftWidth: 3,
    padding: spacing.md,
    marginVertical: spacing.sm,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.sm,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: spacing.sm,
  },
  title: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
  },
  provenBadge: {
    backgroundColor: Colors.light.success,
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: radius.xs,
  },
  provenText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.bold,
    color: Colors.light.textLight,
    letterSpacing: 0.5,
  },

  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  statItem: {
    alignItems: 'center',
    gap: spacing.xs,
  },
  statValue: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  statLabel: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
  },
});

export default FormulaFeedbackIndicator;
