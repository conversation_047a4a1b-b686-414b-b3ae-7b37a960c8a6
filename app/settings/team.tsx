import React, { useState } from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { Stack } from 'expo-router';
import { Users, UserPlus, Edit2, Trash2, Shield } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius, shadows } from '@/constants/theme';
import { BaseHeader } from '@/components/base';
import { useTeamStore } from '@/stores/team-store';
import { useAuthStore } from '@/stores/auth-store';
import { PERMISSION_LABELS, Permission } from '@/types/permissions';
import AddEmployeeModal from '@/components/team/AddEmployeeModal';
import EditEmployeeModal from '@/components/team/EditEmployeeModal';

export default function TeamManagementScreen() {
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedMember, setSelectedMember] = useState<string | null>(null);

  const user = useAuthStore(state => state.user);
  const { members, removeMember } = useTeamStore();

  if (!user) return null;

  // Filtrar solo miembros del mismo salón
  const teamMembers = members.filter(m => m.salonId === user.salonId);

  const handleDeleteMember = (member: { id: string; name: string }) => {
    Alert.alert(
      'Eliminar Usuario',
      `¿Estás seguro de que quieres eliminar a ${member.name}?\n\nEsta acción no se puede deshacer.`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Eliminar',
          style: 'destructive',
          onPress: async () => {
            await removeMember(member.id);
            Alert.alert('Usuario eliminado', `${member.name} ha sido eliminado del equipo.`);
          },
        },
      ]
    );
  };

  const handleEditMember = (memberId: string) => {
    setSelectedMember(memberId);
    setShowEditModal(true);
  };

  const renderMember = ({
    item,
  }: {
    item: { id: string; name: string; email: string; isOwner?: boolean; permissions?: string[] };
  }) => {
    const isCurrentUser = item.id === user.id;

    return (
      <View style={styles.memberCard}>
        <View style={styles.memberHeader}>
          <View style={styles.memberInfo}>
            <Text style={styles.memberName}>{item.name}</Text>
            <Text style={styles.memberEmail}>{item.email}</Text>
            {item.isOwner && (
              <View style={styles.ownerBadge}>
                <Shield size={12} color={Colors.light.primary} />
                <Text style={styles.ownerText}>Propietario</Text>
              </View>
            )}
          </View>
          {!isCurrentUser && !item.isOwner && (
            <View style={styles.memberActions}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => handleEditMember(item.id)}
              >
                <Edit2 size={18} color={Colors.light.primary} />
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.actionButton, styles.deleteButton]}
                onPress={() => handleDeleteMember(item)}
              >
                <Trash2 size={18} color={Colors.light.error} />
              </TouchableOpacity>
            </View>
          )}
        </View>

        {!item.isOwner && item.permissions && item.permissions.length > 0 && (
          <View style={styles.permissionsContainer}>
            <Text style={styles.permissionsTitle}>Permisos:</Text>
            <View style={styles.permissionsList}>
              {item.permissions.map((perm: string) => (
                <View key={perm} style={styles.permissionChip}>
                  <Text style={styles.permissionText}>
                    {PERMISSION_LABELS[perm as Permission] || perm}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        )}
      </View>
    );
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <View style={styles.container}>
        <BaseHeader title="Mi Equipo" subtitle="Gestiona los usuarios del salón" />

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Users size={24} color={Colors.light.primary} />
              <Text style={styles.sectionTitle}>Miembros del Equipo</Text>
              <Text style={styles.memberCount}>({teamMembers.length})</Text>
            </View>

            {teamMembers.length === 0 ? (
              <View style={styles.emptyState}>
                <Text style={styles.emptyText}>No hay empleados registrados aún</Text>
              </View>
            ) : (
              teamMembers.map(item => <View key={item.id}>{renderMember({ item })}</View>)
            )}

            <TouchableOpacity
              style={styles.addButton}
              onPress={() => setShowAddModal(true)}
              activeOpacity={0.8}
            >
              <UserPlus size={20} color={Colors.light.textLight} />
              <Text style={styles.addButtonText}>Añadir Empleado</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>

      <AddEmployeeModal
        visible={showAddModal}
        onClose={() => setShowAddModal(false)}
        salonId={user.salonId}
        createdBy={user.id}
      />

      {selectedMember && (
        <EditEmployeeModal
          visible={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setSelectedMember(null);
          }}
          memberId={selectedMember}
        />
      )}
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  content: {
    flex: 1,
  },
  section: {
    padding: spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginLeft: spacing.sm,
  },
  memberCount: {
    fontSize: typography.sizes.base,
    color: Colors.light.textSecondary,
    marginLeft: spacing.xs,
  },
  memberCard: {
    backgroundColor: Colors.light.card,
    borderRadius: radius.lg,
    padding: spacing.md,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: Colors.light.surface,
    ...shadows.sm,
  },
  memberHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  memberInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: 4,
  },
  memberEmail: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: spacing.xs,
  },
  ownerBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary + '15',
    paddingHorizontal: spacing.sm,
    paddingVertical: 4,
    borderRadius: radius.sm,
    alignSelf: 'flex-start',
    marginTop: spacing.xs,
  },
  ownerText: {
    fontSize: typography.sizes.xs,
    color: Colors.light.primary,
    fontWeight: typography.weights.medium,
    marginLeft: 4,
  },
  memberActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  actionButton: {
    padding: spacing.sm,
    backgroundColor: Colors.light.surface,
    borderRadius: radius.sm,
  },
  deleteButton: {
    backgroundColor: Colors.light.error + '10',
  },
  permissionsContainer: {
    marginTop: spacing.md,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.light.surface,
  },
  permissionsTitle: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: spacing.sm,
  },
  permissionsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.xs,
  },
  permissionChip: {
    backgroundColor: Colors.light.surface,
    paddingHorizontal: spacing.sm,
    paddingVertical: 4,
    borderRadius: radius.sm,
  },
  permissionText: {
    fontSize: typography.sizes.xs,
    color: Colors.light.text,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.primary,
    paddingVertical: spacing.md,
    borderRadius: radius.md,
    marginTop: spacing.lg,
    ...shadows.sm,
  },
  addButtonText: {
    color: Colors.light.textLight,
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    marginLeft: spacing.sm,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyText: {
    fontSize: typography.sizes.base,
    color: Colors.light.textSecondary,
  },
});
