/**
 * Signed URL Manager
 *
 * Centralizes the logic for managing signed URLs with automatic refresh
 * and caching to optimize performance and ensure GDPR compliance.
 *
 * Features:
 * - Automatic URL refresh before expiration
 * - Local caching to minimize API calls
 * - Error handling and retry logic
 * - GDPR-compliant logging
 */

import { supabase } from '@/lib/supabase';
import { logger } from '@/utils/logger';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface SignedUrlCacheEntry {
  url: string;
  expiresAt: number; // Unix timestamp
  bucket: string;
  path: string;
  createdAt: number;
}

interface SignedUrlOptions {
  expiresIn?: number; // seconds, default 3600 (1 hour)
  forceRefresh?: boolean;
}

interface SignedUrlResult {
  url: string;
  expiresAt: number;
  isFromCache: boolean;
}

export class SignedUrlManager {
  private static readonly DEFAULT_EXPIRES_IN = 3600; // 1 hour
  private static readonly REFRESH_BUFFER = 300; // 5 minutes before expiration
  private static readonly CACHE_KEY_PREFIX = 'signed_url_cache_';
  private static readonly MAX_CACHE_ENTRIES = 50;

  // In-memory cache for frequently accessed URLs
  private static memoryCache = new Map<string, SignedUrlCacheEntry>();

  /**
   * Get a signed URL for a file, with automatic caching and refresh
   */
  static async getSignedUrl(
    bucket: string,
    path: string,
    options: SignedUrlOptions = {}
  ): Promise<SignedUrlResult> {
    const { expiresIn = this.DEFAULT_EXPIRES_IN, forceRefresh = false } = options;
    const cacheKey = this.getCacheKey(bucket, path);

    try {
      // Check if we have a valid cached URL
      if (!forceRefresh) {
        const cachedEntry = await this.getCachedUrl(cacheKey);
        if (cachedEntry && this.isUrlValid(cachedEntry)) {
          logger.debug('Using cached signed URL', 'SignedUrlManager', {
            bucket,
            path,
            expiresAt: cachedEntry.expiresAt,
          });
          return {
            url: cachedEntry.url,
            expiresAt: cachedEntry.expiresAt,
            isFromCache: true,
          };
        }
      }

      // Generate new signed URL
      logger.info('Generating new signed URL', 'SignedUrlManager', {
        bucket,
        path,
        expiresIn,
      });
      const newUrl = await this.generateSignedUrl(bucket, path, expiresIn);

      // Cache the new URL
      const cacheEntry: SignedUrlCacheEntry = {
        url: newUrl,
        expiresAt: Date.now() + expiresIn * 1000,
        bucket,
        path,
        createdAt: Date.now(),
      };

      await this.cacheUrl(cacheKey, cacheEntry);

      return {
        url: newUrl,
        expiresAt: cacheEntry.expiresAt,
        isFromCache: false,
      };
    } catch (error: unknown) {
      logger.error(
        `Failed to get signed URL - bucket: ${bucket}, path: ${path}, error: ${error.message}`
      );
      throw new Error(`Failed to get signed URL: ${error.message}`);
    }
  }

  /**
   * Refresh a signed URL if it's close to expiration
   */
  static async refreshUrlIfNeeded(
    bucket: string,
    path: string,
    _currentUrl?: string
  ): Promise<string | null> {
    try {
      const cacheKey = this.getCacheKey(bucket, path);
      const cachedEntry = await this.getCachedUrl(cacheKey);

      if (!cachedEntry) {
        logger.debug('No cached URL found for refresh check', 'SignedUrlManager', { bucket, path });
        return null;
      }

      // Check if URL needs refresh (within buffer time)
      const needsRefresh = this.needsRefresh(cachedEntry);

      if (needsRefresh) {
        logger.info('Refreshing signed URL', 'SignedUrlManager', {
          bucket,
          path,
          expiresAt: cachedEntry.expiresAt,
        });
        const result = await this.getSignedUrl(bucket, path, {
          forceRefresh: true,
        });
        return result.url;
      }

      return cachedEntry.url;
    } catch (error: unknown) {
      logger.error(
        `Failed to refresh signed URL - bucket: ${bucket}, path: ${path}, error: ${error.message}`
      );
      return null;
    }
  }

  /**
   * Generate a new signed URL using Supabase
   */
  private static async generateSignedUrl(
    bucket: string,
    path: string,
    expiresIn: number
  ): Promise<string> {
    const { data, error } = await supabase.storage.from(bucket).createSignedUrl(path, expiresIn);

    if (error) {
      throw new Error(`Supabase signed URL error: ${error.message}`);
    }

    if (!data?.signedUrl) {
      throw new Error('No signed URL returned from Supabase');
    }

    // Log for GDPR audit trail
    await this.logPhotoAccess(bucket, path, 'signed_url_generated');

    return data.signedUrl;
  }

  /**
   * Check if a cached URL is still valid
   */
  private static isUrlValid(entry: SignedUrlCacheEntry): boolean {
    const now = Date.now();
    const timeUntilExpiry = entry.expiresAt - now;

    // Consider valid if more than buffer time remaining
    return timeUntilExpiry > this.REFRESH_BUFFER * 1000;
  }

  /**
   * Check if a URL needs refresh (within buffer time of expiry)
   */
  private static needsRefresh(entry: SignedUrlCacheEntry): boolean {
    const now = Date.now();
    const timeUntilExpiry = entry.expiresAt - now;

    // Needs refresh if within buffer time or already expired
    return timeUntilExpiry <= this.REFRESH_BUFFER * 1000;
  }

  /**
   * Get cached URL entry
   */
  private static async getCachedUrl(cacheKey: string): Promise<SignedUrlCacheEntry | null> {
    try {
      // Check memory cache first
      const memoryEntry = this.memoryCache.get(cacheKey);
      if (memoryEntry) {
        return memoryEntry;
      }

      // Check persistent cache
      const stored = await AsyncStorage.getItem(cacheKey);
      if (stored) {
        const entry = JSON.parse(stored) as SignedUrlCacheEntry;

        // Add to memory cache for faster access
        this.memoryCache.set(cacheKey, entry);

        return entry;
      }

      return null;
    } catch (error) {
      logger.error(`Error reading cached URL - cacheKey: ${cacheKey}, error: ${error}`);
      return null;
    }
  }

  /**
   * Cache a URL entry
   */
  private static async cacheUrl(cacheKey: string, entry: SignedUrlCacheEntry): Promise<void> {
    try {
      // Store in memory cache
      this.memoryCache.set(cacheKey, entry);

      // Clean memory cache if too large
      if (this.memoryCache.size > this.MAX_CACHE_ENTRIES) {
        this.cleanMemoryCache();
      }

      // Store in persistent cache
      await AsyncStorage.setItem(cacheKey, JSON.stringify(entry));

      logger.debug('Cached signed URL', 'SignedUrlManager', {
        cacheKey,
        bucket: entry.bucket,
        path: entry.path,
        expiresAt: entry.expiresAt,
      });
    } catch (error) {
      logger.error(`Error caching URL - cacheKey: ${cacheKey}, error: ${error}`);
    }
  }

  /**
   * Clean memory cache by removing oldest entries
   */
  private static cleanMemoryCache(): void {
    const entries = Array.from(this.memoryCache.entries());

    // Sort by creation time (oldest first)
    entries.sort((a, b) => a[1].createdAt - b[1].createdAt);

    // Remove oldest 25% of entries
    const toRemove = Math.floor(entries.length * 0.25);
    for (let i = 0; i < toRemove; i++) {
      this.memoryCache.delete(entries[i][0]);
    }

    logger.debug('Cleaned memory cache', 'SignedUrlManager', {
      removedEntries: toRemove,
      remainingEntries: this.memoryCache.size,
    });
  }

  /**
   * Clear all cached URLs
   */
  static async clearCache(): Promise<void> {
    try {
      // Clear memory cache
      this.memoryCache.clear();

      // Clear persistent cache
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(this.CACHE_KEY_PREFIX));

      if (cacheKeys.length > 0) {
        await AsyncStorage.multiRemove(cacheKeys);
        logger.info('Cleared signed URL cache', 'SignedUrlManager', {
          clearedKeys: cacheKeys.length,
        });
      }
    } catch (error) {
      logger.error(`Error clearing cache: ${error}`);
    }
  }

  /**
   * Clear expired URLs from cache
   */
  static async clearExpiredUrls(): Promise<void> {
    try {
      const now = Date.now();
      let clearedCount = 0;

      // Clear from memory cache
      for (const [key, entry] of this.memoryCache.entries()) {
        if (entry.expiresAt <= now) {
          this.memoryCache.delete(key);
          clearedCount++;
        }
      }

      // Clear from persistent cache
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(this.CACHE_KEY_PREFIX));

      // Process cache keys in parallel for better performance
      const clearOperations = cacheKeys.map(async key => {
        try {
          const stored = await AsyncStorage.getItem(key);
          if (stored) {
            const entry = JSON.parse(stored) as SignedUrlCacheEntry;
            if (entry.expiresAt <= now) {
              await AsyncStorage.removeItem(key);
              return 1; // Cleared one item
            }
          }
          return 0; // Nothing cleared
        } catch {
          // Remove corrupted cache entries
          await AsyncStorage.removeItem(key);
          return 1; // Cleared one corrupted item
        }
      });

      const results = await Promise.allSettled(clearOperations);
      clearedCount += results
        .filter(result => result.status === 'fulfilled')
        .reduce((sum, result) => sum + result.value, 0);

      if (clearedCount > 0) {
        logger.info('Cleared expired URLs from cache', 'SignedUrlManager', {
          clearedCount,
        });
      }
    } catch (error) {
      logger.error(`Error clearing expired URLs: ${error}`);
    }
  }

  /**
   * Get cache key for a bucket/path combination
   */
  private static getCacheKey(bucket: string, path: string): string {
    return `${this.CACHE_KEY_PREFIX}${bucket}_${path.replace(/[^a-zA-Z0-9]/g, '_')}`;
  }

  /**
   * Log photo access for GDPR audit trail
   */
  private static async logPhotoAccess(bucket: string, path: string, action: string): Promise<void> {
    try {
      // This will be called by Edge Functions with service role
      // Client-side logging is not implemented for security reasons
      logger.debug('Photo access logged', 'SignedUrlManager', {
        bucket,
        path,
        action,
      });
    } catch (error) {
      // Non-critical error, don't break the flow
      logger.error(
        `Failed to log photo access - bucket: ${bucket}, path: ${path}, action: ${action}, error: ${error}`
      );
    }
  }

  /**
   * Get cache statistics for debugging
   */
  static async getCacheStats(): Promise<{
    memoryEntries: number;
    persistentEntries: number;
    oldestEntry?: number;
    newestEntry?: number;
  }> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(this.CACHE_KEY_PREFIX));

      let oldestEntry: number | undefined;
      let newestEntry: number | undefined;

      // Check memory cache for age info
      for (const entry of this.memoryCache.values()) {
        if (!oldestEntry || entry.createdAt < oldestEntry) {
          oldestEntry = entry.createdAt;
        }
        if (!newestEntry || entry.createdAt > newestEntry) {
          newestEntry = entry.createdAt;
        }
      }

      return {
        memoryEntries: this.memoryCache.size,
        persistentEntries: cacheKeys.length,
        oldestEntry,
        newestEntry,
      };
    } catch (error) {
      logger.error(`Error getting cache stats: ${error}`);
      return {
        memoryEntries: this.memoryCache.size,
        persistentEntries: 0,
      };
    }
  }
}

// Cleanup expired URLs on app start
SignedUrlManager.clearExpiredUrls().catch(_error => {
  logger.error(`Failed to clear expired URLs on startup: ${_error}`);
});

// Export convenience functions
export const getSignedUrl = SignedUrlManager.getSignedUrl.bind(SignedUrlManager);
export const refreshUrlIfNeeded = SignedUrlManager.refreshUrlIfNeeded.bind(SignedUrlManager);
export const clearSignedUrlCache = SignedUrlManager.clearCache.bind(SignedUrlManager);
