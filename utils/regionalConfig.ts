export interface RegionalConfig {
  country: string;
  countryCode: string;
  language: string;
  languageCode: string;
  currency: string;
  currencySymbol: string;
  measurementSystem: 'metric' | 'imperial';
  dateFormat: string;
  timeFormat: '12h' | '24h';
  defaultBrands: string[];
}

export const REGIONAL_CONFIGS: Record<string, RegionalConfig> = {
  ES: {
    country: 'España',
    countryCode: 'ES',
    language: 'Español',
    languageCode: 'es',
    currency: 'EUR',
    currencySymbol: '€',
    measurementSystem: 'metric',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24h',
    defaultBrands: ['Loreal', 'Schwarzkopf', 'Wella', 'Revlon', 'Inoa', 'Majirel'],
  },
  MX: {
    country: 'México',
    countryCode: 'MX',
    language: 'Español',
    languageCode: 'es',
    currency: 'MXN',
    currencySymbol: '$',
    measurementSystem: 'metric',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '12h',
    defaultBrands: ['Loreal', 'Wella', '<PERSON><PERSON>rzkopf', 'Revlon', 'Alfaparf'],
  },
  AR: {
    country: 'Argentina',
    countryCode: 'AR',
    language: 'Español',
    languageCode: 'es',
    currency: 'ARS',
    currencySymbol: '$',
    measurementSystem: 'metric',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24h',
    defaultBrands: ['Loreal', 'Issue', 'Schwarzkopf', 'Wella', 'Alfaparf'],
  },
  CO: {
    country: 'Colombia',
    countryCode: 'CO',
    language: 'Español',
    languageCode: 'es',
    currency: 'COP',
    currencySymbol: '$',
    measurementSystem: 'metric',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '12h',
    defaultBrands: ['Loreal', 'Schwarzkopf', 'Wella', 'Igora', 'Majirel'],
  },
  CL: {
    country: 'Chile',
    countryCode: 'CL',
    language: 'Español',
    languageCode: 'es',
    currency: 'CLP',
    currencySymbol: '$',
    measurementSystem: 'metric',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24h',
    defaultBrands: ['Loreal', 'Wella', 'Schwarzkopf', 'Revlon', 'Inoa'],
  },
  PE: {
    country: 'Perú',
    countryCode: 'PE',
    language: 'Español',
    languageCode: 'es',
    currency: 'PEN',
    currencySymbol: 'S/',
    measurementSystem: 'metric',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24h',
    defaultBrands: ['Loreal', 'Wella', 'Schwarzkopf', 'Igora', 'Koleston'],
  },
  US: {
    country: 'United States',
    countryCode: 'US',
    language: 'English',
    languageCode: 'en',
    currency: 'USD',
    currencySymbol: '$',
    measurementSystem: 'imperial',
    dateFormat: 'MM/DD/YYYY',
    timeFormat: '12h',
    defaultBrands: ['Redken', 'Matrix', 'Paul Mitchell', 'Joico', 'Pravana'],
  },
  BR: {
    country: 'Brasil',
    countryCode: 'BR',
    language: 'Português',
    languageCode: 'pt',
    currency: 'BRL',
    currencySymbol: 'R$',
    measurementSystem: 'metric',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24h',
    defaultBrands: ['Loreal', 'Wella', 'Keune', 'Alfaparf', 'Igora'],
  },
  FR: {
    country: 'France',
    countryCode: 'FR',
    language: 'Français',
    languageCode: 'fr',
    currency: 'EUR',
    currencySymbol: '€',
    measurementSystem: 'metric',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24h',
    defaultBrands: ['Loreal', 'Inoa', 'Majirel', 'Dia Light', 'Schwarzkopf'],
  },
  IT: {
    country: 'Italia',
    countryCode: 'IT',
    language: 'Italiano',
    languageCode: 'it',
    currency: 'EUR',
    currencySymbol: '€',
    measurementSystem: 'metric',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24h',
    defaultBrands: ['Alfaparf', 'Inebrya', 'Loreal', 'Wella', 'Davines'],
  },
};

export function getCountryList() {
  return Object.values(REGIONAL_CONFIGS).map(config => ({
    label: config.country,
    value: config.countryCode,
  }));
}

export function getRegionalConfig(countryCode: string): RegionalConfig {
  return REGIONAL_CONFIGS[countryCode] || REGIONAL_CONFIGS.ES;
}

export function detectUserCountry(): string {
  // En una app real, usaríamos geolocalización o el locale del dispositivo
  // Por ahora, devolvemos España como default
  return 'ES';
}
