# CLAUDE.md - Utility Functions & Business Logic

## 🎯 Propósito

Funciones utilitarias que encapsulan lógica de negocio crítica: colorimetría profesional, validación química, procesamiento de imágenes, análisis de viabilidad y helpers diversos. Todas las funciones DEBEN ser puras y bien testeadas.

## 📁 Utilidades Disponibles

### 🧪 Core Business Logic

- `professional-colorimetry.ts` - Cálculos de mezclas y neutralización
- `chemical-validator.ts` - Validación de compatibilidad química
- `viability-analyzer.ts` - Análisis de viabilidad de transformación
- `parseFormula.ts` - Parser de fórmulas de coloración
- `visualFormulaParser.ts` - Parser visual de fórmulas complejas

### 🖼️ Image & Media Processing

- `image-processor.ts` - Procesamiento y optimización de imágenes
- `secure-image-upload.ts` - Upload seguro con validaciones
- `signed-url-manager.ts` - Gestión de URLs firmadas

### 🔐 Security & Privacy

- `privacy-filter.ts` - Filtros de datos sensibles
- `permission-helpers.ts` - Helpers de permisos y roles
- `ai-error-handler.ts` - Manejo robusto de errores AI

### 🌍 Regional & Configuration

- `regionalConfig.ts` - Configuración regional y localización
- `brand-preferences.ts` - Preferencias por marca
- `contrast-checker.ts` - Verificación de contraste accesibilidad

### 🧪 Testing & Development

- `testVisualParser.ts` - Testing del parser visual
- `logger.ts` - Sistema de logging estructurado
- `ab-testing-framework.ts` - Framework A/B testing

## 🧪 Professional Colorimetry

### Cálculos de Mezclas

```typescript
// professional-colorimetry.ts

interface ColorMix {
  baseColor: HairColor;
  targetColor: HairColor;
  neutralizers: Neutralizer[];
  developer: Developer;
  processingTime: number;
}

interface NeutralizationRule {
  unwantedTone: UnwantedTone;
  neutralizer: string;
  ratio: number;
  processingTime: number;
}

// Calcular mezcla para neutralización
export function calculateNeutralization(
  currentColor: HairColor,
  targetColor: HairColor
): NeutralizationRule | null {
  const unwantedTone = detectUnwantedTone(currentColor, targetColor);
  if (!unwantedTone) return null;

  const neutralizationRules: Record<UnwantedTone, NeutralizationRule> = {
    [UnwantedTone.ORANGE]: {
      unwantedTone: UnwantedTone.ORANGE,
      neutralizer: 'blue', // 0.1, 0.11, 0.2
      ratio: 0.25,
      processingTime: 15,
    },
    [UnwantedTone.YELLOW]: {
      unwantedTone: UnwantedTone.YELLOW,
      neutralizer: 'violet', // 0.2, 0.22, 0.6
      ratio: 0.2,
      processingTime: 10,
    },
    [UnwantedTone.GREEN]: {
      unwantedTone: UnwantedTone.GREEN,
      neutralizer: 'red', // 0.4, 0.43, 0.5
      ratio: 0.15,
      processingTime: 12,
    },
  };

  return neutralizationRules[unwantedTone];
}

// Detectar tonos no deseados por análisis HSL
function detectUnwantedTone(current: HairColor, target: HairColor): UnwantedTone | null {
  const currentHSL = rgbToHsl(current.rgb);
  const targetHSL = rgbToHsl(target.rgb);

  const hueDifference = Math.abs(currentHSL.h - targetHSL.h);

  // Análisis por rango de matiz
  if (currentHSL.h >= 15 && currentHSL.h <= 35 && currentHSL.s > 0.4) {
    return UnwantedTone.ORANGE;
  }

  if (currentHSL.h >= 45 && currentHSL.h <= 65 && currentHSL.s > 0.3) {
    return UnwantedTone.YELLOW;
  }

  if (currentHSL.h >= 120 && currentHSL.h <= 140) {
    return UnwantedTone.GREEN;
  }

  return null;
}

// Calcular proporciones de mezcla
export function calculateMixProportions(formula: ColorFormula): MixProportions {
  const totalColorVolume = formula.colors.reduce((sum, color) => sum + color.quantity, 0);

  const developerVolume = calculateDeveloperVolume(totalColorVolume, formula.developer.volume);

  return {
    colors: formula.colors.map(color => ({
      ...color,
      percentage: (color.quantity / totalColorVolume) * 100,
    })),
    developer: {
      ...formula.developer,
      volume: developerVolume,
      ratio: `1:${developerVolume / totalColorVolume}`,
    },
    totalVolume: totalColorVolume + developerVolume,
  };
}
```

### Validación Química

```typescript
// chemical-validator.ts

export interface ChemicalValidation {
  isValid: boolean;
  confidence: number;
  errors: string[];
  warnings: string[];
  recommendations: string[];
}

export class ChemicalValidator {
  static async validate(formula: ColorFormula): Promise<ChemicalValidation> {
    const validation: ChemicalValidation = {
      isValid: true,
      confidence: 100,
      errors: [],
      warnings: [],
      recommendations: [],
    };

    // 1. Validar compatibilidad entre productos
    this.validateProductCompatibility(formula, validation);

    // 2. Validar proporciones químicas
    this.validateChemicalProportions(formula, validation);

    // 3. Validar seguridad del proceso
    this.validateProcessSafety(formula, validation);

    // 4. Calcular confianza final
    validation.confidence = this.calculateConfidence(validation);
    validation.isValid = validation.errors.length === 0;

    return validation;
  }

  private static validateProductCompatibility(
    formula: ColorFormula,
    validation: ChemicalValidation
  ): void {
    // Verificar que no se mezclen marcas incompatibles
    const brands = [...new Set(formula.products.map(p => p.brand))];
    if (brands.length > 1) {
      const incompatibleBrands = this.checkBrandCompatibility(brands);
      if (incompatibleBrands.length > 0) {
        validation.errors.push(`Marcas incompatibles detectadas: ${incompatibleBrands.join(', ')}`);
      }
    }

    // Verificar pH compatibility
    const phLevels = formula.products.map(p => p.ph);
    const phRange = Math.max(...phLevels) - Math.min(...phLevels);
    if (phRange > 2.0) {
      validation.warnings.push('Diferencia de pH significativa entre productos (>2.0)');
    }
  }

  private static validateChemicalProportions(
    formula: ColorFormula,
    validation: ChemicalValidation
  ): void {
    // Validar ratio color:developer
    const colorVolume = formula.colors.reduce((sum, c) => sum + c.quantity, 0);
    const developerVolume = formula.developer.quantity;
    const ratio = developerVolume / colorVolume;

    if (ratio < 0.5 || ratio > 2.0) {
      validation.errors.push(`Ratio color:developer fuera de rango seguro (${ratio.toFixed(2)}:1)`);
    }

    // Validar volumen total
    const totalVolume = colorVolume + developerVolume;
    if (totalVolume > 200) {
      // ml
      validation.warnings.push('Volumen total alto - considerar dividir en aplicaciones');
    }
  }

  private static validateProcessSafety(
    formula: ColorFormula,
    validation: ChemicalValidation
  ): void {
    // Verificar tiempo de procesamiento
    if (formula.processingTime > 60) {
      // minutos
      validation.warnings.push('Tiempo de procesamiento alto - monitorear cada 10 minutos');
    }

    // Verificar temperatura de procesamiento
    if (formula.temperature && formula.temperature > 40) {
      // °C
      validation.warnings.push('Temperatura alta - usar protección térmica');
    }

    // Verificar ingredientes alergénicos
    const allergens = this.detectCommonAllergens(formula.products);
    if (allergens.length > 0) {
      validation.warnings.push(`Ingredientes potencialmente alergénicos: ${allergens.join(', ')}`);
    }
  }
}
```

## 📊 Viability Analyzer

```typescript
// viability-analyzer.ts

export interface ViabilityAnalysis {
  isViable: boolean;
  confidence: number;
  difficulty: 'easy' | 'medium' | 'hard' | 'expert';
  requiredSessions: number;
  estimatedTime: number; // minutes
  riskLevel: 'low' | 'medium' | 'high';
  warnings: string[];
  recommendations: string[];
  alternativeOptions?: ColorOption[];
}

export function analyzeViability(
  currentState: HairState,
  desiredColor: DesiredColor
): ViabilityAnalysis {
  const analysis: ViabilityAnalysis = {
    isViable: true,
    confidence: 100,
    difficulty: 'easy',
    requiredSessions: 1,
    estimatedTime: 60,
    riskLevel: 'low',
    warnings: [],
    recommendations: [],
  };

  // 1. Analizar distancia de color
  const colorDistance = calculateColorDistance(currentState.baseColor, desiredColor.targetColor);

  // 2. Evaluar condición del cabello
  const conditionFactor = evaluateHairCondition(currentState);

  // 3. Considerar procesamiento previo
  const processingHistory = evaluateProcessingHistory(currentState);

  // 4. Calcular viabilidad combinada
  return calculateViabilityScore(colorDistance, conditionFactor, processingHistory, analysis);
}

function calculateColorDistance(current: HairColor, target: HairColor): number {
  // Usar CIE Delta E para precisión profesional
  const currentLab = rgbToLab(current.rgb);
  const targetLab = rgbToLab(target.rgb);

  return deltaE2000(currentLab, targetLab);
}

function evaluateHairCondition(state: HairState): ConditionFactor {
  const factors = {
    porosity: state.porosity, // 'low' | 'normal' | 'high'
    elasticity: state.elasticity, // 'poor' | 'good' | 'excellent'
    damage: state.damageLevel, // 0-10 scale
    density: state.density, // 'thin' | 'medium' | 'thick'
  };

  // Calcular score de condición (0-100)
  let score = 100;

  if (factors.damage > 7) score -= 30;
  if (factors.porosity === 'high') score -= 20;
  if (factors.elasticity === 'poor') score -= 25;

  return {
    score: Math.max(0, score),
    riskFactors: identifyRiskFactors(factors),
  };
}
```

## 🖼️ Image Processing

```typescript
// image-processor.ts

export interface ProcessingOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'jpeg' | 'webp' | 'png';
  removeExif?: boolean;
}

export async function processImage(
  file: File,
  options: ProcessingOptions = {}
): Promise<ProcessedImage> {
  const {
    maxWidth = 1920,
    maxHeight = 1080,
    quality = 85,
    format = 'jpeg',
    removeExif = true,
  } = options;

  // 1. Validar archivo
  const validation = validateImageFile(file);
  if (!validation.isValid) {
    throw new Error(`Invalid image: ${validation.errors.join(', ')}`);
  }

  // 2. Crear canvas para procesamiento
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d')!;
  const img = new Image();

  return new Promise((resolve, reject) => {
    img.onload = () => {
      // 3. Calcular dimensiones preservando aspect ratio
      const { width, height } = calculateDimensions(img.width, img.height, maxWidth, maxHeight);

      canvas.width = width;
      canvas.height = height;

      // 4. Dibujar imagen redimensionada
      ctx.drawImage(img, 0, 0, width, height);

      // 5. Exportar con configuración
      canvas.toBlob(
        blob => {
          if (!blob) {
            reject(new Error('Failed to process image'));
            return;
          }

          resolve({
            blob,
            width,
            height,
            size: blob.size,
            format,
            originalSize: file.size,
            compressionRatio: (1 - blob.size / file.size) * 100,
          });
        },
        `image/${format}`,
        quality / 100
      );
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}

function validateImageFile(file: File): ValidationResult {
  const errors: string[] = [];

  // Verificar tipo MIME
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    errors.push(`Unsupported file type: ${file.type}`);
  }

  // Verificar tamaño
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    errors.push(`File too large: ${file.size} bytes (max: ${maxSize})`);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
```

## 🔐 Privacy & Security

```typescript
// privacy-filter.ts

export interface PrivacyConfig {
  anonymizeNames: boolean;
  anonymizePhotos: boolean;
  retentionDays: number;
  salonId: string;
}

export function anonymizeClientData(
  clientData: ClientData,
  config: PrivacyConfig
): AnonymizedClientData {
  const anonymized = { ...clientData };

  if (config.anonymizeNames) {
    anonymized.name = generateAnonymousName(clientData.id);
    anonymized.email = anonymizeEmail(clientData.email);
    anonymized.phone = anonymizePhone(clientData.phone);
  }

  if (config.anonymizePhotos) {
    anonymized.photos = anonymized.photos.map(photo => ({
      ...photo,
      url: generateAnonymousPhotoUrl(photo.id, config.salonId),
      metadata: removeExifData(photo.metadata),
    }));
  }

  // Limpiar datos sensibles
  delete anonymized.personalNotes;
  delete anonymized.medicalConditions;

  return anonymized;
}

function generateAnonymousName(clientId: string): string {
  // Generar nombre consistente pero anónimo
  const hash = simpleHash(clientId);
  const names = ['Cliente A', 'Cliente B', 'Cliente C' /* ... */];
  return names[hash % names.length] + ` ${String(hash).slice(-3)}`;
}

// permission-helpers.ts
export async function checkPermission(permission: string, userId?: string): Promise<boolean> {
  const user = userId ? await getUser(userId) : await getCurrentUser();
  if (!user) return false;

  // Verificar permisos por rol
  const rolePermissions = getRolePermissions(user.role);
  if (rolePermissions.includes('*') || rolePermissions.includes(permission)) {
    return true;
  }

  // Verificar permisos individuales
  return user.permissions?.includes(permission) || false;
}

export function requirePermission(permission: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const hasPermission = await checkPermission(permission);
      if (!hasPermission) {
        throw new Error(`Permission denied: ${permission}`);
      }

      return originalMethod.apply(this, args);
    };

    return descriptor;
  };
}
```

## 🌍 Regional Configuration

```typescript
// regionalConfig.ts

export interface RegionalConfig {
  country: string;
  currency: string;
  language: string;
  dateFormat: string;
  numberFormat: Intl.NumberFormatOptions;
  measurementUnits: 'metric' | 'imperial';
  taxRate: number;
  colorNaming: ColorNamingSystem;
}

export function getRegionalConfig(countryCode: string): RegionalConfig {
  const configs: Record<string, RegionalConfig> = {
    ES: {
      country: 'España',
      currency: 'EUR',
      language: 'es-ES',
      dateFormat: 'dd/MM/yyyy',
      numberFormat: {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      },
      measurementUnits: 'metric',
      taxRate: 0.21, // 21% IVA
      colorNaming: ColorNamingSystem.EUROPEAN,
    },
    US: {
      country: 'United States',
      currency: 'USD',
      language: 'en-US',
      dateFormat: 'MM/dd/yyyy',
      numberFormat: {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      },
      measurementUnits: 'imperial',
      taxRate: 0.08, // Varía por estado
      colorNaming: ColorNamingSystem.AMERICAN,
    },
  };

  return configs[countryCode] || configs['US']; // Default to US
}

export function formatCurrency(amount: number, config: RegionalConfig): string {
  return new Intl.NumberFormat(config.language, {
    style: 'currency',
    currency: config.currency,
    ...config.numberFormat,
  }).format(amount);
}

export function convertUnits(value: number, from: MeasurementUnit, to: MeasurementUnit): number {
  const conversions: Record<string, number> = {
    ml_to_floz: 0.033814,
    floz_to_ml: 29.5735,
    g_to_oz: 0.035274,
    oz_to_g: 28.3495,
  };

  const conversionKey = `${from}_to_${to}`;
  return conversions[conversionKey] ? value * conversions[conversionKey] : value;
}
```

## 🧪 Testing Utilities

```typescript
// Common test helpers
export const testUtils = {
  // Mock data generators
  createMockFormula: (overrides?: Partial<ColorFormula>): ColorFormula => ({
    id: 'test-formula-1',
    colors: [{ brand: 'Wella', shade: '7/1', quantity: 30 }],
    developer: { volume: 20, quantity: 30 },
    processingTime: 35,
    ...overrides,
  }),

  createMockHairState: (overrides?: Partial<HairState>): HairState => ({
    baseColor: { level: 6, tone: 'natural' },
    condition: 'good',
    porosity: 'normal',
    elasticity: 'good',
    previouslyColored: false,
    ...overrides,
  }),

  // Assertion helpers
  expectValidFormula: (formula: ColorFormula) => {
    expect(formula.colors).toBeDefined();
    expect(formula.colors.length).toBeGreaterThan(0);
    expect(formula.developer).toBeDefined();
    expect(formula.processingTime).toBeGreaterThan(0);
  },

  expectValidColor: (color: HairColor) => {
    expect(color.level).toBeGreaterThanOrEqual(1);
    expect(color.level).toBeLessThanOrEqual(10);
    expect(color.tone).toBeDefined();
  },
};
```

## 📊 Performance Considerations

### Función Pura Guidelines

```typescript
// ✅ CORRECTO: Función pura
export function calculateRatio(a: number, b: number): number {
  return a / b;
}

// ❌ INCORRECTO: Función con side effects
export function calculateAndLog(a: number, b: number): number {
  console.log('Calculating...'); // Side effect
  return a / b;
}
```

### Memoización para Funciones Costosas

```typescript
import { memoize } from 'lodash';

// Memoizar cálculos pesados
export const calculateComplexColor = memoize(
  (baseColor: HairColor, targetColor: HairColor): ColorTransformation => {
    // Cálculo complejo aquí
    return performExpensiveCalculation(baseColor, targetColor);
  },
  // Key function para cache
  (base, target) => `${base.level}-${base.tone}-${target.level}-${target.tone}`
);
```

## 🤖 Agentes Recomendados

### Para este módulo usar:

**colorimetry-expert** - Validación química y colorimetría

- Validar fórmulas y cálculos químicos en utilidades
- Revisar terminología profesional en helpers
- Verificar precisión en conversiones de color
- PROACTIVAMENTE usar al revisar utilidades químicas

**test-runner** - Testing de utilidades críticas

- Unit tests para funciones puras
- Testing de edge cases y validaciones
- Integration tests para helpers complejos
- PROACTIVAMENTE usar después de implementar utilidades

**frontend-developer** - Implementación de utilidades

- Patterns para funciones puras y reutilizables
- Optimización de performance en helpers
- Integration con stores y servicios
- PROACTIVAMENTE usar para nuevas utilidades

**debug-specialist** - Debugging de utilidades complejas

- Root cause analysis en cálculos erróneos
- Debugging de validaciones químicas
- Análisis de performance en helpers pesados
- PROACTIVAMENTE usar para errores en utilidades

**security-privacy-auditor** - Auditoría de utilidades sensibles

- Validar helpers de privacidad y anonymización
- Auditar functions de permissions y auth
- Verificar manejo seguro de datos en helpers
- Usar antes de releases con cambios de seguridad

### 💡 Ejemplos de Uso

```bash
# Validar precisión de cálculos químicos
Task: Use colorimetry-expert to validate color neutralization calculations in professional-colorimetry.ts

# Testear utilidades de validación
Task: Use test-runner to create comprehensive tests for chemical-validator.ts

# Optimizar performance de image processor
Task: Use frontend-developer to optimize image processing utilities for large files

# Debug error en validación química
Task: Use debug-specialist to investigate incorrect results in ChemicalValidator.validate
```

## 🔌 MCPs Disponibles

### Funciones MCP útiles:

**Para análisis de código:**

- `mcp__serena__get_symbols_overview` - Estructura de archivos de utils
- `mcp__serena__find_symbol` - Localizar functions específicas
- `mcp__serena__find_referencing_symbols` - Ver dónde se usan utilities
- `mcp__serena__search_for_pattern` - Buscar patterns en validaciones

**Para documentación:**

- `mcp__context7__resolve_library_id` - Docs de librerías usadas
- `mcp__context7__get_library_docs` - Documentación específica

**Para diagnósticos:**

- `mcp__ide__getDiagnostics` - Errores TypeScript en utils
- `mcp__serena__write_memory` - Documentar algoritmos complejos

### 📝 Ejemplos MCP

```bash
# Analizar estructura de professional-colorimetry
mcp__serena__get_symbols_overview: "utils/professional-colorimetry.ts"
mcp__serena__find_symbol: "calculateNeutralization"

# Encontrar todos los usos de ChemicalValidator
mcp__serena__find_referencing_symbols: "ChemicalValidator" in "utils/chemical-validator.ts"

# Buscar todas las funciones pure en utils
mcp__serena__search_for_pattern: "export.*function.*\\(" in "utils/"

# Obtener docs para optimización de imágenes
mcp__context7__resolve_library_id: "canvas"
mcp__context7__get_library_docs: "/Automattic/node-canvas"

# Verificar errores TypeScript
mcp__ide__getDiagnostics: "utils/"

# Documentar algoritmos complejos
mcp__serena__write_memory: "colorimetry-algorithms" "Algoritmos de neutralización y mezclas químicas"
```

### 🔄 Combinaciones Recomendadas

**Validación Química:**

1. `colorimetry-expert` + `mcp__serena__find_symbol`
2. `test-runner` + `mcp__ide__getDiagnostics`

**Performance Optimization:**

1. `frontend-developer` + `mcp__context7__get_library_docs`
2. `debug-specialist` + `mcp__serena__search_for_pattern`

**Security Validation:**

1. `security-privacy-auditor` + `mcp__serena__find_referencing_symbols`
2. `debug-specialist` + `mcp__serena__get_symbols_overview`

## 📊 Patterns de Utilidades con Agentes

### 🧪 Chemical Validation Pattern

```typescript
// Usar colorimetry-expert para validar este pattern
export class ChemicalValidator {
  static validate(formula: ColorFormula): ValidationResult {
    // 1. Validación básica (colorimetry-expert)
    const basicValidation = this.validateBasicCompatibility(formula);

    // 2. Validación química avanzada (colorimetry-expert)
    const chemicalValidation = this.validateChemicalCompatibility(formula);

    // 3. Validación de seguridad (colorimetry-expert)
    const safetyValidation = this.validateSafety(formula);

    return combineValidations(basicValidation, chemicalValidation, safetyValidation);
  }
}
```

### 🔒 Security Pattern

```typescript
// Usar security-privacy-auditor para validar
export const securityHelpers = {
  // 1. Anonymización segura (security-privacy-auditor)
  anonymizeClientData: (data: ClientData): AnonymizedData => {
    const { sensitiveField, ...safeData } = data;
    return safeData;
  },

  // 2. Validación de permisos (security-privacy-auditor)
  checkPermission: async (permission: string): Promise<boolean> => {
    // Implementación segura de verificación
  },

  // 3. Filtro multi-tenant (security-privacy-auditor)
  filterBySalon: <T extends { salon_id: string }>(data: T[], salonId: string): T[] => {
    return data.filter(item => item.salon_id === salonId);
  },
};
```

### ⚡ Performance Pattern

```typescript
// Usar frontend-developer para optimizar
export const performanceUtils = {
  // 1. Memoización inteligente (frontend-developer)
  memoizeExpensive: <T, R>(fn: (arg: T) => R, keyFn?: (arg: T) => string) => {
    const cache = new Map<string, R>();
    return (arg: T): R => {
      const key = keyFn ? keyFn(arg) : JSON.stringify(arg);
      if (cache.has(key)) return cache.get(key)!;
      const result = fn(arg);
      cache.set(key, result);
      return result;
    };
  },

  // 2. Debounce para operaciones pesadas (frontend-developer)
  debounceHeavy: <T extends (...args: any[]) => any>(fn: T, delay: number): T => {
    let timeoutId: NodeJS.Timeout;
    return ((...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => fn(...args), delay);
    }) as T;
  },

  // 3. Batch processing (frontend-developer)
  batchProcess: async <T, R>(
    items: T[],
    processor: (batch: T[]) => Promise<R[]>,
    batchSize = 10
  ): Promise<R[]> => {
    const results: R[] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchResults = await processor(batch);
      results.push(...batchResults);
    }
    return results;
  },
};
```

### 🧪 Testing Pattern

```typescript
// Usar test-runner para implementar
export const testUtils = {
  // 1. Mock data generators (test-runner)
  createMockFormula: (overrides?: Partial<ColorFormula>): ColorFormula => ({
    id: 'test-formula-1',
    colors: [{ brand: 'Wella', shade: '7/1', quantity: 30 }],
    developer: { volume: 20, quantity: 30 },
    processingTime: 35,
    ...overrides,
  }),

  // 2. Assertion helpers (test-runner)
  expectValidColor: (color: HairColor) => {
    expect(color.level).toBeGreaterThanOrEqual(1);
    expect(color.level).toBeLessThanOrEqual(10);
    expect(color.tone).toBeDefined();
  },

  // 3. Performance testing (test-runner)
  measurePerformance: async <T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> => {
    const start = performance.now();
    const result = await fn();
    const duration = performance.now() - start;
    return { result, duration };
  },
};
```

### 🎨 Color Calculation Pattern

```typescript
// Usar colorimetry-expert para validar precisión
export const colorUtils = {
  // 1. Neutralización química (colorimetry-expert)
  calculateNeutralization: (current: HairColor, target: HairColor): NeutralizationRule => {
    // Algoritmo validado por experto
    const unwantedTone = detectUnwantedTone(current, target);
    return getNeutralizationRule(unwantedTone);
  },

  // 2. Conversión de espacios de color (colorimetry-expert)
  convertColorSpace: (color: HairColor, targetSpace: ColorSpace): ConvertedColor => {
    // Conversión precisa validada
  },

  // 3. Análisis de viabilidad (colorimetry-expert)
  analyzeViability: (current: HairState, desired: DesiredColor): ViabilityResult => {
    // Análisis técnico validado por experto
  },
};
```

## 🔗 Archivos Relacionados

- `../stores/` - Stores que usan estas utilidades
- `../services/` - Servicios que implementan lógica de negocio
- `../types/` - Tipos TypeScript utilizados
- `../supabase/functions/` - Edge Functions que usan validadores

---

**⚡ Recuerda:** Las utilidades son la base de la lógica de negocio. Mantenerlas puras, bien testeadas y documentadas es crítico. Usa `colorimetry-expert` para validación química y `test-runner` para testing comprehensivo.
