/**
 * Rate Limiter for Security Protection
 *
 * Provides comprehensive rate limiting to prevent abuse and attacks:
 * - Brute force attack prevention
 * - API endpoint protection
 * - Resource usage control
 * - DDoS mitigation
 *
 * SECURITY FEATURES:
 * - Per-user and per-IP rate limiting
 * - Different limits for different operations
 * - Automatic cleanup of expired entries
 * - Graceful degradation under load
 */

import { logger } from './logger';

interface RateLimit {
  requests: number;
  windowMs: number;
  blockDurationMs?: number; // How long to block after limit exceeded
}

interface RateLimitEntry {
  count: number;
  resetTime: number;
  blocked?: boolean;
  blockUntil?: number;
}

interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  blocked?: boolean;
  blockUntil?: number;
}

export class RateLimiter {
  private static attempts = new Map<string, RateLimitEntry>();

  // Rate limit configurations for different operations
  private static readonly RATE_LIMITS: Record<string, RateLimit> = {
    // Authentication limits (stricter)
    AUTH_LOGIN: {
      requests: 5,
      windowMs: 15 * 60 * 1000, // 15 minutes
      blockDurationMs: 30 * 60 * 1000, // Block for 30 minutes after exceeded
    },
    AUTH_SIGNUP: {
      requests: 3,
      windowMs: 60 * 60 * 1000, // 1 hour
      blockDurationMs: 60 * 60 * 1000, // Block for 1 hour
    },
    AUTH_PASSWORD_RESET: {
      requests: 3,
      windowMs: 60 * 60 * 1000, // 1 hour
      blockDurationMs: 60 * 60 * 1000,
    },

    // Image processing limits
    IMAGE_UPLOAD: {
      requests: 20,
      windowMs: 10 * 60 * 1000, // 10 minutes
      blockDurationMs: 5 * 60 * 1000, // Block for 5 minutes
    },
    AI_ANALYSIS: {
      requests: 30,
      windowMs: 10 * 60 * 1000, // 10 minutes
      blockDurationMs: 10 * 60 * 1000,
    },
    PHOTO_PROCESSING: {
      requests: 15,
      windowMs: 5 * 60 * 1000, // 5 minutes
      blockDurationMs: 5 * 60 * 1000,
    },

    // Database operation limits
    DATABASE_WRITE: {
      requests: 100,
      windowMs: 5 * 60 * 1000, // 5 minutes
      blockDurationMs: 2 * 60 * 1000,
    },
    DATABASE_READ: {
      requests: 300,
      windowMs: 5 * 60 * 1000, // 5 minutes
    },

    // API endpoint limits
    API_GENERAL: {
      requests: 200,
      windowMs: 5 * 60 * 1000, // 5 minutes
    },
    API_SENSITIVE: {
      requests: 50,
      windowMs: 5 * 60 * 1000, // 5 minutes
      blockDurationMs: 10 * 60 * 1000,
    },

    // Chat and communication limits
    CHAT_MESSAGE: {
      requests: 50,
      windowMs: 5 * 60 * 1000, // 5 minutes
    },
    CHAT_ATTACHMENT: {
      requests: 10,
      windowMs: 10 * 60 * 1000, // 10 minutes
    },

    // Edge Function limits
    EDGE_FUNCTION: {
      requests: 100,
      windowMs: 5 * 60 * 1000, // 5 minutes
    },

    // Admin operations (very strict)
    ADMIN_OPERATION: {
      requests: 10,
      windowMs: 10 * 60 * 1000, // 10 minutes
      blockDurationMs: 30 * 60 * 1000,
    },
  };

  /**
   * Check if request is allowed under rate limit
   */
  static checkRateLimit(
    key: string,
    type: keyof typeof RateLimiter.RATE_LIMITS,
    metadata?: { ip?: string; userId?: string; userAgent?: string }
  ): RateLimitResult {
    const now = Date.now();
    const limit = this.RATE_LIMITS[type];

    if (!limit) {
      logger.warn(`Unknown rate limit type: ${type}`);
      return {
        allowed: true,
        remaining: 999,
        resetTime: now + 60000,
      };
    }

    const windowKey = `${key}_${type}`;

    // Clean expired entries periodically
    this.cleanupExpiredEntries(now);

    // Check if currently blocked
    const entry = this.attempts.get(windowKey);
    if (entry?.blocked && entry.blockUntil && now < entry.blockUntil) {
      // Still blocked
      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.resetTime,
        blocked: true,
        blockUntil: entry.blockUntil,
      };
    }

    // Reset if window has expired or if block has expired
    if (entry && (now >= entry.resetTime || (entry.blockUntil && now >= entry.blockUntil))) {
      this.attempts.delete(windowKey);
    }

    // Get or create entry
    const currentEntry = this.attempts.get(windowKey) || {
      count: 0,
      resetTime: now + limit.windowMs,
    };

    // Check if limit exceeded
    if (currentEntry.count >= limit.requests) {
      // Limit exceeded - apply block if configured
      if (limit.blockDurationMs) {
        currentEntry.blocked = true;
        currentEntry.blockUntil = now + limit.blockDurationMs;
        this.attempts.set(windowKey, currentEntry);

        // Log security event
        this.logRateLimitViolation(key, type, currentEntry.count, metadata);

        return {
          allowed: false,
          remaining: 0,
          resetTime: currentEntry.resetTime,
          blocked: true,
          blockUntil: currentEntry.blockUntil,
        };
      } else {
        // Just deny request without blocking
        return {
          allowed: false,
          remaining: 0,
          resetTime: currentEntry.resetTime,
        };
      }
    }

    // Increment count
    currentEntry.count += 1;
    this.attempts.set(windowKey, currentEntry);

    const remaining = limit.requests - currentEntry.count;

    // Log warning if approaching limit
    if (remaining <= Math.ceil(limit.requests * 0.1)) {
      // 10% remaining
      logger.warn(`Rate limit warning: ${key} (${type}) has ${remaining} requests remaining`);
    }

    return {
      allowed: true,
      remaining,
      resetTime: currentEntry.resetTime,
    };
  }

  /**
   * Check multiple rate limits simultaneously
   */
  static checkMultipleRateLimits(
    checks: Array<{ key: string; type: keyof typeof RateLimiter.RATE_LIMITS }>,
    metadata?: { ip?: string; userId?: string; userAgent?: string }
  ): { allowed: boolean; results: RateLimitResult[] } {
    const results: RateLimitResult[] = [];
    let allAllowed = true;

    for (const check of checks) {
      const result = this.checkRateLimit(check.key, check.type, metadata);
      results.push(result);

      if (!result.allowed) {
        allAllowed = false;
      }
    }

    return { allowed: allAllowed, results };
  }

  /**
   * Get current status for a rate limit key
   */
  static getStatus(
    key: string,
    type: keyof typeof RateLimiter.RATE_LIMITS
  ): RateLimitResult | null {
    const limit = this.RATE_LIMITS[type];
    if (!limit) return null;

    const windowKey = `${key}_${type}`;
    const entry = this.attempts.get(windowKey);
    const now = Date.now();

    if (!entry) {
      return {
        allowed: true,
        remaining: limit.requests,
        resetTime: now + limit.windowMs,
      };
    }

    // Check if blocked
    if (entry.blocked && entry.blockUntil && now < entry.blockUntil) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.resetTime,
        blocked: true,
        blockUntil: entry.blockUntil,
      };
    }

    // Check if window expired
    if (now >= entry.resetTime) {
      return {
        allowed: true,
        remaining: limit.requests,
        resetTime: now + limit.windowMs,
      };
    }

    const remaining = Math.max(0, limit.requests - entry.count);

    return {
      allowed: remaining > 0,
      remaining,
      resetTime: entry.resetTime,
    };
  }

  /**
   * Reset rate limit for a specific key (admin function)
   */
  static resetRateLimit(key: string, type: keyof typeof RateLimiter.RATE_LIMITS): boolean {
    const windowKey = `${key}_${type}`;
    const deleted = this.attempts.delete(windowKey);

    if (deleted) {
      logger.info(`Rate limit reset for ${windowKey}`);
    }

    return deleted;
  }

  /**
   * Get rate limit configuration
   */
  static getConfiguration(type: keyof typeof RateLimiter.RATE_LIMITS): RateLimit | null {
    return this.RATE_LIMITS[type] || null;
  }

  /**
   * Clean up expired entries to prevent memory leaks
   */
  private static cleanupExpiredEntries(now: number): void {
    // Run cleanup only occasionally to avoid performance impact
    if (now % 60000 < 1000) {
      // Approximately once per minute
      let cleanedCount = 0;

      for (const [key, entry] of this.attempts.entries()) {
        const expired = now >= entry.resetTime;
        const blockExpired = entry.blockUntil ? now >= entry.blockUntil : true;

        if (expired && blockExpired) {
          this.attempts.delete(key);
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        logger.debug(`Cleaned up ${cleanedCount} expired rate limit entries`);
      }
    }
  }

  /**
   * Log rate limit violations for security monitoring
   */
  private static logRateLimitViolation(
    key: string,
    type: string,
    attempts: number,
    metadata?: { ip?: string; userId?: string; userAgent?: string }
  ): void {
    logger.warn('Rate limit exceeded', {
      key: key.substring(0, 20) + '...', // Truncate for security
      type,
      attempts,
      timestamp: new Date().toISOString(),
      ip: metadata?.ip,
      userId: metadata?.userId,
      userAgent: metadata?.userAgent?.substring(0, 100), // Truncate user agent
    });
  }

  /**
   * Get statistics about current rate limiting
   */
  static getStatistics(): {
    totalActiveWindows: number;
    blockedWindows: number;
    averageUsagePercentage: number;
  } {
    const now = Date.now();
    let totalWindows = 0;
    let blockedWindows = 0;
    let totalUsage = 0;

    for (const [key, entry] of this.attempts.entries()) {
      // Skip expired entries
      if (now >= entry.resetTime && (!entry.blockUntil || now >= entry.blockUntil)) {
        continue;
      }

      totalWindows++;

      if (entry.blocked) {
        blockedWindows++;
      }

      // Calculate usage percentage
      const type = key.split('_').pop() as keyof typeof RateLimiter.RATE_LIMITS;
      const limit = this.RATE_LIMITS[type];
      if (limit) {
        const usage = (entry.count / limit.requests) * 100;
        totalUsage += usage;
      }
    }

    return {
      totalActiveWindows: totalWindows,
      blockedWindows,
      averageUsagePercentage: totalWindows > 0 ? totalUsage / totalWindows : 0,
    };
  }
}

/**
 * Specialized rate limiter for authentication operations
 */
export class AuthenticationRateLimiter extends RateLimiter {
  /**
   * Check login attempt rate limit
   */
  static checkLoginAttempt(identifier: string, ip?: string): RateLimitResult {
    // Check both by identifier and IP
    const identifierResult = this.checkRateLimit(identifier, 'AUTH_LOGIN', { ip });

    if (ip) {
      const ipResult = this.checkRateLimit(ip, 'AUTH_LOGIN', { ip });

      // If either is blocked, deny the request
      if (!identifierResult.allowed || !ipResult.allowed) {
        return {
          allowed: false,
          remaining: Math.min(identifierResult.remaining, ipResult.remaining),
          resetTime: Math.max(identifierResult.resetTime, ipResult.resetTime),
          blocked: identifierResult.blocked || ipResult.blocked,
          blockUntil: Math.max(identifierResult.blockUntil || 0, ipResult.blockUntil || 0),
        };
      }
    }

    return identifierResult;
  }

  /**
   * Check signup rate limit
   */
  static checkSignupAttempt(email: string, ip?: string): RateLimitResult {
    return this.checkRateLimit(ip || email, 'AUTH_SIGNUP', { ip });
  }

  /**
   * Check password reset rate limit
   */
  static checkPasswordResetAttempt(email: string, ip?: string): RateLimitResult {
    return this.checkRateLimit(email, 'AUTH_PASSWORD_RESET', { ip });
  }
}

// Export convenience functions
export const checkRateLimit = RateLimiter.checkRateLimit.bind(RateLimiter);
export const checkAuthLogin =
  AuthenticationRateLimiter.checkLoginAttempt.bind(AuthenticationRateLimiter);
export const getRateLimitStatus = RateLimiter.getStatus.bind(RateLimiter);
export const resetRateLimit = RateLimiter.resetRateLimit.bind(RateLimiter);
