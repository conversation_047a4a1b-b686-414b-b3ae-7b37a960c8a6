import { categorizeError, EdgeFunctionError, handleAIError } from '../ai-error-handler';

// Test usage to avoid ESLint unused vars
const __testError = EdgeFunctionError;
const __testHandler = handleAIError;

describe('AI Error Handler System', () => {
  describe('categorizeError', () => {
    it('should categorize network errors correctly', () => {
      const networkError = new Error('fetch failed: network error');
      const result = categorizeError(networkError);

      expect(result.type).toBe('network');
      expect(result.canRetry).toBe(true);
      expect(result.userFriendlyMessage).toContain('conexión a internet');
    });

    it('should categorize timeout errors correctly', () => {
      const timeoutError = new Error('Request timeout after 30s');
      const result = categorizeError(timeoutError);

      expect(result.type).toBe('timeout');
      expect(result.canRetry).toBe(true);
      expect(result.userFriendlyMessage).toContain('tardó demasiado tiempo');
    });

    it('should categorize auth errors correctly', () => {
      const authError = new Error('401 unauthorized');
      const result = categorizeError(authError);

      expect(result.type).toBe('auth');
      expect(result.canRetry).toBe(false);
      expect(result.userFriendlyMessage).toContain('Sesión expirada');
    });

    it('should categorize validation errors correctly', () => {
      const validationError = new Error('No se pudo identificar el producto');
      const result = categorizeError(validationError);

      expect(result.type).toBe('validation');
      expect(result.canRetry).toBe(false);
      expect(result.userFriendlyMessage).toContain('Producto no reconocido');
    });

    it('should categorize AI errors correctly', () => {
      const aiError = new Error('AI model failed to generate formula');
      const result = categorizeError(aiError);

      expect(result.type).toBe('ai');
      expect(result.canRetry).toBe(true);
      expect(result.userFriendlyMessage).toContain('Error procesando con IA');
    });

    it('should handle unknown errors gracefully', () => {
      const unknownError = new Error('Something unexpected happened');
      const result = categorizeError(unknownError);

      expect(result.type).toBe('unknown');
      expect(result.canRetry).toBe(true);
      expect(result.userFriendlyMessage).toContain('Error inesperado');
    });

    it('should handle null/undefined errors', () => {
      const result1 = categorizeError(null);
      const result2 = categorizeError(undefined);

      expect(result1.type).toBe('unknown');
      expect(result2.type).toBe('unknown');
      expect(result1.message).toBe('Unknown error');
      expect(result2.message).toBe('Unknown error');
    });
  });

  describe('Error Recovery System', () => {
    it('should suggest retry for retryable errors', () => {
      const networkError = categorizeError(new Error('network failure'));
      expect(networkError.canRetry).toBe(true);

      const timeoutError = categorizeError(new Error('timeout'));
      expect(timeoutError.canRetry).toBe(true);

      const aiError = categorizeError(new Error('AI generation failed'));
      expect(aiError.canRetry).toBe(true);
    });

    it('should not suggest retry for non-retryable errors', () => {
      const authError = categorizeError(new Error('401 unauthorized'));
      expect(authError.canRetry).toBe(false);

      const validationError = categorizeError(new Error('No se pudo identificar el producto'));
      expect(validationError.canRetry).toBe(false);
    });
  });

  describe('User-Friendly Messages', () => {
    it('should provide Spanish user-friendly messages', () => {
      const errors = [
        categorizeError(new Error('network error')),
        categorizeError(new Error('timeout')),
        categorizeError(new Error('401 auth')),
        categorizeError(new Error('validation failed')),
        categorizeError(new Error('AI error')),
        categorizeError(new Error('unknown issue')),
      ];

      errors.forEach(error => {
        expect(error.userFriendlyMessage).toBeTruthy();
        expect(error.userFriendlyMessage.length).toBeGreaterThan(10);
        // Should not contain technical jargon
        expect(error.userFriendlyMessage).not.toContain('API');
        expect(error.userFriendlyMessage).not.toContain('HTTP');
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle error objects without message property', () => {
      const weirdError = { status: 500, code: 'INTERNAL_ERROR' };
      const result = categorizeError(weirdError);

      expect(result.type).toBe('unknown');
      expect(result.message).toBe('[object Object]');
    });

    it('should handle string errors', () => {
      const stringError = 'Simple string error';
      const result = categorizeError(stringError);

      expect(result.message).toBe(stringError);
      expect(result.type).toBe('unknown');
    });

    it('should handle errors with HTTP status codes', () => {
      const httpError = new Error('Server error HTTP 503');
      const result = categorizeError(httpError);

      expect(result.httpStatus).toBe(503);
      expect(result.type).toBe('unknown');
    });
  });
});
