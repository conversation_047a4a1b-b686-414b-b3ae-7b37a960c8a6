/**
 * Comprehensive tests for Visual Formula Parser
 * Testing formula parsing, structure extraction, and visual data creation
 */

import { VisualFormulaParser } from '../visualFormulaParser';

describe('VisualFormulaParser', () => {
  let parser: VisualFormulaParser;

  beforeEach(() => {
    parser = new VisualFormulaParser();
  });

  describe('Initialization', () => {
    it('should initialize with default options', () => {
      const defaultParser = new VisualFormulaParser();
      expect(defaultParser).toBeDefined();
    });

    it('should initialize with custom options', () => {
      const customParser = new VisualFormulaParser({
        language: 'en',
        measurementSystem: 'imperial',
        includeDefaults: false,
        strictMode: true,
      });
      expect(customParser).toBeDefined();
    });
  });

  describe('Basic Formula Parsing', () => {
    it('should parse simple formula successfully', () => {
      const formulaText = `Wella Illumina Color:
- 8/1 Rubio <PERSON> (30g)
- 8/69 Rubio Claro Rojo <PERSON> (10g)
- Welloxon Perfect 30 vol (60g)

Aplicación:
1. Dividir el cabello en secciones
2. Aplicar de medios a puntas
3. Procesar 35 minutos
4. Enjuagar y acondicionar`;

      const result = parser.parse(formulaText);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.brand).toBe('Wella');
      expect(result.data?.originalText).toBe(formulaText);
      expect(result.warnings).toBeDefined();
    });

    it('should handle parsing errors gracefully', () => {
      const invalidFormula = null as any;

      const result = parser.parse(invalidFormula);

      expect(result.success).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors?.length).toBeGreaterThan(0);
    });

    it('should return warnings for incomplete formulas', () => {
      const incompleteFormula = 'Just some text without proper structure';

      const result = parser.parse(incompleteFormula);

      expect(result.success).toBe(true);
      expect(result.warnings).toBeDefined();
    });
  });

  describe('Brand Extraction', () => {
    it('should extract brand from standard format', () => {
      const formulaText = 'Wella Professionals Illumina Color:\n- 8/1 (30g)';
      const result = parser.parse(formulaText);

      expect(result.data?.brand).toBe('Wella');
    });

    it('should extract brand from explicit marker', () => {
      const formulaText = "Marca: L'Oreal Professional\n- Majirel 6/1 (40g)";
      const result = parser.parse(formulaText);

      expect(result.data?.brand).toBe("L'Oreal");
    });

    it('should handle brands with special characters', () => {
      const formulaText = "L'Oreal Professional Inoa:\n- 6/1 (30g)";
      const result = parser.parse(formulaText);

      expect(result.data?.brand).toBe("L'Oreal");
    });

    it('should return Unknown for unrecognizable brand', () => {
      const formulaText = 'Some random text without brand';
      const result = parser.parse(formulaText);

      expect(result.data?.brand).toBe('Unknown');
    });
  });

  describe('Product Line Extraction', () => {
    it('should extract line from brand context', () => {
      const formulaText = 'Wella Professionals Illumina Color:\n- 8/1 (30g)';
      const result = parser.parse(formulaText);

      expect(result.data?.line).toBeDefined();
    });

    it('should extract line from explicit marker', () => {
      const formulaText = 'Línea: Koleston Perfect\n- 6/1 (40g)';
      const result = parser.parse(formulaText);

      expect(result.data?.line).toBeDefined();
    });

    it('should return Unknown for unrecognizable line', () => {
      const formulaText = 'Some formula without clear line';
      const result = parser.parse(formulaText);

      expect(result.data?.line).toBe('Unknown');
    });
  });

  describe('Zone Formula Extraction', () => {
    it('should extract zone-specific formulas', () => {
      const formulaText = `Raíces:
- 6/1 (20g)
- 20 vol (30g)

Medios:
- 7/1 (15g)
- 30 vol (22g)

Puntas:
- 8/1 (10g)
- 40 vol (15g)`;

      const result = parser.parse(formulaText);

      expect(result.success).toBe(true);
      expect(result.data?.zones).toBeDefined();
    });

    it('should handle single formula for all zones', () => {
      const formulaText = `Fórmula completa:
- 7/1 (30g)
- 30 vol (45g)`;

      const result = parser.parse(formulaText);

      expect(result.success).toBe(true);
      expect(result.data?.zones).toBeDefined();
    });

    it('should extract ingredients with amounts', () => {
      const formulaText = `Wella Illumina:
- 8/1 Rubio Claro Cenizo (30g)
- 8/69 Rubio Claro Rojo (10g)
- Oxidante 30 vol (60g)`;

      const result = parser.parse(formulaText);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
    });
  });

  describe('Color Transition Analysis', () => {
    it('should extract color transition from context', () => {
      const context = {
        currentLevel: 5,
        targetLevel: 8,
        currentTone: 'Castaño',
        targetTone: 'Rubio',
      };

      const formulaText = 'Simple formula';
      const result = parser.parse(formulaText, context);

      expect(result.data?.colorTransition).toBeDefined();
      expect(result.data?.colorTransition.current.level).toBe(5);
      expect(result.data?.colorTransition.target.level).toBe(8);
    });

    it('should provide default transition when no context', () => {
      const formulaText = 'Simple formula';
      const result = parser.parse(formulaText);

      expect(result.data?.colorTransition).toBeDefined();
      expect(result.data?.colorTransition.current.level).toBe(5);
      expect(result.data?.colorTransition.target.level).toBe(7);
      expect(result.data?.colorTransition.difficulty).toBe('moderate');
    });

    it('should assess difficulty based on level difference', () => {
      const extremeContext = {
        currentLevel: 2,
        targetLevel: 10,
      };

      const formulaText = 'Extreme lift formula';
      const result = parser.parse(formulaText, extremeContext);

      expect(result.data?.colorTransition.difficulty).toBe('high');
    });
  });

  describe('Timeline Extraction', () => {
    it('should extract timeline from formula text', () => {
      const formulaText = `Wella Formula:
- Mix products (5 min)
- Apply to hair (15 min)
- Process (35 min)
- Rinse (5 min)`;

      const result = parser.parse(formulaText);

      expect(result.data?.timeline).toBeDefined();
      expect(Array.isArray(result.data?.timeline)).toBe(true);
    });

    it('should create default timeline when none specified', () => {
      const formulaText = 'Simple formula without timeline';
      const result = parser.parse(formulaText);

      expect(result.data?.timeline).toBeDefined();
      expect(result.data?.timeline.length).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Mixing Proportions', () => {
    it('should extract mixing ratios from formula', () => {
      const formulaText = `Formula Base:
- Color (60g)
- Oxidante 30vol (90g)
Ratio 1:1.5`;

      const result = parser.parse(formulaText);

      expect(result.data?.mixingProportions).toBeDefined();
      expect(result.data?.mixingProportions.ratio).toBeDefined();
      expect(result.data?.mixingProportions.colorAmount).toBeGreaterThan(0);
      expect(result.data?.mixingProportions.developerAmount).toBeGreaterThan(0);
    });

    it('should calculate total amounts correctly', () => {
      const formulaText = 'Formula with amounts';
      const result = parser.parse(formulaText);

      const proportions = result.data?.mixingProportions;
      expect(proportions?.totalAmount).toBe(
        proportions?.colorAmount + proportions?.developerAmount
      );
    });

    it('should provide default proportions', () => {
      const formulaText = 'Simple formula';
      const result = parser.parse(formulaText);

      expect(result.data?.mixingProportions.ratio).toBe('1:1.5');
      expect(result.data?.mixingProportions.unit).toBe('g');
    });
  });

  describe('Application Guide Extraction', () => {
    it('should extract application steps', () => {
      const formulaText = `Formula:
Aplicación:
1. Dividir cabello en 4 secciones
2. Aplicar de raíz a punta
3. Procesar 35 minutos
4. Enjuagar con agua tibia`;

      const result = parser.parse(formulaText);

      expect(result.data?.applicationGuide).toBeDefined();
      expect(result.data?.applicationGuide.steps).toBeDefined();
      expect(result.data?.applicationGuide.technique).toBeDefined();
    });

    it('should identify application technique', () => {
      const formulaText = 'Técnica balayage aplicar en diagonal';
      const result = parser.parse(formulaText);

      expect(result.data?.applicationGuide.technique).toBeDefined();
    });

    it('should extract processing time', () => {
      const formulaText = 'Procesar durante 45 minutos';
      const result = parser.parse(formulaText);

      expect(result.data?.applicationGuide.totalTime).toBeGreaterThan(0);
    });
  });

  describe('Tips and Context Extraction', () => {
    it('should extract professional tips', () => {
      const context = {
        hairCondition: 'dañado',
        previousColor: 'rubio',
      };

      const formulaText = 'Formula with professional context';
      const result = parser.parse(formulaText, context);

      expect(result.data?.tips).toBeDefined();
      expect(Array.isArray(result.data?.tips)).toBe(true);
    });

    it('should provide contextual recommendations', () => {
      const context = {
        clientHistory: 'sensitive scalp',
        hairType: 'fine',
      };

      const formulaText = 'Sensitive scalp formula';
      const result = parser.parse(formulaText, context);

      expect(result.data?.tips.length).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Conversion Information', () => {
    it('should extract brand conversion info', () => {
      const formulaText = `Conversión de L'Oreal a Wella:
L'Oreal 6/1 = Wella 6/1
Proceso equivalente`;

      const result = parser.parse(formulaText);

      expect(result.data?.conversion).toBeDefined();
    });

    it('should handle formulas without conversion', () => {
      const formulaText = 'Standard formula';
      const result = parser.parse(formulaText);

      expect(result.data?.conversion).toBeDefined();
    });
  });

  describe('Expected Result Extraction', () => {
    it('should extract expected color result', () => {
      const formulaText = `Formula:
Resultado esperado: Rubio claro cenizo brillante
Cobertura de canas: 100%`;

      const result = parser.parse(formulaText);

      expect(result.data?.expectedResult).toBeDefined();
    });

    it('should provide default result when none specified', () => {
      const formulaText = 'Basic formula';
      const result = parser.parse(formulaText);

      expect(result.data?.expectedResult).toBeDefined();
    });
  });

  describe('Client Context Integration', () => {
    it('should include client information when provided', () => {
      const context = {
        clientName: 'María García',
        serviceDate: '2024-01-15',
        stylistName: 'Ana López',
      };

      const formulaText = 'Client-specific formula';
      const result = parser.parse(formulaText, context);

      expect(result.data?.clientName).toBe('María García');
      expect(result.data?.serviceDate).toBe('2024-01-15');
      expect(result.data?.stylistName).toBe('Ana López');
    });

    it('should work without client context', () => {
      const formulaText = 'Generic formula';
      const result = parser.parse(formulaText);

      expect(result.success).toBe(true);
      expect(result.data?.clientName).toBeUndefined();
    });
  });

  describe('Parser Configuration', () => {
    it('should respect language settings', () => {
      const englishParser = new VisualFormulaParser({ language: 'en' });
      const formulaText = 'Brand: Wella Professional\nShade: 8/1 Light Ash Blonde';

      const result = englishParser.parse(formulaText);

      expect(result.success).toBe(true);
    });

    it('should respect measurement system', () => {
      const imperialParser = new VisualFormulaParser({ measurementSystem: 'imperial' });
      const formulaText = 'Color: 2 oz\nDeveloper: 3 oz';

      const result = imperialParser.parse(formulaText);

      expect(result.success).toBe(true);
    });

    it('should work in strict mode', () => {
      const strictParser = new VisualFormulaParser({ strictMode: true });
      const incompleteFormula = 'Incomplete data';

      const result = strictParser.parse(incompleteFormula);

      // Strict mode might have different behavior
      expect(result).toBeDefined();
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle empty formula text', () => {
      const result = parser.parse('');

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
    });

    it('should handle very long formula text', () => {
      const longFormula = 'Formula: ' + 'x'.repeat(10000);

      const result = parser.parse(longFormula);

      expect(result.success).toBe(true);
    });

    it('should handle special characters', () => {
      const specialFormula =
        'Wella™ Koleston® Perfect:\n- 6/1 Rubio Oscuro Cenizo (30g)\n- Welloxon® 30 vol (45g)';

      const result = parser.parse(specialFormula);

      expect(result.success).toBe(true);
    });

    it('should handle multilingual content', () => {
      const multilingualFormula = `Wella Professionals:
- Couleur: 8/1 Blond Clair Cendré
- Desarrollador: 30 vol
- Developer: 30 volume`;

      const result = parser.parse(multilingualFormula);

      expect(result.success).toBe(true);
    });

    it('should handle malformed input gracefully', () => {
      const malformedInputs = [undefined, 123, {}, [], true];

      malformedInputs.forEach(input => {
        const result = parser.parse(input as any);
        expect(result.success).toBe(false);
        expect(result.errors).toBeDefined();
      });
    });
  });

  describe('Performance', () => {
    it('should parse formulas efficiently', () => {
      const complexFormula = `Wella Professionals Illumina Color:
Raíces (0-2cm):
- 6/1 Rubio Oscuro Cenizo (15g)
- 6/19 Rubio Oscuro Cenizo Violáceo (5g)
- Welloxon Perfect 20 vol (30g)

Medios (2cm-10cm):
- 7/1 Rubio Medio Cenizo (12g)
- 7/81 Rubio Medio Perla Cenizo (3g)
- Welloxon Perfect 30 vol (22g)

Puntas (10cm-final):
- 8/1 Rubio Claro Cenizo (10g)
- 8/81 Rubio Claro Perla Cenizo (2g)
- Welloxon Perfect 30 vol (18g)

Aplicación:
1. Preparar mezclas por separado
2. Aplicar primero en raíces
3. Procesar 20 minutos
4. Aplicar medios y puntas
5. Procesar 25 minutos adicionales
6. Enjuagar con agua tibia
7. Shampoo neutralizante
8. Acondicionador de color`;

      const startTime = performance.now();
      const result = parser.parse(complexFormula);
      const endTime = performance.now();

      expect(result.success).toBe(true);
      expect(endTime - startTime).toBeLessThan(50); // Should complete in under 50ms
    });

    it('should handle multiple parsing operations efficiently', () => {
      const formulas = Array(100).fill('Wella: 8/1 (30g) + 30vol (45g)');

      const startTime = performance.now();
      formulas.forEach(formula => parser.parse(formula));
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(1000); // 100 parses in under 1 second
    });
  });

  describe('Data Validation', () => {
    it('should validate parsed data structure', () => {
      const formulaText = 'Wella Illumina: 8/1 (30g)';
      const result = parser.parse(formulaText);

      expect(result.data).toEqual(
        expect.objectContaining({
          brand: expect.any(String),
          line: expect.any(String),
          zones: expect.any(Array),
          colorTransition: expect.any(Object),
          timeline: expect.any(Array),
          mixingProportions: expect.any(Object),
          applicationGuide: expect.any(Object),
          tips: expect.any(Array),
          originalText: expect.any(String),
          confidence: expect.any(Number),
          source: expect.any(String),
          parseVersion: expect.any(String),
        })
      );
    });

    it('should maintain confidence scoring', () => {
      const wellFormedFormula = `Wella Illumina Color:
- 8/1 (30g)
- 30 vol (45g)
Aplicar 35 minutos`;

      const result = parser.parse(wellFormedFormula);

      expect(result.data?.confidence).toBeGreaterThan(0);
      expect(result.data?.confidence).toBeLessThanOrEqual(1);
    });
  });
});
