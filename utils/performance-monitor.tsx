import * as React from 'react';
import { InteractionManager } from 'react-native';
import { logger } from './logger';

// Performance monitoring and optimization utilities
export interface PerformanceMetrics {
  renderTime: number;
  memoryUsage: number;
  jsHeapSize: number;
  timestamp: number;
  componentName: string;
  props?: Record<string, unknown>;
}

export interface PerformanceThresholds {
  renderTime: { warning: number; critical: number };
  memoryUsage: { warning: number; critical: number };
  jsHeapSize: { warning: number; critical: number };
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetrics[] = [];
  private thresholds: PerformanceThresholds = {
    renderTime: { warning: 16, critical: 32 }, // 60FPS = 16ms per frame
    memoryUsage: { warning: 100, critical: 200 }, // MB
    jsHeapSize: { warning: 50, critical: 100 }, // MB
  };
  private isMonitoring = __DEV__; // Only monitor in development

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  startComponentRender(componentName: string, props?: Record<string, unknown>): () => void {
    if (!this.isMonitoring) return () => {};

    const startTime = Date.now();
    const startMemory = this.getMemoryUsage();

    return () => {
      const renderTime = Date.now() - startTime;
      const memoryUsage = this.getMemoryUsage() - startMemory;

      const metric: PerformanceMetrics = {
        renderTime,
        memoryUsage,
        jsHeapSize: this.getJSHeapSize(),
        timestamp: Date.now(),
        componentName,
        props: this.sanitizeProps(props),
      };

      this.addMetric(metric);
      this.checkThresholds(metric);
    };
  }

  private getMemoryUsage(): number {
    if (typeof performance !== 'undefined' && 'memory' in performance) {
      const memory = (performance as PerformanceNavigator & { memory?: { usedJSHeapSize: number } })
        .memory;
      return (memory?.usedJSHeapSize || 0) / (1024 * 1024); // Convert to MB
    }
    return 0;
  }

  private getJSHeapSize(): number {
    if (typeof performance !== 'undefined' && 'memory' in performance) {
      const memory = (
        performance as PerformanceNavigator & { memory?: { totalJSHeapSize: number } }
      ).memory;
      return (memory?.totalJSHeapSize || 0) / (1024 * 1024); // Convert to MB
    }
    return 0;
  }

  private sanitizeProps(props?: Record<string, unknown>): Record<string, unknown> | undefined {
    if (!props) return undefined;

    // Remove functions and large objects to avoid memory leaks
    const sanitized: Record<string, unknown> = {};

    Object.entries(props).forEach(([key, value]) => {
      if (typeof value === 'function') {
        sanitized[key] = '[Function]';
      } else if (typeof value === 'object' && value !== null) {
        // Limit object size to prevent memory issues
        const str = JSON.stringify(value);
        if (str.length > 1000) {
          sanitized[key] = '[Large Object]';
        } else {
          sanitized[key] = value;
        }
      } else {
        sanitized[key] = value;
      }
    });

    return sanitized;
  }

  private addMetric(metric: PerformanceMetrics): void {
    this.metrics.push(metric);

    // Keep only last 100 metrics to prevent memory leaks
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100);
    }
  }

  private checkThresholds(metric: PerformanceMetrics): void {
    const { renderTime, memoryUsage, jsHeapSize, componentName } = metric;

    // Check render time
    if (renderTime > this.thresholds.renderTime.critical) {
      logger.warn(`= Critical render time in ${componentName}: ${renderTime}ms`);
    } else if (renderTime > this.thresholds.renderTime.warning) {
      logger.info(`� Slow render in ${componentName}: ${renderTime}ms`);
    }

    // Check memory usage
    if (memoryUsage > this.thresholds.memoryUsage.critical) {
      logger.warn(`>� Critical memory usage in ${componentName}: ${memoryUsage.toFixed(2)}MB`);
    } else if (memoryUsage > this.thresholds.memoryUsage.warning) {
      logger.info(`� High memory usage in ${componentName}: ${memoryUsage.toFixed(2)}MB`);
    }

    // Check JS heap size
    if (jsHeapSize > this.thresholds.jsHeapSize.critical) {
      logger.warn(`=� Critical JS heap size: ${jsHeapSize.toFixed(2)}MB`);
      this.scheduleGarbageCollection();
    }
  }

  private scheduleGarbageCollection(): void {
    // Schedule garbage collection after current interactions
    InteractionManager.runAfterInteractions(() => {
      if (global.gc) {
        global.gc();
        logger.info('=� Garbage collection triggered');
      }
    });
  }

  getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  getComponentMetrics(componentName: string): PerformanceMetrics[] {
    return this.metrics.filter(m => m.componentName === componentName);
  }

  getAverageRenderTime(componentName?: string): number {
    const relevantMetrics = componentName ? this.getComponentMetrics(componentName) : this.metrics;

    if (relevantMetrics.length === 0) return 0;

    const total = relevantMetrics.reduce((sum, m) => sum + m.renderTime, 0);
    return total / relevantMetrics.length;
  }

  getSlowestComponents(count = 5): Array<{ name: string; avgRenderTime: number }> {
    const componentStats = new Map<string, { total: number; count: number }>();

    this.metrics.forEach(metric => {
      const stats = componentStats.get(metric.componentName) || { total: 0, count: 0 };
      stats.total += metric.renderTime;
      stats.count += 1;
      componentStats.set(metric.componentName, stats);
    });

    return Array.from(componentStats.entries())
      .map(([name, stats]) => ({
        name,
        avgRenderTime: stats.total / stats.count,
      }))
      .sort((a, b) => b.avgRenderTime - a.avgRenderTime)
      .slice(0, count);
  }

  generateReport(): {
    summary: {
      totalComponents: number;
      avgRenderTime: number;
      slowComponents: number;
      memoryLeaks: number;
    };
    recommendations: string[];
  } {
    const slowComponents = this.metrics.filter(
      m => m.renderTime > this.thresholds.renderTime.warning
    ).length;

    const memoryLeaks = this.metrics.filter(
      m => m.memoryUsage > this.thresholds.memoryUsage.warning
    ).length;

    const uniqueComponents = new Set(this.metrics.map(m => m.componentName)).size;

    const recommendations: string[] = [];

    if (slowComponents > this.metrics.length * 0.2) {
      recommendations.push('Consider memoizing components with React.memo()');
      recommendations.push('Review useEffect dependencies to avoid unnecessary re-renders');
    }

    if (memoryLeaks > 0) {
      recommendations.push('Check for memory leaks in useEffect cleanup functions');
      recommendations.push('Ensure all timers and subscriptions are properly cleaned up');
    }

    const avgRenderTime = this.getAverageRenderTime();
    if (avgRenderTime > this.thresholds.renderTime.warning) {
      recommendations.push('Consider lazy loading for heavy components');
      recommendations.push('Optimize expensive calculations with useMemo()');
    }

    return {
      summary: {
        totalComponents: uniqueComponents,
        avgRenderTime,
        slowComponents,
        memoryLeaks,
      },
      recommendations,
    };
  }

  clearMetrics(): void {
    this.metrics = [];
  }

  setThresholds(newThresholds: Partial<PerformanceThresholds>): void {
    this.thresholds = { ...this.thresholds, ...newThresholds };
  }
}

// React hook for performance monitoring
export function usePerformanceMonitor(componentName: string, props?: Record<string, unknown>) {
  const monitor = PerformanceMonitor.getInstance();

  React.useEffect(() => {
    const stopMonitoring = monitor.startComponentRender(componentName, props);
    return stopMonitoring;
  }, [monitor, componentName, props]);
}

// HOC for automatic performance monitoring
export function withPerformanceMonitoring<P extends Record<string, unknown>>(
  Component: React.ComponentType<P>,
  componentName?: string
) {
  const displayName = componentName || Component.displayName || Component.name || 'Component';

  return function PerformanceMonitoredComponent(props: P) {
    usePerformanceMonitor(displayName, props);
    return <Component {...props} />;
  };
}

// Performance measurement utilities
export const performanceUtils = {
  measureAsync: async <T,>(
    operation: () => Promise<T>,
    operationName: string
  ): Promise<{ result: T; duration: number }> => {
    const start = Date.now();
    const result = await operation();
    const duration = Date.now() - start;

    logger.info(`� ${operationName} completed in ${duration}ms`);

    return { result, duration };
  },

  measureSync: <T,>(operation: () => T, operationName: string): { result: T; duration: number } => {
    const start = Date.now();
    const result = operation();
    const duration = Date.now() - start;

    logger.info(`� ${operationName} completed in ${duration}ms`);

    return { result, duration };
  },

  throttle: <T extends (...args: unknown[]) => unknown>(func: T, limit: number): T => {
    let inThrottle: boolean;
    return ((...args: Parameters<T>) => {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => (inThrottle = false), limit) as NodeJS.Timeout;
      }
    }) as T;
  },

  debounce: <T extends (...args: unknown[]) => unknown>(func: T, delay: number): T => {
    let timeoutId: NodeJS.Timeout;
    return ((...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay) as NodeJS.Timeout;
    }) as T;
  },
};

export default PerformanceMonitor.getInstance();
