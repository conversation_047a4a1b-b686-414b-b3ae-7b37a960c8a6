import { MaintenanceLevel } from '@/types/lifestyle-preferences';
// import { ColorTechnique } from '@/types/desired-photo';

// Map maintenance levels to recommended technique IDs
export const getRecommendedTechniques = (level: MaintenanceLevel): string[] => {
  switch (level) {
    case MaintenanceLevel.LOW:
      // Techniques that grow out naturally and require less frequent touch-ups
      return ['balayage', 'ombre', 'babylights', 'foilyage'];

    case MaintenanceLevel.MEDIUM:
      // Techniques that need moderate maintenance
      return ['highlights', 'money_piece', 'reverse_balayage', 'foilyage'];

    case MaintenanceLevel.HIGH:
      // Techniques that require frequent visits
      return ['full_color', 'chunky_highlights', 'color_correction'];

    default:
      return [];
  }
};

// Get maintenance level based on selected technique
export const getMaintenanceLevelForTechnique = (techniqueId: string): MaintenanceLevel | null => {
  const techniqueMaintenance: Record<string, MaintenanceLevel> = {
    // Low maintenance techniques
    balayage: MaintenanceLevel.LOW,
    ombre: MaintenanceLevel.LOW,
    babylights: MaintenanceLevel.LOW,

    // Medium maintenance techniques
    highlights: MaintenanceLevel.MEDIUM,
    foilyage: MaintenanceLevel.MEDIUM,
    money_piece: MaintenanceLevel.MEDIUM,
    reverse_balayage: MaintenanceLevel.MEDIUM,

    // High maintenance techniques
    full_color: MaintenanceLevel.HIGH,
    chunky_highlights: MaintenanceLevel.HIGH,
    color_correction: MaintenanceLevel.HIGH,
  };

  return techniqueMaintenance[techniqueId] || null;
};

// Check if technique matches maintenance preference
export const isTechniqueCompatible = (
  techniqueId: string,
  maintenanceLevel: MaintenanceLevel
): boolean => {
  const recommendedTechniques = getRecommendedTechniques(maintenanceLevel);
  return recommendedTechniques.includes(techniqueId);
};

// Get warning message for mismatched selection
export const getTechniqueMismatchWarning = (
  techniqueId: string,
  maintenanceLevel: MaintenanceLevel
): string | null => {
  const techniqueMaintenanceLevel = getMaintenanceLevelForTechnique(techniqueId);

  if (!techniqueMaintenanceLevel || techniqueMaintenanceLevel === maintenanceLevel) {
    return null;
  }

  const maintenanceLabels = {
    [MaintenanceLevel.LOW]: 'baja (3-4 meses)',
    [MaintenanceLevel.MEDIUM]: 'media (6-8 semanas)',
    [MaintenanceLevel.HIGH]: 'alta (3-4 semanas)',
  };

  const techniqueLabels = {
    [MaintenanceLevel.LOW]: 'poco mantenimiento',
    [MaintenanceLevel.MEDIUM]: 'mantenimiento moderado',
    [MaintenanceLevel.HIGH]: 'alto mantenimiento',
  };

  return `⚠️ Esta técnica requiere ${techniqueLabels[techniqueMaintenanceLevel]}, pero prefieres frecuencia ${maintenanceLabels[maintenanceLevel]}`;
};

// Get explanation for technique maintenance
export const getTechniqueMaintenanceExplanation = (techniqueId: string): string => {
  const explanations: Record<string, string> = {
    full_color: 'Requiere retoque de raíces cada 3-4 semanas',
    highlights: 'Retoque cada 6-8 semanas para mantener el contraste',
    balayage: 'Crece naturalmente, retoque cada 3-4 meses',
    ombre: 'Mínimo mantenimiento, crece de forma natural',
    babylights: 'Efecto muy natural, retoque cada 3-4 meses',
    color_correction: 'Requiere seguimiento frecuente para mantener',
    foilyage: 'Mantenimiento moderado, cada 2-3 meses',
    money_piece: 'Retoque frontal cada 6-8 semanas',
    chunky_highlights: 'Líneas definidas requieren retoque frecuente',
    reverse_balayage: 'Mantenimiento moderado para conservar contraste',
  };

  return explanations[techniqueId] || '';
};
