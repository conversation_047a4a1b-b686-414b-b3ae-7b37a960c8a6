/**
 * Contrast Checker for Salonier
 * Verifies all color combinations meet WCAG 2.1 AA standards
 */

import Colors from '@/constants/colors';
import { getContrastRatio, meetsWCAGAA, getContrastLabel } from './accessibility';

interface ContrastResult {
  combination: string;
  foreground: string;
  background: string;
  ratio: number;
  meetsAA: boolean;
  meetsAALarge: boolean;
  label: string;
}

/**
 * Check all color combinations in the app
 */
export const checkAppContrast = (): ContrastResult[] => {
  const results: ContrastResult[] = [];
  const { light } = Colors;

  // Critical text combinations
  const criticalCombinations = [
    // Primary text
    { name: 'Text on Background', fg: light.text, bg: light.background },
    {
      name: 'Text on Secondary Background',
      fg: light.text,
      bg: light.backgroundSecondary,
    },
    { name: 'Text on Card', fg: light.text, bg: light.card },
    {
      name: 'Secondary Text on Background',
      fg: light.textSecondary,
      bg: light.background,
    },

    // Light text
    { name: 'Light Text on Primary', fg: light.textLight, bg: light.primary },
    {
      name: 'Light Text on Primary Dark',
      fg: light.textLight,
      bg: light.primaryDark,
    },
    {
      name: 'Light Text on Dark Background',
      fg: light.textLight,
      bg: light.backgroundDark,
    },
    {
      name: 'Light Text on Card Dark',
      fg: light.textLight,
      bg: light.cardDark,
    },

    // Status colors
    { name: 'Success Text', fg: light.success, bg: light.background },
    { name: 'Warning Text', fg: light.warning, bg: light.background },
    { name: 'Error Text', fg: light.error, bg: light.background },
    { name: 'Info Text', fg: light.info, bg: light.background },

    // Buttons
    { name: 'Primary Button Text', fg: light.textLight, bg: light.primary },
    { name: 'Secondary Button Text', fg: light.text, bg: light.secondary },
    { name: 'Success Button Text', fg: light.textLight, bg: light.success },
    { name: 'Error Button Text', fg: light.textLight, bg: light.error },

    // Navigation
    {
      name: 'Tab Icon Default',
      fg: light.tabIconDefault,
      bg: light.background,
    },
    {
      name: 'Tab Icon Selected',
      fg: light.tabIconSelected,
      bg: light.background,
    },

    // AI specific
    {
      name: 'AI Processing Text',
      fg: light.aiProcessing,
      bg: light.background,
    },
    { name: 'AI Success Text', fg: light.aiSuccess, bg: light.background },
    { name: 'AI Warning Text', fg: light.aiWarning, bg: light.background },
    { name: 'AI Error Text', fg: light.aiError, bg: light.background },

    // Special states
    { name: 'Privacy Badge', fg: light.textLight, bg: light.privacy },
    { name: 'Security Badge', fg: light.textLight, bg: light.security },
    {
      name: 'Quality Excellent',
      fg: light.qualityExcellent,
      bg: light.background,
    },
    { name: 'Quality Good', fg: light.qualityGood, bg: light.background },
    { name: 'Quality Poor', fg: light.qualityPoor, bg: light.background },
  ];

  criticalCombinations.forEach(combo => {
    const ratio = getContrastRatio(combo.fg, combo.bg);
    results.push({
      combination: combo.name,
      foreground: combo.fg,
      background: combo.bg,
      ratio: Math.round(ratio * 100) / 100,
      meetsAA: meetsWCAGAA(ratio, false),
      meetsAALarge: meetsWCAGAA(ratio, true),
      label: getContrastLabel(ratio),
    });
  });

  return results;
};

/**
 * Get failing combinations
 */
export const getFailingCombinations = (): ContrastResult[] => {
  return checkAppContrast().filter(result => !result.meetsAA);
};

/**
 * Get warning combinations (pass for large text only)
 */
export const getWarningCombinations = (): ContrastResult[] => {
  return checkAppContrast().filter(result => !result.meetsAA && result.meetsAALarge);
};

/**
 * Generate contrast report
 */
export const generateContrastReport = (): string => {
  const results = checkAppContrast();
  const failing = results.filter(r => !r.meetsAA);
  const warnings = results.filter(r => !r.meetsAA && r.meetsAALarge);
  const passing = results.filter(r => r.meetsAA);

  let report = '# Contrast Ratio Report\n\n';
  report += `Total combinations checked: ${results.length}\n`;
  report += `✅ Passing: ${passing.length}\n`;
  report += `⚠️ Warnings (large text only): ${warnings.length}\n`;
  report += `❌ Failing: ${failing.length}\n\n`;

  if (failing.length > 0) {
    report += '## ❌ Failing Combinations\n\n';
    failing.forEach(r => {
      report += `- **${r.combination}**: ${r.ratio}:1 (${r.label})\n`;
      report += `  - Foreground: ${r.foreground}\n`;
      report += `  - Background: ${r.background}\n\n`;
    });
  }

  if (warnings.length > 0) {
    report += '## ⚠️ Warning Combinations\n\n';
    warnings.forEach(r => {
      report += `- **${r.combination}**: ${r.ratio}:1 (${r.label})\n`;
      report += `  - Foreground: ${r.foreground}\n`;
      report += `  - Background: ${r.background}\n`;
      report += `  - Note: Only passes for large text\n\n`;
    });
  }

  report += '## ✅ Passing Combinations\n\n';
  passing.forEach(r => {
    report += `- **${r.combination}**: ${r.ratio}:1 (${r.label})\n`;
  });

  return report;
};
