/**
 * Tests for Product Availability Validation System
 * Verifica que la validación de disponibilidad funcione correctamente
 */

import {
  validateFormulaWithAvailability,
  generateAvailabilitySuggestions,
  createProductAvailabilityFromInventory,
  checkProductsAvailability,
  type Formula,
} from '../../supabase/functions/salonier-assistant/utils/formula-validator';

describe('Product Availability Validation', () => {
  // Mock inventory data
  const mockInventoryProducts = [
    {
      id: '1',
      brand: 'Wella',
      line: 'Koleston Perfect',
      category: 'tinte',
      shade: '7.1',
      currentStock: 85,
      isActive: true,
    },
    {
      id: '2',
      brand: 'Wella',
      line: 'Koleston Perfect',
      category: 'oxidante',
      shade: '30vol',
      currentStock: 150,
      isActive: true,
    },
    {
      id: '3',
      brand: 'Wella',
      line: 'Blondor',
      category: 'decolorante',
      shade: 'Polvo',
      currentStock: 100,
      isActive: true,
    },
  ];

  const mockFormula: Formula = {
    brand: 'Wella',
    line: 'Koleston Perfect',
    currentLevel: 6,
    desiredLevel: 7, // Only 1 level lift - doesn't need bleach
    currentState: 'natural',
    totalTime: 60,
    steps: [
      {
        id: 'step1',
        title: 'Color',
        type: 'DIRECT_COLOR' as any,
        processingTime: 30,
        instructions: 'Apply color',
        ingredients: [
          {
            product: 'Koleston 7.1',
            shade: '7.1',
            amount: 60,
            unit: 'ml',
            type: 'color',
          },
          {
            product: 'Oxidante 30vol',
            amount: 60,
            unit: 'ml',
            type: 'developer',
            volume: 30,
          },
        ],
      },
    ],
  };

  describe('createProductAvailabilityFromInventory', () => {
    it('should convert inventory products to availability format', () => {
      const availability = createProductAvailabilityFromInventory(mockInventoryProducts);

      expect(availability).toHaveLength(3);

      const colorProduct = availability.find(p => p.type === 'color');
      expect(colorProduct).toBeDefined();
      expect(colorProduct?.brand).toBe('Wella');
      expect(colorProduct?.line).toBe('Koleston Perfect');
      expect(colorProduct?.availableShades).toContain('7.1');

      const developerProduct = availability.find(p => p.type === 'developer');
      expect(developerProduct).toBeDefined();
      expect(developerProduct?.availableDeveloperVolumes).toContain(30);

      const bleachProduct = availability.find(p => p.type === 'bleach');
      expect(bleachProduct).toBeDefined();
      expect(bleachProduct?.hasDecolorante).toBe(true);
    });
  });

  describe('validateFormulaWithAvailability', () => {
    it('should validate formula successfully when all products are available', () => {
      const availability = createProductAvailabilityFromInventory(mockInventoryProducts);
      const result = validateFormulaWithAvailability(mockFormula, availability);

      // Should pass availability checks for this simple formula
      const availabilityViolations = result.violations.filter(v => v.type === 'availability');
      expect(availabilityViolations).toHaveLength(0);
    });

    it('should detect missing decolorante for high lift requirements', () => {
      const highLiftFormula: Formula = {
        ...mockFormula,
        currentLevel: 3,
        desiredLevel: 8, // 5 levels - needs bleach
        steps: [
          {
            id: 'bleach-step',
            title: 'Bleach',
            type: 'BLEACHING' as any,
            processingTime: 45,
            instructions: 'Apply bleach',
            ingredients: [
              {
                product: 'Koleston Bleach',
                amount: 50,
                unit: 'ml',
                type: 'bleach',
              },
            ],
          },
        ],
      };

      // Remove bleach from available products (keep only Koleston line)
      const limitedProducts = mockInventoryProducts.filter(p => p.line === 'Koleston Perfect');
      const availability = createProductAvailabilityFromInventory(limitedProducts);

      const result = validateFormulaWithAvailability(highLiftFormula, availability);

      const availabilityViolations = result.violations.filter(v => v.type === 'availability');
      expect(availabilityViolations.length).toBeGreaterThan(0);

      const decoloranteError = availabilityViolations.find(v =>
        v.message.includes('no incluye decolorante')
      );
      expect(decoloranteError).toBeDefined();
      expect(decoloranteError?.severity).toBe('critical');
    });

    it('should detect unavailable shade', () => {
      const unavailableShadeFormula: Formula = {
        ...mockFormula,
        steps: [
          {
            id: 'step1',
            title: 'Color',
            type: 'DIRECT_COLOR' as any,
            processingTime: 30,
            instructions: 'Apply color',
            ingredients: [
              {
                product: 'Koleston 9.11',
                shade: '9.11', // Not available in inventory
                amount: 60,
                unit: 'ml',
                type: 'color',
              },
            ],
          },
        ],
      };

      const availability = createProductAvailabilityFromInventory(mockInventoryProducts);
      const result = validateFormulaWithAvailability(unavailableShadeFormula, availability);

      const availabilityViolations = result.violations.filter(v => v.type === 'availability');
      const shadeError = availabilityViolations.find(
        v => v.message.includes('9.11') && v.message.includes('no disponible')
      );
      expect(shadeError).toBeDefined();
    });

    it('should detect unavailable developer volume', () => {
      const unavailableVolumeFormula: Formula = {
        ...mockFormula,
        steps: [
          {
            id: 'step1',
            title: 'Color',
            type: 'DIRECT_COLOR' as any,
            processingTime: 30,
            instructions: 'Apply color',
            ingredients: [
              {
                product: 'Oxidante 40vol',
                amount: 60,
                unit: 'ml',
                type: 'developer',
                volume: 40, // Not available - only 30vol exists
              },
            ],
          },
        ],
      };

      const availability = createProductAvailabilityFromInventory(mockInventoryProducts);
      const result = validateFormulaWithAvailability(unavailableVolumeFormula, availability);

      const availabilityViolations = result.violations.filter(v => v.type === 'availability');
      const volumeError = availabilityViolations.find(
        v => v.message.includes('40vol') && v.message.includes('máximo disponible')
      );
      expect(volumeError).toBeDefined();
    });
  });

  describe('generateAvailabilitySuggestions', () => {
    it('should generate suggestions for missing products', () => {
      const problematicFormula: Formula = {
        ...mockFormula,
        currentLevel: 3,
        desiredLevel: 9,
        steps: [
          {
            id: 'bleach-step',
            title: 'Bleach',
            type: 'BLEACHING' as any,
            processingTime: 45,
            instructions: 'Apply bleach',
            ingredients: [
              {
                product: 'Koleston Bleach',
                amount: 50,
                unit: 'ml',
                type: 'bleach',
              },
            ],
          },
        ],
      };

      // Use limited inventory without bleach in Koleston line
      const limitedProducts = mockInventoryProducts.filter(p => p.line === 'Koleston Perfect');
      const availability = createProductAvailabilityFromInventory(limitedProducts);

      const suggestions = generateAvailabilitySuggestions(problematicFormula, availability);

      expect(suggestions.canProceed).toBe(false);
      expect(suggestions.criticalIssues.length).toBeGreaterThan(0);
      expect(suggestions.suggestions.length).toBeGreaterThan(0);
    });

    it('should provide alternative formula when possible', () => {
      const fullAvailability = createProductAvailabilityFromInventory(mockInventoryProducts);

      const formulaWithMissingVolume: Formula = {
        ...mockFormula,
        steps: [
          {
            id: 'step1',
            title: 'Color',
            type: 'DIRECT_COLOR' as any,
            processingTime: 30,
            instructions: 'Apply color',
            ingredients: [
              {
                product: 'Oxidante 40vol',
                amount: 60,
                unit: 'ml',
                type: 'developer',
                volume: 40,
              },
            ],
          },
        ],
      };

      const suggestions = generateAvailabilitySuggestions(
        formulaWithMissingVolume,
        fullAvailability
      );

      // Should suggest using 30vol instead of 40vol or adjust to expected volume
      if (suggestions.alternativeFormula) {
        const correctedStep = suggestions.alternativeFormula.steps[0];
        const correctedDeveloper = correctedStep.ingredients.find(ing => ing.type === 'developer');
        // The auto-correct might set to expected volume (20) or available volume (30)
        expect([20, 30]).toContain(correctedDeveloper?.volume);
      }
    });
  });

  describe('checkProductsAvailability', () => {
    it('should identify available products correctly', () => {
      const availability = createProductAvailabilityFromInventory(mockInventoryProducts);

      const requiredProducts = [
        { type: 'color', shade: '7.1' },
        { type: 'developer', volume: 30 },
      ];

      const result = checkProductsAvailability(requiredProducts, availability);

      expect(result.available).toBe(true);
      expect(result.missing).toHaveLength(0);
    });

    it('should identify missing products and suggest alternatives', () => {
      const availability = createProductAvailabilityFromInventory(mockInventoryProducts);

      const requiredProducts = [
        { type: 'color', shade: '9.11' }, // Not available
        { type: 'developer', volume: 40 }, // Not available
      ];

      const result = checkProductsAvailability(requiredProducts, availability);

      expect(result.available).toBe(false);
      expect(result.missing).toContain('color 9.11');
      expect(result.missing).toContain('developer 40vol');
      expect(result.alternatives.length).toBeGreaterThan(0);
    });
  });

  describe('Stock validation', () => {
    it('should warn about insufficient stock', () => {
      const lowStockProducts = [
        {
          ...mockInventoryProducts[0],
          currentStock: 10, // Very low stock
        },
        ...mockInventoryProducts.slice(1),
      ];

      const highConsumptionFormula: Formula = {
        ...mockFormula,
        steps: [
          {
            id: 'step1',
            title: 'Color',
            type: 'DIRECT_COLOR' as any,
            processingTime: 30,
            instructions: 'Apply color',
            ingredients: [
              {
                product: 'Koleston 7.1',
                shade: '7.1',
                amount: 50, // More than available stock (10ml)
                unit: 'ml',
                type: 'color',
              },
            ],
          },
        ],
      };

      const availability = createProductAvailabilityFromInventory(lowStockProducts);
      const result = validateFormulaWithAvailability(highConsumptionFormula, availability);

      const stockWarnings = result.violations.filter(
        v => v.type === 'availability' && v.message.includes('Stock insuficiente')
      );
      expect(stockWarnings.length).toBeGreaterThan(0);
    });
  });
});
