/**
 * Tests for FormulaExplainer system
 */

import {
  FormulaExplainer,
  generateQuickExplanation,
} from '../supabase/functions/salonier-assistant/utils/formula-explainer';
import {
  validateColorProcess,
  ProcessType,
} from '../supabase/functions/salonier-assistant/utils/colorimetry-rules';

describe('FormulaExplainer', () => {
  describe('generateQuickExplanation', () => {
    it('should generate correct explanation for lightening process', () => {
      const result = generateQuickExplanation(4, 6, 30, 'es');
      expect(result).toContain('aclaramos');
      expect(result).toContain('2 niveles');
      expect(result).toContain('30vol');
    });

    it('should generate correct explanation for darkening process', () => {
      const result = generateQuickExplanation(7, 4, 10, 'es');
      expect(result).toContain('oscurecemos');
      expect(result).toContain('3 niveles');
      expect(result).toContain('10vol');
    });

    it('should generate correct explanation for toning process', () => {
      const result = generateQuickExplanation(6, 6, 20, 'es');
      expect(result).toContain('matizamos');
      expect(result).toContain('0 niveles');
      expect(result).toContain('20vol');
    });

    it('should generate English explanations when requested', () => {
      const result = generateQuickExplanation(4, 6, 30, 'en');
      expect(result).toContain('lighten');
      expect(result).toContain('2 levels');
      expect(result).toContain('30vol');
    });
  });

  describe('FormulaExplainer class', () => {
    let explainer: FormulaExplainer;
    let mockAnalysis: any;

    beforeEach(() => {
      explainer = new FormulaExplainer('es');

      // Mock analysis data
      mockAnalysis = {
        diagnosis: {
          averageDepthLevel: 4,
          overallConfidence: 85,
          zoneAnalysis: {
            roots: {
              state: 'Natural',
              damage: 'Leve',
              porosity: 'Media',
              resistance: 'Media',
              grayPercentage: 0,
            },
          },
          detectedRisks: {
            metallic: false,
            henna: false,
            damaged: false,
          },
        },
        desiredResult: {
          detectedLevel: 6,
          general: {
            technique: 'full_color',
          },
        },
        formula: {
          level: 6,
          processingTime: '35-45 minutos',
        },
        selectedProducts: [],
        processValidation: {
          isViable: true,
          requiredProcesses: [ProcessType.DIRECT_COLOR],
          warnings: [],
          recommendedDeveloperVolume: 30,
          estimatedSessions: 1,
        },
        brand: "L'Oréal",
        line: 'Professional',
        technique: 'full_color',
      };
    });

    it('should generate a complete explanation', () => {
      const result = explainer.generateExplanation(mockAnalysis);

      expect(result).toHaveProperty('confidenceScore');
      expect(result).toHaveProperty('overallStrategy');
      expect(result).toHaveProperty('levelExplanation');
      expect(result).toHaveProperty('developerExplanation');
      expect(result).toHaveProperty('productChoiceExplanation');
      expect(result).toHaveProperty('timingExplanation');
      expect(result).toHaveProperty('processSteps');
      expect(result).toHaveProperty('riskFactors');
      expect(result).toHaveProperty('successTips');
      expect(result).toHaveProperty('colorimetryReasoning');
    });

    it('should calculate confidence score correctly', () => {
      const result = explainer.generateExplanation(mockAnalysis);

      // High confidence for simple, clear diagnosis
      expect(result.confidenceScore).toBeGreaterThan(80);
    });

    it('should reduce confidence for damaged hair', () => {
      const damagedAnalysis = {
        ...mockAnalysis,
        diagnosis: {
          ...mockAnalysis.diagnosis,
          zoneAnalysis: {
            roots: {
              ...mockAnalysis.diagnosis.zoneAnalysis.roots,
              damage: 'Severo',
            },
          },
        },
      };

      const result = explainer.generateExplanation(damagedAnalysis);
      const originalResult = explainer.generateExplanation(mockAnalysis);

      expect(result.confidenceScore).toBeLessThan(originalResult.confidenceScore);
    });

    it('should generate process steps for direct color', () => {
      const result = explainer.generateExplanation(mockAnalysis);

      expect(result.processSteps).toHaveLength(1);
      expect(result.processSteps[0].action).toContain('Aplicación de Color');
      expect(result.processSteps[0].icon).toBe('🎯');
      expect(result.processSteps[0].warningLevel).toBe('low');
    });

    it('should generate process steps for bleaching', () => {
      const bleachingAnalysis = {
        ...mockAnalysis,
        processValidation: {
          ...mockAnalysis.processValidation,
          requiredProcesses: [ProcessType.BLEACHING, ProcessType.DIRECT_COLOR],
          warnings: ['Se requiere decoloración'],
        },
      };

      const result = explainer.generateExplanation(bleachingAnalysis);

      expect(result.processSteps).toHaveLength(2);
      expect(result.processSteps[0].action).toContain('Decoloración');
      expect(result.processSteps[0].icon).toBe('⚡');
      expect(result.processSteps[0].warningLevel).toBe('high');
    });

    it('should identify risk factors for damaged hair', () => {
      const damagedAnalysis = {
        ...mockAnalysis,
        diagnosis: {
          ...mockAnalysis.diagnosis,
          zoneAnalysis: {
            roots: {
              ...mockAnalysis.diagnosis.zoneAnalysis.roots,
              damage: 'Moderado',
            },
          },
        },
      };

      const result = explainer.generateExplanation(damagedAnalysis);

      expect(result.riskFactors).toHaveLength(1);
      expect(result.riskFactors[0].factor).toContain('Cabello Previamente Dañado');
      expect(result.riskFactors[0].level).toBe('medium');
    });

    it('should identify risk factors for high porosity', () => {
      const porosityAnalysis = {
        ...mockAnalysis,
        diagnosis: {
          ...mockAnalysis.diagnosis,
          zoneAnalysis: {
            roots: {
              ...mockAnalysis.diagnosis.zoneAnalysis.roots,
              porosity: 'Alta',
            },
          },
        },
      };

      const result = explainer.generateExplanation(porosityAnalysis);

      expect(result.riskFactors).toHaveLength(1);
      expect(result.riskFactors[0].factor).toContain('Porosidad Alta');
      expect(result.riskFactors[0].level).toBe('medium');
    });

    it('should generate appropriate success tips', () => {
      const result = explainer.generateExplanation(mockAnalysis);

      expect(result.successTips).toContain(
        '🎯 Realiza siempre una prueba de mechón 48 horas antes'
      );
      expect(result.successTips).toContain('⏰ No dejes el producto más tiempo del recomendado');
      expect(result.successTips).toContain('🌡️ Mantén temperatura ambiente constante (20-25°C)');
      expect(result.successTips).toContain('📸 Documenta el resultado para futuras referencias');
    });

    it('should generate gray-specific tips for high gray percentage', () => {
      const grayAnalysis = {
        ...mockAnalysis,
        diagnosis: {
          ...mockAnalysis.diagnosis,
          zoneAnalysis: {
            roots: {
              ...mockAnalysis.diagnosis.zoneAnalysis.roots,
              grayPercentage: 50,
            },
          },
        },
      };

      const result = explainer.generateExplanation(grayAnalysis);

      expect(result.successTips).toContain(
        '👩‍🦳 Para buena cobertura de canas, aplica primero en raíces'
      );
    });

    it('should generate bleaching-specific tips when required', () => {
      const bleachingAnalysis = {
        ...mockAnalysis,
        processValidation: {
          ...mockAnalysis.processValidation,
          requiredProcesses: [ProcessType.BLEACHING],
        },
      };

      const result = explainer.generateExplanation(bleachingAnalysis);

      expect(result.successTips).toContain('⚡ Monitorea constantemente durante la decoloración');
      expect(result.successTips).toContain(
        '💧 Aplica tratamiento reparador inmediatamente después'
      );
    });

    it('should work with English language', () => {
      const englishExplainer = new FormulaExplainer('en');
      const result = englishExplainer.generateExplanation(mockAnalysis);

      expect(result.overallStrategy).toContain('Lightening Strategy');
      expect(result.levelExplanation).toContain('Chosen Level');
      expect(result.processSteps[0].action).toContain('Color Application');
    });
  });

  describe('colorimetry integration', () => {
    it('should integrate with colorimetry validation', () => {
      const colorProcess = {
        currentLevel: 4,
        desiredLevel: 6,
        currentState: 'natural' as const,
        hasMetallicSalts: false,
        hasHenna: false,
      };

      const validation = validateColorProcess(colorProcess, 40);

      expect(validation.isViable).toBe(true);
      expect(validation.requiredProcesses).toContain(ProcessType.DIRECT_COLOR);
      expect(validation.recommendedDeveloperVolume).toBe(30);
    });

    it('should handle complex processes requiring bleaching', () => {
      const colorProcess = {
        currentLevel: 2,
        desiredLevel: 10,
        currentState: 'natural' as const,
        hasMetallicSalts: false,
        hasHenna: false,
      };

      const validation = validateColorProcess(colorProcess, 40);

      expect(validation.isViable).toBe(true);
      expect(validation.requiredProcesses).toContain(ProcessType.BLEACHING);
      expect(validation.estimatedSessions).toBeGreaterThan(1);
      expect(validation.warnings.length).toBeGreaterThan(0);
    });

    it('should handle incompatible products', () => {
      const colorProcess = {
        currentLevel: 4,
        desiredLevel: 6,
        currentState: 'natural' as const,
        hasMetallicSalts: true,
        hasHenna: false,
      };

      const validation = validateColorProcess(colorProcess, 40);

      expect(validation.isViable).toBe(false);
      expect(validation.warnings).toContain(
        'Presencia de sales metálicas detectada. Requiere test de mechón y posible tratamiento de eliminación.'
      );
    });
  });
});
