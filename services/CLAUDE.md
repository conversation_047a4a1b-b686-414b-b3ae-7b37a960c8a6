# CLAUDE.md - Business Logic Services

## 🎯 Propósito

Servicios de lógica de negocio que encapsulan operaciones complejas: conversión entre marcas, corrección de color, consumo de inventario, matching de productos y normalización. Todos los servicios DEBEN ser idempotentes y thread-safe.

## 📁 Servicios Disponibles

### Core Services

- `brandConversionService.ts` - Conversión entre marcas de tintes
- `colorCorrectionService.ts` - Corrección y neutralización de tonos
- `inventoryConsumptionService.ts` - Tracking de consumo de productos
- `productMatcherService.ts` - Matching fuzzy AI ↔ inventario
- `productNamingService.ts` - Normalización de nombres
- `productNormalizationService.ts` - Estandarización de datos

## 🔄 Brand Conversion Service

### Propósito

Convierte fórmulas entre diferentes marcas de coloración capilar con análisis de confianza y ajustes específicos.

```typescript
interface ConversionResult {
  originalFormula: ColorFormula;
  targetFormula: ColorFormula;
  adjustments: {
    toneMapping: string; // "7/1 → 7.13"
    mixRatio: string; // "1:1 → 1:1.5"
    processingTime: number; // +5 min
    additionalNotes: string[];
  };
  confidence: number; // 0-100
  warnings?: string[];
}
```

### Uso del Servicio

```typescript
import { brandConversionService } from '@/services/brandConversionService';

// Verificar si la conversión existe
const hasConversion = brandConversionService.hasConversion(
  'Wella',
  'Koleston',
  "L'Oréal",
  'Majirel'
);

if (hasConversion) {
  // Convertir fórmula
  const conversion = await brandConversionService.convert(originalFormula, "L'Oréal", 'Majirel');

  // Verificar confianza
  if (conversion.confidence < 70) {
    showConversionWarning(conversion);
  }

  // Usar fórmula convertida
  useConvertedFormula(conversion.targetFormula);
}
```

### Base de Conversiones

```typescript
// Estructura de conversion database
const conversionDatabase = {
  'Wella:Koleston:7/1': {
    "L'Oréal:Majirel": {
      tone: '7.13',
      adjustments: {
        mixRatio: '1:1 → 1:1.5',
        processingTime: 5,
        notes: [
          'Majirel 7.1 es ceniza puro, usar 7.13 para incluir beige',
          'Mayor proporción de oxidante para mejor cobertura',
        ],
      },
      confidence: 85,
    },
  },
  // ... más conversiones
};
```

## 🎨 Color Correction Service

### Propósito

Analiza tonos no deseados y genera fórmulas de corrección con neutralización específica.

```typescript
interface ColorCorrectionResult {
  problem: UnwantedTone;
  correction: CorrectionFormula;
  technique: CorrectionTechnique;
  expectedResult: ColorResult;
  riskAssessment: RiskLevel;
}

enum UnwantedTone {
  ORANGE = 'orange',
  YELLOW = 'yellow',
  GREEN = 'green',
  BRASSY = 'brassy',
  RED = 'red',
}
```

### Lógica de Neutralización

```typescript
const neutralizationRules = {
  [UnwantedTone.ORANGE]: {
    neutralizer: 'blue',
    pigments: ['0.1', '0.11', '0.2'],
    ratio: 0.25, // 25% del volumen total
    processingTime: 15,
  },
  [UnwantedTone.YELLOW]: {
    neutralizer: 'violet',
    pigments: ['0.2', '0.22', '0.6'],
    ratio: 0.2,
    processingTime: 10,
  },
  [UnwantedTone.GREEN]: {
    neutralizer: 'red',
    pigments: ['0.4', '0.43', '0.5'],
    ratio: 0.15,
    processingTime: 12,
  },
};

// Análisis automático
const analyzeColorProblem = (currentColor: HairColor): UnwantedTone | null => {
  const { hue, saturation, lightness } = currentColor.hsl;

  if (hue >= 15 && hue <= 35 && saturation > 0.4) return UnwantedTone.ORANGE;
  if (hue >= 45 && hue <= 65 && saturation > 0.3) return UnwantedTone.YELLOW;
  if (hue >= 120 && hue <= 140) return UnwantedTone.GREEN;

  return null;
};
```

## 📦 Inventory Consumption Service

### Propósito

Rastrea el consumo de productos en servicios con validación de stock y cálculo de costos reales.

```typescript
interface ConsumptionRecord {
  serviceId: string;
  productId: string;
  plannedQuantity: number; // Cantidad en fórmula
  actualQuantity: number; // Cantidad realmente usada
  wastage: number; // Desperdicios
  cost: number; // Costo real
  timestamp: string;
  userId: string;
}
```

### Tracking de Consumo

```typescript
class InventoryConsumptionService {
  // Registrar consumo planificado (de fórmula)
  async recordPlannedConsumption(
    serviceId: string,
    consumptions: PlannedConsumption[]
  ): Promise<void> {
    // Validar stock disponible
    for (const consumption of consumptions) {
      if (!this.validateStock(consumption.productId, consumption.quantity)) {
        throw new Error(`Insufficient stock for ${consumption.productId}`);
      }
    }

    // Reservar productos
    await this.reserveProducts(serviceId, consumptions);
  }

  // Registrar consumo real (al finalizar servicio)
  async recordActualConsumption(
    serviceId: string,
    actualConsumptions: ActualConsumption[]
  ): Promise<ConsumptionAnalysis> {
    const analysis = this.calculateConsumptionAnalysis(actualConsumptions);

    // Ajustar stock con consumo real
    await this.adjustStockLevels(actualConsumptions);

    // Calcular costos reales
    const costs = await this.calculateRealCosts(actualConsumptions);

    return {
      totalCost: costs.total,
      efficiency: analysis.efficiency,
      wastage: analysis.wastage,
      profitMargin: costs.profitMargin,
    };
  }

  private calculateConsumptionAnalysis(consumptions: ActualConsumption[]): AnalysisResult {
    const totalPlanned = consumptions.reduce((sum, c) => sum + c.plannedQuantity, 0);
    const totalActual = consumptions.reduce((sum, c) => sum + c.actualQuantity, 0);

    return {
      efficiency: (totalPlanned / totalActual) * 100,
      wastage: totalActual - totalPlanned,
      variance: ((totalActual - totalPlanned) / totalPlanned) * 100,
    };
  }
}
```

## 🔍 Product Matcher Service

### Propósito

Hace matching fuzzy entre productos generados por AI y productos del inventario local.

```typescript
interface MatchResult {
  inventoryProduct: Product;
  confidence: number; // 0-100
  matchType: MatchType;
  alternativeMatches?: Product[];
}

enum MatchType {
  EXACT = 'exact', // 100% match
  BRAND_LINE_TYPE = 'brand_line_type', // 90-99%
  FUZZY_NAME = 'fuzzy_name', // 70-89%
  CATEGORY_ONLY = 'category_only', // 50-69%
  NO_MATCH = 'no_match', // <50%
}
```

### Algoritmo de Matching

```typescript
class ProductMatcherService {
  async findBestMatch(aiProduct: AIProduct): Promise<MatchResult | null> {
    const candidates = await this.getCandidates(aiProduct.category);

    // 1. Exact match (brand + name + type)
    const exactMatch = this.findExactMatch(aiProduct, candidates);
    if (exactMatch) {
      return {
        inventoryProduct: exactMatch,
        confidence: 100,
        matchType: MatchType.EXACT,
      };
    }

    // 2. Brand + Line + Type match
    const brandMatch = this.findBrandMatch(aiProduct, candidates);
    if (brandMatch && brandMatch.confidence > 90) {
      return brandMatch;
    }

    // 3. Fuzzy name matching
    const fuzzyMatch = this.findFuzzyMatch(aiProduct, candidates);
    if (fuzzyMatch && fuzzyMatch.confidence > 70) {
      return fuzzyMatch;
    }

    // 4. Category-only match
    const categoryMatch = this.findCategoryMatch(aiProduct, candidates);
    if (categoryMatch && categoryMatch.confidence > 50) {
      return categoryMatch;
    }

    return null;
  }

  private findFuzzyMatch(aiProduct: AIProduct, candidates: Product[]): MatchResult | null {
    const scores = candidates.map(candidate => ({
      product: candidate,
      score: this.calculateSimilarity(aiProduct.name, candidate.name),
    }));

    scores.sort((a, b) => b.score - a.score);

    const best = scores[0];
    if (best.score > 0.7) {
      return {
        inventoryProduct: best.product,
        confidence: Math.round(best.score * 100),
        matchType: MatchType.FUZZY_NAME,
        alternativeMatches: scores.slice(1, 4).map(s => s.product),
      };
    }

    return null;
  }

  private calculateSimilarity(str1: string, str2: string): number {
    // Implementación de Levenshtein distance + weighted factors
    const factors = {
      levenshtein: this.levenshteinDistance(str1, str2),
      commonWords: this.countCommonWords(str1, str2),
      brandWeight: this.brandSimilarity(str1, str2),
      numericalMatch: this.numericalSimilarity(str1, str2),
    };

    return this.calculateWeightedScore(factors);
  }
}
```

## 📝 Product Naming Service

### Propósito

Normaliza y estandariza nombres de productos para consistencia y mejor matching.

```typescript
interface NamingResult {
  standardName: string;
  brand: string;
  line: string;
  type: string;
  shade: string | null;
  confidence: number;
}
```

### Reglas de Normalización

```typescript
const namingRules = {
  // Estandarización de marcas
  brandNormalization: {
    wella: 'Wella',
    "l'oreal": "L'Oréal",
    loreal: "L'Oréal",
    schwarzkopf: 'Schwarzkopf',
  },

  // Estandarización de tipos
  typeNormalization: {
    tinte: 'color',
    oxidante: 'developer',
    'agua oxigenada': 'developer',
    decolorante: 'bleach',
    blanqueador: 'bleach',
  },

  // Patrones de tonos
  shadePatterns: [
    /(\d+)\/(\d+)/, // 7/1, 8/43
    /(\d+)\.(\d+)/, // 7.1, 8.43
    /(\d+)-(\d+)/, // 7-1, 8-43
    /(\d+)(\w+)/, // 7N, 8A
  ],
};

class ProductNamingService {
  standardizeName(rawName: string): NamingResult {
    const normalized = this.normalizeString(rawName);

    const brand = this.extractBrand(normalized);
    const line = this.extractLine(normalized, brand);
    const type = this.extractType(normalized);
    const shade = this.extractShade(normalized);

    const standardName = this.buildStandardName(brand, line, type, shade);

    return {
      standardName,
      brand,
      line,
      type,
      shade,
      confidence: this.calculateNamingConfidence(normalized, standardName),
    };
  }

  private normalizeString(name: string): string {
    return name
      .toLowerCase()
      .trim()
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s\/\.\-]/g, '');
  }

  private extractShade(name: string): string | null {
    for (const pattern of namingRules.shadePatterns) {
      const match = name.match(pattern);
      if (match) {
        return match[0]; // Return full shade match
      }
    }
    return null;
  }
}
```

## 🔧 Patrón de Implementación

### Idempotencia Obligatoria

```typescript
// ✅ CORRECTO: Operación idempotente
class IdempotentService {
  private operationCache = new Map<string, any>();

  async performOperation(key: string, data: any): Promise<any> {
    // Check cache first
    if (this.operationCache.has(key)) {
      return this.operationCache.get(key);
    }

    const result = await this.executeOperation(data);

    // Cache result
    this.operationCache.set(key, result);

    return result;
  }
}

// ❌ INCORRECTO: Operación que puede duplicar datos
class NonIdempotentService {
  async addToInventory(product: Product): Promise<void> {
    // Esto puede crear duplicados
    await inventory.add(product);
  }
}
```

### Error Handling Consistente

```typescript
abstract class BaseService {
  protected handleError(error: Error, operation: string): ServiceError {
    logger.error(`Service error in ${operation}`, { error, service: this.constructor.name });

    return {
      type: this.categorizeError(error),
      message: this.getUserFriendlyMessage(error),
      originalError: error,
    };
  }

  protected categorizeError(error: Error): ServiceErrorType {
    if (error.message.includes('network')) return ServiceErrorType.NETWORK;
    if (error.message.includes('validation')) return ServiceErrorType.VALIDATION;
    if (error.message.includes('permission')) return ServiceErrorType.PERMISSION;
    return ServiceErrorType.INTERNAL;
  }
}
```

## 🧪 Testing Patterns

### Service Testing

```typescript
describe('BrandConversionService', () => {
  let service: BrandConversionService;

  beforeEach(() => {
    service = new BrandConversionService();
  });

  test("should convert Wella to L'Oréal with high confidence", async () => {
    const formula = createMockFormula('Wella', 'Koleston', '7/1');

    const result = await service.convert(formula, "L'Oréal", 'Majirel');

    expect(result.confidence).toBeGreaterThan(80);
    expect(result.targetFormula.brand).toBe("L'Oréal");
    expect(result.adjustments.toneMapping).toBe('7/1 → 7.13');
  });

  test('should be idempotent', async () => {
    const formula = createMockFormula('Wella', 'Koleston', '7/1');

    const result1 = await service.convert(formula, "L'Oréal", 'Majirel');
    const result2 = await service.convert(formula, "L'Oréal", 'Majirel');

    expect(result1).toEqual(result2);
  });
});
```

## 📊 Performance Guidelines

### Caching Strategy

```typescript
class CachedService {
  private cache = new Map<string, { data: any; timestamp: number }>();
  private TTL = 24 * 60 * 60 * 1000; // 24 hours

  private getCached<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    if (Date.now() - cached.timestamp > this.TTL) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  private setCached(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }
}
```

## 🤖 Agentes Recomendados

### Para este módulo usar:

**ai-integration-specialist** - Optimización de AI en servicios

- Optimización de prompts en servicios AI
- Reducción de costos de API OpenAI
- Mejora de accuracy en resultados
- PROACTIVAMENTE usar cuando trabajar con features de IA

**colorimetry-expert** - Validación técnica de servicios

- Validar fórmulas generadas por servicios
- Revisar terminología profesional
- Verificar precisión técnica en conversiones
- PROACTIVAMENTE usar al revisar servicios químicos

**frontend-developer** - Implementación de servicios

- Integración de servicios con stores offline-first
- Patterns de error handling en servicios
- Optimización de llamadas a servicios
- PROACTIVAMENTE usar para nuevas integraciones

**test-runner** - Testing de servicios

- Unit tests para lógica de negocio
- Integration tests con APIs externas
- Testing de casos edge y errores
- PROACTIVAMENTE usar después de cambios en servicios

**debug-specialist** - Debugging de servicios complejos

- Root cause analysis en fallos de servicios
- Debugging de flujos de conversión complejos
- Análisis de performance en servicios pesados
- PROACTIVAMENTE usar para errores de servicios

### 💡 Ejemplos de Uso

```bash
# Optimizar servicio de conversión entre marcas
Task: Use colorimetry-expert to validate brand conversion accuracy in brandConversionService

# Mejorar performance de matching de productos
Task: Use ai-integration-specialist to optimize AI prompts in productMatcherService

# Debug fallo en consumo de inventario
Task: Use debug-specialist to investigate inconsistent behavior in inventoryConsumptionService

# Testear servicio de corrección de color
Task: Use test-runner to create comprehensive tests for colorCorrectionService
```

## 🔌 MCPs Disponibles

### Funciones MCP útiles:

**Para documentación de servicios:**

- `mcp__context7__resolve_library_id` - Buscar docs de librerías usadas
- `mcp__context7__get_library_docs` - Obtener documentación específica

**Para análisis de código:**

- `mcp__serena__get_symbols_overview` - Estructura de servicios
- `mcp__serena__find_symbol` - Localizar métodos específicos
- `mcp__serena__find_referencing_symbols` - Ver dónde se usan servicios
- `mcp__serena__search_for_pattern` - Buscar patterns en servicios

**Para diagnósticos:**

- `mcp__ide__getDiagnostics` - Errores TypeScript en servicios
- `mcp__supabase__get_logs` - Logs de servicios que usan Supabase

### 📝 Ejemplos MCP

```bash
# Analizar estructura de brandConversionService
mcp__serena__get_symbols_overview: "services/brandConversionService.ts"
mcp__serena__find_symbol: "convert"

# Encontrar todos los usos de productMatcherService
mcp__serena__find_referencing_symbols: "productMatcherService" in "services/productMatcherService.ts"

# Buscar todos los async functions en servicios
mcp__serena__search_for_pattern: "async.*function" in "services/"

# Obtener docs para optimización de fuzzy matching
mcp__context7__resolve_library_id: "fuse.js"
mcp__context7__get_library_docs: "/krisk/fuse"

# Verificar errores TypeScript
mcp__ide__getDiagnostics: "services/"
```

### 🔄 Combinaciones Recomendadas

**Optimización de IA:**

1. `ai-integration-specialist` + `mcp__context7__get_library_docs`
2. `colorimetry-expert` + `mcp__serena__find_symbol`

**Testing y Debugging:**

1. `test-runner` + `mcp__ide__getDiagnostics`
2. `debug-specialist` + `mcp__supabase__get_logs`

**Desarrollo de Nuevos Servicios:**

1. `frontend-developer` + `mcp__serena__search_for_pattern`
2. `colorimetry-expert` + `mcp__context7__resolve_library_id`

## 📊 Patterns de Servicios con Agentes

### 🧪 Chemical Validation Pattern

```typescript
// Usar colorimetry-expert para validar este pattern
const validateFormula = async (formula: ColorFormula): Promise<ValidationResult> => {
  // 1. Validación química básica
  const chemicalValidation = await ChemicalValidator.validate(formula);

  // 2. Validación por experto (usar colorimetry-expert aquí)
  const expertValidation = await ColorimetryExpert.validate(formula);

  return combineValidations(chemicalValidation, expertValidation);
};
```

### 🤖 AI Service Pattern

```typescript
// Usar ai-integration-specialist para optimizar
const aiServicePattern = async (input: AIInput): Promise<AIOutput> => {
  // 1. Preparar prompt optimizado (ai-integration-specialist)
  const optimizedPrompt = await PromptOptimizer.optimize(input);

  // 2. Llamada con fallback
  const result = await AIRouter.call(optimizedPrompt);

  // 3. Validación post-proceso (colorimetry-expert si aplica)
  return validateResult(result);
};
```

### 🔧 Service Testing Pattern

```typescript
// Usar test-runner para implementar
describe('BrandConversionService', () => {
  test('should convert with high confidence', async () => {
    // Test implementation using test-runner agent
  });

  test('should handle edge cases', async () => {
    // Edge case testing
  });
});
```

## 🔗 Archivos Relacionados

- `../stores/inventory-store.ts` - Estado de inventario
- `../data/conversionDatabase.ts` - Base de conversiones
- `../utils/professional-colorimetry.ts` - Cálculos químicos
- `../types/formulation.ts` - Tipos de fórmulas

---

**⚡ Recuerda:** Los servicios son la lógica de negocio crítica. Deben ser robustos, idempotentes y bien testeados. Usa `colorimetry-expert` para validación química y `ai-integration-specialist` para optimización de IA.
