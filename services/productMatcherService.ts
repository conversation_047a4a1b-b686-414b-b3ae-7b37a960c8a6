import { logger } from '@/utils/logger';

export interface ProductFingerprint {
  brand: string;
  productType: string;
  level: number | null;
  tone: string | null;
  volume: number | null;
}

interface BrandPattern {
  keywords: string[];
  officialName: string;
  dbName?: string; // Nombre exacto en la BD para matching preciso
}

interface ProductTypePattern {
  keywords: string[];
  type: string;
}

export class ProductMatcherService {
  // Patrones de marcas conocidas - Incluye nombres completos como en BD
  private static brandPatterns: BrandPattern[] = [
    {
      keywords: ['wella professionals', 'wella', 'wel', 'wellaton'],
      officialName: 'wella professionals',
      dbName: 'Wella Professionals',
    },
    {
      keywords: ["l'oréal professionnel", "l'oreal professionnel", 'loreal', "l'oreal", 'oreal'],
      officialName: 'loreal professionnel',
      dbName: "L'Oréal Professionnel",
    },
    {
      keywords: ['schwarzkopf professional', 'schwarzkopf', 'schwarz'],
      officialName: 'schwarzkopf professional',
      dbName: 'Schwarzkopf Professional',
    },
    {
      keywords: ['redken professional', 'redken'],
      officialName: 'redken',
      dbName: 'Redken Professional',
    },
    {
      keywords: ['matrix professional', 'matrix'],
      officialName: 'matrix',
      dbName: 'Matrix Professional',
    },
    { keywords: ['joico'], officialName: 'joico', dbName: 'Joico' },
    {
      keywords: ['goldwell', 'gold'],
      officialName: 'goldwell',
      dbName: 'Goldwell',
    },
    {
      keywords: ['alfaparf milano', 'alfaparf', 'alfa'],
      officialName: 'alfaparf',
      dbName: 'Alfaparf Milano',
    },
    { keywords: ['indola'], officialName: 'indola', dbName: 'Indola' },
    {
      keywords: ['revlon professional', 'revlon'],
      officialName: 'revlon',
      dbName: 'Revlon Professional',
    },
    {
      keywords: ['salerm cosmetics', 'salerm', 'saler', 'salem'],
      officialName: 'salerm cosmetics',
      dbName: 'Salerm Cosmetics',
    },
    { keywords: ['olaplex'], officialName: 'olaplex', dbName: 'Olaplex' },
    {
      keywords: ['genérico', 'generico', 'generic'],
      officialName: 'generico',
      dbName: 'Genérico',
    },
  ];

  // Patrones de tipos de producto
  private static productTypePatterns: ProductTypePattern[] = [
    {
      keywords: [
        'oxidante',
        'oxidant',
        'developer',
        'peróxido',
        'peroxide',
        'oxigenada',
        'activador',
        'oxydant',
        'welloxon',
      ],
      type: 'developer',
    },
    {
      keywords: [
        'tinte',
        'color',
        'coloración',
        'dye',
        'koleston',
        'majirel',
        'igora',
        'illumina',
        'inoa',
        'vison',
      ],
      type: 'color',
    },
    {
      keywords: [
        'decolorante',
        'bleach',
        'polvo',
        'lightener',
        'blanqueador',
        'blondor',
        'platinium',
      ],
      type: 'bleach',
    },
    {
      keywords: ['matizador', 'toner', 'tonalizador', 'silver', 'violet'],
      type: 'toner',
    },
    {
      keywords: ['tratamiento', 'treatment', 'mascarilla', 'mask', 'acondicionador', 'conditioner'],
      type: 'treatment',
    },
    { keywords: ['champú', 'shampoo', 'champu'], type: 'shampoo' },
    {
      keywords: ['plex', 'bond', 'protector', 'olaplex', 'wellaplex'],
      type: 'treatment',
    },
  ];

  /**
   * Genera una huella digital (fingerprint) de un producto
   */
  static generateFingerprint(productName: string): ProductFingerprint {
    let normalized = productName.toLowerCase().trim();

    // Limpiar formato incorrecto como "(Tinte)" al final antes de procesar
    normalized = normalized.replace(/\s*\([^)]+\)\s*$/g, '');

    return {
      brand: this.detectBrand(normalized),
      productType: this.detectType(normalized),
      level: this.extractLevel(normalized),
      tone: this.extractTone(normalized),
      volume: this.extractVolume(normalized),
    };
  }

  /**
   * Detecta la marca del producto
   */
  private static detectBrand(text: string): string {
    for (const pattern of this.brandPatterns) {
      if (pattern.keywords.some(keyword => text.includes(keyword))) {
        return pattern.officialName;
      }
    }
    return 'unknown';
  }

  /**
   * Detecta el tipo de producto
   */
  private static detectType(text: string): string {
    for (const pattern of this.productTypePatterns) {
      if (pattern.keywords.some(keyword => text.includes(keyword))) {
        return pattern.type;
      }
    }
    return 'other';
  }

  /**
   * Extrae el nivel/altura de tono (número antes del separador)
   */
  private static extractLevel(text: string): number | null {
    // Buscar patrones como 7/43, 7.43, 7-43, 7,43
    const levelMatch = text.match(/\b(\d{1,2})\s*[\/\-\.,]\s*\d{1,2}\b/);
    if (levelMatch) {
      return parseInt(levelMatch[1]);
    }

    // Buscar nivel solo (ej: "nivel 7")
    const singleLevelMatch = text.match(/\b(?:nivel|level|altura)\s*(\d{1,2})\b/);
    if (singleLevelMatch) {
      return parseInt(singleLevelMatch[1]);
    }

    return null;
  }

  /**
   * Normaliza un tono para comparación (convierte todos los separadores a punto)
   */
  static normalizeShade(shade: string | null): string | null {
    if (!shade) return null;
    // Convertir todos los separadores (coma, barra, guión) a punto
    return shade.replace(/[,\/\-]/g, '.');
  }

  /**
   * Extrae el tono/reflejo (números después del separador)
   */
  private static extractTone(text: string): string | null {
    // Buscar patrones como 7/43, 7.43, 7-43, 7,43
    const toneMatch = text.match(/\b\d{1,2}\s*[\/\-\.,]\s*(\d{1,2})\b/);
    if (toneMatch) {
      return toneMatch[1];
    }

    // Para productos con formato especial (ej: "7/43" o "9/60")
    const fullMatch = text.match(/\b(\d{1,2}[\/\-\.,]\d{1,2})\b/);
    if (fullMatch) {
      return fullMatch[1].replace(/[\/\-\.,]/, '');
    }

    // Si encontramos un nivel sin tono (ej: "Color 8" o "Tinte 8")
    // y es un producto de coloración, marcarlo como "NATURAL"
    const productType = this.detectType(text);
    if (productType === 'color') {
      const levelOnly = text.match(
        /\b(?:color|tinte|illumina|koleston|majirel|igora|vison)\s+(\d{1,2})\b/i
      );
      if (levelOnly) {
        return 'NATURAL'; // Indicador especial para tonos naturales sin reflejo
      }
    }

    return null;
  }

  /**
   * Extrae el volumen para oxidantes
   */
  private static extractVolume(text: string): number | null {
    // Buscar patrones como "20 vol", "20vol", "20 volúmenes", "6%"
    const volumeMatch = text.match(/\b(\d{1,2})\s*(?:vol(?:umen|úmenes)?|%)\b/);
    if (volumeMatch) {
      const value = parseInt(volumeMatch[1]);
      // Si es porcentaje, convertir a volúmenes
      if (text.includes('%')) {
        const volumeMap: { [key: number]: number } = {
          3: 10,
          6: 20,
          9: 30,
          12: 40,
        };
        return volumeMap[value] || value;
      }
      return value;
    }
    return null;
  }

  /**
   * Verifica si dos marcas son la misma (considerando variaciones)
   */
  static isSameBrand(fp1: ProductFingerprint, fp2: ProductFingerprint): boolean {
    if (fp1.brand === fp2.brand && fp1.brand !== 'unknown') {
      return true;
    }

    // Buscar si ambas marcas pertenecen al mismo patrón
    for (const pattern of this.brandPatterns) {
      const fp1Matches = pattern.keywords.some(k => fp1.brand?.includes(k));
      const fp2Matches = pattern.keywords.some(k => fp2.brand?.includes(k));
      if (fp1Matches && fp2Matches) {
        return true;
      }
    }

    return false;
  }

  /**
   * Verifica si dos productos de coloración coinciden exactamente
   */
  static isExactColorMatch(fp1: ProductFingerprint, fp2: ProductFingerprint): boolean {
    // Ambos deben ser productos de coloración
    if (fp1.productType !== 'color' || fp2.productType !== 'color') {
      return false;
    }

    // La marca debe coincidir (usando comparación flexible)
    if (!this.isSameBrand(fp1, fp2)) {
      return false;
    }

    // El nivel debe coincidir exactamente
    if (fp1.level === null || fp2.level === null || fp1.level !== fp2.level) {
      return false;
    }

    // El tono debe coincidir (normalizado)
    const tone1 = this.normalizeShade(fp1.tone);
    const tone2 = this.normalizeShade(fp2.tone);

    if (tone1 !== tone2) {
      return false;
    }

    return true;
  }

  /**
   * Compara dos fingerprints y devuelve un score de similitud (0-100)
   */
  static compareFingerprints(fp1: ProductFingerprint, fp2: ProductFingerprint): number {
    // Para productos de coloración, aplicar reglas estrictas con niveles
    if (fp1.productType === 'color' && fp2.productType === 'color') {
      // Nivel 1: Match exacto (100%)
      if (this.isExactColorMatch(fp1, fp2)) {
        return 100;
      }

      // Nivel 2: Marca + Nivel + Tono normalizado (90%)
      const tone1 = this.normalizeShade(fp1.tone);
      const tone2 = this.normalizeShade(fp2.tone);

      if (this.isSameBrand(fp1, fp2) && fp1.level === fp2.level && tone1 === tone2) {
        return 90;
      }

      // Nivel 3: Marca + Nivel (70%)
      if (this.isSameBrand(fp1, fp2) && fp1.level === fp2.level) {
        return 70;
      }

      // Nivel 4: Solo marca (40%)
      if (this.isSameBrand(fp1, fp2)) {
        return 40;
      }

      return 20; // Marcas diferentes
    }

    // Para otros productos, mantener lógica flexible
    let score = 0;

    // Marca (40 puntos) - muy importante
    if (this.isSameBrand(fp1, fp2)) {
      score += 40;
    }

    // Tipo de producto (20 puntos) - importante
    if (fp1.productType === fp2.productType) {
      score += 20;
    }

    // Para oxidantes: volumen es crítico
    if (fp1.productType === 'developer' && fp2.productType === 'developer') {
      if (fp1.volume !== null && fp2.volume !== null && fp1.volume === fp2.volume) {
        score += 40; // Muy importante para oxidantes
      }
    }

    return score;
  }

  /**
   * Normaliza un nombre de producto para comparación flexible
   */
  static normalizeForMatching(productName: string): string {
    let normalized = productName.toLowerCase().trim();

    // Limpiar formato incorrecto como "(Tinte)" al final
    normalized = normalized.replace(/\s*\([^)]+\)\s*$/g, '');

    return (
      normalized
        // Normalizar separadores de tono (mantener consistencia)
        .replace(/(\d+)\s*[,\/\-]\s*(\d+)/g, '$1.$2') // 7/43, 7-43, 7,43 → 7.43
        // Normalizar volúmenes
        .replace(/(\d+)\s*vol(?:umen|úmenes)?/gi, '$1vol')
        // Normalizar espacios
        .replace(/\s+/g, ' ')
        // Quitar caracteres especiales excepto puntos en números
        .replace(/[^\w\s.]/g, '')
    );
  }

  /**
   * Calcula similitud entre dos nombres de productos usando múltiples estrategias
   */
  static calculateSmartSimilarity(iaProduct: string, inventoryProduct: string): number {
    // 1. Comparación por fingerprint (más confiable)
    const fp1 = this.generateFingerprint(iaProduct);
    const fp2 = this.generateFingerprint(inventoryProduct);
    const fingerprintScore = this.compareFingerprints(fp1, fp2);

    // Para productos de coloración, SOLO usar fingerprint score
    if (fp1.productType === 'color' && fp2.productType === 'color') {
      logger.info('ProductMatcher comparison (COLOR PRODUCT)', 'ProductMatcherService', {
        iaProduct,
        inventoryProduct,
        fingerprint1: fp1,
        fingerprint2: fp2,
        fingerprintScore,
        isExactMatch: this.isExactColorMatch(fp1, fp2),
        finalScore: fingerprintScore,
      });

      return fingerprintScore; // Para tintes, no usar otros métodos
    }

    // 2. Para otros productos, usar comparación por normalización como fallback
    const normalized1 = this.normalizeForMatching(iaProduct);
    const normalized2 = this.normalizeForMatching(inventoryProduct);
    let stringScore = 0;

    if (normalized1 === normalized2) {
      stringScore = 100;
    } else if (normalized1.includes(normalized2) || normalized2.includes(normalized1)) {
      stringScore = 70;
    }

    // 3. Para productos no-coloración, usar el mejor score
    const finalScore = Math.max(fingerprintScore, stringScore);

    logger.info('ProductMatcher comparison', 'ProductMatcherService', {
      iaProduct,
      inventoryProduct,
      fingerprint1: fp1,
      fingerprint2: fp2,
      fingerprintScore,
      stringScore,
      finalScore,
    });

    return finalScore;
  }

  /**
   * Busca equivalencias conocidas entre diferentes nomenclaturas
   */
  static areProductsEquivalent(name1: string, name2: string): boolean {
    const equivalences = [
      // Oxidantes
      [
        'oxidante',
        'developer',
        'oxydant',
        'oxidant',
        'peróxido',
        'peroxide',
        'oxigenada',
        'welloxon',
      ],
      // Decolorantes
      ['decolorante', 'bleach', 'polvo decolorante', 'lightener', 'blanqueador', 'blondor'],
      // Protectores
      ['olaplex', 'wellaplex', 'fibreplex', 'smartbond', 'bond'],
      // Volúmenes
      ['10 vol', '3%', '10v', '10 volúmenes'],
      ['20 vol', '6%', '20v', '20 volúmenes'],
      ['30 vol', '9%', '30v', '30 volúmenes'],
      ['40 vol', '12%', '40v', '40 volúmenes'],
    ];

    const lower1 = name1.toLowerCase();
    const lower2 = name2.toLowerCase();

    for (const group of equivalences) {
      const has1 = group.some(term => lower1.includes(term));
      const has2 = group.some(term => lower2.includes(term));
      if (has1 && has2) {
        return true;
      }
    }

    return false;
  }
}
