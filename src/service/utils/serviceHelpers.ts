import { DesiredPhoto } from '@/types/desired-photo';
import { DesiredColorAnalysisResult, DesiredCaptureStep } from '@/types/desired-analysis';
import { PhotoAngle } from '@/types/photo-capture';
import { HairZone } from '@/types/hair-diagnosis';
import { MaintenanceLevel, BudgetLevel } from '@/types/lifestyle-preferences';

// Helper function to safely get angle for desired capture step
export const getDesiredCaptureAngle = (step: DesiredCaptureStep): PhotoAngle => {
  const angleMap: Record<DesiredCaptureStep, PhotoAngle> = {
    [DesiredCaptureStep.OVERALL]: PhotoAngle.FRONT,
    [DesiredCaptureStep.ROOTS_DETAIL]: PhotoAngle.CROWN,
    [DesiredCaptureStep.HIGHLIGHTS]: PhotoAngle.LEFT_SIDE,
    [DesiredCaptureStep.ENDS_DETAIL]: PhotoAngle.BACK,
  };

  const angle = angleMap[step];

  if (!angle) {
    // Invalid capture step, using default
    return PhotoAngle.FRONT;
  }

  return angle;
};

// Aggregate multiple desired photo analyses into a single result
export const aggregateDesiredPhotoAnalyses = (
  analyses: any[],
  photos: DesiredPhoto[]
): DesiredColorAnalysisResult => {
  if (analyses.length === 0) {
    throw new Error('No valid analyses to aggregate');
  }

  // Find the most common values across all analyses
  const levels = analyses
    .map(a => parseInt(a.general?.overallLevel?.split('/')[0] || '7'))
    .filter(l => !isNaN(l));
  const avgLevel = Math.round(levels.reduce((sum, l) => sum + l, 0) / levels.length);

  const tones = analyses.map(a => a.general?.overallTone).filter(Boolean);
  const mostCommonTone = tones.length > 0 ? tones[0] : 'Rubio';

  const techniques = analyses.map(a => a.general?.technique).filter(Boolean);
  const mostCommonTechnique = techniques.length > 0 ? techniques[0] : 'Balayage';

  // Determine direction based on analysis
  const directions = analyses.map(a => a.advanced?.direction).filter(Boolean);
  const direction = directions.length > 0 ? directions[0] : 'neutral';

  // Check if any analysis has zone data
  const analysesWithZones = analyses.filter(a => a.zoneAnalysis);
  const hasZoneData = analysesWithZones.length > 0;

  return {
    general: {
      overallLevel: avgLevel.toString(),
      overallTone: mostCommonTone,
      technique: mostCommonTechnique.toLowerCase().replace(/\s+/g, '_'),
      customTechnique: '',
    },
    zones: hasZoneData
      ? {
          // Usar datos reales de zonas cuando están disponibles
          [HairZone.ROOTS]: {
            zone: HairZone.ROOTS,
            desiredLevel:
              analysesWithZones[0].zoneAnalysis.roots?.level || Math.floor(avgLevel - 0.5),
            desiredTone: analysesWithZones[0].zoneAnalysis.roots?.tone || mostCommonTone,
            desiredReflect:
              analysesWithZones[0].zoneAnalysis.roots?.reflect ||
              (direction === 'cooler' ? 'Ceniza' : 'Natural'),
            coverage: 100,
          },
          [HairZone.MIDS]: {
            zone: HairZone.MIDS,
            desiredLevel: analysesWithZones[0].zoneAnalysis.mids?.level || Math.round(avgLevel),
            desiredTone: analysesWithZones[0].zoneAnalysis.mids?.tone || mostCommonTone,
            desiredReflect:
              analysesWithZones[0].zoneAnalysis.mids?.reflect ||
              (direction === 'cooler' ? 'Beige' : 'Dorado'),
            coverage: 90,
          },
          [HairZone.ENDS]: {
            zone: HairZone.ENDS,
            desiredLevel:
              analysesWithZones[0].zoneAnalysis.ends?.level || Math.ceil(avgLevel + 0.5),
            desiredTone: analysesWithZones[0].zoneAnalysis.ends?.tone || mostCommonTone,
            desiredReflect:
              analysesWithZones[0].zoneAnalysis.ends?.reflect ||
              (direction === 'cooler' ? 'Platino' : 'Miel'),
            coverage: 100,
          },
        }
      : {
          // Fallback cuando no hay datos de zonas
          [HairZone.ROOTS]: {
            zone: HairZone.ROOTS,
            desiredLevel: Math.floor(avgLevel - 0.5),
            desiredTone: mostCommonTone,
            desiredReflect: direction === 'cooler' ? 'Ceniza' : 'Natural',
            coverage: 100,
          },
          [HairZone.MIDS]: {
            zone: HairZone.MIDS,
            desiredLevel: Math.round(avgLevel),
            desiredTone: mostCommonTone,
            desiredReflect: direction === 'cooler' ? 'Beige' : 'Dorado',
            coverage: 90,
          },
          [HairZone.ENDS]: {
            zone: HairZone.ENDS,
            desiredLevel: Math.ceil(avgLevel + 0.5),
            desiredTone: mostCommonTone,
            desiredReflect: direction === 'cooler' ? 'Platino' : 'Miel',
            coverage: 100,
          },
        },
    advanced: {
      contrast: 'medium',
      direction: direction as any,
      graysCoverage: 100,
      finalTexture: 'glossy',
      specialNotes: `Análisis basado en ${photos.length} foto${photos.length > 1 ? 's' : ''} de referencia`,
    },
    lifestyle: {
      maintenanceLevel: MaintenanceLevel.MEDIUM,
      avoidTones: [],
      budgetLevel: BudgetLevel.STANDARD,
    },
    confidence: Math.min(95, 70 + analyses.length * 5), // Higher confidence with more photos
    isFromAI: true,
  };
};

// Format time duration for display
export const formatDuration = (minutes: number): string => {
  if (minutes < 60) {
    return `${minutes} min`;
  }

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  if (remainingMinutes === 0) {
    return `${hours}h`;
  }

  return `${hours}h ${remainingMinutes}min`;
};

// Calculate processing time based on level difference and technique
export const calculateProcessingTime = (
  currentLevel: number,
  targetLevel: number,
  technique: string,
  hairCondition: 'good' | 'damaged' | 'severely_damaged' = 'good'
): number => {
  const levelDifference = Math.abs(targetLevel - currentLevel);
  let baseTime = 35; // Base processing time in minutes

  // Adjust for level difference
  if (levelDifference > 2) {
    baseTime += (levelDifference - 2) * 10;
  }

  // Adjust for technique
  const techniqueMultipliers: Record<string, number> = {
    full_color: 1.0,
    highlights: 1.2,
    balayage: 1.3,
    ombre: 1.4,
    color_correction: 1.5,
    bleach_and_tone: 1.6,
  };

  const multiplier = techniqueMultipliers[technique] || 1.0;
  baseTime *= multiplier;

  // Adjust for hair condition
  const conditionMultipliers = {
    good: 1.0,
    damaged: 0.9, // Reduce time for damaged hair
    severely_damaged: 0.8,
  };

  baseTime *= conditionMultipliers[hairCondition];

  return Math.round(baseTime);
};

// Generate color transition description
export const generateColorTransitionDescription = (
  currentLevel: number,
  currentTone: string,
  targetLevel: number,
  targetTone: string
): string => {
  const levelChange = targetLevel - currentLevel;

  let description = `Transformación de ${currentTone} nivel ${currentLevel} a ${targetTone} nivel ${targetLevel}`;

  if (levelChange > 0) {
    description += ` (aclarado de ${levelChange} nivel${levelChange > 1 ? 'es' : ''})`;
  } else if (levelChange < 0) {
    description += ` (oscurecimiento de ${Math.abs(levelChange)} nivel${Math.abs(levelChange) > 1 ? 'es' : ''})`;
  } else {
    description += ` (cambio de tono sin modificar nivel)`;
  }

  return description;
};

// Calculate estimated cost range
export const calculateEstimatedCostRange = (
  levelDifference: number,
  technique: string,
  hairLength: 'short' | 'medium' | 'long' = 'medium'
): { min: number; max: number; currency: string } => {
  // Base costs in EUR (can be configured per salon)
  const baseCosts = {
    short: { min: 60, max: 120 },
    medium: { min: 80, max: 160 },
    long: { min: 100, max: 200 },
  };

  let { min, max } = baseCosts[hairLength];

  // Adjust for level difference
  const levelMultiplier = 1 + levelDifference * 0.2;
  min *= levelMultiplier;
  max *= levelMultiplier;

  // Adjust for technique complexity
  const techniqueMultipliers: Record<string, number> = {
    full_color: 1.0,
    highlights: 1.3,
    balayage: 1.4,
    ombre: 1.2,
    color_correction: 1.8,
    bleach_and_tone: 1.5,
  };

  const multiplier = techniqueMultipliers[technique] || 1.0;
  min *= multiplier;
  max *= multiplier;

  return {
    min: Math.round(min),
    max: Math.round(max),
    currency: 'EUR',
  };
};

// Generate maintenance recommendations
export const generateMaintenanceRecommendations = (
  technique: string,
  targetLevel: number,
  hairCondition: string
): string[] => {
  const recommendations: string[] = [];

  // Base recommendations
  recommendations.push('Usar champú sin sulfatos para prolongar el color');
  recommendations.push('Aplicar mascarilla nutritiva semanalmente');

  // Technique-specific recommendations
  if (technique.includes('bleach') || targetLevel > 8) {
    recommendations.push('Usar protector térmico antes del peinado');
    recommendations.push('Evitar herramientas de calor excesivo');
  }

  if (technique === 'balayage' || technique === 'highlights') {
    recommendations.push('Retoque de raíces cada 8-12 semanas');
    recommendations.push('Tóner de mantenimiento cada 6-8 semanas');
  }

  if (technique === 'full_color') {
    recommendations.push('Retoque completo cada 6-8 semanas');
  }

  // Condition-specific recommendations
  if (hairCondition === 'damaged' || hairCondition === 'severely_damaged') {
    recommendations.push('Tratamiento reconstructor mensual');
    recommendations.push('Aceite capilar en medios y puntas');
  }

  return recommendations;
};

// Validate color compatibility
export const validateColorCompatibility = (
  currentTone: string,
  targetTone: string,
  previousChemicalProcesses: string[]
): { compatible: boolean; warnings: string[]; recommendations: string[] } => {
  const warnings: string[] = [];
  const recommendations: string[] = [];
  const compatible = true;

  // Check for conflicting tones
  const conflictingCombinations = [
    {
      current: 'rojizo',
      target: 'ceniza',
      warning: 'Los tonos rojizos pueden interferir con los cenizas',
    },
    {
      current: 'dorado',
      target: 'platino',
      warning: 'Puede requerir neutralización previa',
    },
    {
      current: 'cobrizo',
      target: 'beige',
      warning: 'Los cobrizos son difíciles de neutralizar',
    },
  ];

  conflictingCombinations.forEach(combo => {
    if (
      currentTone.toLowerCase().includes(combo.current) &&
      targetTone.toLowerCase().includes(combo.target)
    ) {
      warnings.push(combo.warning);
      recommendations.push('Considerar pre-pigmentación o neutralización');
    }
  });

  // Check previous chemical processes
  if (
    previousChemicalProcesses.includes('alisado') ||
    previousChemicalProcesses.includes('permanente')
  ) {
    warnings.push('Cabello con tratamiento químico previo');
    recommendations.push('Realizar prueba de mecha');
    recommendations.push('Reducir tiempo de procesamiento');
  }

  if (previousChemicalProcesses.includes('decoloración')) {
    recommendations.push('Evaluar porosidad antes del color');
    recommendations.push('Usar productos de baja alcalinidad');
  }

  return { compatible, warnings, recommendations };
};
