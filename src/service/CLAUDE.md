# CLAUDE.md - Service Flow Components

## 🎯 Propósito

Flujo principal de servicio de coloración capilar: **Diagnosis → Desired Color → Formulation → Completion**. Cada paso tiene validaciones específicas y persistencia automática para recuperación de borradores.

## 🔄 Flujo de Servicio Principal

### Arquitectura de Pasos

```
┌─ Diagnosis Step ────┐    ┌─ Desired Color ──┐    ┌─ Formulation ───┐    ┌─ Completion ────┐
│ • Client selection  │    │ • Target analysis│    │ • AI generation │    │ • Final review  │
│ • Hair photo        │───▶│ • Color matching │───▶│ • Product match │───▶│ • Consumption   │
│ • Visual analysis   │    │ • Viability      │    │ • Instructions  │    │ • Signature     │
│ • Current state     │    │ • Technique      │    │ • Validation    │    │ • Completion    │
└─────────────────────┘    └──────────────────┘    └─────────────────┘    └─────────────────┘
```

### Estado Global del Servicio

```typescript
interface ServiceDraft {
  id: string;
  clientId: string | null;
  currentStep: 'diagnosis' | 'desired' | 'formulation' | 'completion';

  // Diagnosis step data
  photos: PhotoCapture[];
  hairAnalysis: HairDiagnosis;
  visualAnalysis: VisualAnalysis;

  // Desired color step data
  desiredColor: DesiredColorAnalysis;
  viabilityAnalysis: ViabilityAnalysis;
  selectedTechnique: string;

  // Formulation step data
  formula: ColorFormula | null;
  mappedProducts: ProductMapping[];
  instructions: FormulationInstructions;

  // Completion step data
  satisfaction: number;
  clientSignature: string | null;
  notes: string;

  // Metadata
  createdAt: string;
  lastUpdated: string;
  isCompleted: boolean;
}
```

## 📁 Estructura de Componentes

### /components/

- `DiagnosisStep.tsx` - Captura y análisis inicial
- `DesiredColorStep.tsx` - Definición del objetivo
- `FormulationStep.tsx` - Generación y validación de fórmula
- `CompletionStep.tsx` - Finalización y firma
- `ServiceHeader.tsx` - Header con progreso
- `ServiceBreadcrumbs.tsx` - Navegación entre pasos
- `StepIndicator.tsx` - Indicador visual de progreso
- `AutoSaveIndicator.tsx` - Estado de guardado automático

### /hooks/

- `useServiceFlow.ts` - Lógica de navegación entre pasos
- `useServicePersistence.ts` - Auto-save y recuperación
- `useFormulation.ts` - Gestión de fórmulas
- `usePhotoAnalysis.ts` - Procesamiento de imágenes

### /utils/

- `serviceHelpers.ts` - Funciones auxiliares
- `serviceValidations.ts` - Validaciones por paso

## 🔍 Diagnosis Step (DiagnosisStep.tsx)

### Responsabilidades

1. **Selección de cliente obligatoria**
2. **Captura fotográfica guiada**
3. **Análisis visual con AI**
4. **Diagnóstico del estado actual**

### Validaciones Obligatorias

```typescript
const validateDiagnosisStep = (data: DiagnosisData): ValidationResult => {
  const errors: string[] = [];

  // Cliente obligatorio
  if (!data.clientId) {
    errors.push('Cliente es obligatorio para continuar');
  }

  // Al menos una foto
  if (!data.photos || data.photos.length === 0) {
    errors.push('Se requiere al menos una foto del cabello');
  }

  // Validar calidad de fotos
  for (const photo of data.photos) {
    if (photo.fileSize > 5 * 1024 * 1024) {
      // 5MB max
      errors.push(`Foto ${photo.id} excede el tamaño máximo (5MB)`);
    }
  }

  // Análisis visual completado
  if (!data.hairAnalysis || !data.hairAnalysis.baseColor) {
    errors.push('Análisis visual incompleto');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};
```

### Captura Fotográfica

```typescript
// Guía fotográfica obligatoria
const PHOTO_REQUIREMENTS = {
  minPhotos: 1,
  maxPhotos: 5,
  maxFileSize: 5 * 1024 * 1024, // 5MB
  requiredAngles: ['front', 'back', 'sides'],
  lighting: 'natural_daylight',
  resolution: { min: 1080, recommended: 1920 },
};

// Validación automática
const validatePhotoQuality = (photo: File): PhotoValidation => {
  return {
    lighting: checkLighting(photo),
    focus: checkFocus(photo),
    angle: detectAngle(photo),
    hairVisibility: checkHairVisibility(photo),
  };
};
```

## 🎨 Desired Color Step (DesiredColorStep.tsx)

### Responsabilidades

1. **Análisis del color objetivo**
2. **Evaluación de viabilidad**
3. **Selección de técnica**
4. **Consideraciones de alergias**

### Análisis de Viabilidad

```typescript
interface ViabilityAnalysis {
  isViable: boolean;
  confidence: number; // 0-100
  requiredSessions: number;
  estimatedTime: number; // minutes
  difficulty: 'easy' | 'medium' | 'hard' | 'expert';
  warnings: string[];
  recommendations: string[];
  alternativeOptions?: ColorOption[];
}

// Factores de viabilidad
const analyzeViability = (current: HairState, desired: DesiredColor): ViabilityAnalysis => {
  const factors = {
    colorDistance: calculateColorDistance(current.baseColor, desired.targetColor),
    hairCondition: current.condition, // 'excellent' | 'good' | 'fair' | 'poor'
    previousProcessing: current.lastProcessed,
    naturalLevel: current.naturalLevel,
    porosity: current.porosity,
  };

  return calculateViability(factors);
};
```

### Consideraciones de Seguridad

```typescript
// Verificación de alergias OBLIGATORIA
const checkAllergies = (clientId: string, plannedProducts: Product[]): SafetyCheck => {
  const client = clientStore.getClient(clientId);
  const knownAllergies = client?.allergies || [];

  const risks = plannedProducts.filter(product =>
    product.ingredients.some(ingredient => knownAllergies.includes(ingredient))
  );

  return {
    hasConcerns: risks.length > 0,
    riskProducts: risks,
    severity: calculateSeverity(risks),
    recommendation: risks.length > 0 ? 'patch_test_required' : 'proceed',
  };
};
```

## ⚗️ Formulation Step (FormulationStep.tsx)

### Responsabilidades

1. **Generación de fórmula con AI**
2. **Mapeo con inventario local**
3. **Validación química**
4. **Instrucciones detalladas**

### Generación de Fórmula

```typescript
const generateFormula = async (
  diagnosis: HairDiagnosis,
  desired: DesiredColorAnalysis
): Promise<FormulaGeneration> => {
  // 1. Llamada al Edge Function principal
  const aiResult = await supabase.functions.invoke('salonier-assistant', {
    body: {
      type: 'formula_generation',
      currentState: diagnosis,
      targetColor: desired,
      availableProducts: getAvailableProducts(),
      salonPreferences: getSalonConfig(),
    },
  });

  // 2. Validación química obligatoria
  const validation = await ChemicalValidator.validate(aiResult.data.formula);
  if (!validation.isValid) {
    throw new Error(`Chemical validation failed: ${validation.errors.join(', ')}`);
  }

  // 3. Mapeo con inventario
  const mappedProducts = await mapProductsToInventory(aiResult.data.products);

  return {
    formula: aiResult.data.formula,
    products: mappedProducts,
    instructions: aiResult.data.instructions,
    confidence: aiResult.data.confidence,
    warnings: validation.warnings,
  };
};
```

### Mapeo de Productos

```typescript
// Mapeo automático AI → Inventario
const mapProductsToInventory = async (aiProducts: AIProduct[]): Promise<ProductMapping[]> => {
  const mappings: ProductMapping[] = [];

  for (const aiProduct of aiProducts) {
    // Buscar match exacto
    let inventoryProduct = findExactMatch(aiProduct);

    // Buscar match por similitud
    if (!inventoryProduct) {
      inventoryProduct = findSimilarProduct(aiProduct);
    }

    // Si no hay match, requerir mapping manual
    if (!inventoryProduct) {
      const manualMapping = await showMappingModal(aiProduct);
      inventoryProduct = manualMapping;
    }

    mappings.push({
      aiProduct,
      inventoryProduct,
      confidence: calculateMappingConfidence(aiProduct, inventoryProduct),
    });
  }

  return mappings;
};
```

## ✅ Completion Step (CompletionStep.tsx)

### Responsabilidades

1. **Revisión final del servicio**
2. **Registro de consumo de productos**
3. **Firma digital del cliente**
4. **Puntuación de satisfacción**

### Registro de Consumo

```typescript
const recordProductConsumption = async (
  serviceId: string,
  formula: ColorFormula,
  clientName: string
): Promise<void> => {
  const consumptions = formula.products.map(product => ({
    productId: product.id,
    quantity: calculateActualUsage(product.quantity, formula.totalVolume),
  }));

  // Validar stock disponible
  for (const consumption of consumptions) {
    if (!validateStock(consumption.productId, consumption.quantity)) {
      throw new Error(`Insufficient stock for ${consumption.productId}`);
    }
  }

  // Registrar consumo
  await inventoryStore.consumeProducts(consumptions, serviceId, clientName);

  // Generar alertas de stock bajo si es necesario
  checkAndCreateLowStockAlerts(consumptions);
};
```

### Firma Digital

```typescript
// Componente de firma obligatorio
<SignatureCanvas
  onSignatureCapture={(signature) => {
    setClientSignature(signature);
    setIsSignatureValid(true);
  }}
  required={true}
  clearButton={true}
  guidelines="Por favor firme para confirmar su satisfacción con el servicio"
/>

// Validación de firma
const validateSignature = (signature: string): boolean => {
  return signature && signature.length > 100; // SVG path mínimo
};
```

## 🔄 Navegación y Persistencia

### Hook useServiceFlow

```typescript
const useServiceFlow = () => {
  const { currentStep, canAdvance, canGoBack, goToNextStep, goToPreviousStep } =
    useServiceDraftStore();

  const validateCurrentStep = (): boolean => {
    switch (currentStep) {
      case 'diagnosis':
        return validateDiagnosisStep(diagnosisData).isValid;
      case 'desired':
        return validateDesiredStep(desiredData).isValid;
      case 'formulation':
        return validateFormulationStep(formulationData).isValid;
      case 'completion':
        return validateCompletionStep(completionData).isValid;
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (validateCurrentStep()) {
      goToNextStep();
    } else {
      showValidationErrors();
    }
  };

  return { currentStep, canAdvance, handleNext, goToPreviousStep };
};
```

### Auto-Save

```typescript
// Guardado automático cada 30 segundos
useEffect(() => {
  const interval = setInterval(() => {
    if (hasUnsavedChanges) {
      saveServiceDraft();
    }
  }, 30000);

  return () => clearInterval(interval);
}, [hasUnsavedChanges]);

// Guardado en cambios críticos
const saveOnCriticalChange = (data: any) => {
  useServiceDraftStore.getState().updateDraft(data);
  debouncedSave(); // Debounce para evitar saves excesivos
};
```

## 🚨 Validaciones de Seguridad

### Verificación Pre-Servicio

```typescript
const performSafetyChecks = async (serviceData: ServiceDraft): Promise<SafetyCheckResult> => {
  const checks: SafetyCheck[] = [];

  // 1. Verificar alergias conocidas
  checks.push(await checkClientAllergies(serviceData.clientId, serviceData.formula));

  // 2. Validar compatibilidad química
  checks.push(await validateChemicalCompatibility(serviceData.formula));

  // 3. Verificar estado del cabello
  checks.push(await assessHairCondition(serviceData.hairAnalysis));

  // 4. Evaluar tiempo desde último proceso
  checks.push(await checkLastProcessing(serviceData.clientId));

  const criticalIssues = checks.filter(check => check.severity === 'critical');

  return {
    passed: criticalIssues.length === 0,
    issues: checks.filter(check => !check.passed),
    recommendations: generateSafetyRecommendations(checks),
  };
};
```

## 📊 Métricas y KPIs

### Performance Targets

- **Tiempo por paso:** <2 minutos promedio
- **Tasa de completión:** >90%
- **Errores de validación:** <5%
- **Auto-save success:** 99.9%

### Tracking de Progreso

```typescript
// Analytics de flujo
const trackStepCompletion = (step: string, timeSpent: number) => {
  analytics.track('service_step_completed', {
    step,
    timeSpent,
    userId: currentUser.id,
    salonId: currentSalon.id,
  });
};

const trackServiceAbandonment = (step: string, reason?: string) => {
  analytics.track('service_abandoned', {
    lastStep: step,
    reason,
    sessionDuration: getSessionDuration(),
  });
};
```

## 🤖 Agentes Recomendados

### Para este módulo usar:

**ux-researcher** - Análisis de flujos de usuario

- Identificar puntos de fricción en el flujo de servicio
- Analizar abandono en pasos específicos
- Optimización de experiencia móvil
- PROACTIVAMENTE usar para nuevas features y problemas de usabilidad

**frontend-developer** - Implementación de flujo completo

- Lógica compleja de navegación entre pasos
- Validaciones en tiempo real y offline-first
- Optimización de re-renders en steps
- PROACTIVAMENTE usar para nuevas features y refactoring

**ai-integration-specialist** - Optimización de AI en diagnosis

- Optimizar prompts de análisis capilar
- Reducir latencia en generación de fórmulas
- Mejora de accuracy en resultados
- PROACTIVAMENTE usar cuando trabajar con diagnosis AI

**colorimetry-expert** - Validación técnica de flujo

- Validar fórmulas generadas en FormulationStep
- Revisar terminología profesional en pasos
- Verificar precisión en análisis visual
- PROACTIVAMENTE usar al revisar flujos químicos

**whimsy-injector** - Micro-interacciones en flujo

- Animaciones suaves entre pasos
- Feedback haptic en validaciones exitosas
- Transiciones delightful en progreso
- Usar cuando funcionalidad base esté completa

### 💡 Ejemplos de Uso

```bash
# Analizar abandono en DesiredColorStep
Task: Use ux-researcher to identify friction points in DesiredColorStep user flow

# Optimizar rendimiento de DiagnosisStep
Task: Use frontend-developer to optimize photo analysis performance in DiagnosisStep

# Mejorar accuracy de análisis AI
Task: Use ai-integration-specialist to optimize hair analysis prompts in DiagnosisStep

# Validar fórmulas en FormulationStep
Task: Use colorimetry-expert to review formula generation accuracy in FormulationStep
```

## 🔌 MCPs Disponibles

### Funciones MCP útiles:

**Para análisis de código:**

- `mcp__serena__get_symbols_overview` - Estructura de componentes de steps
- `mcp__serena__find_symbol` - Localizar step específico
- `mcp__serena__find_referencing_symbols` - Ver dependencias entre steps
- `mcp__serena__search_for_pattern` - Buscar patterns en validaciones

**Para navegación de código:**

- `mcp__serena__replace_symbol_body` - Actualizar lógica de steps
- `mcp__serena__insert_after_symbol` - Añadir nuevos steps

**Para diagnósticos:**

- `mcp__ide__getDiagnostics` - Errores TypeScript en steps
- `mcp__supabase__get_logs` - Logs de AI processing

**Para documentación:**

- `mcp__context7__resolve_library_id` - Docs de React Navigation
- `mcp__context7__get_library_docs` - Documentación de componentes

### 📝 Ejemplos MCP

```bash
# Analizar estructura de DiagnosisStep
mcp__serena__get_symbols_overview: "src/service/DiagnosisStep.tsx"
mcp__serena__find_symbol: "validateDiagnosisStep"

# Encontrar todos los usos de useServiceFlow
mcp__serena__find_referencing_symbols: "useServiceFlow" in "src/service/"

# Buscar validaciones en todos los steps
mcp__serena__search_for_pattern: "validate.*Step" in "src/service/"

# Obtener docs de React Navigation para flujo
mcp__context7__resolve_library_id: "react-navigation"
mcp__context7__get_library_docs: "/react-navigation/react-navigation"

# Verificar errores en steps
mcp__ide__getDiagnostics: "src/service/"
```

### 🔄 Combinaciones Recomendadas

**Análisis de UX:**

1. `ux-researcher` + `mcp__serena__search_for_pattern`
2. `whimsy-injector` + `mcp__context7__get_library_docs`

**Optimización de Performance:**

1. `frontend-developer` + `mcp__ide__getDiagnostics`
2. `ai-integration-specialist` + `mcp__supabase__get_logs`

**Validación Técnica:**

1. `colorimetry-expert` + `mcp__serena__find_symbol`
2. `debug-specialist` + `mcp__serena__find_referencing_symbols`

## 📊 Patterns de Steps con Agentes

### 🔍 Step Validation Pattern

```typescript
// Usar frontend-developer para implementar
const validateStep = async (stepData: StepData): Promise<ValidationResult> => {
  // 1. Validación básica de campos
  const basicValidation = validateRequiredFields(stepData);

  // 2. Validación de negocio (usar colorimetry-expert si aplica)
  const businessValidation = await validateBusinessRules(stepData);

  // 3. Validación de UX (usar ux-researcher para optimizar)
  const uxValidation = validateUserExperience(stepData);

  return combineValidations(basicValidation, businessValidation, uxValidation);
};
```

### 🎯 AI Integration Pattern

```typescript
// Usar ai-integration-specialist para optimizar
const processWithAI = async (inputData: AIInput): Promise<AIResult> => {
  // 1. Preparación optimizada (ai-integration-specialist)
  const optimizedInput = await prepareForAI(inputData);

  // 2. Llamada con fallback
  const result = await callAIService(optimizedInput);

  // 3. Validación post-proceso (colorimetry-expert)
  return await validateAIResult(result);
};
```

### 📱 Step Navigation Pattern

```typescript
// Usar ux-researcher para optimizar flujo
const navigateToNextStep = async (): Promise<void> => {
  // 1. Validar step actual (frontend-developer)
  const isValid = await validateCurrentStep();

  // 2. Auto-save state (offline-first)
  await saveStepData();

  // 3. Animación suave (whimsy-injector)
  await animateTransition();

  // 4. Navegar
  navigation.navigate(nextStep);
};
```

## 🔗 Archivos Relacionados

- `../../stores/service-draft-store.ts` - Estado del servicio
- `../../stores/client-store.ts` - Datos del cliente
- `../../stores/inventory-store.ts` - Inventario para mapeo
- `../../supabase/functions/salonier-assistant/` - AI processing
- `../../utils/viability-analyzer.ts` - Análisis de viabilidad
- `../../utils/chemical-validator.ts` - Validación química

---

**⚡ Recuerda:** El flujo de servicio es el core business del sistema. Cada paso debe ser robusto y a prueba de errores. Usa `ux-researcher` para optimizar experiencia y `ai-integration-specialist` para mejorar AI processing.
