/**
 * Comprehensive tests for useServiceFlow hook
 * Testing service flow navigation, validation, and state management
 */

import { renderHook, act } from '@testing-library/react-native';
import { Alert } from 'react-native';
import { router } from 'expo-router';
import { useServiceFlow } from '../useServiceFlow';
import { HairZone } from '@/types/hair-diagnosis';

// Mock dependencies
jest.mock('react-native', () => ({
  Alert: {
    alert: jest.fn(),
  },
}));

jest.mock('expo-router', () => ({
  router: {
    back: jest.fn(),
  },
}));

const mockAlert = Alert.alert as jest.MockedFunction<typeof Alert.alert>;
const mockRouterBack = router.back as jest.MockedFunction<typeof router.back>;

// Mock data
const mockClient = {
  id: 'client-123',
  name: '<PERSON>',
  email: '<EMAIL>',
};

const completeZoneColorAnalysis = {
  [HairZone.ROOTS]: {
    zone: HairZone.ROOTS,
    level: 4,
    tone: 'Castaño',
    reflect: 'Dorado',
    state: 'Natural',
  },
  [HairZone.MIDS]: {
    zone: HairZone.MIDS,
    level: 5,
    tone: 'Castaño',
    reflect: 'Dorado',
    state: 'Natural',
  },
  [HairZone.ENDS]: {
    zone: HairZone.ENDS,
    level: 6,
    tone: 'Castaño',
    reflect: 'Dorado',
    state: 'Natural',
  },
};

const completeZonePhysicalAnalysis = {
  [HairZone.ROOTS]: {
    zone: HairZone.ROOTS,
    porosity: 'Media',
    elasticity: 'Buena',
    resistance: 'Alta',
    damage: 'Bajo',
  },
  [HairZone.MIDS]: {
    zone: HairZone.MIDS,
    porosity: 'Media',
    elasticity: 'Buena',
    resistance: 'Media',
    damage: 'Bajo',
  },
  [HairZone.ENDS]: {
    zone: HairZone.ENDS,
    porosity: 'Alta',
    elasticity: 'Regular',
    resistance: 'Media',
    damage: 'Medio',
  },
};

const mockDesiredAnalysisResult = {
  general: {
    overallLevel: '8/1',
    technique: 'full_color',
  },
  confidence: 0.85,
};

describe('useServiceFlow', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Initial State', () => {
    it('should initialize with correct default values', () => {
      const { result } = renderHook(() => useServiceFlow());

      expect(result.current.currentStep).toBe(0);
      expect(result.current.serviceData.diagnosisMethod).toBe('ai');
      expect(result.current.serviceData.selectedBrand).toBe('Wella Professionals');
      expect(result.current.serviceData.selectedLine).toBe('Illumina Color');
      expect(result.current.serviceData.isFormulaFromAI).toBe(true);
      expect(result.current.serviceData.clientSatisfaction).toBe(5);
      expect(result.current.serviceData.monthlyGrowth).toBe(1.25);
    });

    it('should initialize STEPS correctly', () => {
      const { result } = renderHook(() => useServiceFlow());

      expect(result.current.STEPS).toHaveLength(4);
      expect(result.current.STEPS[0]).toEqual({ id: 'diagnosis', title: 'Diagnóstico Capilar' });
      expect(result.current.STEPS[1]).toEqual({ id: 'desired', title: 'Resultado Deseado' });
      expect(result.current.STEPS[2]).toEqual({ id: 'formulation', title: 'Formulación' });
      expect(result.current.STEPS[3]).toEqual({ id: 'result', title: 'Resultado Final' });
    });

    it('should initialize zone analysis with correct structure', () => {
      const { result } = renderHook(() => useServiceFlow());

      const zoneColorAnalysis = result.current.serviceData.zoneColorAnalysis;
      const zonePhysicalAnalysis = result.current.serviceData.zonePhysicalAnalysis;

      expect(Object.keys(zoneColorAnalysis)).toEqual(['ROOTS', 'MIDS', 'ENDS']);
      expect(Object.keys(zonePhysicalAnalysis)).toEqual(['ROOTS', 'MIDS', 'ENDS']);

      expect(zoneColorAnalysis[HairZone.ROOTS].zone).toBe(HairZone.ROOTS);
      expect(zonePhysicalAnalysis[HairZone.ROOTS].zone).toBe(HairZone.ROOTS);
    });
  });

  describe('updateServiceData', () => {
    it('should update service data correctly', () => {
      const { result } = renderHook(() => useServiceFlow());

      act(() => {
        result.current.updateServiceData({
          client: mockClient,
          clientId: 'client-123',
          hairThickness: 'Grueso',
        });
      });

      expect(result.current.serviceData.client).toEqual(mockClient);
      expect(result.current.serviceData.clientId).toBe('client-123');
      expect(result.current.serviceData.hairThickness).toBe('Grueso');
    });

    it('should merge updates without overwriting existing data', () => {
      const { result } = renderHook(() => useServiceFlow());

      act(() => {
        result.current.updateServiceData({
          hairThickness: 'Grueso',
          hairDensity: 'Alta',
        });
      });

      act(() => {
        result.current.updateServiceData({
          overallTone: 'Rubio',
        });
      });

      expect(result.current.serviceData.hairThickness).toBe('Grueso');
      expect(result.current.serviceData.hairDensity).toBe('Alta');
      expect(result.current.serviceData.overallTone).toBe('Rubio');
    });
  });

  describe('validateDiagnosis', () => {
    it('should return false for incomplete diagnosis', () => {
      const { result } = renderHook(() => useServiceFlow());

      expect(!!result.current.validateDiagnosis()).toBe(false);
    });

    it('should return false when general data is incomplete', () => {
      const { result } = renderHook(() => useServiceFlow());

      act(() => {
        result.current.updateServiceData({
          hairThickness: 'Medio',
          hairDensity: 'Alta',
          overallTone: 'Castaño',
          // Missing overallReflect
          zoneColorAnalysis: completeZoneColorAnalysis,
          zonePhysicalAnalysis: completeZonePhysicalAnalysis,
        });
      });

      expect(!!result.current.validateDiagnosis()).toBe(false);
    });

    it('should return false when zone color analysis is incomplete', () => {
      const { result } = renderHook(() => useServiceFlow());

      const incompleteZoneColorAnalysis = {
        ...completeZoneColorAnalysis,
        [HairZone.ROOTS]: {
          ...completeZoneColorAnalysis[HairZone.ROOTS],
          level: undefined, // Missing required field
        },
      };

      act(() => {
        result.current.updateServiceData({
          hairThickness: 'Medio',
          hairDensity: 'Alta',
          overallTone: 'Castaño',
          overallReflect: 'Dorado',
          zoneColorAnalysis: incompleteZoneColorAnalysis,
          zonePhysicalAnalysis: completeZonePhysicalAnalysis,
        });
      });

      expect(!!result.current.validateDiagnosis()).toBe(false);
    });

    it('should return false when zone physical analysis is incomplete', () => {
      const { result } = renderHook(() => useServiceFlow());

      const incompleteZonePhysicalAnalysis = {
        ...completeZonePhysicalAnalysis,
        [HairZone.ENDS]: {
          ...completeZonePhysicalAnalysis[HairZone.ENDS],
          porosity: undefined, // Missing required field
        },
      };

      act(() => {
        result.current.updateServiceData({
          hairThickness: 'Medio',
          hairDensity: 'Alta',
          overallTone: 'Castaño',
          overallReflect: 'Dorado',
          zoneColorAnalysis: completeZoneColorAnalysis,
          zonePhysicalAnalysis: incompleteZonePhysicalAnalysis,
        });
      });

      expect(!!result.current.validateDiagnosis()).toBe(false);
    });

    it('should return true when all diagnosis data is complete', () => {
      const { result } = renderHook(() => useServiceFlow());

      act(() => {
        result.current.updateServiceData({
          hairThickness: 'Medio',
          hairDensity: 'Alta',
          overallTone: 'Castaño',
          overallReflect: 'Dorado',
          zoneColorAnalysis: completeZoneColorAnalysis,
          zonePhysicalAnalysis: completeZonePhysicalAnalysis,
        });
      });

      expect(!!result.current.validateDiagnosis()).toBe(true);
    });
  });

  describe('Navigation', () => {
    it('should go to next step when validation passes', () => {
      const { result } = renderHook(() => useServiceFlow());

      // Complete diagnosis
      act(() => {
        result.current.updateServiceData({
          hairThickness: 'Medio',
          hairDensity: 'Alta',
          overallTone: 'Castaño',
          overallReflect: 'Dorado',
          zoneColorAnalysis: completeZoneColorAnalysis,
          zonePhysicalAnalysis: completeZonePhysicalAnalysis,
        });
      });

      act(() => {
        result.current.goToNextStep();
      });

      expect(result.current.currentStep).toBe(1);
    });

    it('should show alert when trying to advance with invalid diagnosis', () => {
      const { result } = renderHook(() => useServiceFlow());

      act(() => {
        result.current.goToNextStep();
      });

      expect(mockAlert).toHaveBeenCalledWith(
        'Diagnóstico incompleto',
        'Por favor completa todos los campos requeridos antes de continuar.',
        [{ text: 'OK' }]
      );

      expect(result.current.currentStep).toBe(0);
    });

    it('should not advance beyond last step', () => {
      const { result } = renderHook(() => useServiceFlow());

      // Navigate to last step
      act(() => {
        result.current.goToStep(3);
      });

      act(() => {
        result.current.goToNextStep();
      });

      expect(result.current.currentStep).toBe(3);
    });

    it('should go to previous step', () => {
      const { result } = renderHook(() => useServiceFlow());

      // Go to step 2 first
      act(() => {
        result.current.goToStep(2);
      });

      act(() => {
        result.current.goToPreviousStep();
      });

      expect(result.current.currentStep).toBe(1);
    });

    it('should call router.back() when on first step and going back', () => {
      const { result } = renderHook(() => useServiceFlow());

      act(() => {
        result.current.goToPreviousStep();
      });

      expect(mockRouterBack).toHaveBeenCalled();
      expect(result.current.currentStep).toBe(0);
    });

    it('should go to specific step when valid', () => {
      const { result } = renderHook(() => useServiceFlow());

      act(() => {
        result.current.goToStep(2);
      });

      expect(result.current.currentStep).toBe(2);
    });

    it('should not go to invalid step index', () => {
      const { result } = renderHook(() => useServiceFlow());

      act(() => {
        result.current.goToStep(-1);
      });
      expect(result.current.currentStep).toBe(0);

      act(() => {
        result.current.goToStep(10);
      });
      expect(result.current.currentStep).toBe(0);
    });
  });

  describe('canNavigateToStep', () => {
    it('should allow navigation to previous steps', () => {
      const { result } = renderHook(() => useServiceFlow());

      act(() => {
        result.current.goToStep(2);
      });

      expect(result.current.canNavigateToStep(0)).toBe(true);
      expect(!!result.current.canNavigateToStep(1)).toBe(true);
      expect(!!result.current.canNavigateToStep(2)).toBe(true);
    });

    it('should require valid diagnosis to navigate to step 1', () => {
      const { result } = renderHook(() => useServiceFlow());

      expect(!!result.current.canNavigateToStep(1)).toBe(false);

      act(() => {
        result.current.updateServiceData({
          hairThickness: 'Medio',
          hairDensity: 'Alta',
          overallTone: 'Castaño',
          overallReflect: 'Dorado',
          zoneColorAnalysis: completeZoneColorAnalysis,
          zonePhysicalAnalysis: completeZonePhysicalAnalysis,
        });
      });

      expect(!!result.current.canNavigateToStep(1)).toBe(true);
    });

    it('should require desired analysis result to navigate to step 2', () => {
      const { result } = renderHook(() => useServiceFlow());

      // Complete diagnosis
      act(() => {
        result.current.updateServiceData({
          hairThickness: 'Medio',
          hairDensity: 'Alta',
          overallTone: 'Castaño',
          overallReflect: 'Dorado',
          zoneColorAnalysis: completeZoneColorAnalysis,
          zonePhysicalAnalysis: completeZonePhysicalAnalysis,
        });
      });

      expect(!!result.current.canNavigateToStep(2)).toBe(false);

      act(() => {
        result.current.updateServiceData({
          desiredAnalysisResult: mockDesiredAnalysisResult,
        });
      });

      expect(!!result.current.canNavigateToStep(2)).toBe(true);
    });

    it('should require formula to navigate to step 3', () => {
      const { result } = renderHook(() => useServiceFlow());

      // Complete diagnosis and desired result
      act(() => {
        result.current.updateServiceData({
          hairThickness: 'Medio',
          hairDensity: 'Alta',
          overallTone: 'Castaño',
          overallReflect: 'Dorado',
          zoneColorAnalysis: completeZoneColorAnalysis,
          zonePhysicalAnalysis: completeZonePhysicalAnalysis,
          desiredAnalysisResult: mockDesiredAnalysisResult,
        });
      });

      expect(!!result.current.canNavigateToStep(3)).toBe(false);

      act(() => {
        result.current.updateServiceData({
          formula: 'Test formula',
        });
      });

      expect(!!result.current.canNavigateToStep(3)).toBe(true);
    });

    it('should return false for invalid step indices', () => {
      const { result } = renderHook(() => useServiceFlow());

      // stepIndex -1 returns true because -1 <= currentStep (0)
      expect(result.current.canNavigateToStep(-1)).toBe(true);
      expect(result.current.canNavigateToStep(4)).toBe(false);
    });
  });

  describe('Complex Scenarios', () => {
    it('should handle partial zone analysis correctly', () => {
      const { result } = renderHook(() => useServiceFlow());

      const partialZoneColorAnalysis = {
        [HairZone.ROOTS]: {
          zone: HairZone.ROOTS,
          level: 4,
          tone: 'Castaño',
          reflect: 'Dorado',
          state: 'Natural',
        },
        [HairZone.MIDS]: {
          zone: HairZone.MIDS,
          level: 5,
          tone: 'Castaño',
          reflect: 'Dorado',
          // Missing state
        },
        [HairZone.ENDS]: {
          zone: HairZone.ENDS,
          level: 6,
          tone: 'Castaño',
          reflect: 'Dorado',
          state: 'Natural',
        },
      };

      act(() => {
        result.current.updateServiceData({
          hairThickness: 'Medio',
          hairDensity: 'Alta',
          overallTone: 'Castaño',
          overallReflect: 'Dorado',
          zoneColorAnalysis: partialZoneColorAnalysis,
          zonePhysicalAnalysis: completeZonePhysicalAnalysis,
        });
      });

      expect(!!result.current.validateDiagnosis()).toBe(false);
    });

    it('should maintain backward compatibility with overallUndertone field', () => {
      const { result } = renderHook(() => useServiceFlow());

      expect(result.current.serviceData.overallUndertone).toBe('');

      act(() => {
        result.current.updateServiceData({
          overallUndertone: 'Cenizo',
        });
      });

      expect(result.current.serviceData.overallUndertone).toBe('Cenizo');
    });

    it('should handle complete service flow data', () => {
      const { result } = renderHook(() => useServiceFlow());

      const completeServiceData = {
        client: mockClient,
        clientId: 'client-123',
        hairThickness: 'Medio',
        hairDensity: 'Alta',
        overallTone: 'Castaño',
        overallReflect: 'Dorado',
        lastChemicalProcessType: 'Color',
        lastChemicalProcessDate: '2024-01-01',
        hasUsedHomeRemedies: false,
        hairLength: 25,
        diagnosisNotes: 'Test notes',
        zoneColorAnalysis: completeZoneColorAnalysis,
        zonePhysicalAnalysis: completeZonePhysicalAnalysis,
        desiredAnalysisResult: mockDesiredAnalysisResult,
        formula: 'Test formula',
        resultImage: 'result-image-url',
        resultNotes: 'Great result',
      };

      act(() => {
        result.current.updateServiceData(completeServiceData);
      });

      expect(!!result.current.validateDiagnosis()).toBe(true);
      expect(!!result.current.canNavigateToStep(3)).toBe(true);
      expect(result.current.serviceData).toEqual(expect.objectContaining(completeServiceData));
    });
  });

  describe('Stock Validation', () => {
    it('should initialize with default stock validation state', () => {
      const { result } = renderHook(() => useServiceFlow());

      expect(result.current.serviceData.stockValidation).toEqual({
        isChecking: false,
        hasStock: true,
        missingProducts: [],
        checked: false,
      });
    });

    it('should update stock validation state', () => {
      const { result } = renderHook(() => useServiceFlow());

      const stockValidationUpdate = {
        isChecking: true,
        hasStock: false,
        missingProducts: ['Producto A', 'Producto B'],
        checked: true,
      };

      act(() => {
        result.current.updateServiceData({
          stockValidation: stockValidationUpdate,
        });
      });

      expect(result.current.serviceData.stockValidation).toEqual(stockValidationUpdate);
    });
  });

  describe('Memory Stability', () => {
    it('should maintain referential stability for updateServiceData callback', () => {
      const { result, rerender } = renderHook(() => useServiceFlow());

      const firstUpdateCallback = result.current.updateServiceData;

      rerender();

      const secondUpdateCallback = result.current.updateServiceData;

      expect(firstUpdateCallback).toBe(secondUpdateCallback);
    });

    it('should maintain referential stability for validation callbacks', () => {
      const { result, rerender } = renderHook(() => useServiceFlow());

      const firstValidateCallback = result.current.validateDiagnosis;
      const firstCanNavigateCallback = result.current.canNavigateToStep;

      rerender();

      const secondValidateCallback = result.current.validateDiagnosis;
      const secondCanNavigateCallback = result.current.canNavigateToStep;

      expect(firstValidateCallback).toBe(secondValidateCallback);
      expect(firstCanNavigateCallback).toBe(secondCanNavigateCallback);
    });
  });
});
