import { useCallback } from 'react';
import { useServiceDraftStore } from '@/stores/service-draft-store';
import { ServiceData } from '@/src/service/hooks/useServiceFlow';

export const useServicePersistence = () => {
  const { saveDraft, getDraft, deleteDraft } = useServiceDraftStore();

  const saveServiceDraft = useCallback(
    (serviceData: ServiceData, currentStep: number) => {
      if (!serviceData.client || !serviceData.clientId) return;

      const serviceState = {
        diagnosisMethod: serviceData.diagnosisMethod,
        hairPhotos: serviceData.hairPhotos,
        hairThickness: serviceData.hairThickness,
        hairDensity: serviceData.hairDensity,
        overallTone: serviceData.overallTone,
        overallUndertone: serviceData.overallUndertone,
        lastChemicalProcessType: serviceData.lastChemicalProcessType,
        lastChemicalProcessDate: serviceData.lastChemicalProcessDate,
        diagnosisNotes: serviceData.diagnosisNotes,
        zoneColorAnalysis: serviceData.zoneColorAnalysis,
        zonePhysicalAnalysis: serviceData.zonePhysicalAnalysis,
        desiredMethod: serviceData.desiredMethod,
        desiredPhotos: serviceData.desiredPhotos,
        desiredAnalysisResult: serviceData.desiredAnalysisResult,
        desiredNotes: serviceData.desiredNotes,
        selectedBrand: serviceData.selectedBrand,
        selectedLine: serviceData.selectedLine,
        formula: serviceData.formula,
        isFormulaFromAI: serviceData.isFormulaFromAI,
        formulaCost: serviceData.formulaCost,
        viabilityAnalysis: serviceData.viabilityAnalysis,
        stockValidation: serviceData.stockValidation,
        resultImage: serviceData.resultImage,
        clientSatisfaction: serviceData.clientSatisfaction,
        resultNotes: serviceData.resultNotes,
      };

      const draft = useServiceDraftStore
        .getState()
        .createDraftFromServiceState(
          serviceData.clientId,
          serviceData.client.name,
          currentStep,
          serviceState
        );

      saveDraft(draft);
      // Draft saved for current step
    },
    [saveDraft]
  );

  const loadServiceDraft = useCallback(
    (clientId: string) => {
      const draft = getDraft(clientId);
      if (!draft) return null;

      // Loading draft for client

      return {
        currentStep: draft.currentStep,
        diagnosisData: draft.diagnosisData,
        desiredData: draft.desiredData,
        formulationData: draft.formulationData,
        resultData: draft.resultData,
      };
    },
    [getDraft]
  );

  const deleteServiceDraft = useCallback(
    (clientId: string) => {
      // First, find the draft by clientId
      const draft = getDraft(clientId);
      if (draft) {
        // Then delete it using the draft.id
        deleteDraft(draft.id);
        // Draft deleted for client
      } else {
        // No draft found for client
      }
    },
    [deleteDraft, getDraft]
  );

  const hasDraft = useCallback(
    (clientId: string) => {
      const draft = getDraft(clientId);
      return !!draft;
    },
    [getDraft]
  );

  return {
    saveServiceDraft,
    loadServiceDraft,
    deleteServiceDraft,
    hasDraft,
  };
};
