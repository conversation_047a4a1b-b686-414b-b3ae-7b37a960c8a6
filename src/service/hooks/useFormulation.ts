import { useState, useCallback, useEffect } from 'react';
import { Alert } from 'react-native';
import { supabase } from '@/lib/supabase';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { useClientHistoryStore } from '@/stores/client-history-store';
import { useAuthStore } from '@/stores/auth-store';
import { ColorCorrectionService } from '@/services/colorCorrectionService';
import { InventoryConsumptionService } from '@/services/inventoryConsumptionService';
import { parseFormulaText, calculateSimpleFormulaCost } from '@/utils/parseFormula';
import { getDefaultBrandAndLine } from '@/utils/brand-preferences';
import { ViabilityAnalysis, FormulaCost, Formulation } from '@/types/formulation';
import { DesiredColorAnalysisResult } from '@/types/desired-analysis';
import {
  HairZone,
  ZoneColorAnalysis,
  ZonePhysicalAnalysis,
  UnwantedTone,
} from '@/types/hair-diagnosis';

// Import AI analysis result type
import type { AIAnalysisResult } from '@/stores/ai-analysis-store';
import { COLOR_TECHNIQUES } from '@/types/desired-photo';

export const useFormulation = () => {
  const { preferredBrandLines } = useAuthStore();
  const defaultBrand = getDefaultBrandAndLine(preferredBrandLines);

  const [selectedBrand, setSelectedBrand] = useState(defaultBrand.brandName);
  const [selectedLine, setSelectedLine] = useState(defaultBrand.lineName);
  const [formula, setFormula] = useState('');
  const [formulationData, setFormulationData] = useState<Formulation | null>(null);
  const [isFormulaFromAI, setIsFormulaFromAI] = useState(true);
  const [isGeneratingFormula, setIsGeneratingFormula] = useState(false);

  // Brand conversion states
  const [showBrandModal, setShowBrandModal] = useState(false);
  const [brandModalType, setBrandModalType] = useState<'main' | 'conversion'>('main');
  const [conversionMode, setConversionMode] = useState(false);
  const [originalBrand, setOriginalBrand] = useState('');
  const [originalLine, setOriginalLine] = useState('');
  const [originalFormula, setOriginalFormula] = useState('');

  const [formulaCost, setFormulaCost] = useState<FormulaCost | null>(null);
  const [viabilityAnalysis, setViabilityAnalysis] = useState<ViabilityAnalysis | null>(null);
  const [stockValidation, setStockValidation] = useState<{
    isChecking: boolean;
    hasStock: boolean;
    missingProducts: string[];
    checked: boolean;
  }>({
    isChecking: false,
    hasStock: true,
    missingProducts: [],
    checked: false,
  });

  // Client History Store
  const { getCompatibleFormulas, getRecommendationsForClient } = useClientHistoryStore();

  // Update brand and line when user preferences change
  useEffect(() => {
    const newDefault = getDefaultBrandAndLine(preferredBrandLines);
    setSelectedBrand(newDefault.brandName);
    setSelectedLine(newDefault.lineName);
    // Updated default brand preferences
  }, [preferredBrandLines]);

  const calculateFormulaCost = useCallback(async (formulaText: string): Promise<FormulaCost> => {
    try {
      const salonConfig = useSalonConfigStore.getState();

      if (salonConfig.configuration.inventoryControlLevel === 'solo-formulas') {
        // Use simple calculation for formula-only mode
        return calculateSimpleFormulaCost(formulaText);
      } else {
        // Use new text-based calculation that parses exact products
        const consumptionAnalysis =
          await InventoryConsumptionService.calculateFormulationCostFromText(formulaText);

        // Convert to FormulaCost format
        const items: FormulaCost['items'] = consumptionAnalysis.items.map(item => ({
          product: item.productName,
          amount: `${item.amount}${item.unit}`,
          unitCost: item.unitCost,
          totalCost: item.totalCost,
        }));

        const totalMaterialCost = consumptionAnalysis.totalCost;

        // Apply salon's configured markup
        const suggestedServicePrice = salonConfig.applyMarkup(totalMaterialCost);
        const profitMargin = suggestedServicePrice - totalMaterialCost;

        return {
          items,
          totalMaterialCost: Math.round(totalMaterialCost * 100) / 100,
          suggestedServicePrice: Math.round(suggestedServicePrice * 100) / 100,
          profitMargin: Math.round(profitMargin * 100) / 100,
          hasAllRealCosts: consumptionAnalysis.hasAllRealCosts,
        };
      }
    } catch {
      // Formula cost calculation failed - using fallback
      return calculateSimpleFormulaCost(formulaText);
    }
  }, []);

  const analyzeViability = useCallback(
    (
      analysisResult: AIAnalysisResult | null,
      desiredAnalysisResult: DesiredColorAnalysisResult | null,
      zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>
    ): ViabilityAnalysis => {
      if (!analysisResult || !desiredAnalysisResult) {
        return {
          score: 'caution' as const,
          factors: {
            levelDifference: 0,
            hairHealth: 'good' as const,
            chemicalHistory: [],
            estimatedSessions: 1,
          },
          recommendations: [],
          warnings: [],
        };
      }

      const factorsList: string[] = [];
      const recommendations: string[] = [];
      const warnings: string[] = [];
      let overall: 'low' | 'medium' | 'high' = 'medium';
      let _riskLevel: 'low' | 'medium' | 'high' = 'medium';
      let sessionsNeeded = 1;

      // Analyze level difference
      const currentLevel = analysisResult.averageLevel || analysisResult.averageDepthLevel || 5;
      const targetLevel =
        parseInt(desiredAnalysisResult.general.overallLevel?.split('/')[0] || '7') || 7;
      const levelDifference = Math.abs(targetLevel - currentLevel);

      if (levelDifference > 3) {
        factorsList.push('Cambio de nivel significativo');
        warnings.push('Cambio de más de 3 niveles puede requerir múltiples sesiones');
        _riskLevel = 'high';
        sessionsNeeded = Math.ceil(levelDifference / 3);
      } else if (levelDifference > 1) {
        factorsList.push('Cambio de nivel moderado');
        overall = 'medium';
      } else {
        factorsList.push('Cambio de nivel mínimo');
        overall = 'high';
      }

      // Analyze hair condition
      const rootsPhysical = zoneColorAnalysis[HairZone.ROOTS];
      if (rootsPhysical?.damage === 'Alto' || rootsPhysical?.damage === 'Medio') {
        warnings.push('Cabello con daño severo - considerar tratamiento previo');
        _riskLevel = 'high';
        recommendations.push('Aplicar tratamiento reconstructor antes del color');
      }

      // Analyze porosity
      const rootsPhysicalAnalysis = rootsPhysical as Partial<ZonePhysicalAnalysis>;
      if (rootsPhysicalAnalysis?.porosity === 'Alta') {
        factorsList.push('Porosidad alta detectada');
        recommendations.push('Usar productos de baja alcalinidad');
        recommendations.push('Reducir tiempo de procesamiento');
      }

      // Time estimation
      let _timeEstimate = '1-2 horas';
      if (levelDifference > 2) _timeEstimate = '2-3 horas';
      if (levelDifference > 4) _timeEstimate = '3-4 horas';
      if (sessionsNeeded > 1) _timeEstimate = `${sessionsNeeded} sesiones de 2-3 horas cada una`;

      return {
        score: overall === 'low' ? 'safe' : overall === 'medium' ? 'caution' : 'risky',
        factors: {
          levelDifference,
          hairHealth:
            rootsPhysical?.damage === 'Alto' || rootsPhysical?.damage === 'Medio'
              ? 'poor'
              : rootsPhysical?.damage === 'Bajo'
                ? 'good'
                : 'fair',
          chemicalHistory: factorsList,
          estimatedSessions: sessionsNeeded,
        },
        recommendations,
        warnings,
      };
    },
    []
  );

  const checkStockAvailability = useCallback(async () => {
    if (!formula) return;

    setStockValidation(prev => ({ ...prev, isChecking: true }));

    try {
      const colorFormula = parseFormulaText(formula);
      const stockCheck = await InventoryConsumptionService.checkStock(colorFormula);

      setStockValidation({
        isChecking: false,
        hasStock: stockCheck.hasStock,
        missingProducts: stockCheck.missingProducts,
        checked: true,
      });

      return stockCheck;
    } catch {
      // Stock check failed
      setStockValidation(prev => ({
        ...prev,
        isChecking: false,
        checked: true,
        hasStock: false,
        missingProducts: ['Error al verificar stock'],
      }));
    }
  }, [formula]);

  const generateFormulaWithAI = useCallback(
    async (
      analysisResult: AIAnalysisResult | null,
      desiredAnalysisResult: DesiredColorAnalysisResult | null,
      zoneColorAnalysis: Record<HairZone, Partial<ZoneColorAnalysis>>,
      clientId?: string,
      adjustmentContext?: string
    ) => {
      if (!analysisResult && !desiredAnalysisResult) {
        Alert.alert(
          'Error',
          'Necesitas completar el diagnóstico y el análisis del color deseado antes de generar la fórmula'
        );
        return;
      }

      setIsGeneratingFormula(true);

      try {
        // Get client history for better formula generation
        let historyContext = '';
        if (clientId) {
          const compatibleFormulas = getCompatibleFormulas(clientId);
          const recommendations = getRecommendationsForClient(clientId);

          if (compatibleFormulas.length > 0) {
            historyContext += `\nFórmulas exitosas anteriores:\n${compatibleFormulas
              .slice(0, 2)
              .map(f => `- ${f.formula} (Satisfacción: ${f.satisfaction}/5)`)
              .join('\n')}`;
          }

          if (recommendations.length > 0) {
            historyContext += `\nRecomendaciones basadas en historial:\n${recommendations
              .slice(0, 3)
              .map(r => `- ${r}`)
              .join('\n')}`;
          }
        }

        // Analyze color correction needs
        const unwantedTones: Partial<Record<HairZone, UnwantedTone>> = {};
        Object.values(zoneColorAnalysis).forEach(zone => {
          if (zone.unwantedTone && zone.zone) {
            unwantedTones[zone.zone] = zone.unwantedTone;
          }
        });

        const _correctionAnalysis = ColorCorrectionService.analyzeColorCorrection(
          zoneColorAnalysis as Record<HairZone, ZoneColorAnalysis>,
          desiredAnalysisResult!,
          unwantedTones,
          selectedBrand,
          selectedLine
        );

        // Check if we should use the regional formulation service
        const salonConfig = useSalonConfigStore.getState();

        console.log('🔍 GENERATE FORMULA DEBUG:', {
          hasAnalysisResult: !!analysisResult,
          hasDesiredAnalysisResult: !!desiredAnalysisResult,
          selectedBrand,
          selectedLine,
          hasRegionalConfig: !!salonConfig.regionalConfig,
        });

        if (analysisResult && desiredAnalysisResult) {
          console.log('✅ CALLING EDGE FUNCTION FOR FORMULA GENERATION');
          const formulaContext = {
            currentDiagnosis: analysisResult,
            desiredResult: desiredAnalysisResult,
            brand: selectedBrand,
            line: selectedLine,
            regionalConfig: salonConfig.regionalConfig,
            clientHistory: historyContext,
            conversionMode: conversionMode
              ? {
                  originalBrand: originalBrand || '',
                  originalLine: originalLine || '',
                  originalFormula: originalFormula || '',
                }
              : undefined,
          };

          // Call Supabase Edge Function
          console.log('🚀 ABOUT TO CALL EDGE FUNCTION:', {
            task: 'generate_formula',
            brand: formulaContext.brand,
            line: formulaContext.line,
          });

          const { data, error } = await supabase.functions.invoke('salonier-assistant', {
            body: {
              task: 'generate_formula',
              payload: {
                diagnosis: formulaContext.currentDiagnosis,
                desiredResult: formulaContext.desiredResult,
                brand: formulaContext.brand,
                line: formulaContext.line,
                clientHistory: formulaContext.clientHistory,
                regionalConfig: salonConfig.regionalConfig,
                adjustmentContext: adjustmentContext || undefined,
              },
            },
          });

          console.log('📡 EDGE FUNCTION RESPONSE:', { data, error });

          if (error) {
            throw new Error('Error al generar la fórmula con IA');
          }

          // Try multiple response structures
          let generatedFormula = null;
          let structuredFormula = null;

          if (data && data.success && data.data) {
            generatedFormula = data.data.formulaText;
            structuredFormula = data.data.formulationData;
          } else if (data && data.formulaText) {
            generatedFormula = data.formulaText;
            structuredFormula = data.formulationData;
          } else if (data && data.result) {
            generatedFormula = data.result.formulaText;
            structuredFormula = data.result.formulationData;
          } else if (data && typeof data === 'string') {
            generatedFormula = data;
          }

          if (!generatedFormula) {
            throw new Error('Respuesta inválida del servidor');
          }

          setFormula(generatedFormula);
          setFormulationData(structuredFormula);
          setIsFormulaFromAI(true);

          // Log structured data if available
          // Structured formula data received

          // DEBUG: Log warnings received from Edge Function
          if (structuredFormula?.warnings && structuredFormula.warnings.length > 0) {
            console.log('⚠️ WARNINGS RECEIVED FROM EDGE FUNCTION:', {
              warningCount: structuredFormula.warnings.length,
              warnings: structuredFormula.warnings,
              formulaTitle: structuredFormula.formulaTitle,
            });
          } else {
            console.log('✅ No warnings received from Edge Function', {
              hasFormulationData: !!structuredFormula,
              formulaTitle: structuredFormula?.formulaTitle,
            });
          }

          // Calculate formula cost
          const cost = await calculateFormulaCost(generatedFormula);
          setFormulaCost(cost);

          // Auto-validate stock if inventory control is enabled
          if (salonConfig.configuration.inventoryControlLevel === 'control-total') {
            const stockCheck = await checkStockAvailability();
            if (stockCheck && !stockCheck.hasStock) {
              return `⚠️ Stock insuficiente: ${stockCheck.missingProducts.join(', ')}`;
            }
          }

          return '✅ Fórmula generada con IA';
        }
      } catch (error) {
        // Formula generation failed
        console.error('🚨 ERROR GENERATING FORMULA:', error);
        console.log('🔍 ERROR DETAILS:', {
          message: error?.message,
          stack: error?.stack,
          name: error?.name,
        });

        // Fallback to basic formula generation
        Alert.alert(
          '⚠️ Sin conexión con IA',
          `Error al generar fórmula con IA.\n\nGenerando fórmula de ejemplo. Por favor ajusta manualmente según tu criterio profesional.\n\nPara usar IA verifica:\n• Conexión a internet\n• Configuración de OpenAI en Supabase`,
          [{ text: 'Entendido', style: 'default' }]
        );

        // Generate fallback formula
        const currentLevel = analysisResult?.averageLevel || analysisResult?.averageDepthLevel || 5;
        const targetLevel =
          parseInt(desiredAnalysisResult?.general?.overallLevel?.split('/')[0] || '7') || 7;
        const levelDifference = Math.abs(targetLevel - currentLevel);
        const selectedTechnique = desiredAnalysisResult?.general?.technique || 'full_color';

        const fallbackFormula = `Fórmula Base:
- ${selectedLine} ${targetLevel}/1 (30g)
- ${selectedLine} ${targetLevel}/69 (10g)  
- Oxidante ${levelDifference > 2 ? '30' : '20'} vol (60g)

Aplicación estándar:
1. Dividir el cabello en secciones
2. Aplicar según técnica ${COLOR_TECHNIQUES.find(t => t.id === selectedTechnique)?.name || 'seleccionada'}
3. Procesar 35 minutos
4. Enjuagar y acondicionar`;

        setFormula(fallbackFormula);
        setIsFormulaFromAI(false);

        const cost = await calculateFormulaCost(fallbackFormula);
        setFormulaCost(cost);

        return '⚠️ Fórmula de ejemplo generada. Ajusta manualmente.';
      } finally {
        setIsGeneratingFormula(false);
      }
    },
    [
      selectedBrand,
      selectedLine,
      conversionMode,
      originalBrand,
      originalLine,
      originalFormula,
      getCompatibleFormulas,
      getRecommendationsForClient,
      calculateFormulaCost,
      checkStockAvailability,
    ]
  );

  // Effect to recalculate cost when formula changes
  useEffect(() => {
    if (!formula || formula.trim() === '') {
      setFormulaCost(null);
      return;
    }

    const timeoutId = setTimeout(() => {
      calculateFormulaCost(formula)
        .then(cost => {
          setFormulaCost(cost);
        })
        .catch(_error => {
          // Cost calculation failed - using simple fallback
          const simpleCost = calculateSimpleFormulaCost(formula);
          setFormulaCost(simpleCost);
        });
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [formula]);

  return {
    // Brand and formula state
    selectedBrand,
    setSelectedBrand,
    selectedLine,
    setSelectedLine,
    formula,
    setFormula,
    isFormulaFromAI,
    setIsFormulaFromAI,
    isGeneratingFormula,

    // Brand conversion state
    showBrandModal,
    setShowBrandModal,
    brandModalType,
    setBrandModalType,
    conversionMode,
    setConversionMode,
    originalBrand,
    setOriginalBrand,
    originalLine,
    setOriginalLine,
    originalFormula,
    setOriginalFormula,

    // Analysis and cost state
    formulaCost,
    setFormulaCost,
    viabilityAnalysis,
    setViabilityAnalysis,
    stockValidation,
    setStockValidation,

    // Functions
    calculateFormulaCost,
    analyzeViability,
    checkStockAvailability,
    generateFormulaWithAI,
    formulationData,
    setFormulationData,
  };
};
