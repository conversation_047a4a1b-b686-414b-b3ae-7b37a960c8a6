import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Colors from '@/constants/colors';
import { Formulation, ViabilityAnalysis } from '@/types/formulation';

// Import existing components we'll reuse
import { EnhancedFormulationView } from '@/components/formulation/EnhancedFormulationView';
import FormulaDisplay from '@/components/formulation/FormulaDisplay';

interface ZonalFormulaDisplayProps {
  formula: string;
  selectedBrand: string;
  selectedLine: string;
  technique: string;
  formulationData?: Formulation | null;
  clientName?: string;
  isFromAI?: boolean;
  onEdit?: (newFormula: string) => void;
  viabilityAnalysis?: ViabilityAnalysis;
  currentLevel?: number;
  targetLevel?: number;
}

export const ZonalFormulaDisplay: React.FC<ZonalFormulaDisplayProps> = ({
  formula,
  selectedBrand,
  selectedLine,
  technique,
  formulationData,
  clientName,
  isFromAI = true,
  onEdit,
  viabilityAnalysis,
  currentLevel,
  targetLevel,
}) => {
  const getTechniqueTitle = () => {
    switch (technique) {
      case 'balayage':
        return 'Fórmula Balayage';
      case 'highlights':
        return 'Fórmula de Mechas';
      case 'foilyage':
        return 'Fórmula Foilyage';
      case 'babylights':
        return 'Fórmula Babylights';
      case 'chunky_highlights':
        return 'Fórmula Mechas Gruesas';
      case 'reverse_balayage':
        return 'Fórmula Reverse Balayage';
      default:
        return 'Fórmula por Zonas';
    }
  };

  const getTechniqueDescription = () => {
    switch (technique) {
      case 'balayage':
        return 'Técnica de barrido a mano alzada con transiciones suaves';
      case 'highlights':
        return 'Mechas tradicionales con papel aluminio para mayor contraste';
      case 'foilyage':
        return 'Combinación de foil y balayage para efectos naturales';
      case 'babylights':
        return 'Mechas ultrafinas para un efecto natural y sutil';
      case 'chunky_highlights':
        return 'Mechas gruesas y definidas para máximo contraste';
      case 'reverse_balayage':
        return 'Balayage oscuro sobre base clara para profundidad';
      default:
        return 'Aplicación diferenciada por zonas capilares';
    }
  };

  const getZoneLabels = () => {
    switch (technique) {
      case 'balayage':
      case 'reverse_balayage':
        return {
          primary: 'Fórmula de Aclaración',
          secondary: 'Fórmula de Matiz',
        };
      case 'highlights':
      case 'chunky_highlights':
        return {
          primary: 'Fórmula de Mechas',
          secondary: 'Fórmula Base/Brillo',
        };
      case 'foilyage':
        return {
          primary: 'Fórmula Foil',
          secondary: 'Fórmula Balayage',
        };
      case 'babylights':
        return {
          primary: 'Fórmula Principal',
          secondary: 'Fórmula de Refuerzo',
        };
      default:
        return {
          primary: 'Fórmula Raíces',
          secondary: 'Fórmula Medios y Puntas',
        };
    }
  };

  return (
    <View style={styles.container}>
      {/* Technique Header */}
      <View style={styles.techniqueHeader}>
        <Text style={styles.techniqueTitle}>{getTechniqueTitle()}</Text>
        <Text style={styles.techniqueDescription}>{getTechniqueDescription()}</Text>
      </View>

      {/* Zone Information Banner */}
      <View style={styles.zoneInfoBanner}>
        <Text style={styles.zoneInfoText}>
          Esta técnica requiere aplicación especializada por zonas
        </Text>
        <View style={styles.zoneLabelsContainer}>
          <View style={styles.zoneLabel}>
            <View style={[styles.zoneDot, styles.zoneDotPrimary]} />
            <Text style={styles.zoneLabelText}>{getZoneLabels().primary}</Text>
          </View>
          <View style={styles.zoneLabel}>
            <View style={[styles.zoneDot, styles.zoneDotSecondary]} />
            <Text style={styles.zoneLabelText}>{getZoneLabels().secondary}</Text>
          </View>
        </View>
      </View>

      {/* Enhanced Formula View - Shows structured steps if available */}
      {formula && formulationData && (
        <EnhancedFormulationView
          formulationData={formulationData}
          formulaText={formula}
          selectedBrand={selectedBrand}
          selectedLine={selectedLine}
        />
      )}

      {/* Original Formula Display - Fallback for text-only formulas */}
      {formula && !formulationData && (
        <FormulaDisplay
          formulaText={formula}
          clientName={clientName}
          serviceDate={new Date().toLocaleDateString()}
          isFromAI={isFromAI}
          onEdit={onEdit}
          editable={true}
          viabilityAnalysis={viabilityAnalysis}
          currentLevel={currentLevel || 5}
          targetLevel={targetLevel || 7}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 16,
  },
  techniqueHeader: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  techniqueTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.primary,
    marginBottom: 4,
  },
  techniqueDescription: {
    fontSize: 14,
    color: Colors.light.gray,
    lineHeight: 20,
  },
  zoneInfoBanner: {
    backgroundColor: Colors.light.accent + '15',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: Colors.light.accent + '30',
  },
  zoneInfoText: {
    fontSize: 14,
    color: Colors.light.text,
    textAlign: 'center',
    marginBottom: 12,
    fontWeight: '500',
  },
  zoneLabelsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 20,
  },
  zoneLabel: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  zoneDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  zoneDotPrimary: {
    backgroundColor: Colors.light.zonePrimary,
  },
  zoneDotSecondary: {
    backgroundColor: Colors.light.zoneSecondary,
  },
  zoneLabelText: {
    fontSize: 12,
    color: Colors.light.gray,
    fontWeight: '500',
  },
});
