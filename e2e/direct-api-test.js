/**
 * Direct API Test Script - Manual E2E Testing
 *
 * This script directly tests the Edge Functions to validate the complete flow
 * JavaScript version for easy execution
 */

// const { createClient } = require('@supabase/supabase-js'); // Not used in current test
const fetch = require('node-fetch'); // For Node.js fetch support

// Configuration
const SUPABASE_URL =
  process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://ajsamgugqfbttkrlgvbr.supabase.co';
const SUPABASE_ANON_KEY =
  process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY ||
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqc2FtZ3VncWZidHRrcmxndmJyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxNzYwOTcsImV4cCI6MjA2Nzc1MjA5N30.r6IC-i_GYrSHvS2tKudyGONrhX8-NlyNRl1aHo_AToY';

console.log('🚀 Starting Direct API Testing for Salonier Assistant Edge Functions');
console.log('📍 Supabase URL:', SUPABASE_URL);

async function testEdgeFunctionDirectly(task, payload) {
  const startTime = Date.now();
  const functionUrl = `${SUPABASE_URL}/functions/v1/salonier-assistant`;

  console.log(`\n🧪 Testing ${task}...`);
  console.log(`📤 Payload:`, JSON.stringify(payload, null, 2));

  try {
    const response = await fetch(functionUrl, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
        apikey: SUPABASE_ANON_KEY,
      },
      body: JSON.stringify({
        task,
        payload,
      }),
    });

    const duration = Date.now() - startTime;
    const responseText = await response.text();

    console.log(`⏱️  Duration: ${duration}ms`);
    console.log(`📊 Status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      console.log(`❌ Error Response:`, responseText);
      return {
        task,
        success: false,
        duration,
        error: {
          status: response.status,
          statusText: response.statusText,
          body: responseText,
        },
      };
    }

    let data;
    try {
      data = JSON.parse(responseText);
      console.log(`✅ Success! Data Keys:`, Object.keys(data));

      // Show key fields without overwhelming output
      if (data.averageLevel) console.log(`   Hair Level: ${data.averageLevel}`);
      if (data.overallTone) console.log(`   Tone: ${data.overallTone}`);
      if (data.overallCondition) console.log(`   Condition: ${data.overallCondition}`);
      if (data.overallConfidence) console.log(`   Confidence: ${data.overallConfidence}`);
      if (data.targetLevel) console.log(`   Target Level: ${data.targetLevel}`);
      if (data.viability) console.log(`   Viability: ${data.viability}`);
      if (data.formula) console.log(`   Formula Length: ${data.formula.length} chars`);
      if (data.products && Array.isArray(data.products))
        console.log(`   Products: ${data.products.length} items`);
    } catch {
      console.log(`✅ Success! Raw Response Length:`, responseText.length);
      data = responseText;
    }

    return {
      task,
      success: true,
      duration,
      data,
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    console.log(`❌ Request Failed:`, error.message);

    return {
      task,
      success: false,
      duration,
      error: error,
    };
  }
}

async function testCompleteFlow() {
  const results = [];

  console.log('\n🎯 PHASE 1: Basic Connectivity and Parse Testing');

  // Test 1: Basic product parsing (should work without authentication)
  const parseResult = await testEdgeFunctionDirectly('parse_product_text', {
    text: 'tubo de tinte Wella Koleston 8.1 rubio ceniza claro',
  });
  results.push(parseResult);

  console.log('\n🎯 PHASE 2: Hair Diagnosis Analysis');

  // Test 2: Hair diagnosis with real image
  const diagnosisResult = await testEdgeFunctionDirectly('diagnose_image', {
    imageUrl: 'https://images.unsplash.com/photo-1522338242992-e1a54906a8da?w=800&q=80',
  });
  results.push(diagnosisResult);

  if (diagnosisResult.success && diagnosisResult.data) {
    console.log('\n🔍 Validating Hair Diagnosis Result...');
    const diagnosis = diagnosisResult.data;

    // Validate required fields
    const requiredFields = ['averageLevel', 'overallTone', 'overallCondition', 'zoneAnalysis'];
    const missingFields = requiredFields.filter(field => !diagnosis[field]);

    if (missingFields.length > 0) {
      console.log(`⚠️  Missing required fields: ${missingFields.join(', ')}`);
    } else {
      console.log('✅ All required diagnosis fields present');
    }

    // Validate data ranges
    if (diagnosis.averageLevel && (diagnosis.averageLevel < 1 || diagnosis.averageLevel > 10)) {
      console.log(`⚠️  Invalid hair level: ${diagnosis.averageLevel}`);
    } else if (diagnosis.averageLevel) {
      console.log(`✅ Hair level valid: ${diagnosis.averageLevel}`);
    }

    // Check for detailed logging data
    if (diagnosis.bucketInfo) {
      console.log(
        `✅ Bucket info present: ${diagnosis.bucketInfo.description || 'No description'}`
      );
    }

    if (diagnosis.zoneAnalysis && diagnosis.zoneAnalysis.roots) {
      console.log('✅ Zone analysis detailed data present');
      console.log(
        `   Roots: Level ${diagnosis.zoneAnalysis.roots.level}, Tone: ${diagnosis.zoneAnalysis.roots.tone}`
      );
      if (diagnosis.zoneAnalysis.mids) {
        console.log(
          `   Mids: Level ${diagnosis.zoneAnalysis.mids.level}, Tone: ${diagnosis.zoneAnalysis.mids.tone}`
        );
      }
      if (diagnosis.zoneAnalysis.ends) {
        console.log(
          `   Ends: Level ${diagnosis.zoneAnalysis.ends.level}, Tone: ${diagnosis.zoneAnalysis.ends.tone}`
        );
      }
    }
  }

  console.log('\n🎯 PHASE 3: Desired Look Analysis');

  // Test 3: Desired look analysis
  const desiredLookResult = await testEdgeFunctionDirectly('analyze_desired_look', {
    imageUrl: 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=800&q=80',
    currentLevel: diagnosisResult.success ? diagnosisResult.data.averageLevel : 6,
  });
  results.push(desiredLookResult);

  console.log('\n🎯 PHASE 4: Formula Generation');

  // Test 4: Formula generation
  if (diagnosisResult.success) {
    const formulaResult = await testEdgeFunctionDirectly('generate_formula', {
      diagnosis: diagnosisResult.data,
      desiredResult: desiredLookResult.success
        ? desiredLookResult.data
        : {
            targetLevel: 8,
            targetTone: 'neutral',
            viability: 'feasible',
          },
      brand: 'Wella Professionals',
      line: 'Koleston Perfect',
      clientHistory: 'E2E Test - Virgin hair analysis',
    });
    results.push(formulaResult);

    if (formulaResult.success) {
      console.log('\n🔍 Validating Formula Result...');
      const formula = formulaResult.data;

      // Check formula structure
      if (formula.formula && formula.formula.includes('ml')) {
        console.log('✅ Formula contains measurements');
      } else if (formula.formula) {
        console.log('⚠️  Formula present but missing measurements');
      } else {
        console.log('⚠️  Formula missing');
      }

      if (formula.products && Array.isArray(formula.products)) {
        console.log(`✅ Products array present with ${formula.products.length} items`);
      } else {
        console.log('⚠️  Products array missing or invalid');
      }

      if (formula.technique) {
        console.log('✅ Application technique provided');
      }

      if (formula.processingTime) {
        console.log(`✅ Processing time specified: ${formula.processingTime}`);
      }
    }
  } else {
    console.log('⚠️  Skipping formula generation due to failed diagnosis');
  }

  console.log('\n🎯 PHASE 5: Error Handling Tests');

  // Test 5: Invalid task
  const invalidTaskResult = await testEdgeFunctionDirectly('invalid_task', {
    test: 'data',
  });
  results.push(invalidTaskResult);

  return results;
}

async function generateReport(results) {
  console.log('\n📊 COMPREHENSIVE E2E TEST REPORT');
  console.log('='.repeat(50));

  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);

  console.log(`\n📈 SUMMARY:`);
  console.log(`   Total Tests: ${results.length}`);
  console.log(`   Successful: ${successful.length}`);
  console.log(`   Failed: ${failed.length}`);
  console.log(`   Success Rate: ${Math.round((successful.length / results.length) * 100)}%`);

  const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);
  console.log(`   Total Duration: ${totalDuration}ms (${(totalDuration / 1000).toFixed(1)}s)`);

  console.log(`\n📋 DETAILED RESULTS:`);

  results.forEach((result, index) => {
    const status = result.success ? '✅' : '❌';
    const duration = `${result.duration}ms`;

    console.log(`\n${index + 1}. ${status} ${result.task} (${duration})`);

    if (result.success && result.data) {
      // Show key data points
      if (result.task === 'diagnose_image') {
        console.log(
          `   → Level: ${result.data.averageLevel || 'N/A'}, Tone: ${result.data.overallTone || 'N/A'}`
        );
        console.log(`   → Condition: ${result.data.overallCondition || 'N/A'}`);
        console.log(`   → Confidence: ${result.data.overallConfidence || 'N/A'}`);
      } else if (result.task === 'generate_formula') {
        console.log(`   → Products: ${result.data.products?.length || 0}`);
        console.log(`   → Processing Time: ${result.data.processingTime || 'N/A'}`);
      } else if (result.task === 'analyze_desired_look') {
        console.log(`   → Target Level: ${result.data.targetLevel || 'N/A'}`);
        console.log(`   → Viability: ${result.data.viability || 'N/A'}`);
      }
    } else if (result.error) {
      console.log(`   → Error: ${result.error.status || 'Unknown'}`);
      if (result.error.body && typeof result.error.body === 'string') {
        // Show first 200 chars of error
        const errorPreview = result.error.body.substring(0, 200);
        console.log(`   → Details: ${errorPreview}${result.error.body.length > 200 ? '...' : ''}`);
      }
    }
  });

  console.log(`\n🎯 KEY VALIDATIONS:`);
  const diagnosisTest = results.find(r => r.task === 'diagnose_image');
  if (diagnosisTest?.success) {
    console.log('✅ Hair diagnosis works with detailed zone analysis');
    console.log('✅ Confidence scoring operational');
    console.log('✅ Bucket classification system active');
  } else {
    console.log('❌ Hair diagnosis failed - check image analysis');
  }

  const formulaTest = results.find(r => r.task === 'generate_formula');
  if (formulaTest?.success) {
    console.log('✅ Formula generation works end-to-end');
    console.log('✅ Product recommendations included');
    console.log('✅ Processing time calculations working');
  } else {
    console.log('❌ Formula generation failed - check AI integration');
  }

  console.log(`\n🚀 NEXT STEPS:`);
  const criticalFailures = failed.filter(f => !['invalid_task'].includes(f.task));

  if (criticalFailures.length === 0) {
    console.log('🎉 All core functionality working! Ready for detailed logging verification.');
    console.log('📋 Recommended actions:');
    console.log('   1. Check Supabase Edge Function logs for [DIAGNOSTIC] entries');
    console.log('   2. Verify detailed hair analysis data appears in logs');
    console.log('   3. Confirm zone analysis logging is working');
    console.log('   4. Test with mobile app integration');
  } else {
    console.log('⚠️  Issues found that need addressing:');
    criticalFailures.forEach(failure => {
      console.log(`   • ${failure.task}: ${failure.error?.statusText || 'Unknown error'}`);
    });
  }

  return criticalFailures.length === 0;
}

// Run the comprehensive test
async function main() {
  try {
    const results = await testCompleteFlow();
    const success = await generateReport(results);

    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main();
}

module.exports = { testEdgeFunctionDirectly, testCompleteFlow, generateReport };
