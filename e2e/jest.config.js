module.exports = {
  displayName: 'E2E Tests',

  // Test environment for Node.js (Edge Functions testing)
  testEnvironment: 'node',

  // Root directory for E2E tests
  rootDir: '.',

  // Test patterns - only E2E tests in this directory
  testMatch: ['**/*.test.ts'],

  // Transform TypeScript files
  preset: 'ts-jest',

  // Module name mapping for project paths
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/../$1',
  },

  // Setup files
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],

  // Extended timeouts for E2E tests (AI calls can be slow)
  testTimeout: 60000, // 60 seconds

  // Coverage collection for E2E tests
  collectCoverageFrom: [
    '../supabase/functions/**/*.ts',
    '!../supabase/functions/**/*.d.ts',
    '!../node_modules/**',
  ],

  // Coverage thresholds for integration tests
  coverageThreshold: {
    global: {
      branches: 60,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },

  // Reporters for detailed output
  reporters: [
    'default',
    [
      'jest-html-reporters',
      {
        publicPath: './e2e-report',
        filename: 'e2e-results.html',
        expand: true,
      },
    ],
  ],

  // Verbose output for debugging
  verbose: true,

  // Don't clear mocks between tests (we handle this manually)
  clearMocks: false,

  // Max concurrent tests (limit for API rate limiting)
  maxConcurrency: 3,

  // Fail fast on first error for CI
  bail: false,

  // Global variables available in tests
  globals: {
    'ts-jest': {
      useESM: true,
    },
  },

  // Module file extensions
  moduleFileExtensions: ['ts', 'tsx', 'js', 'json'],
};
