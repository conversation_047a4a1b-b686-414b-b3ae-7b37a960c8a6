/**
 * Jest setup for E2E tests
 * Configures test environment for Edge Function integration testing
 */
/* global jest, beforeAll, afterAll */

// Extended timeout for all E2E tests (AI operations can be slow)
jest.setTimeout(60000);

// Global test configuration
global.console = {
  ...console,
  // Enhanced logging for E2E tests
  log: jest.fn((...args) => {
    if (process.env.JEST_VERBOSE === 'true') {
      console.log(...args);
    }
  }),
  warn: jest.fn(console.warn),
  error: jest.fn(console.error),
  info: jest.fn(console.info),
  debug: jest.fn((...args) => {
    if (process.env.DEBUG === 'true') {
      console.debug(...args);
    }
  }),
};

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Performance measurement utilities
global.measurePerformance = (name, fn) => {
  return async (...args) => {
    const start = Date.now();
    try {
      const result = await fn(...args);
      const duration = Date.now() - start;
      console.log(`⚡ ${name}: ${duration}ms`);
      return { result, duration };
    } catch (error) {
      const duration = Date.now() - start;
      console.log(`❌ ${name} failed after: ${duration}ms`);
      throw error;
    }
  };
};

// Test data validation utilities
global.validateHairAnalysis = analysis => {
  const required = ['averageLevel', 'overallTone', 'overallCondition', 'zoneAnalysis'];
  for (const field of required) {
    if (!analysis[field]) {
      throw new Error(`Missing required field in hair analysis: ${field}`);
    }
  }

  if (analysis.averageLevel < 1 || analysis.averageLevel > 10) {
    throw new Error(`Invalid hair level: ${analysis.averageLevel}`);
  }

  if (!analysis.zoneAnalysis.roots || !analysis.zoneAnalysis.mids || !analysis.zoneAnalysis.ends) {
    throw new Error('Missing zone analysis data');
  }

  return true;
};

global.validateFormula = formula => {
  const required = ['formula', 'products', 'technique', 'processingTime'];
  for (const field of required) {
    if (!formula[field]) {
      throw new Error(`Missing required field in formula: ${field}`);
    }
  }

  if (!Array.isArray(formula.products) || formula.products.length === 0) {
    throw new Error('Formula must contain at least one product');
  }

  if (!formula.formula.includes('ml') && !formula.formula.includes('g')) {
    throw new Error('Formula must contain measurements');
  }

  return true;
};

// Test environment checks
beforeAll(() => {
  // Check required environment variables
  const requiredEnvVars = ['EXPO_PUBLIC_SUPABASE_URL', 'EXPO_PUBLIC_SUPABASE_ANON_KEY'];

  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      console.warn(`⚠️  Warning: ${envVar} not set. Some tests may fail.`);
    }
  }

  console.log('🚀 E2E Test Environment Setup Complete');
  console.log(`📊 Node Version: ${process.version}`);
  console.log(`🌐 Test Environment: ${process.env.NODE_ENV || 'development'}`);
});

afterAll(() => {
  console.log('🏁 E2E Test Environment Teardown Complete');
});
