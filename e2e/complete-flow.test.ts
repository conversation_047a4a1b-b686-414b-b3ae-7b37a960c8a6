/**
 * Complete End-to-End Hair Analysis and Formulation Flow Tests
 * Tests the entire pipeline from hair diagnosis to formula generation
 *
 * Coverage:
 * 1. Hair Diagnosis Analysis (diagnose_image task)
 * 2. Desired Color Selection
 * 3. Formula Generation (generate_formula task)
 * 4. Result Integration and logging verification
 */

import { createClient } from '@supabase/supabase-js';

// Test configuration
const SUPABASE_URL =
  process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://wekqsxzjhhiuxahxpfin.supabase.co';
const SUPABASE_ANON_KEY =
  process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY ||
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indla3FzeHpqaGhpdXhhaHhwZmluIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQyOTI0MDksImV4cCI6MjA0OTg2ODQwOX0.LoV7pFBz7N2ZNGEJIFf0AKfPY3CqwJpCO5NeUH0_qeg';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Mock image URLs for testing (use actual images from project or placeholder)
const MOCK_IMAGES = {
  virginHairLevel6: 'https://images.unsplash.com/photo-1522338242992-e1a54906a8da?w=800',
  damagedHairLevel4: 'https://images.unsplash.com/photo-1494790108755-2616c24ca249?w=800',
  greyHair: 'https://images.unsplash.com/photo-1488426862026-3ee34a7d66df?w=800',
  blondeHair: 'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?w=800',
  desiredBlonde9: 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=800',
  desiredRed7: 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=800',
  fashionColor: 'https://images.unsplash.com/photo-1549236177-89b7b5fcfc52?w=800',
};

interface TestScenario {
  name: string;
  description: string;
  currentHairImage: string;
  desiredColorImage?: string;
  expectedDiagnosis: {
    minLevel: number;
    maxLevel: number;
    expectedTones?: string[];
    expectedCondition?: string;
  };
  expectedFormula: {
    shouldContainProducts: string[];
    shouldContainProcessingTime: boolean;
    shouldContainRisk: boolean;
  };
}

// Test scenarios covering all critical use cases
const TEST_SCENARIOS: TestScenario[] = [
  {
    name: 'Virgin Hair Level 6 to Blonde Level 9',
    description: 'Virgin dark blonde to light blonde - requires bleaching',
    currentHairImage: MOCK_IMAGES.virginHairLevel6,
    desiredColorImage: MOCK_IMAGES.desiredBlonde9,
    expectedDiagnosis: {
      minLevel: 5,
      maxLevel: 7,
      expectedTones: ['neutral', 'warm'],
      expectedCondition: 'good',
    },
    expectedFormula: {
      shouldContainProducts: ['bleach', 'developer', 'toner'],
      shouldContainProcessingTime: true,
      shouldContainRisk: true,
    },
  },
  {
    name: 'Previously Colored Hair Color Correction',
    description: 'Damaged colored hair requiring correction',
    currentHairImage: MOCK_IMAGES.damagedHairLevel4,
    desiredColorImage: MOCK_IMAGES.desiredRed7,
    expectedDiagnosis: {
      minLevel: 3,
      maxLevel: 5,
      expectedTones: ['orange', 'brassy'],
      expectedCondition: 'damaged',
    },
    expectedFormula: {
      shouldContainProducts: ['color', 'developer', 'treatment'],
      shouldContainProcessingTime: true,
      shouldContainRisk: true,
    },
  },
  {
    name: 'Gray Coverage Natural Hair',
    description: 'Natural hair with gray coverage needs',
    currentHairImage: MOCK_IMAGES.greyHair,
    expectedDiagnosis: {
      minLevel: 4,
      maxLevel: 8,
      expectedCondition: 'good',
    },
    expectedFormula: {
      shouldContainProducts: ['color', 'developer'],
      shouldContainProcessingTime: true,
      shouldContainRisk: false,
    },
  },
  {
    name: 'Fashion Color on Bleached Base',
    description: 'Fashion color application on pre-lightened hair',
    currentHairImage: MOCK_IMAGES.blondeHair,
    desiredColorImage: MOCK_IMAGES.fashionColor,
    expectedDiagnosis: {
      minLevel: 8,
      maxLevel: 10,
      expectedCondition: 'damaged',
    },
    expectedFormula: {
      shouldContainProducts: ['toner', 'color'],
      shouldContainProcessingTime: true,
      shouldContainRisk: false,
    },
  },
];

describe('Complete Hair Analysis and Formulation Flow E2E Tests', () => {
  let authToken: string;

  beforeAll(async () => {
    // Get authentication token - try demo user or create test user
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'demo123456',
    });

    if (authError) {
      console.log('Demo user not available, attempting to create test user...');

      const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
        email: '<EMAIL>',
        password: 'testpassword123',
        options: {
          data: {
            full_name: 'E2E Test User',
            salon_name: 'Test Salon',
          },
        },
      });

      if (signUpError) {
        throw new Error(`Failed to create test user: ${signUpError.message}`);
      }

      authToken = signUpData.session?.access_token || '';
    } else {
      authToken = authData.session?.access_token || '';
    }

    expect(authToken).toBeTruthy();
  });

  describe.each(TEST_SCENARIOS)('$name', scenario => {
    let diagnosisResult: any;
    let desiredColorResult: any;
    let formulaResult: any;

    test(`should analyze hair image and return detailed diagnosis`, async () => {
      const response = await fetch(`${SUPABASE_URL}/functions/v1/salonier-assistant`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          task: 'diagnose_image',
          payload: {
            imageUrl: scenario.currentHairImage,
          },
        }),
      });

      expect(response.ok).toBe(true);
      diagnosisResult = await response.json();

      console.log(`[${scenario.name}] Diagnosis Result:`, JSON.stringify(diagnosisResult, null, 2));

      // Validate diagnosis structure
      expect(diagnosisResult).toHaveProperty('averageLevel');
      expect(diagnosisResult).toHaveProperty('overallTone');
      expect(diagnosisResult).toHaveProperty('overallCondition');
      expect(diagnosisResult).toHaveProperty('zoneAnalysis');
      expect(diagnosisResult).toHaveProperty('bucketInfo');
      expect(diagnosisResult).toHaveProperty('overallConfidence');

      // Validate level is within expected range
      expect(diagnosisResult.averageLevel).toBeGreaterThanOrEqual(
        scenario.expectedDiagnosis.minLevel
      );
      expect(diagnosisResult.averageLevel).toBeLessThanOrEqual(scenario.expectedDiagnosis.maxLevel);

      // Validate zone analysis exists
      expect(diagnosisResult.zoneAnalysis).toHaveProperty('roots');
      expect(diagnosisResult.zoneAnalysis).toHaveProperty('mids');
      expect(diagnosisResult.zoneAnalysis).toHaveProperty('ends');

      // Validate confidence is reasonable
      expect(diagnosisResult.overallConfidence).toBeGreaterThan(0.5);
      expect(diagnosisResult.overallConfidence).toBeLessThanOrEqual(1.0);

      // Validate bucket info exists
      expect(diagnosisResult.bucketInfo).toBeDefined();
      expect(diagnosisResult.bucketInfo.bucket).toBeDefined();
      expect(diagnosisResult.bucketInfo.description).toBeDefined();
    }, 30000); // 30s timeout for AI analysis

    test(`should analyze desired color if provided`, async () => {
      if (!scenario.desiredColorImage) {
        return; // Skip if no desired color image
      }

      const response = await fetch(`${SUPABASE_URL}/functions/v1/salonier-assistant`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          task: 'analyze_desired_look',
          payload: {
            imageUrl: scenario.desiredColorImage,
            currentLevel: diagnosisResult.averageLevel,
          },
        }),
      });

      expect(response.ok).toBe(true);
      desiredColorResult = await response.json();

      console.log(
        `[${scenario.name}] Desired Color Result:`,
        JSON.stringify(desiredColorResult, null, 2)
      );

      // Validate desired color structure
      expect(desiredColorResult).toHaveProperty('targetLevel');
      expect(desiredColorResult).toHaveProperty('targetTone');
      expect(desiredColorResult).toHaveProperty('viability');
      expect(desiredColorResult).toHaveProperty('technique');
    }, 30000);

    test(`should generate complete formula with detailed instructions`, async () => {
      const formulaPayload = {
        task: 'generate_formula',
        payload: {
          diagnosis: diagnosisResult,
          desiredResult: desiredColorResult || {
            targetLevel: diagnosisResult.averageLevel + 2,
            targetTone: 'neutral',
            viability: 'feasible',
          },
          brand: 'Wella Professionals',
          line: 'Koleston Perfect',
          clientHistory: `Test scenario: ${scenario.description}`,
        },
      };

      const response = await fetch(`${SUPABASE_URL}/functions/v1/salonier-assistant`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formulaPayload),
      });

      expect(response.ok).toBe(true);
      formulaResult = await response.json();

      console.log(`[${scenario.name}] Formula Result:`, JSON.stringify(formulaResult, null, 2));

      // Validate formula structure
      expect(formulaResult).toHaveProperty('formula');
      expect(formulaResult).toHaveProperty('products');
      expect(formulaResult).toHaveProperty('technique');
      expect(formulaResult).toHaveProperty('processingTime');

      // Validate formula content
      expect(formulaResult.formula).toContain('ml'); // Should contain measurements
      expect(formulaResult.technique).toBeTruthy();
      expect(formulaResult.processingTime).toBeTruthy();

      // Check for expected products
      const formulaText = JSON.stringify(formulaResult).toLowerCase();
      scenario.expectedFormula.shouldContainProducts.forEach(productType => {
        expect(formulaText).toContain(productType.toLowerCase());
      });

      // Check processing time mentioned
      if (scenario.expectedFormula.shouldContainProcessingTime) {
        expect(formulaText).toMatch(/\d+\s*(minutes?|mins?)/i);
      }

      // Check risk assessment if expected
      if (scenario.expectedFormula.shouldContainRisk) {
        expect(formulaText).toMatch(/(risk|caution|warning|damage)/i);
      }
    }, 45000); // 45s timeout for formula generation

    test(`should validate complete service flow integration`, () => {
      // Ensure we have all required data for a complete service
      expect(diagnosisResult).toBeDefined();
      expect(formulaResult).toBeDefined();

      // Validate data consistency between steps
      expect(diagnosisResult.averageLevel).toBeGreaterThan(0);
      expect(diagnosisResult.averageLevel).toBeLessThanOrEqual(10);

      // Validate formula references diagnosis correctly
      const serviceData = {
        diagnosis: diagnosisResult,
        desiredColor: desiredColorResult,
        formula: formulaResult,
        timestamp: new Date().toISOString(),
        scenario: scenario.name,
      };

      console.log(
        `[${scenario.name}] Complete Service Data:`,
        JSON.stringify(serviceData, null, 2)
      );

      // Calculate estimated cost (mock calculation)
      const estimatedCost = calculateMockServiceCost(serviceData);
      expect(estimatedCost).toBeGreaterThan(0);

      console.log(`[${scenario.name}] Estimated Service Cost: $${estimatedCost}`);
    });
  });

  test('should verify detailed logging appears in Edge Function logs', async () => {
    // Wait a moment for logs to be written
    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log('Checking Edge Function logs for detailed diagnostic information...');
    console.log('Expected log entries:');
    console.log('- [DIAGNOSTIC] messages with hairAnalysis details');
    console.log('- Zone analysis (roots, mids, ends)');
    console.log('- Bucket information');
    console.log('- Confidence scores');
    console.log('- Tone and reflection data');

    // Note: In a real implementation, you would use:
    // const logs = await mcp__supabase__get_logs('salonier-assistant');
    // And validate the logs contain the expected diagnostic information

    expect(true).toBe(true); // Placeholder - implement actual log checking
  });
});

// Helper function to calculate mock service cost
function calculateMockServiceCost(serviceData: any): number {
  let baseCost = 50; // Base service cost

  // Add cost based on products needed
  const formulaText = JSON.stringify(serviceData.formula).toLowerCase();

  if (formulaText.includes('bleach')) baseCost += 30;
  if (formulaText.includes('color') || formulaText.includes('tint')) baseCost += 20;
  if (formulaText.includes('toner')) baseCost += 15;
  if (formulaText.includes('treatment')) baseCost += 25;

  // Add complexity cost based on level change
  if (serviceData.desiredColor && serviceData.diagnosis) {
    const levelDifference = Math.abs(
      (serviceData.desiredColor.targetLevel || serviceData.diagnosis.averageLevel) -
        serviceData.diagnosis.averageLevel
    );
    baseCost += levelDifference * 10;
  }

  return Math.round(baseCost * 100) / 100;
}

// Export test utilities for other test files
export { TEST_SCENARIOS, MOCK_IMAGES, calculateMockServiceCost };
