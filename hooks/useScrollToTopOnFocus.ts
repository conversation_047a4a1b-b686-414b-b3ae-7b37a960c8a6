import React from 'react';
import { useFocusEffect } from 'expo-router';
import { ScrollView } from 'react-native';

/**
 * Hook that automatically scrolls to top when a screen receives focus.
 * Useful for ensuring consistent navigation experience across tab screens.
 *
 * @param scrollRef - Reference to the ScrollView component
 */
export const useScrollToTopOnFocus = (scrollRef: React.RefObject<ScrollView>) => {
  useFocusEffect(
    React.useCallback(() => {
      // Scroll to top immediately when screen receives focus
      scrollRef.current?.scrollTo({ y: 0, animated: false });
      return () => {};
    }, [scrollRef])
  );
};
