import { useAuthStore } from '@/stores/auth-store';
import { Permission, PERMISSIONS } from '@/types/permissions';

export function usePermissions() {
  const user = useAuthStore(state => state.user);

  const hasPermission = (permission: Permission): boolean => {
    if (!user) return false;

    // Los owners tienen todos los permisos
    if (user.isOwner) return true;

    // Los empleados verifican sus permisos específicos
    return user.permissions?.includes(permission) || false;
  };

  // Helpers convenientes para uso en componentes
  const can = {
    viewAllClients: hasPermission(PERMISSIONS.VIEW_ALL_CLIENTS),
    viewCosts: hasPermission(PERMISSIONS.VIEW_COSTS),
    modifyPrices: hasPermission(PERMISSIONS.MODIFY_PRICES),
    manageInventory: hasPermission(PERMISSIONS.MANAGE_INVENTORY),
    viewReports: hasPermission(PERMISSIONS.VIEW_REPORTS),
    createUsers: hasPermission(PERMISSIONS.CREATE_USERS),
    deleteData: hasPermission(PERMISSIONS.DELETE_DATA),
  };

  const isOwner = user?.isOwner || false;

  return {
    user,
    hasPermission,
    can,
    isOwner,
  };
}
