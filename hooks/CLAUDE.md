# CLAUDE.md - Custom React Native Hooks

## 🎯 Propósito

Custom hooks para React Native que encapsulan lógica reutilizable: permisos del dispositivo, feedback háptico, gestos, validación inteligente y navegación optimizada. Todos los hooks DEBEN ser puros y sin side effects directos.

## 📁 Hooks Disponibles

### 🛡️ Permisos y Seguridad

- `usePermissions.ts` - Gestión de permisos del dispositivo
- `useSmartValidation.ts` - Validación inteligente con debounce

### 📱 UX y Interacción

- `useHaptics.ts` - Feedback háptico inteligente
- `useGestureHandler.ts` - Gestión de gestos táctiles
- `useSwipeNavigation.ts` - Navegación por gestos

### 🖼️ Media y Análisis

- `usePhotoAnalysis.ts` - Análisis de fotos con AI
- `useRegionalUnits.ts` - Conversión de unidades regionales

### 🔄 Navegación y Performance

- `useScrollToTopOnFocus.ts` - Auto-scroll en focus de pantalla

## 🛡️ Permissions Hook (usePermissions.ts)

### Gestión Centralizada de Permisos

```typescript
import { useState, useEffect, useCallback } from 'react';
import * as MediaLibrary from 'expo-media-library';
import * as Camera from 'expo-camera';
import * as Location from 'expo-location';
import { logger } from '@/utils/logger';

export interface PermissionState {
  camera: 'granted' | 'denied' | 'undetermined';
  mediaLibrary: 'granted' | 'denied' | 'undetermined';
  location: 'granted' | 'denied' | 'undetermined';
}

export interface PermissionActions {
  requestCamera: () => Promise<boolean>;
  requestMediaLibrary: () => Promise<boolean>;
  requestLocation: () => Promise<boolean>;
  requestAllRequired: () => Promise<boolean>;
  checkPermissions: () => Promise<void>;
}

export function usePermissions(): PermissionState & PermissionActions {
  const [permissions, setPermissions] = useState<PermissionState>({
    camera: 'undetermined',
    mediaLibrary: 'undetermined',
    location: 'undetermined',
  });

  // Verificar permisos actuales
  const checkPermissions = useCallback(async () => {
    try {
      const [cameraStatus, mediaStatus, locationStatus] = await Promise.all([
        Camera.getCameraPermissionsAsync(),
        MediaLibrary.getPermissionsAsync(),
        Location.getForegroundPermissionsAsync(),
      ]);

      setPermissions({
        camera: cameraStatus.status,
        mediaLibrary: mediaStatus.status,
        location: locationStatus.status,
      });
    } catch (error) {
      logger.error('Failed to check permissions', { error });
    }
  }, []);

  // Solicitar permiso de cámara
  const requestCamera = useCallback(async (): Promise<boolean> => {
    try {
      const { status } = await Camera.requestCameraPermissionsAsync();

      setPermissions(prev => ({ ...prev, camera: status }));

      if (status !== 'granted') {
        logger.warn('Camera permission denied by user');
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Failed to request camera permission', { error });
      return false;
    }
  }, []);

  // Solicitar permiso de galería
  const requestMediaLibrary = useCallback(async (): Promise<boolean> => {
    try {
      const { status } = await MediaLibrary.requestPermissionsAsync();

      setPermissions(prev => ({ ...prev, mediaLibrary: status }));

      if (status !== 'granted') {
        logger.warn('Media library permission denied by user');
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Failed to request media library permission', { error });
      return false;
    }
  }, []);

  // Solicitar permiso de ubicación
  const requestLocation = useCallback(async (): Promise<boolean> => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();

      setPermissions(prev => ({ ...prev, location: status }));

      if (status !== 'granted') {
        logger.warn('Location permission denied by user');
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Failed to request location permission', { error });
      return false;
    }
  }, []);

  // Solicitar todos los permisos requeridos
  const requestAllRequired = useCallback(async (): Promise<boolean> => {
    const results = await Promise.all([
      requestCamera(),
      requestMediaLibrary(),
      // Location es opcional para esta app
    ]);

    return results.every(result => result === true);
  }, [requestCamera, requestMediaLibrary]);

  // Verificar permisos al montar
  useEffect(() => {
    checkPermissions();
  }, [checkPermissions]);

  return {
    ...permissions,
    requestCamera,
    requestMediaLibrary,
    requestLocation,
    requestAllRequired,
    checkPermissions,
  };
}

// Hook para verificar permiso específico antes de acción
export function useRequirePermission(permission: keyof PermissionState, showAlert: boolean = true) {
  const permissions = usePermissions();

  const executeWithPermission = useCallback(
    async (action: () => void | Promise<void>): Promise<boolean> => {
      const currentStatus = permissions[permission];

      if (currentStatus === 'granted') {
        await action();
        return true;
      }

      // Intentar solicitar permiso
      let granted = false;
      switch (permission) {
        case 'camera':
          granted = await permissions.requestCamera();
          break;
        case 'mediaLibrary':
          granted = await permissions.requestMediaLibrary();
          break;
        case 'location':
          granted = await permissions.requestLocation();
          break;
      }

      if (granted) {
        await action();
        return true;
      }

      if (showAlert) {
        // showPermissionAlert(permission);
      }

      return false;
    },
    [permission, permissions, showAlert]
  );

  return {
    hasPermission: permissions[permission] === 'granted',
    executeWithPermission,
  };
}
```

## 📳 Haptics Hook (useHaptics.ts)

### Feedback Háptico Inteligente

```typescript
import { useCallback } from 'react';
import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';
import { logger } from '@/utils/logger';

export type HapticType =
  | 'light'
  | 'medium'
  | 'heavy'
  | 'success'
  | 'warning'
  | 'error'
  | 'selection';

export interface HapticConfig {
  enabled: boolean;
  intensity: 'light' | 'medium' | 'heavy';
  debounceMs: number;
}

export function useHaptics(config: Partial<HapticConfig> = {}) {
  const { enabled = true, intensity = 'medium', debounceMs = 50 } = config;

  // Cache para debounce
  const lastHapticTime = useRef<Record<HapticType, number>>({});

  const triggerHaptic = useCallback(
    async (type: HapticType, force: boolean = false) => {
      if (!enabled && !force) return;

      // Debounce para evitar haptics excesivos
      const now = Date.now();
      const lastTime = lastHapticTime.current[type] || 0;

      if (now - lastTime < debounceMs && !force) {
        return;
      }

      lastHapticTime.current[type] = now;

      try {
        // Solo en dispositivos físicos
        if (Platform.OS === 'ios' || Platform.OS === 'android') {
          switch (type) {
            case 'light':
              await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              break;

            case 'medium':
              await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
              break;

            case 'heavy':
              await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
              break;

            case 'success':
              await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
              break;

            case 'warning':
              await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
              break;

            case 'error':
              await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
              break;

            case 'selection':
              await Haptics.selectionAsync();
              break;
          }
        }
      } catch (error) {
        logger.error('Haptic feedback failed', { error, type });
      }
    },
    [enabled, debounceMs]
  );

  // Helpers para casos comunes
  const success = useCallback(() => triggerHaptic('success'), [triggerHaptic]);
  const error = useCallback(() => triggerHaptic('error'), [triggerHaptic]);
  const warning = useCallback(() => triggerHaptic('warning'), [triggerHaptic]);
  const selection = useCallback(() => triggerHaptic('selection'), [triggerHaptic]);
  const tap = useCallback(() => triggerHaptic(intensity), [triggerHaptic, intensity]);

  return {
    triggerHaptic,
    success,
    error,
    warning,
    selection,
    tap,
  };
}

// Hook para botones con haptic feedback automático
export function useHapticButton(onPress: () => void, hapticType: HapticType = 'light') {
  const { triggerHaptic } = useHaptics();

  const handlePress = useCallback(() => {
    triggerHaptic(hapticType);
    onPress();
  }, [onPress, triggerHaptic, hapticType]);

  return handlePress;
}
```

## 🖼️ Photo Analysis Hook (usePhotoAnalysis.ts)

### Análisis de Fotos con AI

```typescript
import { useState, useCallback } from 'react';
import { supabase } from '@/lib/supabase';
import { logger } from '@/utils/logger';
import { compressImage } from '@/utils/image-processor';

export interface PhotoAnalysisResult {
  hairAnalysis: HairAnalysis;
  confidence: number;
  processing_time: number;
  warnings: string[];
}

export interface AnalysisOptions {
  maxWidth: number;
  maxHeight: number;
  quality: number;
  includeDetailed: boolean;
}

export function usePhotoAnalysis() {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);

  const analyzePhoto = useCallback(
    async (
      photoUri: string,
      options: Partial<AnalysisOptions> = {}
    ): Promise<PhotoAnalysisResult | null> => {
      const { maxWidth = 1080, maxHeight = 1080, quality = 85, includeDetailed = false } = options;

      setIsAnalyzing(true);
      setProgress(0);
      setError(null);

      try {
        // 1. Comprimir imagen (20%)
        setProgress(20);
        const compressedImage = await compressImage(photoUri, {
          maxWidth,
          maxHeight,
          quality,
        });

        // 2. Convertir a base64 (40%)
        setProgress(40);
        const base64 = await convertToBase64(compressedImage);

        // 3. Enviar a Edge Function (60%)
        setProgress(60);
        const { data, error: analysisError } = await supabase.functions.invoke(
          'salonier-assistant',
          {
            body: {
              type: 'hair_analysis',
              image: base64,
              include_detailed: includeDetailed,
            },
          }
        );

        if (analysisError) {
          throw new Error(analysisError.message);
        }

        // 4. Procesar resultado (100%)
        setProgress(100);

        const result: PhotoAnalysisResult = {
          hairAnalysis: data.analysis,
          confidence: data.confidence,
          processing_time: data.processing_time,
          warnings: data.warnings || [],
        };

        logger.info('Photo analysis completed', {
          confidence: result.confidence,
          processingTime: result.processing_time,
        });

        return result;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Analysis failed';
        setError(errorMessage);
        logger.error('Photo analysis failed', { error });
        return null;
      } finally {
        setIsAnalyzing(false);
        setProgress(0);
      }
    },
    []
  );

  // Análisis por lotes
  const analyzeMultiplePhotos = useCallback(
    async (
      photoUris: string[],
      options: Partial<AnalysisOptions> = {}
    ): Promise<PhotoAnalysisResult[]> => {
      const results: PhotoAnalysisResult[] = [];

      for (let i = 0; i < photoUris.length; i++) {
        const uri = photoUris[i];

        // Update progress basado en foto actual
        setProgress((i / photoUris.length) * 100);

        const result = await analyzePhoto(uri, options);
        if (result) {
          results.push(result);
        }
      }

      return results;
    },
    [analyzePhoto]
  );

  // Validar calidad de foto antes de análisis
  const validatePhotoQuality = useCallback(
    async (photoUri: string): Promise<{ isValid: boolean; issues: string[] }> => {
      const issues: string[] = [];

      try {
        // Verificar resolución mínima
        const imageInfo = await getImageInfo(photoUri);

        if (imageInfo.width < 640 || imageInfo.height < 640) {
          issues.push('Resolución muy baja (mínimo 640x640)');
        }

        if (imageInfo.size > 10 * 1024 * 1024) {
          // 10MB
          issues.push('Archivo muy grande (máximo 10MB)');
        }

        // Verificar iluminación usando histograma básico
        const lightingAnalysis = await analyzeLighting(photoUri);
        if (lightingAnalysis.isDark) {
          issues.push('Imagen muy oscura - usar mejor iluminación');
        }

        if (lightingAnalysis.isOverexposed) {
          issues.push('Imagen sobreexpuesta - reducir luz directa');
        }
      } catch (error) {
        issues.push('Error al validar la imagen');
      }

      return {
        isValid: issues.length === 0,
        issues,
      };
    },
    []
  );

  return {
    analyzePhoto,
    analyzeMultiplePhotos,
    validatePhotoQuality,
    isAnalyzing,
    progress,
    error,
  };
}

// Helper functions
async function convertToBase64(imageUri: string): Promise<string> {
  const response = await fetch(imageUri);
  const blob = await response.blob();

  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const base64 = (reader.result as string).split(',')[1];
      resolve(base64);
    };
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
}

async function getImageInfo(uri: string): Promise<{
  width: number;
  height: number;
  size: number;
}> {
  // Implementation para obtener info de imagen
  // Usando expo-image-manipulator o similar
}

async function analyzeLighting(uri: string): Promise<{
  isDark: boolean;
  isOverexposed: boolean;
  averageBrightness: number;
}> {
  // Implementation básica de análisis de iluminación
  // Usando canvas para analizar histograma
}
```

## 🎯 Smart Validation Hook (useSmartValidation.ts)

### Validación Inteligente con Debounce

```typescript
import { useState, useEffect, useCallback, useRef } from 'react';
import { z } from 'zod';

export interface ValidationRule<T> {
  field: keyof T;
  validator: (value: any) => string | null;
  debounceMs?: number;
  immediate?: boolean;
}

export interface ValidationState<T> {
  errors: Partial<Record<keyof T, string>>;
  isValid: boolean;
  isValidating: boolean;
  hasBeenValidated: boolean;
}

export function useSmartValidation<T extends Record<string, any>>(
  initialData: T,
  rules: ValidationRule<T>[] | z.ZodSchema<T>,
  options: {
    validateOnChange?: boolean;
    validateOnBlur?: boolean;
    debounceMs?: number;
  } = {}
) {
  const { validateOnChange = true, validateOnBlur = true, debounceMs = 300 } = options;

  const [data, setData] = useState<T>(initialData);
  const [validation, setValidation] = useState<ValidationState<T>>({
    errors: {},
    isValid: true,
    isValidating: false,
    hasBeenValidated: false,
  });

  const timeoutRefs = useRef<Record<string, NodeJS.Timeout>>({});

  // Validar usando Zod schema
  const validateWithZod = useCallback((schema: z.ZodSchema<T>, data: T) => {
    try {
      schema.parse(data);
      return { errors: {}, isValid: true };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: Partial<Record<keyof T, string>> = {};

        error.errors.forEach(err => {
          const field = err.path[0] as keyof T;
          errors[field] = err.message;
        });

        return { errors, isValid: false };
      }

      return { errors: {}, isValid: false };
    }
  }, []);

  // Validar usando reglas custom
  const validateWithRules = useCallback((rules: ValidationRule<T>[], data: T) => {
    const errors: Partial<Record<keyof T, string>> = {};

    rules.forEach(rule => {
      const value = data[rule.field];
      const error = rule.validator(value);

      if (error) {
        errors[rule.field] = error;
      }
    });

    return {
      errors,
      isValid: Object.keys(errors).length === 0,
    };
  }, []);

  // Función de validación principal
  const validateData = useCallback(
    (dataToValidate: T) => {
      setValidation(prev => ({ ...prev, isValidating: true }));

      const result = Array.isArray(rules)
        ? validateWithRules(rules, dataToValidate)
        : validateWithZod(rules, dataToValidate);

      setValidation({
        ...result,
        isValidating: false,
        hasBeenValidated: true,
      });

      return result.isValid;
    },
    [rules, validateWithRules, validateWithZod]
  );

  // Validar campo específico
  const validateField = useCallback(
    (field: keyof T, value: any) => {
      if (Array.isArray(rules)) {
        const rule = rules.find(r => r.field === field);
        if (rule) {
          const error = rule.validator(value);

          setValidation(prev => ({
            ...prev,
            errors: {
              ...prev.errors,
              [field]: error || undefined,
            },
            isValid:
              !error &&
              Object.keys({ ...prev.errors, [field]: undefined }).filter(
                k => prev.errors[k as keyof T]
              ).length === 0,
          }));
        }
      } else {
        // Para Zod, validar todo el objeto
        validateData({ ...data, [field]: value });
      }
    },
    [rules, data, validateData]
  );

  // Update con validación
  const updateField = useCallback(
    (field: keyof T, value: any) => {
      const newData = { ...data, [field]: value };
      setData(newData);

      if (validateOnChange) {
        // Cancelar timeout anterior
        if (timeoutRefs.current[field as string]) {
          clearTimeout(timeoutRefs.current[field as string]);
        }

        // Determinar debounce tiempo
        const fieldRule = Array.isArray(rules) ? rules.find(r => r.field === field) : null;

        const fieldDebounce = fieldRule?.debounceMs ?? debounceMs;
        const immediate = fieldRule?.immediate ?? false;

        if (immediate) {
          validateField(field, value);
        } else {
          // Debounce validation
          timeoutRefs.current[field as string] = setTimeout(() => {
            validateField(field, value);
          }, fieldDebounce);
        }
      }
    },
    [data, validateOnChange, validateField, rules, debounceMs]
  );

  // Handler para onBlur
  const handleFieldBlur = useCallback(
    (field: keyof T) => {
      if (validateOnBlur) {
        validateField(field, data[field]);
      }
    },
    [validateOnBlur, validateField, data]
  );

  // Limpiar timeouts
  useEffect(() => {
    return () => {
      Object.values(timeoutRefs.current).forEach(timeout => {
        if (timeout) clearTimeout(timeout);
      });
    };
  }, []);

  return {
    data,
    validation,
    updateField,
    validateData: () => validateData(data),
    validateField,
    handleFieldBlur,
    setData: (newData: T) => {
      setData(newData);
      if (validateOnChange) {
        validateData(newData);
      }
    },
    resetValidation: () => {
      setValidation({
        errors: {},
        isValid: true,
        isValidating: false,
        hasBeenValidated: false,
      });
    },
  };
}

// Hook para validación de formularios
export function useFormValidation<T extends Record<string, any>>(schema: z.ZodSchema<T>) {
  return useSmartValidation({} as T, schema, {
    validateOnChange: true,
    validateOnBlur: true,
    debounceMs: 300,
  });
}
```

## 🔧 Hook Design Patterns

### 1. Principio de Responsabilidad Única

```typescript
// ✅ CORRECTO: Hook con responsabilidad específica
function usePermissions() {
  // Solo maneja permisos
}

// ❌ INCORRECTO: Hook que hace demasiado
function useAppState() {
  // Maneja permisos, validación, analytics, etc.
}
```

### 2. Hooks Puros Sin Side Effects

```typescript
// ✅ CORRECTO: Hook puro
function useCalculation(a: number, b: number) {
  return useMemo(() => a + b, [a, b]);
}

// ❌ INCORRECTO: Hook con side effects
function useCalculationWithSideEffects(a: number, b: number) {
  useEffect(() => {
    console.log('Calculating...'); // Side effect
  }, [a, b]);

  return a + b;
}
```

### 3. Cleanup Apropiado

```typescript
function useTimer() {
  const [time, setTime] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setTime(prev => prev + 1);
    }, 1000);

    // ✅ CRÍTICO: Cleanup
    return () => clearInterval(interval);
  }, []);

  return time;
}
```

## 🤖 Agentes Recomendados

### Para este módulo usar:

**debug-specialist** - Debugging de hooks complejos

- Investigar problemas de memory leaks en useEffect
- Análisis de dependencias incorrectas en hooks
- Debugging de subscriptions y cleanup
- PROACTIVAMENTE usar para errores de hooks

**performance-benchmarker** - Optimización de hooks

- Análisis de re-renders excesivos
- Optimización de custom hooks pesados
- Memory usage en hooks con state complejo
- PROACTIVAMENTE usar cuando detectes lentitud

**frontend-developer** - Implementación de hooks avanzados

- Patterns para hooks reutilizables
- Implementación de hooks complejos con múltiples estados
- Integration de hooks con stores offline-first
- PROACTIVAMENTE usar para nuevos hooks

**test-runner** - Testing de hooks

- Unit tests para custom hooks con React Testing Library
- Integration tests para hooks que usan APIs
- Testing de cleanup y side effects
- PROACTIVAMENTE usar después de implementar hooks

### 💡 Ejemplos de Uso

```bash
# Debug memory leak en usePhotoAnalysis
Task: Use debug-specialist to investigate memory leak in usePhotoAnalysis hook

# Optimizar re-renders en useSmartValidation
Task: Use performance-benchmarker to optimize excessive re-renders in form validation

# Crear hook para gestión de audio
Task: Use frontend-developer to implement useAudioRecording hook with cleanup

# Testear hooks de permisos
Task: Use test-runner to create comprehensive tests for usePermissions hook
```

## 🔌 MCPs Disponibles

### Funciones MCP útiles:

**Para análisis de código:**

- `mcp__serena__get_symbols_overview` - Entender estructura de hooks
- `mcp__serena__find_symbol` - Localizar hooks específicos
- `mcp__serena__find_referencing_symbols` - Ver donde se usan los hooks
- `mcp__serena__search_for_pattern` - Buscar patterns en useEffect

**Para diagnósticos:**

- `mcp__ide__getDiagnostics` - Errores TypeScript en hooks
- `mcp__supabase__get_logs` - Logs de hooks que usan Supabase

**Para documentación:**

- `mcp__context7__resolve_library_id` - Docs de React hooks
- `mcp__context7__get_library_docs` - Documentación de expo-\*

### 📝 Ejemplos MCP

```bash
# Analizar estructura de usePhotoAnalysis
mcp__serena__get_symbols_overview: "hooks/usePhotoAnalysis.ts"

# Encontrar todos los lugares que usan usePermissions
mcp__serena__find_referencing_symbols: "usePermissions" in "hooks/usePermissions.ts"

# Buscar todos los useEffect con problemas de dependencias
mcp__serena__search_for_pattern: "useEffect.*\\[\\]" in "hooks/"

# Obtener docs de React hooks
mcp__context7__get_library_docs: "/facebook/react/hooks"

# Verificar errores TypeScript en hooks
mcp__ide__getDiagnostics: "hooks/"
```

### 🔄 Combinaciones Recomendadas

**Debugging de Performance:**

1. `debug-specialist` + `mcp__ide__getDiagnostics`
2. `performance-benchmarker` + `mcp__serena__search_for_pattern`

**Desarrollo de Nuevos Hooks:**

1. `frontend-developer` + `mcp__context7__get_library_docs`
2. `test-runner` + `mcp__serena__find_referencing_symbols`

**Análisis de Cleanup:**

1. `debug-specialist` + `mcp__serena__search_for_pattern` (buscar `return () =>`)
2. `test-runner` + `mcp__ide__getDiagnostics`

## 🔗 Archivos Relacionados

- `../stores/` - Stores que usan estos hooks
- `../utils/logger.ts` - Sistema de logging
- `../components/` - Componentes que consumen hooks
- `../lib/supabase.ts` - Cliente para hooks que usan datos

---

**⚡ Recuerda:** Los hooks deben ser reutilizables, puros y seguir las reglas de React. Siempre incluir cleanup cuando sea necesario. Usa `debug-specialist` para problemas complejos y `performance-benchmarker` para optimización.
