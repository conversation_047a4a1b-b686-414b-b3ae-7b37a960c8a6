import { useCallback } from 'react';
import { useAIAnalysisStore } from '@/stores/ai-analysis-store';
import { PhotoAnalysisResult } from '@/types/lifestyle-preferences';
import { DesiredPhoto } from '@/types/desired-photo';
import { logger } from '@/utils/logger';

interface UsePhotoAnalysisProps {
  currentLevel?: number;
  onPhotoAnalyzed?: (photoId: string, analysis: PhotoAnalysisResult) => void;
}

/**
 * Hook compartido para análisis de fotos
 * Centraliza la lógica de análisis que se usa en PhotoGallery y DesiredPhotoGallery
 */
export function usePhotoAnalysis({
  currentLevel = 6,
  onPhotoAnalyzed,
}: UsePhotoAnalysisProps = {}) {
  const { analyzeDesiredPhoto, desiredPhotoAnalyses, isAnalyzingDesiredPhoto, analyzingPhotoId } =
    useAIAnalysisStore();

  const analyzePhoto = useCallback(
    async (photo: DesiredPhoto) => {
      try {
        logger.info('Starting photo analysis', 'usePhotoAnalysis', {
          photoId: photo.id,
        });

        const result = await analyzeDesiredPhoto(photo.id, photo.uri, currentLevel);

        if (result && onPhotoAnalyzed) {
          onPhotoAnalyzed(photo.id, result as PhotoAnalysisResult);
        }

        return result;
      } catch (error) {
        logger.error('Error in photo analysis hook:', error);
        throw error;
      }
    },
    [analyzeDesiredPhoto, currentLevel, onPhotoAnalyzed]
  );

  const isAnalyzingPhoto = useCallback(
    (photoId: string) => {
      return analyzingPhotoId === photoId;
    },
    [analyzingPhotoId]
  );

  const getPhotoAnalysis = useCallback(
    (photoId: string) => {
      return desiredPhotoAnalyses[photoId];
    },
    [desiredPhotoAnalyses]
  );

  return {
    analyzePhoto,
    isAnalyzingPhoto,
    getPhotoAnalysis,
    isAnalyzing: isAnalyzingDesiredPhoto,
    analyzingPhotoId,
    analyses: desiredPhotoAnalyses,
  };
}
