import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';

type HapticType = 'light' | 'medium' | 'heavy' | 'success' | 'warning' | 'error' | 'selection';

export const useHaptics = () => {
  // Check if haptics are supported on the current platform
  const isHapticsSupported = Platform.OS === 'ios' || Platform.OS === 'android';

  const impact = async (type: HapticType = 'light') => {
    if (!isHapticsSupported) return;

    try {
      switch (type) {
        case 'light':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          break;
        case 'medium':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          break;
        case 'heavy':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
          break;
        case 'success':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          break;
        case 'warning':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
          break;
        case 'error':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
          break;
        case 'selection':
          await Haptics.selectionAsync();
          break;
        default:
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    } catch {
      // Silently fail if haptics are not available
      // Haptics not available on this device
    }
  };

  // Specific haptic feedback for common actions
  const buttonPress = () => impact('light');
  const toggleSwitch = () => impact('selection');
  const sliderChange = () => impact('selection');
  const success = () => impact('success');
  const warning = () => impact('warning');
  const error = () => impact('error');
  const longPress = () => impact('medium');
  const dragStart = () => impact('light');
  const dragEnd = () => impact('medium');
  const refresh = () => impact('light');

  // Pattern-based haptics for special effects
  const doubleImpact = async (delay = 100) => {
    await impact('light');
    setTimeout(() => impact('light'), delay);
  };

  const successPattern = async () => {
    await impact('light');
    setTimeout(() => impact('success'), 150);
  };

  const errorPattern = async () => {
    await impact('heavy');
    setTimeout(() => impact('error'), 150);
  };

  return {
    impact,
    buttonPress,
    toggleSwitch,
    sliderChange,
    success,
    warning,
    error,
    longPress,
    dragStart,
    dragEnd,
    refresh,
    doubleImpact,
    successPattern,
    errorPattern,
  };
};
