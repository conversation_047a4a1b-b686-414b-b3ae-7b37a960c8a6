import { HairZone } from './hair-diagnosis';
import { LifestylePreferences, PhotoAnalysisResult } from './lifestyle-preferences';

export interface DesiredZoneColorAnalysis {
  zone: HairZone;
  desiredLevel: number;
  desiredTone: string;
  desiredReflect: string;
  coverage?: number; // 0-100%
}

export interface DesiredGeneralAnalysis {
  overallLevel: string;
  overallTone: string;
  technique: string;
  customTechnique?: string;
}

export interface DesiredAdvancedAnalysis {
  contrast: 'subtle' | 'medium' | 'high';
  direction: 'warmer' | 'cooler' | 'neutral';
  graysCoverage: number;
  finalTexture: 'matte' | 'natural' | 'glossy';
  specialNotes?: string;
}

export interface DesiredColorAnalysisResult {
  general: DesiredGeneralAnalysis;
  zones: Record<HairZone, DesiredZoneColorAnalysis>;
  advanced: DesiredAdvancedAnalysis;
  lifestyle?: LifestylePreferences;
  photoAnalyses?: PhotoAnalysisResult[];
  confidence: number;
  isFromAI: boolean;
}

export enum DesiredCaptureStep {
  OVERALL = 'overall',
  ROOTS_DETAIL = 'roots_detail',
  HIGHLIGHTS = 'highlights',
  ENDS_DETAIL = 'ends_detail',
}

export interface DesiredCaptureGuide {
  step: DesiredCaptureStep;
  title: string;
  description: string;
  icon: string;
  tips: string[];
  required: boolean;
}

export const DESIRED_CAPTURE_GUIDES: DesiredCaptureGuide[] = [
  {
    step: DesiredCaptureStep.OVERALL,
    title: 'Color General Deseado',
    description: 'Muestra el look completo que deseas',
    icon: '🎯',
    tips: [
      'Foto completa del estilo deseado',
      'Preferiblemente con luz natural',
      'Incluye toda la melena',
    ],
    required: true,
  },
  {
    step: DesiredCaptureStep.ROOTS_DETAIL,
    title: 'Detalle de Raíces',
    description: 'Color deseado en la zona de raíces',
    icon: '👑',
    tips: [
      'Enfoca en la parte superior',
      'Muestra si quieres raíces oscuras',
      'O si prefieres todo uniforme',
    ],
    required: false,
  },
  {
    step: DesiredCaptureStep.HIGHLIGHTS,
    title: 'Reflejos y Mechas',
    description: 'Detalles de brillos y matices deseados',
    icon: '✨',
    tips: [
      'Captura los reflejos que te gustan',
      'Muestra la distribución deseada',
      'Enfoca en contrastes',
    ],
    required: false,
  },
  {
    step: DesiredCaptureStep.ENDS_DETAIL,
    title: 'Largo y Puntas',
    description: 'Para degradados o efectos en puntas',
    icon: '🔚',
    tips: [
      'Si quieres puntas más claras',
      'O algún efecto degradado',
      'Muestra el resultado final deseado',
    ],
    required: false,
  },
];

export const getContrastText = (contrast: string): string => {
  switch (contrast) {
    case 'subtle':
      return 'Sutil';
    case 'medium':
      return 'Medio';
    case 'high':
      return 'Alto';
    default:
      return 'Medio';
  }
};

export const getDirectionText = (direction: string): string => {
  switch (direction) {
    case 'warmer':
      return 'Más cálido';
    case 'cooler':
      return 'Más frío';
    case 'neutral':
      return 'Neutral';
    default:
      return 'Neutral';
  }
};

export const getTextureText = (texture: string): string => {
  switch (texture) {
    case 'matte':
      return 'Mate';
    case 'natural':
      return 'Natural';
    case 'glossy':
      return 'Brillante';
    default:
      return 'Natural';
  }
};
