// Enums para valores estandarizados del diagnóstico capilar

export enum HairThickness {
  FINE = 'Fino',
  MEDIUM = 'Medio',
  THICK = 'Grueso',
}

export enum HairPorosity {
  LOW = 'Baja',
  MEDIUM = 'Media',
  HIGH = 'Alta',
}

export enum HairDensity {
  LOW = 'Baja',
  MEDIUM = 'Media',
  HIGH = 'Alta',
}

export enum HairElasticity {
  POOR = 'Pobre',
  MEDIUM = 'Media',
  GOOD = 'Buena',
}

export enum HairResistance {
  LOW = 'Baja',
  MEDIUM = 'Media',
  HIGH = 'Alta',
}

// Nuevos enums para colorimetría
export enum NaturalTone {
  BLACK = 'Negro',
  DARK_BROWN = 'Castaño Oscuro',
  MEDIUM_BROWN = 'Castaño Medio',
  LIGHT_BROWN = 'Castaño Claro',
  DARK_BLONDE = 'Rubio Oscuro',
  MEDIUM_BLONDE = 'Rubio Medio',
  LIGHT_BLONDE = 'Rubio Claro',
  VERY_LIGHT_BLONDE = 'Rubio Muy Claro',
  PLATINUM_BLONDE = 'Rubio Platino',
  RED = 'Rojo',
  AUBURN = 'Caoba',
  COPPER_RED = 'Rojo Cobrizo',
}

export enum Undertone {
  ASH = 'Cenizo',
  NATURAL = 'Natural',
  GOLDEN = 'Dorado',
  COPPER = 'Cobrizo',
  REDDISH = 'Rojizo',
  VIOLET = 'Violeta',
  MAHOGANY = 'Caoba',
  BEIGE = 'Beige',
  IRIDESCENT = 'Irisado',
}

// Estado del cabello
export enum HairState {
  NATURAL = 'Natural',
  COLORED = 'Teñido',
  BLEACHED = 'Decolorado',
  HIGHLIGHTED = 'Con mechas',
  PERMED = 'Permanente',
  STRAIGHTENED = 'Alisado',
  MIXED = 'Mixto',
}

// Zona del cabello
export enum HairZone {
  ROOTS = 'ROOTS',
  MIDS = 'MIDS',
  ENDS = 'ENDS',
}

// Mapeo de zonas a nombres en español
export const HairZoneDisplay: Record<HairZone, string> = {
  [HairZone.ROOTS]: 'Raíces',
  [HairZone.MIDS]: 'Medios',
  [HairZone.ENDS]: 'Puntas',
};

// Tipo de análisis
export enum AnalysisType {
  AI = 'AI',
  MANUAL = 'Manual',
}

// Enum para matices no deseados
export enum UnwantedTone {
  ORANGE = 'Naranja',
  YELLOW = 'Amarillo',
  GREEN = 'Verde',
  RED = 'Rojo',
  ASHY_EXCESS = 'Cenizo excesivo',
}

// Enum para tipos de canas
export enum GrayHairType {
  RESISTANT = 'Resistente/Vidriosa',
  NORMAL = 'Normal',
  POROUS = 'Fina/Porosa',
}

// Enum para patrones de canas
export enum GrayPattern {
  SALT_PEPPER = 'Sal y pimienta',
  STREAKS = 'Mechas',
  PATCHES = 'Placas',
}

// Enum para estado de cutícula
export enum CuticleState {
  SMOOTH = 'Lisa',
  ROUGH = 'Áspera',
  DAMAGED = 'Dañada',
}

// Enum para riesgos detectados
export enum HairRisk {
  METAL_SALTS = 'Sales metálicas',
  HENNA = 'Henna',
  EXTREME_DAMAGE = 'Daño extremo',
  FORMALDEHYDE = 'Formol/Alisado',
  INCOMPATIBLE_PRODUCTS = 'Productos incompatibles',
}

// Distribución de canas por zonas
export interface GrayDistribution {
  frontal: number;
  lateral: number;
  crown: number;
  nape: number;
}

// Banda de demarcación
export interface DemarkationBand {
  location: number; // cm desde la raíz
  contrast: 'Bajo' | 'Medio' | 'Alto';
}

// Análisis de color por zona
export interface ZoneColorAnalysis {
  zone: HairZone;
  level: number; // 1-10
  tone: NaturalTone;
  reflect: Undertone;
  state: HairState;
  grayPercentage?: number; // Solo para raíces
  grayDistribution?: GrayDistribution; // Distribución detallada de canas
  grayType?: GrayHairType; // Tipo de canas
  grayPattern?: GrayPattern; // Patrón de canas
  previousChemical?: string; // Proceso químico previo
  damage?: 'Bajo' | 'Medio' | 'Alto';
  unwantedTone?: UnwantedTone; // Matiz no deseado presente
  pigmentAccumulation?: 'Baja' | 'Media' | 'Alta'; // Acumulación de pigmentos artificiales
  cuticleState?: CuticleState; // Estado de la cutícula
}

// Características físicas por zona
export interface ZonePhysicalAnalysis {
  zone: HairZone;
  porosity: HairPorosity;
  elasticity: HairElasticity;
  resistance: HairResistance;
  damage: 'Bajo' | 'Medio' | 'Alto';
}

// Resultado del test de seguridad
export interface SafetyTestResult {
  metalSalts: boolean;
  henna: boolean;
  incompatibleProducts: string[];
  testDate: string;
  notes?: string;
}

// Mediciones físicas del cabello
export interface HairMeasurements {
  totalLength: number; // cm
  demarkationLine?: number; // cm desde raíz
  monthlyGrowth?: number; // cm/mes promedio
  lastTouchUp?: string; // fecha
}

// Interfaz unificada para el diagnóstico capilar
export interface HairDiagnosis {
  // Características físicas generales
  thickness: HairThickness;
  density: HairDensity;

  // Análisis por zonas
  zoneColorAnalysis: ZoneColorAnalysis[];
  zonePhysicalAnalysis: ZonePhysicalAnalysis[];

  // Colorimetría general
  overallTone: NaturalTone;
  overallReflect: Undertone;
  averageLevel: number; // 1-10

  // Bandas de demarcación
  demarkationBands?: DemarkationBand[];

  // Mediciones físicas
  measurements?: HairMeasurements;

  // Información histórica
  lastChemicalProcess?: {
    type: string;
    date: string;
    products?: string[];
  };

  // Seguridad y compatibilidad
  safetyTests?: SafetyTestResult;
  detectedRisks?: HairRisk[];

  // Metadatos
  analysisType: AnalysisType;
  additionalNotes?: string;

  // Recomendaciones generadas
  recommendations?: HairRecommendations;
}

// Interfaz para recomendaciones
export interface HairRecommendations {
  products: string[];
  treatments: string[];
  precautions: string[];
  coloringSuggestions?: ColoringSuggestion[];
}

export interface ColoringSuggestion {
  targetColor: string;
  difficulty: 'Fácil' | 'Moderada' | 'Difícil';
  requiredProcesses: string[];
  estimatedSessions: number;
}

// Helpers para obtener opciones de selección
export const getHairThicknessOptions = () => Object.values(HairThickness);
export const getHairPorosityOptions = () => Object.values(HairPorosity);
export const getHairDensityOptions = () => Object.values(HairDensity);
export const getHairElasticityOptions = () => Object.values(HairElasticity);
export const getHairResistanceOptions = () => Object.values(HairResistance);
export const getNaturalToneOptions = () => Object.values(NaturalTone);
export const getUndertoneOptions = () => Object.values(Undertone);
export const getReflectOptions = () => Object.values(Undertone); // Alias para consistencia
export const getHairStateOptions = () => Object.values(HairState);
export const getHairZoneOptions = () => Object.values(HairZone);
export const getUnwantedToneOptions = () => Object.values(UnwantedTone);
export const getGrayHairTypeOptions = () => Object.values(GrayHairType);
export const getGrayPatternOptions = () => Object.values(GrayPattern);
export const getCuticleStateOptions = () => Object.values(CuticleState);
export const getHairRiskOptions = () => Object.values(HairRisk);
export const getDepthLevels = () => {
  const levels: number[] = [];
  for (let i = 1; i <= 10; i++) {
    for (let j = 0; j < 10; j++) {
      levels.push(Number((i + j * 0.1).toFixed(1)));
    }
  }
  return levels;
};

// Simplified structure for AI responses
export interface SimpleHairDiagnosis {
  // Color básico
  level: number; // 1-10
  tone: string; // "Castaño Oscuro", "Rubio Medio", etc.
  reflect: string; // "Cenizo", "Dorado", "Natural", etc.

  // Estado del cabello
  state: string; // "Natural", "Teñido", "Decolorado", etc.
  damage: string; // "Bajo", "Medio", "Alto"

  // Características físicas
  porosity: string; // "Baja", "Media", "Alta"
  elasticity: string; // "Pobre", "Media", "Buena"

  // Canas (opcional)
  grayPercentage?: number; // 0-100

  // Observaciones adicionales (opcional)
  additionalNotes?: string;
}

// Interface for mapped complex diagnosis result
interface MappedComplexDiagnosis {
  hairThickness: string;
  hairDensity: string;
  overallTone: string;
  overallUndertone: string;
  averageDepthLevel: number;
  zoneAnalysis: {
    roots: Record<string, unknown>;
    mids: Record<string, unknown>;
    ends: Record<string, unknown>;
  };
  detectedChemicalProcess: string;
  estimatedLastProcessDate: string;
  detectedRisks: {
    metallic: boolean;
    henna: boolean;
    damaged: boolean;
    overProcessed: boolean;
    incompatibleProducts: boolean;
  };
  serviceComplexity: string;
  estimatedTime: number;
  overallCondition: string;
  recommendations: string[];
  overallConfidence: number;
}

// Helper function to map simple diagnosis to complex structure
export function mapSimpleDiagnosisToComplex(simple: SimpleHairDiagnosis): MappedComplexDiagnosis {
  // Map state string to enum-like value
  const mapState = (state: string): string => {
    const stateMap: Record<string, string> = {
      natural: 'Natural',
      teñido: 'Teñido',
      decolorado: 'Decolorado',
      procesado: 'Procesado',
      colored: 'Teñido',
      bleached: 'Decolorado',
      processed: 'Procesado',
    };
    return stateMap[state.toLowerCase()] || 'Natural';
  };

  // Map damage level
  const mapDamage = (damage: string): string => {
    const damageMap: Record<string, string> = {
      bajo: 'Leve',
      medio: 'Moderado',
      alto: 'Severo',
      low: 'Leve',
      medium: 'Moderado',
      high: 'Severo',
      none: 'Ninguno',
      ninguno: 'Ninguno',
    };
    return damageMap[damage.toLowerCase()] || 'Leve';
  };

  // Map porosity
  const mapPorosity = (porosity: string): string => {
    const porosityMap: Record<string, string> = {
      baja: 'Baja',
      media: 'Media',
      alta: 'Alta',
      low: 'Baja',
      medium: 'Media',
      high: 'Alta',
    };
    return porosityMap[porosity.toLowerCase()] || 'Media';
  };

  // Map elasticity
  const mapElasticity = (elasticity: string): string => {
    const elasticityMap: Record<string, string> = {
      pobre: 'Mala',
      media: 'Regular',
      buena: 'Buena',
      poor: 'Mala',
      medium: 'Regular',
      good: 'Buena',
      fair: 'Regular',
    };
    return elasticityMap[elasticity.toLowerCase()] || 'Regular';
  };

  // Create zone analysis with simplified data
  const zoneData = {
    level: simple.level,
    tone: simple.tone,
    reflect:
      simple.reflect === 'Cenizo' || simple.reflect === 'Ash'
        ? 'Frío'
        : simple.reflect === 'Dorado' || simple.reflect === 'Golden'
          ? 'Cálido'
          : 'Neutro',
    percentage: 100,
    state: mapState(simple.state),
    unwantedTone: null,
    grayPercentage: simple.grayPercentage || 0,
    grayType: simple.grayPercentage && simple.grayPercentage > 0 ? 'Mixto' : null,
    grayPattern: simple.grayPercentage && simple.grayPercentage > 0 ? 'Disperso' : null,
    cuticleState: simple.damage === 'Alto' || simple.damage === 'High' ? 'Dañada' : 'Abierta',
    damage: mapDamage(simple.damage),
    elasticity: mapElasticity(simple.elasticity),
    porosity: mapPorosity(simple.porosity),
    resistance: simple.damage === 'Alto' || simple.damage === 'High' ? 'Débil' : 'Media',
  };

  return {
    hairThickness: 'Medio',
    hairDensity: 'Media',
    overallTone: simple.tone,
    overallUndertone:
      simple.reflect === 'Cenizo' || simple.reflect === 'Ash'
        ? 'Frío'
        : simple.reflect === 'Dorado' || simple.reflect === 'Golden'
          ? 'Cálido'
          : 'Neutro',
    averageDepthLevel: simple.level,
    zoneAnalysis: {
      roots: { ...zoneData, grayPercentage: simple.grayPercentage || 0 },
      mids: { ...zoneData, grayPercentage: 0 },
      ends: { ...zoneData, grayPercentage: 0 },
    },
    detectedChemicalProcess:
      simple.state === 'Teñido' || simple.state === 'Colored'
        ? 'Coloración'
        : simple.state === 'Decolorado' || simple.state === 'Bleached'
          ? 'Decoloración'
          : 'Ninguno',
    estimatedLastProcessDate: simple.state !== 'Natural' ? '1-3 meses' : 'No aplica',
    detectedRisks: {
      metallic: false,
      henna: false,
      damaged: simple.damage === 'Alto' || simple.damage === 'High',
      overProcessed: false,
      incompatibleProducts: false,
    },
    serviceComplexity: simple.damage === 'Alto' || simple.damage === 'High' ? 'complex' : 'medium',
    estimatedTime: 120,
    overallCondition: simple.additionalNotes || 'Cabello en condición estándar',
    recommendations: [
      simple.damage === 'Alto' || simple.damage === 'High'
        ? 'Tratamiento reconstructor recomendado'
        : 'Mantener hidratación regular',
      simple.porosity === 'Alta' || simple.porosity === 'High'
        ? 'Usar productos de baja alcalinidad'
        : 'Productos estándar apropiados',
      simple.grayPercentage && simple.grayPercentage > 30
        ? 'Fórmula con mayor poder cubriente'
        : 'Fórmula estándar',
    ],
    overallConfidence: 85,
  };
}

// Función para generar recomendaciones basadas en el diagnóstico
export function generateRecommendations(diagnosis: HairDiagnosis): HairRecommendations {
  const recommendations: HairRecommendations = {
    products: [],
    treatments: [],
    precautions: [],
    coloringSuggestions: [],
  };

  // Analizar por zonas
  diagnosis.zonePhysicalAnalysis.forEach(zone => {
    // Recomendaciones basadas en porosidad por zona
    if (zone.porosity === HairPorosity.HIGH) {
      recommendations.products.push(
        `Productos selladores de cutícula para ${zone.zone.toLowerCase()}`
      );
      if (zone.zone === HairZone.ENDS) {
        recommendations.treatments.push('Corte de puntas + tratamiento sellador');
      }
    }

    // Recomendaciones basadas en elasticidad
    if (zone.elasticity === HairElasticity.POOR) {
      recommendations.treatments.push(
        `Tratamiento de reconstrucción en ${zone.zone.toLowerCase()}`
      );
      recommendations.precautions.push(`${zone.zone}: Riesgo de rotura - manejar con cuidado`);
    }

    // Daño alto
    if (zone.damage === 'Alto') {
      recommendations.precautions.push(
        `⚠️ Daño alto en ${zone.zone.toLowerCase()} - evitar procesos químicos agresivos`
      );
    }
  });

  // Análisis de color por zonas
  const rootAnalysis = diagnosis.zoneColorAnalysis.find(z => z.zone === HairZone.ROOTS);
  const hasMultipleTones = new Set(diagnosis.zoneColorAnalysis.map(z => z.level)).size > 1;

  if (hasMultipleTones) {
    recommendations.treatments.push('Igualación de color recomendada antes de nuevo proceso');
  }

  // Canas
  if (rootAnalysis?.grayPercentage && rootAnalysis.grayPercentage > 30) {
    recommendations.products.push('Fórmula con mayor poder cubriente para canas');
    recommendations.precautions.push(
      `${rootAnalysis.grayPercentage}% de canas - ajustar tiempo de procesamiento`
    );
  }

  // Estado del cabello
  const hasChemicalProcess = diagnosis.zoneColorAnalysis.some(z => z.state !== HairState.NATURAL);

  if (hasChemicalProcess) {
    recommendations.treatments.push('Test de mechón obligatorio antes de aplicación completa');

    const bleachedZones = diagnosis.zoneColorAnalysis.filter(z => z.state === HairState.BLEACHED);
    if (bleachedZones.length > 0) {
      recommendations.precautions.push('⚠️ Cabello decolorado presente - extremar precauciones');
      recommendations.products.push('Usar productos de pH balanceado');
    }
  }

  // Recomendaciones basadas en densidad
  if (diagnosis.density === HairDensity.LOW) {
    recommendations.products.push('Productos voluminizadores sin peso');
    recommendations.treatments.push('Evitar productos pesados que aplasten el cabello');
  }

  // Diferencia de niveles
  const depthLevels = diagnosis.zoneColorAnalysis.map(z => z.level);
  const maxDifference = Math.max(...depthLevels) - Math.min(...depthLevels);

  if (maxDifference > 3) {
    recommendations.coloringSuggestions?.push({
      targetColor: 'Igualación de color',
      difficulty: 'Difícil',
      requiredProcesses: ['Pre-pigmentación en zonas claras', 'Aplicación diferenciada por zonas'],
      estimatedSessions: 2,
    });
  }

  // Proceso químico reciente
  if (diagnosis.lastChemicalProcess) {
    const daysSince = Math.floor(
      (Date.now() - new Date(diagnosis.lastChemicalProcess.date).getTime()) / (1000 * 60 * 60 * 24)
    );
    if (daysSince < 14) {
      recommendations.precautions.push(
        `⚠️ Último proceso hace ${daysSince} días - esperar mínimo 2 semanas`
      );
    }
  }

  return recommendations;
}
