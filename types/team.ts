import { Permission } from './permissions';

export interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: 'owner' | 'stylist' | 'Colorista' | 'Asistente' | 'Estilista' | 'Recepcionista' | 'Manager';
  phone?: string;
  licenseNumber?: string;
  specializations?: string[];
  status: 'active' | 'inactive';
  joinedDate: string;
  avatar?: string;
  passwordHash?: string;
  isOwner?: boolean;
  permissions?: Permission[];
  salonId: string;
}

export interface TeamStore {
  members: TeamMember[];
  addMember: (member: Omit<TeamMember, 'id' | 'joinedDate'>) => Promise<void>;
  updateMember: (id: string, updates: Partial<TeamMember>) => Promise<void>;
  removeMember: (id: string) => Promise<void>;
  toggleMemberStatus: (id: string) => Promise<void>;
  getMemberByEmail: (email: string) => TeamMember | undefined;
  getMembersBySalon: (salonId: string) => TeamMember[];
  verifyPassword: (email: string, password: string) => Promise<boolean>;
  hashPassword: (password: string) => Promise<string>;
  updatePassword: (memberId: string, newPassword: string) => Promise<void>;
  syncWithSupabase: () => Promise<void>;
}
