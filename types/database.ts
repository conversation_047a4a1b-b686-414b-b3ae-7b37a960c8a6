export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: '12.2.3 (519615d)';
  };
  public: {
    Tables: {
      ai_analysis_cache: {
        Row: {
          analysis_type: string;
          cost_usd: number | null;
          created_at: string | null;
          expires_at: string | null;
          id: string;
          input_data: Json | null;
          input_hash: string;
          model_used: string | null;
          result: Json;
          salon_id: string;
          tokens_used: number | null;
        };
        Insert: {
          analysis_type: string;
          cost_usd?: number | null;
          created_at?: string | null;
          expires_at?: string | null;
          id?: string;
          input_data?: Json | null;
          input_hash: string;
          model_used?: string | null;
          result: Json;
          salon_id: string;
          tokens_used?: number | null;
        };
        Update: {
          analysis_type?: string;
          cost_usd?: number | null;
          created_at?: string | null;
          expires_at?: string | null;
          id?: string;
          input_data?: Json | null;
          input_hash?: string;
          model_used?: string | null;
          result?: Json;
          salon_id?: string;
          tokens_used?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'ai_analysis_cache_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
        ];
      };
      client_consents: {
        Row: {
          client_id: string;
          completed_steps: Json | null;
          consent_data: Json | null;
          consent_text: string;
          consent_type: string;
          created_at: string | null;
          critical_verifications: Json | null;
          form_version: string | null;
          id: string;
          ip_address: unknown | null;
          language: string | null;
          patch_test_result: string | null;
          safety_checklist: Json | null;
          salon_id: string;
          service_id: string | null;
          signature_data: string | null;
          signature_url: string | null;
          signed_at: string | null;
          skip_safety: boolean | null;
          user_agent: string | null;
        };
        Insert: {
          client_id: string;
          completed_steps?: Json | null;
          consent_data?: Json | null;
          consent_text: string;
          consent_type: string;
          created_at?: string | null;
          critical_verifications?: Json | null;
          form_version?: string | null;
          id?: string;
          ip_address?: unknown | null;
          language?: string | null;
          patch_test_result?: string | null;
          safety_checklist?: Json | null;
          salon_id: string;
          service_id?: string | null;
          signature_data?: string | null;
          signature_url?: string | null;
          signed_at?: string | null;
          skip_safety?: boolean | null;
          user_agent?: string | null;
        };
        Update: {
          client_id?: string;
          completed_steps?: Json | null;
          consent_data?: Json | null;
          consent_text?: string;
          consent_type?: string;
          created_at?: string | null;
          critical_verifications?: Json | null;
          form_version?: string | null;
          id?: string;
          ip_address?: unknown | null;
          language?: string | null;
          patch_test_result?: string | null;
          safety_checklist?: Json | null;
          salon_id?: string;
          service_id?: string | null;
          signature_data?: string | null;
          signature_url?: string | null;
          signed_at?: string | null;
          skip_safety?: boolean | null;
          user_agent?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'client_consents_client_id_fkey';
            columns: ['client_id'];
            isOneToOne: false;
            referencedRelation: 'clients';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'client_consents_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'client_consents_service_id_fkey';
            columns: ['service_id'];
            isOneToOne: false;
            referencedRelation: 'services';
            referencedColumns: ['id'];
          },
        ];
      };
      clients: {
        Row: {
          allergies: string[] | null;
          birth_date: string | null;
          created_at: string | null;
          created_by: string | null;
          current_medications: string | null;
          email: string | null;
          id: string;
          is_vip: boolean | null;
          medical_conditions: string | null;
          name: string;
          notes: string | null;
          phone: string | null;
          salon_id: string;
          tags: string[] | null;
          updated_at: string | null;
        };
        Insert: {
          allergies?: string[] | null;
          birth_date?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          current_medications?: string | null;
          email?: string | null;
          id?: string;
          is_vip?: boolean | null;
          medical_conditions?: string | null;
          name: string;
          notes?: string | null;
          phone?: string | null;
          salon_id: string;
          tags?: string[] | null;
          updated_at?: string | null;
        };
        Update: {
          allergies?: string[] | null;
          birth_date?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          current_medications?: string | null;
          email?: string | null;
          id?: string;
          is_vip?: boolean | null;
          medical_conditions?: string | null;
          name?: string;
          notes?: string | null;
          phone?: string | null;
          salon_id?: string;
          tags?: string[] | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'clients_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'clients_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
        ];
      };
      formulas: {
        Row: {
          brand: string | null;
          created_at: string | null;
          created_by: string | null;
          formula_data: Json;
          formula_text: string;
          id: string;
          line: string | null;
          name: string | null;
          processing_time_minutes: number | null;
          salon_id: string;
          service_id: string | null;
          technique: string | null;
          total_cost: number | null;
        };
        Insert: {
          brand?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          formula_data: Json;
          formula_text: string;
          id?: string;
          line?: string | null;
          name?: string | null;
          processing_time_minutes?: number | null;
          salon_id: string;
          service_id?: string | null;
          technique?: string | null;
          total_cost?: number | null;
        };
        Update: {
          brand?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          formula_data?: Json;
          formula_text?: string;
          id?: string;
          line?: string | null;
          name?: string | null;
          processing_time_minutes?: number | null;
          salon_id?: string;
          service_id?: string | null;
          technique?: string | null;
          total_cost?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'formulas_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'formulas_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'formulas_service_id_fkey';
            columns: ['service_id'];
            isOneToOne: false;
            referencedRelation: 'services';
            referencedColumns: ['id'];
          },
        ];
      };
      products: {
        Row: {
          barcode: string | null;
          brand: string;
          category: string | null;
          color_code: string | null;
          cost_per_unit: number | null;
          created_at: string | null;
          id: string;
          is_active: boolean | null;
          last_purchase_date: string | null;
          line: string | null;
          max_stock: number | null;
          minimum_stock_ml: number | null;
          name: string;
          notes: string | null;
          sale_price: number | null;
          salon_id: string;
          size_ml: number;
          stock_ml: number | null;
          supplier: string | null;
          type: string | null;
          updated_at: string | null;
        };
        Insert: {
          barcode?: string | null;
          brand: string;
          category?: string | null;
          color_code?: string | null;
          cost_per_unit?: number | null;
          created_at?: string | null;
          id?: string;
          is_active?: boolean | null;
          last_purchase_date?: string | null;
          line?: string | null;
          max_stock?: number | null;
          minimum_stock_ml?: number | null;
          name: string;
          notes?: string | null;
          sale_price?: number | null;
          salon_id: string;
          size_ml: number;
          stock_ml?: number | null;
          supplier?: string | null;
          type?: string | null;
          updated_at?: string | null;
        };
        Update: {
          barcode?: string | null;
          brand?: string;
          category?: string | null;
          color_code?: string | null;
          cost_per_unit?: number | null;
          created_at?: string | null;
          id?: string;
          is_active?: boolean | null;
          last_purchase_date?: string | null;
          line?: string | null;
          max_stock?: number | null;
          minimum_stock_ml?: number | null;
          name?: string;
          notes?: string | null;
          sale_price?: number | null;
          salon_id?: string;
          size_ml?: number;
          stock_ml?: number | null;
          supplier?: string | null;
          type?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'products_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
        ];
      };
      profiles: {
        Row: {
          created_at: string | null;
          email: string;
          full_name: string | null;
          id: string;
          is_active: boolean | null;
          permissions: string[] | null;
          role: string | null;
          salon_id: string | null;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          email: string;
          full_name?: string | null;
          id: string;
          is_active?: boolean | null;
          permissions?: string[] | null;
          role?: string | null;
          salon_id?: string | null;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          email?: string;
          full_name?: string | null;
          id?: string;
          is_active?: boolean | null;
          permissions?: string[] | null;
          role?: string | null;
          salon_id?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'profiles_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
        ];
      };
      salons: {
        Row: {
          created_at: string | null;
          id: string;
          name: string;
          owner_id: string | null;
          settings: Json | null;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          id?: string;
          name: string;
          owner_id?: string | null;
          settings?: Json | null;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          id?: string;
          name?: string;
          owner_id?: string | null;
          settings?: Json | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      services: {
        Row: {
          after_photos: string[] | null;
          ai_analysis: Json | null;
          before_photos: string[] | null;
          client_id: string;
          created_at: string | null;
          duration_minutes: number | null;
          id: string;
          notes: string | null;
          price: number | null;
          salon_id: string;
          service_date: string | null;
          service_type: string;
          status: string | null;
          stylist_id: string;
          updated_at: string | null;
        };
        Insert: {
          after_photos?: string[] | null;
          ai_analysis?: Json | null;
          before_photos?: string[] | null;
          client_id: string;
          created_at?: string | null;
          duration_minutes?: number | null;
          id?: string;
          notes?: string | null;
          price?: number | null;
          salon_id: string;
          service_date?: string | null;
          service_type: string;
          status?: string | null;
          stylist_id: string;
          updated_at?: string | null;
        };
        Update: {
          after_photos?: string[] | null;
          ai_analysis?: Json | null;
          before_photos?: string[] | null;
          client_id?: string;
          created_at?: string | null;
          duration_minutes?: number | null;
          id?: string;
          notes?: string | null;
          price?: string | null;
          salon_id?: string;
          service_date?: string | null;
          service_type?: string;
          status?: string | null;
          stylist_id?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'services_client_id_fkey';
            columns: ['client_id'];
            isOneToOne: false;
            referencedRelation: 'clients';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'services_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'services_stylist_id_fkey';
            columns: ['stylist_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      stock_movements: {
        Row: {
          created_at: string | null;
          created_by: string | null;
          id: string;
          notes: string | null;
          product_id: string;
          quantity_ml: number;
          reference_id: string | null;
          reference_type: string | null;
          salon_id: string;
          type: string;
        };
        Insert: {
          created_at?: string | null;
          created_by?: string | null;
          id?: string;
          notes?: string | null;
          product_id: string;
          quantity_ml: number;
          reference_id?: string | null;
          reference_type?: string | null;
          salon_id: string;
          type: string;
        };
        Update: {
          created_at?: string | null;
          created_by?: string | null;
          id?: string;
          notes?: string | null;
          product_id?: string;
          quantity_ml?: number;
          reference_id?: string | null;
          reference_type?: string | null;
          salon_id?: string;
          type?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'stock_movements_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'stock_movements_product_id_fkey';
            columns: ['product_id'];
            isOneToOne: false;
            referencedRelation: 'products';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'stock_movements_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
        ];
      };
      chat_conversations: {
        Row: {
          id: string;
          salon_id: string;
          user_id: string;
          title: string;
          context_type: 'general' | 'client' | 'service' | 'formula' | 'inventory' | null;
          context_id: string | null;
          status: 'active' | 'archived';
          metadata: Json;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          salon_id: string;
          user_id: string;
          title?: string;
          context_type?: 'general' | 'client' | 'service' | 'formula' | 'inventory' | null;
          context_id?: string | null;
          status?: 'active' | 'archived';
          metadata?: Json;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          salon_id?: string;
          user_id?: string;
          title?: string;
          context_type?: 'general' | 'client' | 'service' | 'formula' | 'inventory' | null;
          context_id?: string | null;
          status?: 'active' | 'archived';
          metadata?: Json;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'chat_conversations_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_conversations_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      chat_messages: {
        Row: {
          id: string;
          conversation_id: string;
          role: 'user' | 'assistant' | 'system';
          content: string;
          prompt_tokens: number;
          completion_tokens: number;
          total_tokens: number;
          cost_usd: number;
          metadata: Json;
          created_at: string;
        };
        Insert: {
          id?: string;
          conversation_id: string;
          role: 'user' | 'assistant' | 'system';
          content: string;
          prompt_tokens?: number;
          completion_tokens?: number;
          total_tokens?: number;
          cost_usd?: number;
          metadata?: Json;
          created_at?: string;
        };
        Update: {
          id?: string;
          conversation_id?: string;
          role?: 'user' | 'assistant' | 'system';
          content?: string;
          prompt_tokens?: number;
          completion_tokens?: number;
          total_tokens?: number;
          cost_usd?: number;
          metadata?: Json;
          created_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'chat_messages_conversation_id_fkey';
            columns: ['conversation_id'];
            isOneToOne: false;
            referencedRelation: 'chat_conversations';
            referencedColumns: ['id'];
          },
        ];
      };
      chat_context_references: {
        Row: {
          id: string;
          message_id: string;
          reference_type: 'client' | 'service' | 'formula' | 'product' | 'image';
          reference_id: string;
          reference_data: Json;
          created_at: string;
        };
        Insert: {
          id?: string;
          message_id: string;
          reference_type: 'client' | 'service' | 'formula' | 'product' | 'image';
          reference_id: string;
          reference_data?: Json;
          created_at?: string;
        };
        Update: {
          id?: string;
          message_id?: string;
          reference_type?: 'client' | 'service' | 'formula' | 'product' | 'image';
          reference_id?: string;
          reference_data?: Json;
          created_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'chat_context_references_message_id_fkey';
            columns: ['message_id'];
            isOneToOne: false;
            referencedRelation: 'chat_messages';
            referencedColumns: ['id'];
          },
        ];
      };
    };
    Views: {
      v_salon_products_summary: {
        Row: {
          available_colors: string[] | null;
          brand: string | null;
          category: string | null;
          line: string | null;
          product_count: number | null;
          salon_id: string | null;
          total_stock_ml: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'products_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
        ];
      };
      chat_conversations_with_stats: {
        Row: {
          id: string | null;
          salon_id: string | null;
          user_id: string | null;
          title: string | null;
          context_type: 'general' | 'client' | 'service' | 'formula' | 'inventory' | null;
          context_id: string | null;
          status: 'active' | 'archived' | null;
          metadata: Json | null;
          created_at: string | null;
          updated_at: string | null;
          message_count: number | null;
          last_message_at: string | null;
          total_tokens: number | null;
          total_cost_usd: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'chat_conversations_salon_id_fkey';
            columns: ['salon_id'];
            isOneToOne: false;
            referencedRelation: 'salons';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'chat_conversations_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
    };
    Functions: {
      clean_temp_photos: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      cleanup_expired_ai_cache: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      delete_old_temp_photos: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      get_low_stock_products: {
        Args: { p_salon_id: string };
        Returns: {
          product_id: string;
          brand: string;
          name: string;
          category: string;
          stock_ml: number;
          minimum_stock_ml: number;
          percentage_remaining: number;
          color_code: string;
        }[];
      };
      manual_user_setup: {
        Args: { p_user_id: string } | { user_id: string; user_email: string; user_name: string };
        Returns: Json;
      };
      update_salon_settings: {
        Args: { p_salon_id: string; p_settings: Json };
        Returns: boolean;
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DatabaseWithoutInternals = Omit<Database, '__InternalSupabase'>;

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, 'public'>];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    ? (DefaultSchema['Tables'] & DefaultSchema['Views'])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema['Enums']
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums']
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums'][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema['Enums']
    ? DefaultSchema['Enums'][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema['CompositeTypes']
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes']
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes'][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema['CompositeTypes']
    ? DefaultSchema['CompositeTypes'][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  public: {
    Enums: {},
  },
} as const;
