/**
 * Types for visual formulation system
 * These types structure the parsed formula data for visual components
 */

/**
 * Represents a single ingredient in a formula
 */
export interface FormulaIngredient {
  type: 'color' | 'developer' | 'additive' | 'treatment';
  name: string;
  code?: string; // e.g., "7/1", "6.3"
  amount: number;
  unit: 'g' | 'ml' | 'oz' | 'fl oz' | 'drops' | 'cm';
  percentage?: number; // For visual representation
  icon?: 'tube' | 'bottle' | 'dropper' | 'jar';
  color?: string; // Hex color for visual representation
}

/**
 * Formula for a specific zone of the hair
 */
export interface ZoneFormula {
  zone: 'roots' | 'mids' | 'ends' | 'global';
  title: string;
  ingredients: FormulaIngredient[];
  mixingRatio?: string; // e.g., "1:1.5"
  processingTime?: number; // in minutes
  specialNotes?: string[];
}

/**
 * Represents a step in the application process
 */
export interface ApplicationStep {
  order: number;
  zone: string;
  description: string;
  duration?: number; // in minutes
  technique?: string;
  tips?: string[];
}

/**
 * Timeline block for processing
 */
export interface TimelineBlock {
  id: string;
  label: string;
  startTime: number; // minutes from start
  endTime: number;
  zones: string[];
  active?: boolean;
  description?: string;
}

/**
 * Color transition visualization data
 */
export interface ColorTransition {
  current: {
    level: number;
    tone: string;
    hex?: string;
  };
  target: {
    level: number;
    tone: string;
    hex?: string;
  };
  difficulty: 'easy' | 'moderate' | 'challenging' | 'complex';
  sessions?: number;
}

/**
 * Mixing proportions for visual calculator
 */
export interface MixingProportions {
  ratio: string; // "1:1.5"
  colorAmount: number;
  developerAmount: number;
  totalAmount: number;
  unit: string;
  presets: {
    ratio: string;
    description: string;
    use: string;
  }[];
}

/**
 * Application guide with zones
 */
export interface ApplicationGuide {
  technique: string; // "full_color", "balayage", "highlights"
  zones: {
    id: string;
    name: string;
    order: number;
    color?: string; // for visualization
    description?: string;
  }[];
  totalTime: number;
  steps: ApplicationStep[];
}

/**
 * Contextual tips based on the formula
 */
export interface ContextualTip {
  id: string;
  type: 'warning' | 'info' | 'success' | 'tip';
  icon?: string;
  title: string;
  description: string;
  relatedTo?: string[]; // zones, ingredients, etc.
}

/**
 * Conversion information if formula was converted
 */
export interface ConversionInfo {
  originalBrand: string;
  originalLine: string;
  originalFormula: string;
  targetBrand: string;
  targetLine: string;
  confidence: number;
  adjustments: string[];
  warnings?: string[];
}

/**
 * Complete visual formulation data
 */
export interface VisualFormulationData {
  // Client information
  clientName?: string;
  serviceDate?: string;
  stylistName?: string;
  serviceType?: string;

  // Core formula data
  brand: string;
  line: string;
  zones: ZoneFormula[];

  // Visual components data
  colorTransition: ColorTransition;
  timeline: TimelineBlock[];
  mixingProportions: MixingProportions;
  applicationGuide: ApplicationGuide;

  // Additional information
  tips: ContextualTip[];
  expectedResult?: {
    description: string;
    coverage?: string;
    duration?: string;
    maintenance?: string[];
  };

  // Conversion data if applicable
  conversion?: ConversionInfo;

  // Original text for reference
  originalText: string;

  // Metadata
  confidence: number;
  source: 'mock' | 'openai' | 'manual';
  parseVersion: string;
}

/**
 * Parser configuration options
 */
export interface ParserOptions {
  language?: 'es' | 'en' | 'pt' | 'fr';
  measurementSystem?: 'metric' | 'imperial';
  includeDefaults?: boolean;
  strictMode?: boolean;
}

/**
 * Parser result with potential errors
 */
export interface ParseResult {
  success: boolean;
  data?: VisualFormulationData;
  errors?: string[];
  warnings?: string[];
}
