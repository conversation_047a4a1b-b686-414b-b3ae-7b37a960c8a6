export type CountryCode =
  // Europe
  | 'ES'
  | 'FR'
  | 'IT'
  | 'DE'
  | 'GB'
  | 'PT'
  | 'NL'
  | 'BE'
  | 'CH'
  | 'AT'
  | 'SE'
  | 'NO'
  | 'DK'
  | 'FI'
  | 'PL'
  | 'CZ'
  | 'HU'
  | 'RO'
  | 'GR'
  | 'IE'
  // North America
  | 'US'
  | 'CA'
  | 'MX'
  // Central America
  | 'GT'
  | 'SV'
  | 'HN'
  | 'NI'
  | 'CR'
  | 'PA'
  // Caribbean
  | 'DO'
  | 'PR'
  | 'CU'
  // South America
  | 'AR'
  | 'BR'
  | 'CL'
  | 'CO'
  | 'PE'
  | 'VE'
  | 'EC'
  | 'BO'
  | 'PY'
  | 'UY';

export type Region = 'Europe' | 'North America' | 'Central America' | 'Caribbean' | 'South America';

export type MeasurementSystem = 'metric' | 'imperial';

export type Currency =
  | 'EUR'
  | 'USD'
  | 'GBP'
  | 'MXN'
  | 'ARS'
  | 'BRL'
  | 'CLP'
  | 'COP'
  | 'PEN'
  | 'VEF'
  | 'BOB'
  | 'PYG'
  | 'UYU'
  | 'CAD'
  | 'CHF'
  | 'SEK'
  | 'NOK'
  | 'DKK'
  | 'PLN'
  | 'CZK'
  | 'HUF'
  | 'RON';

export type Language =
  | 'es'
  | 'en'
  | 'pt'
  | 'fr'
  | 'de'
  | 'it'
  | 'nl'
  | 'sv'
  | 'no'
  | 'da'
  | 'fi'
  | 'pl'
  | 'cs'
  | 'hu'
  | 'ro'
  | 'el';

export type DateFormat = 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY-MM-DD';

export type TimeFormat = '12h' | '24h';

export type DecimalSeparator = '.' | ',';

export type ThousandsSeparator = ',' | '.' | ' ' | "'";

export interface RegionalConfig {
  countryCode: CountryCode;
  countryName: string;
  region: Region;
  measurementSystem: MeasurementSystem;
  currency: Currency;
  currencySymbol: string;
  language: Language;
  dateFormat: DateFormat;
  timeFormat: TimeFormat;
  decimalSeparator: DecimalSeparator;
  thousandsSeparator: ThousandsSeparator;
  // Hair industry specific
  volumeUnit: 'ml' | 'fl oz';
  weightUnit: 'g' | 'oz';
  developerTerminology:
    | 'oxidante'
    | 'peróxido'
    | 'developer'
    | 'oxidant'
    | 'révélateur'
    | 'ossidante';
  colorTerminology: 'tinte' | 'color' | 'coloración' | 'dye' | 'tinta' | 'couleur' | 'farbe';
  // Regulatory
  requiresAllergyTest: boolean;
  maxDeveloperVolume: number; // Some countries limit to 30vol, others allow 40vol
}

export interface CountryInfo {
  code: CountryCode;
  name: string;
  localName: string;
  flag: string; // emoji flag
  config: RegionalConfig;
}
