# CLAUDE.md - TypeScript Types & Interfaces

## 🎯 Propósito

Sistema de tipos TypeScript que define la estructura de datos del dominio: coloración capilar, inventario, servicios, formulación y análisis AI. Los tipos DEBEN estar sincronizados con `database.types.ts` y seguir convenciones estrictas de naming.

## 📁 Tipos Disponibles

### 🗄️ Base de Datos

- `database.ts` - Tipos generados automáticamente desde Supabase
- `formulation.ts` - Tipos de fórmulas y procesos químicos
- `inventory.ts` - Tipos de productos e inventario

### 👤 Usuarios y Servicios

- `hair-diagnosis.ts` - Diagnóstico y análisis capilar
- `desired-analysis.ts` - Análisis de color deseado
- `photo-capture.ts` - Captura y procesamiento de fotos
- `team.ts` - Gestión de equipo y permisos

### 🌍 Configuración Regional

- `regional.ts` - Configuración por país/región
- `lifestyle-preferences.ts` - Preferencias del cliente
- `permissions.ts` - Sistema de permisos y roles
- `visual-formulation.ts` - Formulación visual e interactiva

### 🔄 Estado y Datos

- `desired-photo.ts` - Fotos de referencia deseadas

## 🗄️ Database Types (database.ts)

### Sincronización Automática

```typescript
// GENERADO AUTOMÁTICAMENTE - NO EDITAR MANUALMENTE
// Comando: npx supabase gen types typescript --local > types/database.ts

export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          salon_id: string;
          name: string;
          email: string;
          role: 'owner' | 'admin' | 'stylist' | 'assistant';
          permissions: string[] | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          salon_id: string;
          name: string;
          email: string;
          role?: 'owner' | 'admin' | 'stylist' | 'assistant';
          permissions?: string[] | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          name?: string;
          email?: string;
          role?: 'owner' | 'admin' | 'stylist' | 'assistant';
          permissions?: string[] | null;
          updated_at?: string;
        };
      };
      // ... más tablas
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}

// Tipos helper derivados
export type Profile = Database['public']['Tables']['profiles']['Row'];
export type ProfileInsert = Database['public']['Tables']['profiles']['Insert'];
export type ProfileUpdate = Database['public']['Tables']['profiles']['Update'];
```

### Mantenimiento de Tipos

```bash
# Regenerar tipos después de cambios en schema
npx supabase gen types typescript --local > types/database.ts

# Verificar tipos en CI/CD
npm run typecheck
```

## ⚗️ Formulation Types (formulation.ts)

### Tipos de Fórmula

```typescript
export interface ColorFormula {
  id: string;
  serviceId: string;
  salonId: string;

  // Componentes de color
  colors: ColorComponent[];
  developer: DeveloperComponent;
  additives?: AdditiveComponent[];

  // Instrucciones de proceso
  instructions: ProcessingInstructions;
  processingTime: number; // minutos
  temperature?: number; // celsius

  // Metadatos
  confidence: number; // 0-100
  createdBy: 'ai' | 'manual' | 'hybrid';
  version: number;

  // Auditoría
  createdAt: string;
  updatedAt: string;
}

export interface ColorComponent {
  id: string;
  brand: string;
  line: string;
  shade: string;
  quantity: number; // ml
  percentage: number; // % del total de color

  // Mapeo con inventario
  inventoryProductId?: string;
  mappingConfidence?: number; // 0-100

  // Propiedades químicas
  ph?: number;
  ammonia?: boolean;
  hydrogen_peroxide?: number; // %
}

export interface DeveloperComponent {
  id: string;
  brand: string;
  volume: number; // 10, 20, 30, 40 vol
  quantity: number; // ml
  percentage: number; // % del total

  // Inventario
  inventoryProductId?: string;

  // Propiedades
  hydrogen_peroxide: number; // %
  stabilizers: string[];
}

export interface ProcessingInstructions {
  mixing: MixingStep[];
  application: ApplicationStep[];
  processing: ProcessingStep[];
  rinsing: RinsingStep[];
}

export interface MixingStep {
  order: number;
  description: string;
  duration: number; // segundos
  tools: string[];
  warnings?: string[];
}

export interface ApplicationStep {
  zone: HairZone;
  pattern: ApplicationPattern;
  technique: string;
  duration: number; // minutos
  thickness: 'thin' | 'medium' | 'thick';
}

export enum HairZone {
  ROOTS = 'roots',
  MIDLENGTHS = 'midlengths',
  ENDS = 'ends',
  FULL = 'full',
  HIGHLIGHTS = 'highlights',
  LOWLIGHTS = 'lowlights',
}

export enum ApplicationPattern {
  SECTIONING = 'sectioning',
  WEAVING = 'weaving',
  BALAYAGE = 'balayage',
  FULL_HEAD = 'full_head',
  ROOT_TOUCH_UP = 'root_touch_up',
}

// Conversión entre marcas
export interface BrandConversion {
  originalBrand: string;
  originalLine: string;
  originalShade: string;

  targetBrand: string;
  targetLine: string;
  targetShade: string;

  adjustments: ConversionAdjustments;
  confidence: number;
  warnings: string[];
}

export interface ConversionAdjustments {
  toneMapping: string; // "7/1 → 7.13"
  mixRatio: string; // "1:1 → 1:1.5"
  processingTime: number; // diferencia en minutos
  additionalNotes: string[];
}
```

## 💇‍♀️ Hair Diagnosis Types (hair-diagnosis.ts)

### Análisis Capilar

```typescript
export interface HairDiagnosis {
  id: string;
  clientId: string;
  serviceId: string;

  // Estado actual
  baseColor: HairColor;
  condition: HairCondition;
  texture: HairTexture;
  density: HairDensity;
  porosity: HairPorosity;
  elasticity: HairElasticity;

  // Historial de procesamiento
  previouslyColored: boolean;
  lastProcessDate?: string;
  lastProcessType?: ProcessType;
  previousColors?: HairColor[];

  // Análisis AI
  aiAnalysis: AIHairAnalysis;
  photos: DiagnosisPhoto[];

  // Metadatos
  analyzedBy: 'ai' | 'professional' | 'hybrid';
  confidence: number;
  createdAt: string;
}

export interface HairColor {
  level: number; // 1-10 (1=black, 10=lightest blonde)
  tone: string; // natural, ash, golden, red, etc.

  // Representación visual
  hex: string;
  rgb: { r: number; g: number; b: number };
  hsl: { h: number; s: number; l: number };

  // Análisis técnico
  underlyingPigments: string[];
  dominantTone: string;
  neutralizationNeeded?: string[];
}

export interface HairCondition {
  overall: 'excellent' | 'good' | 'fair' | 'poor' | 'damaged';
  damage_level: number; // 0-10
  specific_issues: HairIssue[];
  recommendations: string[];
}

export enum HairIssue {
  DRYNESS = 'dryness',
  BREAKAGE = 'breakage',
  SPLIT_ENDS = 'split_ends',
  LACK_OF_SHINE = 'lack_of_shine',
  FRIZZ = 'frizz',
  OVER_PROCESSED = 'over_processed',
  CHEMICAL_DAMAGE = 'chemical_damage',
  HEAT_DAMAGE = 'heat_damage',
}

export interface AIHairAnalysis {
  detected_color: HairColor;
  condition_assessment: HairCondition;
  porosity_analysis: PorosityAnalysis;
  texture_analysis: TextureAnalysis;

  // Confianza por aspecto
  color_confidence: number;
  condition_confidence: number;
  porosity_confidence: number;

  // Regiones analizadas
  analyzed_regions: AnalyzedRegion[];

  // Warnings del AI
  analysis_warnings: string[];
  quality_issues: PhotoQualityIssue[];
}

export interface AnalyzedRegion {
  zone: HairZone;
  bounding_box: { x: number; y: number; width: number; height: number };
  color: HairColor;
  condition: string;
  notes: string[];
}

export enum PhotoQualityIssue {
  LOW_LIGHT = 'low_light',
  BLURRY = 'blurry',
  POOR_ANGLE = 'poor_angle',
  HAIR_NOT_VISIBLE = 'hair_not_visible',
  MULTIPLE_COLORS = 'multiple_colors',
  SHADOWS = 'shadows',
}
```

## 📦 Inventory Types (inventory.ts)

### Sistema de Inventario

```typescript
export interface Product {
  id: string;
  salon_id: string;

  // Jerarquía estructurada
  brand: string;
  line: string;
  type: ProductType;
  shade?: string;

  // Información básica
  name: string;
  description?: string;
  sku?: string;
  barcode?: string;

  // Inventario
  stock_ml: number;
  minimum_stock_ml: number;
  unit_cost: number;
  selling_price: number;
  currency: string;

  // Propiedades químicas
  ph?: number;
  volume_strength?: number; // para developers
  ingredients: string[];
  allergens: string[];

  // Metadatos
  is_active: boolean;
  created_at: string;
  updated_at: string;
  created_by: string;
}

export enum ProductType {
  COLOR = 'color',
  DEVELOPER = 'developer',
  BLEACH = 'bleach',
  TONER = 'toner',
  TREATMENT = 'treatment',
  ADDITIVE = 'additive',
  PRE_PIGMENT = 'pre_pigment',
  OTHER = 'other',
}

export interface StockMovement {
  id: string;
  salon_id: string;
  product_id: string;

  // Movimiento
  type: MovementType;
  quantity: number; // puede ser negativo para salidas
  reason: string;
  reference_id?: string; // ID del servicio si es consumo

  // Metadatos
  created_at: string;
  created_by: string;
  notes?: string;
}

export enum MovementType {
  IN = 'in', // Entrada (compra, devolución)
  OUT = 'out', // Salida manual
  ADJUSTMENT = 'adjustment', // Ajuste de inventario
  CONSUMPTION = 'consumption', // Consumo en servicio
  WASTE = 'waste', // Desperdicio
  TRANSFER = 'transfer', // Transferencia entre salones
}

export interface ProductMapping {
  id: string;
  ai_product_name: string;
  ai_brand: string;
  ai_category: string;

  inventory_product_id: string;
  mapping_confidence: number;

  // Metadatos
  created_at: string;
  created_by: string;
  validated: boolean;
  validation_date?: string;
}

export interface InventoryAlert {
  id: string;
  salon_id: string;
  product_id: string;

  type: AlertType;
  severity: AlertSeverity;
  message: string;

  acknowledged: boolean;
  acknowledged_by?: string;
  acknowledged_at?: string;

  created_at: string;
}

export enum AlertType {
  LOW_STOCK = 'low_stock',
  OUT_OF_STOCK = 'out_of_stock',
  EXPIRED = 'expired',
  NEAR_EXPIRY = 'near_expiry',
  NEGATIVE_STOCK = 'negative_stock',
}

export enum AlertSeverity {
  INFO = 'info',
  WARNING = 'warning',
  CRITICAL = 'critical',
}
```

## 🎯 Desired Analysis Types (desired-analysis.ts)

### Análisis del Objetivo

```typescript
export interface DesiredColorAnalysis {
  id: string;
  service_id: string;

  // Color objetivo
  target_color: HairColor;
  reference_photos: DesiredPhoto[];
  client_description: string;

  // Análisis de viabilidad
  viability: ViabilityAnalysis;
  technique_recommendation: TechniqueRecommendation;

  // Consideraciones especiales
  lifestyle_factors: LifestyleFactor[];
  maintenance_requirements: MaintenanceRequirements;

  // Metadatos
  analyzed_by: 'client' | 'professional' | 'ai';
  created_at: string;
}

export interface ViabilityAnalysis {
  is_viable: boolean;
  confidence: number;
  difficulty: DifficultyLevel;

  required_sessions: number;
  estimated_time_per_session: number; // minutos
  total_cost_estimate: number;

  risk_level: RiskLevel;
  risk_factors: RiskFactor[];
  warnings: string[];
  recommendations: string[];

  alternative_options?: ColorOption[];
}

export enum DifficultyLevel {
  EASY = 'easy', // 1 sesión, proceso simple
  MEDIUM = 'medium', // 1-2 sesiones, proceso moderado
  HARD = 'hard', // 2-3 sesiones, proceso complejo
  EXPERT = 'expert', // 3+ sesiones, requiere experiencia avanzada
}

export enum RiskLevel {
  LOW = 'low', // Riesgo mínimo
  MEDIUM = 'medium', // Riesgo moderado, precauciones estándar
  HIGH = 'high', // Alto riesgo, requiere experiencia y precauciones extra
}

export interface RiskFactor {
  type: RiskType;
  severity: RiskLevel;
  description: string;
  mitigation: string;
}

export enum RiskType {
  HAIR_DAMAGE = 'hair_damage',
  ALLERGIC_REACTION = 'allergic_reaction',
  UNPREDICTABLE_RESULT = 'unpredictable_result',
  OVER_PROCESSING = 'over_processing',
  COLOR_FADE = 'color_fade',
  MAINTENANCE_INTENSIVE = 'maintenance_intensive',
}

export interface TechniqueRecommendation {
  primary_technique: ColoringTechnique;
  alternative_techniques: ColoringTechnique[];

  application_method: ApplicationMethod;
  processing_conditions: ProcessingConditions;

  reasoning: string;
  expected_result: ExpectedResult;
}

export enum ColoringTechnique {
  SINGLE_PROCESS = 'single_process',
  DOUBLE_PROCESS = 'double_process',
  HIGHLIGHTS = 'highlights',
  LOWLIGHTS = 'lowlights',
  BALAYAGE = 'balayage',
  OMBRE = 'ombre',
  COLOR_CORRECTION = 'color_correction',
  ROOT_TOUCH_UP = 'root_touch_up',
  FASHION_COLOR = 'fashion_color',
}

export interface ColorOption {
  color: HairColor;
  description: string;
  difficulty: DifficultyLevel;
  sessions_required: number;
  estimated_cost: number;
  pros: string[];
  cons: string[];
}
```

## 🔧 Type Utilities y Patterns

### Utility Types

```typescript
// Helper types para trabajar con la DB
export type WithSalonId<T> = T & { salon_id: string };
export type WithTimestamps<T> = T & {
  created_at: string;
  updated_at: string;
};
export type WithAudit<T> = WithTimestamps<T> & {
  created_by: string;
  updated_by?: string;
};

// Types para API responses
export type ApiResponse<T> = {
  data: T | null;
  error: string | null;
  success: boolean;
};

export type PaginatedResponse<T> = ApiResponse<{
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}>;

// Types para stores
export type LoadingState = {
  isLoading: boolean;
  isInitialized: boolean;
  lastSync: string | null;
  error: string | null;
};

export type AsyncAction<T> = {
  pending: boolean;
  error: string | null;
  lastAttempt: string | null;
  data?: T;
};
```

### Type Guards

```typescript
// Type guards para runtime validation
export function isColorFormula(value: any): value is ColorFormula {
  return (
    typeof value === 'object' &&
    value !== null &&
    typeof value.id === 'string' &&
    Array.isArray(value.colors) &&
    typeof value.developer === 'object'
  );
}

export function isHairColor(value: any): value is HairColor {
  return (
    typeof value === 'object' &&
    value !== null &&
    typeof value.level === 'number' &&
    value.level >= 1 &&
    value.level <= 10 &&
    typeof value.tone === 'string'
  );
}

export function isProduct(value: any): value is Product {
  return (
    typeof value === 'object' &&
    value !== null &&
    typeof value.id === 'string' &&
    typeof value.salon_id === 'string' &&
    typeof value.brand === 'string' &&
    Object.values(ProductType).includes(value.type)
  );
}
```

### Discriminated Unions

```typescript
// Usar discriminated unions para states complejos
export type ServiceState =
  | { status: 'draft'; data: Partial<ServiceData> }
  | { status: 'in_progress'; data: ServiceData; currentStep: string }
  | { status: 'completed'; data: ServiceData; completedAt: string }
  | { status: 'cancelled'; data: Partial<ServiceData>; reason: string };

export type ValidationResult = { isValid: true; data: any } | { isValid: false; errors: string[] };

export type AnalysisResult =
  | { success: true; analysis: HairAnalysis; confidence: number }
  | { success: false; error: string; retry: boolean };
```

## 📊 Naming Conventions

### Convenciones Obligatorias

```typescript
// ✅ CORRECTO: Naming conventions
interface ProductInventory {} // PascalCase para interfaces
type UserRole = 'admin' | 'user'; // PascalCase para types
enum ProductType {} // PascalCase para enums
const PRODUCT_TYPES = {}; // SCREAMING_SNAKE_CASE para constantes

// Propiedades en snake_case para DB compatibility
interface DatabaseEntity {
  salon_id: string; // ✅ snake_case
  created_at: string; // ✅ snake_case
  updated_by: string; // ✅ snake_case
}

// Propiedades en camelCase para app logic
interface AppEntity {
  salonId: string; // ✅ camelCase
  createdAt: string; // ✅ camelCase
  updatedBy: string; // ✅ camelCase
}

// ❌ INCORRECTO: Inconsistent naming
interface badNaming {
  // Debe ser PascalCase
  SalonId: string; // Inconsistente
  created_At: string; // Inconsistente
}
```

### Mapeo Database ↔ App

```typescript
// Función helper para mapear entre formatos
export function dbToApp<T extends Record<string, any>>(dbEntity: T): CamelCaseKeys<T> {
  // Convertir snake_case a camelCase
  return mapKeys(dbEntity, (_, key) => camelCase(key)) as CamelCaseKeys<T>;
}

export function appToDb<T extends Record<string, any>>(appEntity: T): SnakeCaseKeys<T> {
  // Convertir camelCase a snake_case
  return mapKeys(appEntity, (_, key) => snakeCase(key)) as SnakeCaseKeys<T>;
}
```

## 🤖 Agentes Recomendados

### Para este módulo usar:

**debug-specialist** - Debugging de errores de tipos

- Root cause analysis de errores TypeScript complejos
- Debugging de type errors en builds
- Análisis de type inference issues
- PROACTIVAMENTE usar para errores de compilación

**database-architect** - Sincronización de tipos con DB

- Regeneración de tipos después de migrations
- Validación de tipos vs schema de DB
- Optimización de tipos para performance
- PROACTIVAMENTE usar después de cambios de schema

**frontend-developer** - Implementación de tipos complejos

- Utility types y patterns avanzados
- Type guards y runtime validation
- Integration de tipos con stores y componentes
- PROACTIVAMENTE usar para nuevos tipos

**security-privacy-auditor** - Auditoría de tipos sensibles

- Validar que datos sensibles no se expongan en tipos
- Revisar tipos para compliance (GDPR/CCPA)
- Auditar types de autenticación
- Usar antes de releases con cambios de auth

### 💡 Ejemplos de Uso

```bash
# Debug error complejo de tipos
Task: Use debug-specialist to investigate "Type instantiation is excessively deep" error in formulation types

# Regenerar tipos después de migración
Task: Use database-architect to regenerate TypeScript types after products table migration

# Implementar utility types para forms
Task: Use frontend-developer to create utility types for form validation patterns

# Auditar tipos de datos sensibles
Task: Use security-privacy-auditor to review types for potential data exposure issues
```

## 🔌 MCPs Disponibles

### Funciones MCP útiles:

**Para gestión de tipos de DB:**

- `mcp__supabase__generate_typescript_types` - Regenerar tipos automáticamente
- `mcp__supabase__list_tables` - Verificar schema para tipos
- `mcp__supabase__get_advisors` - Validaciones de schema

**Para análisis de código:**

- `mcp__serena__get_symbols_overview` - Estructura de archivos de tipos
- `mcp__serena__find_symbol` - Localizar tipos específicos
- `mcp__serena__find_referencing_symbols` - Ver dónde se usan tipos
- `mcp__serena__search_for_pattern` - Buscar patterns en tipos

**Para diagnósticos:**

- `mcp__ide__getDiagnostics` - Errores TypeScript en tiempo real
- `mcp__serena__write_memory` - Documentar decisiones de tipos

### 📝 Ejemplos MCP

```bash
# Regenerar tipos después de cambios DB
mcp__supabase__generate_typescript_types
mcp__supabase__list_tables
mcp__supabase__get_advisors: "performance"

# Analizar estructura de tipos
mcp__serena__get_symbols_overview: "types/formulation.ts"
mcp__serena__find_symbol: "ColorFormula"

# Encontrar todos los usos de HairColor
mcp__serena__find_referencing_symbols: "HairColor" in "types/hair-diagnosis.ts"

# Buscar todos los interfaces en tipos
mcp__serena__search_for_pattern: "interface\\s+\\w+" in "types/"

# Verificar errores TypeScript
mcp__ide__getDiagnostics: "types/"

# Documentar decisiones de tipos
mcp__serena__write_memory: "type-patterns" "Patterns de tipos para colorimetría y formulación"
```

### 🔄 Combinaciones Recomendadas

**Sincronización DB-Types:**

1. `database-architect` + `mcp__supabase__generate_typescript_types`
2. `debug-specialist` + `mcp__ide__getDiagnostics`

**Desarrollo de Tipos:**

1. `frontend-developer` + `mcp__serena__find_referencing_symbols`
2. `debug-specialist` + `mcp__serena__search_for_pattern`

**Security Audit:**

1. `security-privacy-auditor` + `mcp__serena__search_for_pattern`
2. `database-architect` + `mcp__supabase__list_tables`

## 📊 Patterns de Tipos con Agentes

### 🔄 DB Sync Pattern

```typescript
// Usar database-architect para mantener sincronizado
// 1. Después de cambios de schema
// mcp__supabase__generate_typescript_types

// 2. Mapeo automático DB → App types
export type Product = Database['public']['Tables']['products']['Row'];
export type ProductInsert = Database['public']['Tables']['products']['Insert'];
export type ProductUpdate = Database['public']['Tables']['products']['Update'];

// 3. Validation con database-architect
const validateDbTypes = (): void => {
  // Verificar que tipos coinciden con schema
};
```

### 🛡️ Security-Conscious Types Pattern

```typescript
// Usar security-privacy-auditor para validar
// 1. Tipos que NUNCA deben persistir
interface SensitiveData {
  socialSecurityNumber?: never; // ❌ Never persist
  password?: never; // ❌ Never persist
  apiKey?: never; // ❌ Never persist
}

// 2. Tipos con filtro de salón obligatorio
export type WithSalonFilter<T> = T & {
  salon_id: string; // ✅ Always require salon filtering
};

// 3. Anonymized types para exports
export type AnonymizedClient = Omit<Client, 'personalNotes' | 'medicalConditions'>;
```

### 🔍 Type Guard Pattern

```typescript
// Usar frontend-developer para implementar
// 1. Runtime type validation
export function isColorFormula(value: unknown): value is ColorFormula {
  return (
    typeof value === 'object' &&
    value !== null &&
    'colors' in value &&
    'developer' in value &&
    Array.isArray((value as any).colors)
  );
}

// 2. Type assertion con validation
export function assertColorFormula(value: unknown): asserts value is ColorFormula {
  if (!isColorFormula(value)) {
    throw new Error('Invalid ColorFormula structure');
  }
}

// 3. Safe type conversion
export function safeParseFormula(data: unknown): ColorFormula | null {
  try {
    if (isColorFormula(data)) return data;
    return null;
  } catch {
    return null;
  }
}
```

### ⚡ Performance-Optimized Types Pattern

```typescript
// Usar frontend-developer para optimizar
// 1. Lazy-loaded types para components grandes
export type LazyProductType = () => Promise<Product>;

// 2. Memoized computed types
export type MemoizedColorAnalysis = {
  readonly baseColor: HairColor;
  readonly confidence: number;
  readonly timestamp: number;
};

// 3. Indexed types para fast lookups
export type ProductIndex = Record<string, Product>;
export type BrandProductMap = Record<string, ProductIndex>;
```

### 🧪 Testing Types Pattern

```typescript
// Usar debug-specialist para testing
// 1. Mock types para testing
export type MockProduct = Partial<Product> & {
  id: string;
  salon_id: string;
  name: string;
};

// 2. Factory functions para tests
export const createMockFormula = (overrides?: Partial<ColorFormula>): ColorFormula => ({
  id: 'test-id',
  colors: [],
  developer: { volume: 20, quantity: 30 },
  processingTime: 35,
  ...overrides,
});

// 3. Type assertion helpers para tests
export const expectValidFormula = (formula: unknown): formula is ColorFormula => {
  expect(formula).toBeDefined();
  expect(isColorFormula(formula)).toBe(true);
  return true;
};
```

## 🔗 Archivos Relacionados

- `../lib/database.types.ts` - Tipos generados de Supabase
- `../stores/` - Stores que usan estos tipos
- `../utils/` - Utilidades que implementan estos tipos
- `../supabase/migrations/` - Schema que define estos tipos

## 📚 Comandos Útiles

```bash
# Verificar tipos
npm run typecheck

# Regenerar tipos de DB (o usar MCP)
npx supabase gen types typescript --local > types/database.ts
# Alternativa: mcp__supabase__generate_typescript_types

# Verificar unused types
npx ts-unused-exports tsconfig.json

# Diagnósticos en tiempo real (MCP)
# mcp__ide__getDiagnostics
```

---

**⚡ Recuerda:** Los tipos son el contrato del sistema. Mantenerlos actualizados y consistentes es crítico para la estabilidad del código. Usa `database-architect` para sincronización con DB y `debug-specialist` para errores complejos.
