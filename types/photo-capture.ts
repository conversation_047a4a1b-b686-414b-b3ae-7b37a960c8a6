export enum PhotoAngle {
  CROWN = 'crown',
  LEFT_SIDE = 'left_side',
  RIGHT_SIDE = 'right_side',
  BACK = 'back',
  FRONT = 'front',
}

export interface PhotoGuide {
  angle: PhotoAngle;
  label: string;
  description: string;
  icon: string;
  tips: string[];
  required: boolean;
}

export interface CapturedPhoto {
  id: string;
  uri: string;
  angle: PhotoAngle;
  quality: PhotoQuality;
  timestamp: Date;
}

export interface PhotoQuality {
  lighting: 'good' | 'fair' | 'poor';
  focus: 'good' | 'fair' | 'poor';
  stability: 'good' | 'fair' | 'poor';
  overall: number; // 0-100
}

export const PHOTO_GUIDES: PhotoGuide[] = [
  {
    angle: PhotoAngle.CROWN,
    label: 'Corona',
    description: 'Vista superior para analizar raíces y crecimiento',
    icon: '👑',
    tips: ['Captura desde arriba', 'Incluye toda la corona', 'Evita sombras'],
    required: true,
  },
  {
    angle: PhotoAngle.LEFT_SIDE,
    label: 'Lateral Izquierdo',
    description: 'Perfil izquierdo para textura y largo',
    icon: '👈',
    tips: [
      'Cabello suelto y natural',
      'Desde la raíz hasta las puntas',
      'Buena iluminación lateral',
    ],
    required: true,
  },
  {
    angle: PhotoAngle.RIGHT_SIDE,
    label: 'Lateral Derecho',
    description: 'Perfil derecho para comparación',
    icon: '👉',
    tips: [
      'Mismo ángulo que el izquierdo',
      'Captura cualquier diferencia',
      'Enfoca en medios y puntas',
    ],
    required: false,
  },
  {
    angle: PhotoAngle.BACK,
    label: 'Posterior',
    description: 'Vista trasera completa',
    icon: '🔙',
    tips: ['Desde nuca hasta puntas', 'Cabello bien distribuido', 'Captura variaciones de color'],
    required: true,
  },
  {
    angle: PhotoAngle.FRONT,
    label: 'Frontal',
    description: 'Vista frontal opcional',
    icon: '👤',
    tips: ['Enfoca en el marco del rostro', 'Línea de nacimiento visible', 'Opcional pero útil'],
    required: false,
  },
];

export const getPhotoGuide = (angle: PhotoAngle): PhotoGuide | undefined => {
  return PHOTO_GUIDES.find(guide => guide.angle === angle);
};

export const getRequiredPhotos = (): PhotoGuide[] => {
  return PHOTO_GUIDES.filter(guide => guide.required);
};

export const getOptionalPhotos = (): PhotoGuide[] => {
  return PHOTO_GUIDES.filter(guide => !guide.required);
};
