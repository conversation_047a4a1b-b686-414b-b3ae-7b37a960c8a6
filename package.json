{"name": "expo-app", "main": "expo-router/entry", "version": "2.0.9", "scripts": {"start": "bunx rork start -p 7uznxpq6tsp45s5pvem9d --tunnel", "start-web": "bunx rork start -p 7uznxpq6tsp45s5pvem9d --web --tunnel", "start-web-dev": "DEBUG=expo* bunx rork start -p 7uznxpq6tsp45s5pvem9d --web --tunnel", "mobile": "expo start --lan --clear", "mobile:stable": "expo start --lan", "mobile:tunnel": "expo start --tunnel", "dev": "expo start", "android": "expo run:android", "ios": "expo run:ios", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "prepare": "husky", "lint": "eslint . --ext .ts,.tsx,.js,.jsx --ignore-pattern 'scripts/' --ignore-pattern 'supabase/functions/' --ignore-pattern '*.config.js'", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix --ignore-pattern 'scripts/' --ignore-pattern 'supabase/functions/' --ignore-pattern '*.config.js'", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "code-quality": "npm run lint && npm run format:check", "code-quality:fix": "npm run lint:fix && npm run format", "performance:test": "node scripts/performance-test.js", "performance:analyze": "npm run performance:test", "e2e": "node e2e/direct-api-test.js", "e2e:validate": "node e2e/test-validation.js", "e2e:full": "npm run e2e:validate && npm run e2e", "test:e2e": "jest e2e/ --testTimeout=60000 --verbose", "test:integration": "npm run test:e2e"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/native": "^7.1.6", "@shopify/react-native-skia": "v2.0.0-next.4", "base64-arraybuffer": "^1.0.2", "expo": "^53.0.4", "expo-blur": "~14.1.4", "expo-camera": "^16.1.10", "expo-clipboard": "^7.1.5", "expo-constants": "~17.1.4", "expo-crypto": "^14.1.5", "expo-file-system": "~18.1.11", "expo-font": "~13.3.0", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.4", "expo-location": "~18.1.4", "expo-router": "~5.1.4", "expo-splash-screen": "~0.30.7", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.6", "expo-web-browser": "~14.2.0", "lucide-react-native": "^0.511.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "^0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-markdown-display": "^7.0.2", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-typescript": "^7.27.1", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.32.0", "@expo/ngrok": "^4.1.0", "@supabase/supabase-js": "^2.55.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.2", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "eslint": "^9.32.0", "eslint-config-expo": "^9.2.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-native": "^5.0.0", "husky": "^9.1.7", "jest": "~29.7.0", "jest-expo": "^53.0.9", "lint-staged": "^16.1.4", "metro-react-native-babel-preset": "^0.77.0", "prettier": "^3.6.2", "typescript": "~5.8.3"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write", "git add"], "*.{js,jsx}": ["eslint --fix", "prettier --write", "git add"], "*.{json,md}": ["prettier --write", "git add"]}, "private": true}