# Formula Explainer System - Examples

## Overview

El sistema de explicaciones de fórmulas proporciona explicaciones claras y educativas para cada decisión en la fórmula de coloración, ayudando a los estilistas a entender y confiar en las recomendaciones de IA.

## API Response Structure

```typescript
interface FormulaResponse {
  formulaText: string;           // Fórmula en formato markdown
  formulationData: any;          // Datos estructurados de la fórmula
  explanation: FormulaExplanation;  // Explicación completa (NUEVO)
  quickSummary: string;          // Resumen rápido (NUEVO)
  totalTokens: number;
}
```

## Example API Response

```json
{
  "success": true,
  "data": {
    "formulaText": "# Fórmula Profesional...",
    "formulationData": { /* formula data */ },
    "quickSummary": "💡 **Resumen**: aclaramos 2 niveles usando 30vol de oxidante para un resultado controlado y predecible.",
    "explanation": {
      "confidenceScore": 85,
      "overallStrategy": "🎯 **Estrategia de Aclarado**: Elevamos el cabello desde nivel 4 a nivel 6. Podemos lograrlo directamente con tinte, respetando los límites del cabello.",
      "levelExplanation": "📊 **Nivel Elegido**: 6\n• **Diagnóstico actual**: Nivel 4 (Castaño Medio)\n• **Objetivo**: Nivel 6 (Castaño Claro/Rubio Oscuro)\n• **Decisión**: Aclaramos 2 niveles respetando los límites del cabello",
      "developerExplanation": "⚗️ **Oxigenada 30 Vol**: Aclarado medio, balance entre efectividad y cuidado\n• **Función**: Aclarado moderado\n• **Seguridad**: Volumen óptimo para este proceso sin daño excesivo\n• **Resultado**: Aclarado efectivo",
      "productChoiceExplanation": "🧪 **Productos L'Oréal Professional**:\n• **Calidad**: Línea profesional con consistencia de resultados\n• **Compatibilidad**: Formulados para trabajar juntos sin reacciones\n• **Condición del cabello**: Balance entre efectividad y cuidado\n• **Cobertura**: Sin necesidad especial de cobertura de canas",
      "timingExplanation": "⏱️ **Tiempo de Procesamiento**: 35-45 minutos\n• **Base**: Tiempo estándar para cabello normal\n• **Ajustes**: Sin ajustes necesarios\n• **Monitoreo**: Revisa cada 10 minutos para evaluar progreso",
      "processSteps": [
        {
          "step": 1,
          "action": "Aplicación de Color",
          "reasoning": "Depositar el tono final y conseguir el resultado deseado",
          "timing": "35-45 minutos",
          "warningLevel": "low",
          "icon": "🎯"
        }
      ],
      "riskFactors": [],
      "successTips": [
        "🎯 Realiza siempre una prueba de mechón 48 horas antes",
        "⏰ No dejes el producto más tiempo del recomendado",
        "🌡️ Mantén temperatura ambiente constante (20-25°C)",
        "📸 Documenta el resultado para futuras referencias"
      ],
      "colorimetryReasoning": "**PRINCIPIOS DE COLORIMETRÍA APLICADOS:**\n- COLORACIÓN DIRECTA: Oxidante de 30 volúmenes\n\n**VOLUMEN DE OXIDANTE:**\n- USA EXACTAMENTE 30 VOLÚMENES\n- NO uses volumen mayor al necesario"
    }
  }
}
```

## Frontend Integration Examples

### React Native Component Example

```tsx
// components/ai/FormulaExplanationCard.tsx
import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInDown } from 'react-native-reanimated';
import type { FormulaExplanation } from '@/types';

interface FormulaExplanationCardProps {
  explanation: FormulaExplanation;
  quickSummary: string;
}

export function FormulaExplanationCard({ explanation, quickSummary }: FormulaExplanationCardProps) {
  const [expanded, setExpanded] = useState(false);

  const getConfidenceColor = (score: number) => {
    if (score >= 85) return '#10B981'; // Green
    if (score >= 70) return '#F59E0B'; // Amber
    return '#EF4444'; // Red
  };

  const getRiskLevelColor = (level: 'low' | 'medium' | 'high') => {
    switch (level) {
      case 'low': return '#10B981';
      case 'medium': return '#F59E0B';
      case 'high': return '#EF4444';
    }
  };

  return (
    <Animated.View entering={FadeInDown} style={styles.container}>
      {/* Quick Summary */}
      <View style={styles.summarySection}>
        <Text style={styles.quickSummary}>{quickSummary}</Text>
        
        {/* Confidence Score */}
        <View style={styles.confidenceContainer}>
          <View style={[styles.confidenceBadge, { backgroundColor: getConfidenceColor(explanation.confidenceScore) }]}>
            <Text style={styles.confidenceText}>{explanation.confidenceScore}% Confianza</Text>
          </View>
        </View>
      </View>

      {/* Expand/Collapse Button */}
      <TouchableOpacity 
        style={styles.expandButton} 
        onPress={() => setExpanded(!expanded)}
      >
        <Text style={styles.expandButtonText}>
          {expanded ? 'Ocultar detalles' : 'Ver explicación completa'}
        </Text>
        <Ionicons 
          name={expanded ? 'chevron-up' : 'chevron-down'} 
          size={20} 
          color="#666" 
        />
      </TouchableOpacity>

      {/* Expanded Content */}
      {expanded && (
        <Animated.View entering={FadeInDown} style={styles.expandedContent}>
          
          {/* Overall Strategy */}
          <View style={styles.section}>
            <Text style={styles.markdown}>{explanation.overallStrategy}</Text>
          </View>

          {/* Level Explanation */}
          <View style={styles.section}>
            <Text style={styles.markdown}>{explanation.levelExplanation}</Text>
          </View>

          {/* Developer Explanation */}
          <View style={styles.section}>
            <Text style={styles.markdown}>{explanation.developerExplanation}</Text>
          </View>

          {/* Process Steps */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>🔄 Pasos del Proceso</Text>
            {explanation.processSteps.map((step, index) => (
              <View key={index} style={styles.processStep}>
                <View style={styles.stepHeader}>
                  <Text style={styles.stepIcon}>{step.icon}</Text>
                  <Text style={styles.stepTitle}>{step.action}</Text>
                  <View style={[styles.warningBadge, { backgroundColor: getRiskLevelColor(step.warningLevel) }]}>
                    <Text style={styles.warningText}>{step.warningLevel}</Text>
                  </View>
                </View>
                <Text style={styles.stepReasoning}>{step.reasoning}</Text>
                <Text style={styles.stepTiming}>⏱️ {step.timing}</Text>
              </View>
            ))}
          </View>

          {/* Risk Factors */}
          {explanation.riskFactors.length > 0 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>⚠️ Factores de Riesgo</Text>
              {explanation.riskFactors.map((risk, index) => (
                <View key={index} style={styles.riskFactor}>
                  <View style={styles.riskHeader}>
                    <Text style={styles.riskIcon}>{risk.icon}</Text>
                    <Text style={styles.riskTitle}>{risk.factor}</Text>
                    <View style={[styles.riskBadge, { backgroundColor: getRiskLevelColor(risk.level) }]}>
                      <Text style={styles.riskLevelText}>{risk.level}</Text>
                    </View>
                  </View>
                  <Text style={styles.riskExplanation}>{risk.explanation}</Text>
                  <Text style={styles.riskMitigation}>💡 {risk.mitigation}</Text>
                </View>
              ))}
            </View>
          )}

          {/* Success Tips */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>💡 Tips de Éxito</Text>
            {explanation.successTips.map((tip, index) => (
              <Text key={index} style={styles.tip}>{tip}</Text>
            ))}
          </View>

          {/* Colorimetry Reasoning */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>🔬 Fundamentos de Colorimetría</Text>
            <Text style={styles.colorimetryText}>{explanation.colorimetryReasoning}</Text>
          </View>

        </Animated.View>
      )}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summarySection: {
    marginBottom: 16,
  },
  quickSummary: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  confidenceContainer: {
    alignItems: 'flex-end',
  },
  confidenceBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  confidenceText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  expandButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  expandButtonText: {
    fontSize: 16,
    color: '#3b82f6',
    fontWeight: '500',
  },
  expandedContent: {
    marginTop: 16,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1f2937',
    marginBottom: 8,
  },
  markdown: {
    fontSize: 15,
    lineHeight: 22,
    color: '#374151',
  },
  processStep: {
    backgroundColor: '#f9fafb',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  stepHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  stepIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  stepTitle: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  warningBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  warningText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  stepReasoning: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 4,
  },
  stepTiming: {
    fontSize: 14,
    color: '#3b82f6',
    fontWeight: '500',
  },
  riskFactor: {
    backgroundColor: '#fef2f2',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#ef4444',
  },
  riskHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  riskIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  riskTitle: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  riskBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  riskLevelText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  riskExplanation: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 4,
  },
  riskMitigation: {
    fontSize: 14,
    color: '#059669',
    fontStyle: 'italic',
  },
  tip: {
    fontSize: 14,
    color: '#374151',
    marginBottom: 6,
    paddingLeft: 16,
  },
  colorimetryText: {
    fontSize: 14,
    color: '#374151',
    fontFamily: 'monospace',
    backgroundColor: '#f3f4f6',
    padding: 12,
    borderRadius: 8,
  },
});
```

### Usage in Service Flow

```tsx
// app/service/FormulationStep.tsx
import React from 'react';
import { View, ScrollView } from 'react-native';
import { FormulaExplanationCard } from '@/components/ai/FormulaExplanationCard';
import { useServiceStore } from '@/stores/service-store';

export function FormulationStep() {
  const { formula } = useServiceStore();

  return (
    <ScrollView style={styles.container}>
      
      {/* Quick Summary at the top */}
      {formula?.quickSummary && (
        <View style={styles.quickSummaryContainer}>
          <Text style={styles.quickSummaryText}>{formula.quickSummary}</Text>
        </View>
      )}

      {/* Main Formula */}
      <FormulaCard formula={formula?.formulaText} />

      {/* Explanation Component */}
      {formula?.explanation && (
        <FormulaExplanationCard 
          explanation={formula.explanation}
          quickSummary={formula.quickSummary}
        />
      )}

      {/* Additional components... */}
      
    </ScrollView>
  );
}
```

## Benefits for Stylists

1. **Educational Value**: Aprenden principios de colorimetría con cada fórmula
2. **Confidence Building**: Entienden el "por qué" detrás de cada decisión
3. **Risk Awareness**: Identifican factores de riesgo antes de comenzar
4. **Professional Growth**: Mejoran su técnica con tips específicos
5. **Client Communication**: Pueden explicar el proceso al cliente

## Backend Integration

El sistema está completamente integrado en la Edge Function `salonier-assistant`. Cada respuesta de `generate_formula` ahora incluye:

- `explanation`: Objeto completo con todas las explicaciones
- `quickSummary`: Resumen rápido para mostrar inmediatamente
- `confidenceScore`: Basado en factores como claridad del diagnóstico, complejidad del proceso, etc.

## Future Enhancements

1. **Learning System**: Tracking de qué explicaciones son más útiles
2. **Personalization**: Ajustar explicaciones según el nivel de experiencia del estilista
3. **Interactive Tutorials**: Explicaciones más detalladas para técnicas complejas
4. **Visual Aids**: Diagramas y gráficos para explicar conceptos de colorimetría