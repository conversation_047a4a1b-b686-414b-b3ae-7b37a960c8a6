/**
 * Integration Test for Simple Explainer
 *
 * Tests that the simple explainer is properly integrated into the Edge Function
 * without causing timeouts or errors.
 */

import {
  generateQuickExplanation,
  validateExplanationData,
} from '../supabase/functions/salonier-assistant/utils/simple-explainer';

// Mock formula data for testing
const mockFormulaData = {
  products: [
    {
      brand: "L'Oreal",
      name: 'Inoa 7.1',
      type: 'tinte',
      amount: '60ml',
    },
  ],
  developer: {
    volume: 20,
    amount: '90ml',
  },
  processingTime: 30,
  technique: 'global',
  notes: ['Test formula'],
};

// Mock diagnosis data for testing
const mockDiagnosis = {
  currentLevel: 5,
  targetLevel: 7,
  hairCondition: 'normal',
  previousTreatments: 'ninguno',
  naturalBase: 5,
};

describe('Simple Explainer Integration', () => {
  it('should generate explanations within timeout limit', async () => {
    const startTime = Date.now();

    const explanations = await generateQuickExplanation(mockFormulaData, mockDiagnosis);

    const duration = Date.now() - startTime;

    expect(duration).toBeLessThan(500); // Should be under 500ms
    expect(explanations).toBeInstanceOf(Array);
    expect(explanations.length).toBeGreaterThan(0);
  });

  it('should validate explanation data correctly', () => {
    const isValid = validateExplanationData(mockFormulaData, mockDiagnosis);
    expect(isValid).toBe(true);
  });

  it('should handle invalid data gracefully', () => {
    const isValid = validateExplanationData({}, {});
    expect(isValid).toBe(false);
  });

  it('should generate professional explanations', async () => {
    const explanations = await generateQuickExplanation(mockFormulaData, mockDiagnosis);

    // Check that explanations are professional and informative
    explanations.forEach(explanation => {
      expect(explanation).toMatch(/^[✅🎯⏱️🎨⭐🌟🔥✨💎]/); // Should start with appropriate emoji
      expect(explanation.length).toBeGreaterThan(20); // Should be meaningful
      expect(explanation.length).toBeLessThan(150); // Should be concise
    });
  });

  it('should handle timeout gracefully', async () => {
    // This test simulates what happens in the Edge Function with Promise.race
    const timeoutPromise = new Promise<string[]>(
      (_, reject) => setTimeout(() => reject(new Error('Timeout')), 100) // Very short timeout
    );

    try {
      const result = await Promise.race([
        generateQuickExplanation(mockFormulaData, mockDiagnosis),
        timeoutPromise,
      ]);

      // If we get here, the explanation was fast enough
      expect(result).toBeInstanceOf(Array);
    } catch (error) {
      // If we get here, it timed out (which is also acceptable)
      expect(error.message).toBe('Timeout');
    }
  });

  it('should provide fallback explanations when data is incomplete', async () => {
    const incompleteFormula = {
      products: [], // No products
    };

    const explanations = await generateQuickExplanation(incompleteFormula, {});

    // Should still provide some explanations
    expect(explanations).toBeInstanceOf(Array);
    expect(explanations.length).toBeGreaterThan(0);
  });
});

/**
 * Mock test for Edge Function integration
 *
 * Simulates how the simple explainer would be called from the Edge Function
 */
describe('Edge Function Integration Simulation', () => {
  it('should handle the fail-safe pattern used in generateFormula', async () => {
    let simpleExplanations: string[] = [];
    let enhancedQuickSummary = 'Original summary';

    try {
      if (mockFormulaData && mockDiagnosis) {
        // Simulate the exact pattern used in the Edge Function
        simpleExplanations = await Promise.race([
          generateQuickExplanation(mockFormulaData, mockDiagnosis),
          new Promise<string[]>((_, reject) => setTimeout(() => reject(new Error('Timeout')), 500)),
        ]);

        // Enhance quick summary with first explanation
        if (simpleExplanations.length > 0) {
          enhancedQuickSummary = simpleExplanations[0];
        }

        // Add explanations to formula data (simulation)
        (mockFormulaData as any).explanations = simpleExplanations;
        (mockFormulaData as any).quickSummary = enhancedQuickSummary;
      }
    } catch (error) {
      // This should not cause the test to fail - it's fail-safe
      console.warn('Explanations failed (expected behavior):', error.message);
    }

    // Verify the integration worked or failed gracefully
    expect(simpleExplanations).toBeInstanceOf(Array);
    expect(enhancedQuickSummary).toBeDefined();

    // Even if it failed, we should have the original summary
    expect(enhancedQuickSummary.length).toBeGreaterThan(0);
  });
});
