# CLAUDE.md - Supabase Client & Database Configuration

## 🎯 Propósito

Configuración del cliente Supabase con helpers críticos para autenticación, multi-tenancy y storage. Punto central para todas las operaciones de base de datos y autenticación.

## 📁 Estructura de Archivos

```
lib/
├── supabase.ts          # Cliente principal con helpers
├── database.types.ts    # Tipos TypeScript generados
└── edge-functions.ts    # Helpers para Edge Functions
```

## 🔧 Configuración del Cliente (supabase.ts)

### Variables de Entorno OBLIGATORIAS

```bash
# .env.local o Expo environment
EXPO_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Variables adicionales para Edge Functions
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key (solo server-side)
```

### Cliente Configurado para React Native

```typescript
import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Database } from '@/types/database';

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!;

// Validación obligatoria
if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

// Cliente con configuración React Native
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage, // Persistencia local
    autoRefreshToken: true, // Refresh automático
    persistSession: true, // Mantener sesión
    detectSessionInUrl: false, // No URL detection en mobile
  },
  global: {
    headers: {
      'X-Client-Info': 'salonier-mobile-app',
    },
  },
});
```

## 🔐 Helpers de Autenticación

### getCurrentSalonId()

```typescript
/**
 * Obtiene el salon_id del usuario autenticado
 * CRÍTICO: Usar en TODAS las operaciones que requieren aislamiento
 */
export async function getCurrentSalonId(): Promise<string | null> {
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    logger.warn('getCurrentSalonId called without authenticated user');
    return null;
  }

  const { data: profile, error } = await supabase
    .from('profiles')
    .select('salon_id')
    .eq('id', user.id)
    .single();

  if (error) {
    logger.error('Failed to get salon_id', { error, userId: user.id });
    return null;
  }

  return profile?.salon_id || null;
}
```

### hasPermission()

```typescript
/**
 * Verifica si el usuario tiene un permiso específico
 * Usar antes de operaciones críticas (delete, admin actions)
 */
export async function hasPermission(permission: string): Promise<boolean> {
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) return false;

  const { data: profile } = await supabase
    .from('profiles')
    .select('role, permissions')
    .eq('id', user.id)
    .single();

  if (!profile) return false;

  // Check role-based permissions
  const rolePermissions = getRolePermissions(profile.role);
  if (rolePermissions.includes(permission)) return true;

  // Check individual permissions
  const userPermissions = profile.permissions || [];
  return userPermissions.includes(permission);
}

// Mapeo de permisos por rol
const getRolePermissions = (role: string): string[] => {
  switch (role) {
    case 'owner':
      return ['*']; // Todos los permisos
    case 'admin':
      return ['clients.*', 'inventory.*', 'services.*', 'team.read', 'reports.*'];
    case 'stylist':
      return ['clients.read', 'clients.create', 'services.*', 'inventory.read'];
    case 'assistant':
      return ['clients.read', 'services.read', 'inventory.read'];
    default:
      return [];
  }
};
```

### getCurrentUser()

```typescript
/**
 * Obtiene el usuario actual con información completa del perfil
 */
export async function getCurrentUser() {
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { user: null, profile: null, error: authError };
  }

  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select(
      `
      *,
      salon:salons(
        id,
        name,
        address,
        phone,
        email
      )
    `
    )
    .eq('id', user.id)
    .single();

  return {
    user,
    profile,
    error: profileError,
  };
}
```

## 📁 Helpers de Storage

### getSignedUrl()

```typescript
/**
 * Genera URL firmada para acceso a archivos privados
 * URLs expiran en 1 hora por seguridad
 */
export async function getSignedUrl(
  bucket: string,
  path: string,
  expiresIn: number = 3600 // 1 hora por defecto
): Promise<string | null> {
  // Validar que el path incluye salon_id para aislamiento
  const salonId = await getCurrentSalonId();
  if (!path.startsWith(`${salonId}/`)) {
    logger.error('Storage path must start with salon_id', { path, salonId });
    throw new Error('Invalid storage path - salon isolation required');
  }

  const { data, error } = await supabase.storage.from(bucket).createSignedUrl(path, expiresIn);

  if (error) {
    logger.error('Failed to create signed URL', { error, bucket, path });
    return null;
  }

  return data.signedUrl;
}
```

### uploadFile()

```typescript
/**
 * Sube archivo con validaciones de seguridad y aislamiento por salón
 */
export async function uploadFile(
  bucket: string,
  file: File | Blob,
  fileName: string,
  options: {
    isPublic?: boolean;
    maxSize?: number; // bytes
    allowedTypes?: string[];
  } = {}
): Promise<{ path: string | null; error: string | null }> {
  const { maxSize = 5 * 1024 * 1024, allowedTypes = [] } = options; // 5MB default

  // Validaciones de archivo
  if (file.size > maxSize) {
    return { path: null, error: `File too large. Max size: ${maxSize} bytes` };
  }

  if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
    return { path: null, error: `File type not allowed. Allowed: ${allowedTypes.join(', ')}` };
  }

  // Construir path con aislamiento por salón
  const salonId = await getCurrentSalonId();
  if (!salonId) {
    return { path: null, error: 'No salon_id available for upload' };
  }

  const timestamp = Date.now();
  const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');
  const filePath = `${salonId}/${timestamp}_${sanitizedFileName}`;

  // Upload
  const { data, error } = await supabase.storage.from(bucket).upload(filePath, file, {
    cacheControl: '3600',
    upsert: false,
  });

  if (error) {
    logger.error('Failed to upload file', { error, bucket, filePath });
    return { path: null, error: error.message };
  }

  return { path: data.path, error: null };
}
```

## 🗄️ Helpers de Base de Datos

### executeQuery()

```typescript
/**
 * Helper para queries con aislamiento automático por salon_id
 */
export async function executeQuery<T>(
  queryBuilder: (salonId: string) => PromiseLike<{ data: T | null; error: any }>
): Promise<{ data: T | null; error: string | null }> {
  const salonId = await getCurrentSalonId();
  if (!salonId) {
    return { data: null, error: 'No salon_id available' };
  }

  try {
    const result = await queryBuilder(salonId);

    if (result.error) {
      logger.error('Database query failed', { error: result.error, salonId });
      return { data: null, error: result.error.message };
    }

    return { data: result.data, error: null };
  } catch (error) {
    logger.error('Query execution failed', { error, salonId });
    return { data: null, error: error.message };
  }
}

// Ejemplo de uso
const getProducts = () =>
  executeQuery(async salonId => supabase.from('products').select('*').eq('salon_id', salonId));
```

### withTransaction()

```typescript
/**
 * Helper para transacciones con rollback automático
 */
export async function withTransaction<T>(
  operations: (client: any) => Promise<T>
): Promise<{ data: T | null; error: string | null }> {
  // Nota: Supabase no soporta transacciones explícitas del lado cliente
  // Este helper simula transacciones usando RPC functions

  try {
    const result = await operations(supabase);
    return { data: result, error: null };
  } catch (error) {
    logger.error('Transaction failed', { error });
    return { data: null, error: error.message };
  }
}
```

## 🔄 Helpers de Realtime

### subscribeToChanges()

```typescript
/**
 * Suscripción a cambios en tiempo real con filtro por salón
 */
export function subscribeToChanges<T>(
  table: string,
  callback: (payload: any) => void,
  filter?: string
) {
  return supabase
    .channel(`${table}_changes`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table,
        filter: filter || `salon_id=eq.${getCurrentSalonId()}`,
      },
      callback
    )
    .subscribe();
}

// Ejemplo de uso
const subscription = subscribeToChanges('products', payload => {
  console.log('Product changed:', payload);
  // Update local state
});

// Cleanup
// subscription.unsubscribe();
```

## 🧪 Testing Helpers

### createTestClient()

```typescript
/**
 * Cliente de prueba para testing
 */
export function createTestClient() {
  return createClient(
    process.env.SUPABASE_TEST_URL || supabaseUrl,
    process.env.SUPABASE_TEST_ANON_KEY || supabaseAnonKey,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    }
  );
}

/**
 * Mock user para testing
 */
export async function mockAuthUser(testClient: any, userData: any) {
  // Implementation for testing authentication
  testClient.auth.setAuth(userData);
}
```

## 🚨 Error Handling

### Tipos de Errores Comunes

```typescript
export enum SupabaseErrorType {
  AUTH_ERROR = 'auth_error',
  PERMISSION_ERROR = 'permission_error',
  NETWORK_ERROR = 'network_error',
  RLS_ERROR = 'rls_error',
  VALIDATION_ERROR = 'validation_error',
}

export function categorizeError(error: any): SupabaseErrorType {
  if (error.message?.includes('JWT')) return SupabaseErrorType.AUTH_ERROR;
  if (error.message?.includes('RLS')) return SupabaseErrorType.RLS_ERROR;
  if (error.message?.includes('permission')) return SupabaseErrorType.PERMISSION_ERROR;
  if (error.code === 'NETWORK_ERROR') return SupabaseErrorType.NETWORK_ERROR;
  return SupabaseErrorType.VALIDATION_ERROR;
}

export function handleSupabaseError(error: any, context: string) {
  const errorType = categorizeError(error);

  logger.error(`Supabase error in ${context}`, {
    error,
    errorType,
    timestamp: new Date().toISOString(),
  });

  // Return user-friendly message
  switch (errorType) {
    case SupabaseErrorType.AUTH_ERROR:
      return 'Sesión expirada. Por favor, inicia sesión nuevamente.';
    case SupabaseErrorType.PERMISSION_ERROR:
      return 'No tienes permisos para realizar esta acción.';
    case SupabaseErrorType.RLS_ERROR:
      return 'Error de seguridad. Contacta con soporte.';
    case SupabaseErrorType.NETWORK_ERROR:
      return 'Error de conexión. Verifica tu conexión a internet.';
    default:
      return 'Ha ocurrido un error inesperado.';
  }
}
```

## 📊 Performance Monitoring

### Connection Pool Monitoring

```typescript
export function monitorConnectionHealth() {
  // Log connection metrics
  setInterval(() => {
    const metrics = {
      activeConnections: supabase.storage
        .from('test')
        .list()
        .then(() => 'healthy')
        .catch(() => 'unhealthy'),
      lastHeartbeat: new Date().toISOString(),
    };

    logger.info('Supabase connection health', metrics);
  }, 60000); // Every minute
}
```

## 🤖 Agentes Recomendados

### Para este módulo usar:

**database-architect** - Configuración y optimización de Supabase

- Configuración de cliente con mejores prácticas
- Optimización de helpers para performance
- Design de índices para helpers frecuentes
- PROACTIVAMENTE usar para configuración de base de datos

**security-privacy-auditor** - Revisión de seguridad

- Auditar helpers de autenticación y permisos
- Revisar aislamiento multi-tenant en helpers
- Validar manejo seguro de URLs firmadas
- Usar antes de releases y cambios de auth

**offline-sync-specialist** - Sincronización y caché

- Optimizar helpers para patterns offline-first
- Implementar caché inteligente en helpers
- Gestión de conflicts en helpers de datos
- Usar para problemas de sincronización

**deployment-engineer** - Deploy y configuración

- Configuración de variables de entorno
- Setup de client para diferentes environments
- CI/CD configuration para Supabase
- MUST BE USED para deployments

### 💡 Ejemplos de Uso

```bash
# Auditar security de helpers de auth
Task: Use security-privacy-auditor to review getCurrentSalonId and hasPermission helpers

# Optimizar helper de upload para performance
Task: Use database-architect to optimize uploadFile helper for large files

# Implementar caché en helpers de datos
Task: Use offline-sync-specialist to add intelligent caching to executeQuery helper

# Deploy configuración segura
Task: Use deployment-engineer to setup secure environment variables for Supabase client
```

## 🔌 MCPs Disponibles

### Funciones MCP útiles:

**Para Supabase (MCP principal para este módulo):**

- `mcp__supabase__list_tables` - Inspeccionar schema para helpers
- `mcp__supabase__execute_sql` - Testing directo de queries
- `mcp__supabase__get_logs` - Debug de operaciones
- `mcp__supabase__get_advisors` - Recomendaciones de seguridad/performance
- `mcp__supabase__generate_typescript_types` - Actualizar tipos

**Para análisis de código:**

- `mcp__serena__get_symbols_overview` - Estructura de helpers
- `mcp__serena__find_symbol` - Localizar helper específico
- `mcp__serena__find_referencing_symbols` - Ver uso de helpers

**Para diagnósticos:**

- `mcp__ide__getDiagnostics` - Errores TypeScript en helpers
- `mcp__supabase__get_logs` - Logs de operaciones en tiempo real

### 📝 Ejemplos MCP

```bash
# Analizar configuración de base de datos
mcp__supabase__list_tables
mcp__supabase__get_advisors: "security"
mcp__supabase__get_advisors: "performance"

# Explorar estructura de helpers
mcp__serena__get_symbols_overview: "lib/supabase.ts"
mcp__serena__find_symbol: "getCurrentSalonId"

# Buscar uso de helper específico
mcp__serena__find_referencing_symbols: "uploadFile" in "lib/supabase.ts"

# Debug operaciones Supabase
mcp__supabase__get_logs: "api"
mcp__supabase__execute_sql: "SELECT COUNT(*) FROM profiles WHERE salon_id IS NULL"

# Actualizar tipos después de cambios
mcp__supabase__generate_typescript_types
```

### 🔄 Combinaciones Recomendadas

**Seguridad y Configuración:**

1. `security-privacy-auditor` + `mcp__supabase__get_advisors`
2. `database-architect` + `mcp__supabase__list_tables`

**Performance y Optimización:**

1. `database-architect` + `mcp__supabase__execute_sql`
2. `offline-sync-specialist` + `mcp__supabase__get_logs`

**Development y Testing:**

1. `deployment-engineer` + `mcp__supabase__generate_typescript_types`
2. `debug-specialist` + `mcp__ide__getDiagnostics`

## 🔗 Archivos Relacionados

- `../types/database.ts` - Tipos TypeScript de base de datos
- `../stores/*.ts` - Stores que usan estos helpers
- `../supabase/migrations/` - Schema de base de datos
- `../utils/logger.ts` - Sistema de logging

## 📚 Comandos Útiles

```bash
# Generar tipos TypeScript actualizados (o usar MCP)
npx supabase gen types typescript --local > types/database.ts
# Alternativa: mcp__supabase__generate_typescript_types

# Test conexión
npx supabase status

# Ver logs en tiempo real (o usar MCP)
npx supabase functions logs --follow
# Alternativa: mcp__supabase__get_logs: "edge-function"

# Reset local database
npx supabase db reset
```

---

**⚡ Recuerda:** Estos helpers son la base de toda interacción con Supabase. Úsalos consistentemente para mantener seguridad y aislamiento. Siempre usa `security-privacy-auditor` para cambios de auth y `database-architect` para optimización.
