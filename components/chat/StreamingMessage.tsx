import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Animated, Platform } from 'react-native';
import { ChevronDown } from 'lucide-react-native';
import Markdown from 'react-native-markdown-display';
import Colors from '@/constants/colors';
import { spacing, typography, radius } from '@/constants/theme';

interface StreamingMessageProps {
  content: string;
  isStreaming?: boolean;
  maxPreviewLength?: number;
  isUser?: boolean;
  onExpand?: () => void;
  onCollapse?: () => void;
}

export default function StreamingMessage({
  content,
  isStreaming = false,
  maxPreviewLength = 1000, // Increased significantly - only collapse very long messages
  isUser = false,
  onExpand,
  onCollapse,
}: StreamingMessageProps) {
  const [isExpanded, setIsExpanded] = useState(true); // Start expanded by default
  const [displayedContent, setDisplayedContent] = useState('');
  const rotateAnim = useRef(new Animated.Value(1)).current; // Start rotated (expanded state)

  // Streaming effect for assistant messages
  useEffect(() => {
    if (isStreaming && !isUser) {
      let index = 0;
      const streamInterval = setInterval(() => {
        if (index <= content.length) {
          setDisplayedContent(content.slice(0, index));
          index += 3; // Show 3 characters at a time
        } else {
          clearInterval(streamInterval);
        }
      }, 20); // 20ms for smooth streaming

      return () => clearInterval(streamInterval);
    } else {
      setDisplayedContent(content);
    }
  }, [content, isStreaming, isUser]);

  // Rotation animation for chevron
  useEffect(() => {
    Animated.timing(rotateAnim, {
      toValue: isExpanded ? 1 : 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [isExpanded, rotateAnim]);

  const toggleExpansion = () => {
    const newState = !isExpanded;
    setIsExpanded(newState);
    if (newState && onExpand) {
      onExpand();
    } else if (!newState && onCollapse) {
      onCollapse();
    }
  };

  // Only show collapse option for extremely long messages (>1000 chars)
  const needsCollapseOption = content.length > maxPreviewLength && !isUser;
  const contentToShow =
    needsCollapseOption && !isExpanded
      ? content.slice(0, maxPreviewLength) + '...'
      : displayedContent;

  const rotation = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });

  return (
    <View style={styles.container}>
      <Markdown style={getMarkdownStyles(isUser)}>{contentToShow}</Markdown>

      {needsCollapseOption && (
        <TouchableOpacity style={styles.expandButton} onPress={toggleExpansion} activeOpacity={0.7}>
          <Text style={styles.expandText}>
            {isExpanded ? 'Mostrar menos' : 'Mostrar mensaje completo'}
          </Text>
          <Animated.View style={[styles.rotatingChevron, { transform: [{ rotate: rotation }] }]}>
            <ChevronDown size={16} color={Colors.light.primary} />
          </Animated.View>
        </TouchableOpacity>
      )}

      {isStreaming && !isUser && (
        <View style={styles.streamingIndicator}>
          <View style={[styles.streamingDot, styles.streamingDotFirst]} />
          <View style={[styles.streamingDot, styles.streamingDotSecond]} />
          <View style={[styles.streamingDot, styles.streamingDotThird]} />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%', // Asegura que use todo el ancho disponible
  },
  messageText: {
    fontSize: typography.sizes.base,
    lineHeight: typography.sizes.base * 1.5, // Mejor espaciado entre líneas
    flexWrap: 'wrap', // Asegura que el texto haga wrap
  },
  userMessageText: {
    color: Colors.light.text,
  },
  assistantMessageText: {
    color: Colors.light.text,
  },
  expandButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.sm,
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.sm,
    backgroundColor: Colors.light.primary + '10',
    borderRadius: radius.sm,
    alignSelf: 'flex-start',
  },
  expandText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.primary,
    marginRight: spacing.xs,
    fontWeight: typography.weights.medium,
  },
  streamingIndicator: {
    flexDirection: 'row',
    marginTop: spacing.xs,
    gap: spacing.xs / 2,
  },
  streamingDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: Colors.light.primary,
    opacity: 0.5,
  },
  streamingDotFirst: {
    animationDelay: '0ms',
  },
  streamingDotSecond: {
    animationDelay: '200ms',
  },
  streamingDotThird: {
    animationDelay: '400ms',
  },
  rotatingChevron: {
    // Container for rotating chevron - no additional styling needed
  },
  // Markdown styles
  markdownStrong: {
    fontWeight: '700',
    color: Colors.light.text,
  },
  markdownEm: {
    fontStyle: 'italic',
    color: Colors.light.text,
  },
  markdownBulletList: {
    marginVertical: spacing.xs,
  },
  markdownBulletIcon: {
    color: Colors.light.text,
    fontSize: typography.sizes.base,
  },
  markdownParagraph: {
    marginVertical: spacing.xs / 2,
  },
  markdownCodeInline: {
    backgroundColor: Colors.light.surface,
    paddingHorizontal: spacing.xs / 2,
    paddingVertical: 2,
    borderRadius: 4,
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
  },
});

// Helper function to create markdown styles
const getMarkdownStyles = (isUser: boolean) => ({
  body: {
    ...styles.messageText,
    ...(isUser ? styles.userMessageText : styles.assistantMessageText),
  },
  strong: styles.markdownStrong,
  em: styles.markdownEm,
  bullet_list: styles.markdownBulletList,
  bullet_list_icon: styles.markdownBulletIcon,
  paragraph: styles.markdownParagraph,
  code_inline: styles.markdownCodeInline,
});
