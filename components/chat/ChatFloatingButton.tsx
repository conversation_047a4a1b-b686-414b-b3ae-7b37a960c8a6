import React, { useState } from 'react';
import { TouchableOpacity, StyleSheet, Modal, View, Animated } from 'react-native';
import { MessageCircle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { shadows, spacing, radius } from '@/constants/theme';
import ChatInterface from './ChatInterface';

interface ContextData {
  [key: string]: unknown;
}

interface ChatFloatingButtonProps {
  contextType?: 'general' | 'client' | 'service' | 'formula' | 'inventory';
  contextId?: string;
  contextData?: ContextData;
}

export default function ChatFloatingButton({
  contextType,
  contextId,
  contextData,
}: ChatFloatingButtonProps) {
  const [isVisible, setIsVisible] = useState(false);
  const scaleAnim = React.useRef(new Animated.Value(1)).current;

  const handlePress = () => {
    // Animate button press
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    setIsVisible(true);
  };

  return (
    <>
      <Animated.View
        style={[
          styles.container,
          {
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <TouchableOpacity style={styles.button} onPress={handlePress} activeOpacity={0.8}>
          <MessageCircle size={24} color={Colors.light.surface} />
        </TouchableOpacity>
      </Animated.View>

      <Modal
        visible={isVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setIsVisible(false)}
      >
        <View style={styles.modalContainer}>
          <ChatInterface
            contextType={contextType}
            contextId={contextId}
            contextData={contextData}
            onClose={() => setIsVisible(false)}
            isModal
          />
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: spacing.xl,
    right: spacing.md,
    zIndex: 999,
  },
  button: {
    width: 56,
    height: 56,
    borderRadius: radius.full,
    backgroundColor: Colors.light.primary,
    justifyContent: 'center',
    alignItems: 'center',
    ...shadows.lg,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
});
