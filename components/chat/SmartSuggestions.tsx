import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { Lightbulb, Zap, Camera, Users, Package, Beaker } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { spacing, typography, radius } from '@/constants/theme';
import { useClientStore } from '@/stores/client-store';
import { useSalonConfigStore } from '@/stores/salon-config-store';

interface ContextData {
  name?: string;
  [key: string]: unknown;
}

interface SmartSuggestionsProps {
  input: string;
  onSuggestionSelect: (suggestion: string) => void;
  contextType?: string;
  contextData?: ContextData;
}

interface Suggestion {
  text: string;
  type: 'formula' | 'technique' | 'product' | 'client' | 'general';
  icon: React.ComponentType<{ size?: number; color?: string }>;
  priority: number;
}

export default function SmartSuggestions({
  input,
  onSuggestionSelect,
  contextType,
  contextData,
}: SmartSuggestionsProps) {
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const { clients: _clients } = useClientStore();
  const { configuration: _configuration } = useSalonConfigStore();

  useEffect(() => {
    // IMPROVED UX: Show suggestions from 1 character with debounce
    const debounceTimer = setTimeout(() => {
      if (input.length > 0) {
        generateSuggestions(input);
      } else {
        // Show default suggestions when input is empty
        setDefaultSuggestions();
      }
    }, 300); // 300ms debounce for performance

    return () => clearTimeout(debounceTimer);
  }, [input, contextType, contextData, generateSuggestions]);

  const setDefaultSuggestions = () => {
    // Show helpful starter suggestions
    setSuggestions([
      {
        text: '¿Cómo corrijo un color naranja?',
        type: 'general',
        icon: Lightbulb,
        priority: 10,
      },
      {
        text: 'Fórmula para rubio ceniza nivel 8',
        type: 'formula',
        icon: Beaker,
        priority: 9,
      },
      {
        text: 'Analizar foto de cabello',
        type: 'general',
        icon: Camera,
        priority: 8,
      },
    ]);
  };

  const generateSuggestions = useCallback(
    async (text: string) => {
      setIsLoading(true);
      const newSuggestions: Suggestion[] = [];
      const lowerText = text.toLowerCase();

      // Color-related suggestions
      if (lowerText.includes('rubio') || lowerText.includes('blonde')) {
        newSuggestions.push({
          text: 'Fórmula para rubio ceniza nivel 8',
          type: 'formula',
          icon: Beaker,
          priority: 10,
        });
        newSuggestions.push({
          text: 'Técnica balayage para rubio natural',
          type: 'technique',
          icon: Zap,
          priority: 9,
        });
        newSuggestions.push({
          text: 'Productos Wella para rubios',
          type: 'product',
          icon: Package,
          priority: 8,
        });
      }

      if (lowerText.includes('correc') || lowerText.includes('naranja')) {
        newSuggestions.push({
          text: 'Corrección de color naranja con matizador',
          type: 'formula',
          icon: Beaker,
          priority: 10,
        });
        newSuggestions.push({
          text: 'Neutralizar tonos no deseados paso a paso',
          type: 'technique',
          icon: Zap,
          priority: 9,
        });
      }

      if (lowerText.includes('fórmula') || lowerText.includes('formula')) {
        newSuggestions.push({
          text: 'Fórmula para cabello virgen nivel 6',
          type: 'formula',
          icon: Beaker,
          priority: 8,
        });
        newSuggestions.push({
          text: 'Fórmula para retoque de raíces',
          type: 'formula',
          icon: Beaker,
          priority: 8,
        });
        newSuggestions.push({
          text: 'Calcular proporciones de mezcla',
          type: 'general',
          icon: Lightbulb,
          priority: 7,
        });
      }

      if (lowerText.includes('volumen') || lowerText.includes('peróxido')) {
        newSuggestions.push({
          text: '¿Qué volumen de peróxido usar para nivel 7?',
          type: 'general',
          icon: Lightbulb,
          priority: 9,
        });
        newSuggestions.push({
          text: 'Diferencias entre 20vol y 30vol',
          type: 'general',
          icon: Lightbulb,
          priority: 8,
        });
      }

      // Client-related suggestions
      if (lowerText.includes('cliente') || contextType === 'client') {
        const recentClients = _clients.slice(0, 3);
        recentClients.forEach((client: { name: string; id: string }) => {
          newSuggestions.push({
            text: `Historial de ${client.name}`,
            type: 'client',
            icon: Users,
            priority: 7,
          });
        });
      }

      // Technique suggestions
      if (lowerText.includes('balayage')) {
        newSuggestions.push({
          text: 'Técnica balayage paso a paso',
          type: 'technique',
          icon: Zap,
          priority: 9,
        });
        newSuggestions.push({
          text: 'Productos recomendados para balayage',
          type: 'product',
          icon: Package,
          priority: 8,
        });
      }

      if (lowerText.includes('mechas') || lowerText.includes('highlights')) {
        newSuggestions.push({
          text: 'Técnica de mechas con papel',
          type: 'technique',
          icon: Zap,
          priority: 9,
        });
        newSuggestions.push({
          text: 'Espaciado ideal para mechas',
          type: 'technique',
          icon: Zap,
          priority: 8,
        });
      }

      // Inventory-related suggestions
      if (lowerText.includes('inventario') || lowerText.includes('producto')) {
        // Check if low stock products exist (future implementation)
        newSuggestions.push({
          text: 'Revisar productos con stock bajo',
          type: 'product',
          icon: Package,
          priority: 9,
        });
        newSuggestions.push({
          text: 'Productos más utilizados este mes',
          type: 'product',
          icon: Package,
          priority: 7,
        });
      }

      // Photo analysis suggestions
      if (lowerText.includes('foto') || lowerText.includes('analiz')) {
        newSuggestions.push({
          text: 'Analizar foto de cabello actual',
          type: 'general',
          icon: Camera,
          priority: 8,
        });
        newSuggestions.push({
          text: 'Comparar antes y después',
          type: 'general',
          icon: Camera,
          priority: 7,
        });
      }

      // Contextual suggestions based on current context
      if (contextType === 'client' && contextData?.name) {
        newSuggestions.push({
          text: `¿Qué técnica recomiendas para ${contextData.name}?`,
          type: 'client',
          icon: Users,
          priority: 10,
        });
        newSuggestions.push({
          text: `Última fórmula usada en ${contextData.name}`,
          type: 'formula',
          icon: Beaker,
          priority: 9,
        });
      }

      // Sort by priority and limit to top 4
      const sortedSuggestions = newSuggestions.sort((a, b) => b.priority - a.priority).slice(0, 4);

      setSuggestions(sortedSuggestions);
      setIsLoading(false);
    },
    [contextType, contextData, _clients]
  );

  const getSuggestionStyle = (type: string) => {
    switch (type) {
      case 'formula':
        return {
          backgroundColor: Colors.light.primary + '10',
          borderColor: Colors.light.primary,
        };
      case 'technique':
        return {
          backgroundColor: Colors.light.success + '10',
          borderColor: Colors.light.success,
        };
      case 'product':
        return {
          backgroundColor: Colors.light.warning + '10',
          borderColor: Colors.light.warning,
        };
      case 'client':
        return {
          backgroundColor: Colors.light.info + '10',
          borderColor: Colors.light.info,
        };
      default:
        return {
          backgroundColor: Colors.light.surface,
          borderColor: Colors.light.border,
        };
    }
  };

  if (suggestions.length === 0 && !isLoading) {
    return null;
  }

  return (
    <View style={styles.container}>
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={Colors.light.primary} />
          <Text style={styles.loadingText}>Generando sugerencias...</Text>
        </View>
      ) : (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {suggestions.map((suggestion, index) => {
            const IconComponent = suggestion.icon;
            const suggestionStyle = getSuggestionStyle(suggestion.type);

            return (
              <TouchableOpacity
                key={index}
                style={[styles.suggestionChip, suggestionStyle]}
                onPress={() => onSuggestionSelect(suggestion.text)}
              >
                <IconComponent size={16} color={suggestionStyle.borderColor} />
                <Text style={[styles.suggestionText, { color: suggestionStyle.borderColor }]}>
                  {suggestion.text}
                </Text>
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
    backgroundColor: Colors.light.surface,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.sm,
  },
  loadingText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginLeft: spacing.sm,
    fontStyle: 'italic',
  },
  scrollContent: {
    paddingRight: spacing.md,
  },
  suggestionChip: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: radius.full,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    marginRight: spacing.sm,
    borderWidth: 1,
    maxWidth: 320, // Aumentado de 250 para evitar texto cortado
    minWidth: 150, // Mínimo para mantener consistencia
  },
  suggestionText: {
    fontSize: typography.sizes.sm,
    marginLeft: spacing.xs,
    fontWeight: typography.weights.medium,
    flexShrink: 1, // Permite que el texto se ajuste si es necesario
    flexWrap: 'wrap', // Permite wrap en caso de texto muy largo
  },
});
