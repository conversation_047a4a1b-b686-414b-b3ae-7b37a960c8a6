import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Alert,
  Image,
  Modal,
  Dimensions,
} from 'react-native';
import {
  Send,
  MessageCircle,
  X,
  AlertCircle,
  Camera,
  Image as ImageIcon,
  Menu,
} from 'lucide-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import * as ImagePicker from 'expo-image-picker';
import Markdown from 'react-native-markdown-display';
import Colors from '@/constants/colors';
import { spacing, typography, radius } from '@/constants/theme';
import { BaseCard, BottomSheet } from '@/components/base';
import { useChatStore, ChatMessage, ChatAttachment } from '@/stores/chat-store';
import { ImageProcessor } from '@/utils/image-processor';
import { logger } from '@/utils/logger';
import ConversationsList from './ConversationsList';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const SIDEBAR_WIDTH = 320;
const IS_TABLET = SCREEN_WIDTH >= 768;

interface ChatInterfaceProps {
  conversationId?: string;
  contextType?: 'general' | 'client' | 'service' | 'formula' | 'inventory';
  contextId?: string;
  contextData?: Record<string, unknown>;
  onClose?: () => void;
  isModal?: boolean;
}

export default function ChatInterface({
  conversationId: propConversationId,
  contextType,
  contextId,
  contextData,
  onClose,
  isModal = false,
}: ChatInterfaceProps) {
  const insets = useSafeAreaInsets();
  const scrollViewRef = useRef<ScrollView>(null);
  const [message, setMessage] = useState('');
  const [pendingImages, setPendingImages] = useState<string[]>([]);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isUploadingImages, setIsUploadingImages] = useState(false);
  const [showSidebar, setShowSidebar] = useState(IS_TABLET);
  const {
    conversations,
    messages,
    activeConversationId,
    isLoading,
    isSending,
    error,
    loadConversations,
    createConversation,
    setActiveConversation,
    sendMessage,
    uploadChatImage,
    clearError,
    archiveConversation,
    toggleFavorite,
  } = useChatStore();

  // Initialize conversation
  useEffect(() => {
    const initializeChat = async () => {
      if (propConversationId) {
        setActiveConversation(propConversationId);
      } else if (contextType && contextId) {
        // Check if conversation exists for this context
        await loadConversations();
        const existingConv = conversations.find(
          conv => conv.contextType === contextType && conv.contextId === contextId
        );

        if (existingConv) {
          setActiveConversation(existingConv.id);
        } else {
          // Create new conversation with context
          const title = generateConversationTitle(contextType, contextData);
          const newConv = await createConversation({
            title,
            contextType,
            contextId,
            metadata: { contextData: contextData || {} },
          });
          if (newConv) {
            setActiveConversation(newConv.id);
          }
        }
      } else if (!activeConversationId) {
        // Load conversations if no active one
        loadConversations();
      }
    };

    initializeChat();
  }, [
    propConversationId,
    contextType,
    contextId,
    loadConversations,
    conversations,
    setActiveConversation,
    activeConversationId,
    contextData,
    createConversation,
  ]);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (scrollViewRef.current && activeConversationId && messages[activeConversationId]) {
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages, activeConversationId]);

  // Show error alerts
  useEffect(() => {
    if (error) {
      Alert.alert('Error', error.message || 'Ocurrió un error al procesar tu mensaje', [
        { text: 'OK', onPress: clearError },
      ]);
    }
  }, [error, clearError]);

  const handleSend = async () => {
    if ((!message.trim() && pendingImages.length === 0) || isSending) return;

    const messageText = message.trim();
    setMessage('');

    // Upload images if any
    const attachments: ChatAttachment[] = [];
    const uploadErrors: string[] = [];

    if (pendingImages.length > 0) {
      setIsUploadingImages(true);
      logger.debug('Starting upload of images', 'ChatInterface', {
        count: pendingImages.length,
      });

      try {
        // Upload images in parallel for better performance
        const uploadPromises = pendingImages.map(async (imageUri, index) => {
          logger.debug(`Uploading image ${index + 1}/${pendingImages.length}`);

          try {
            const attachment = await uploadChatImage(imageUri);
            if (attachment) {
              logger.debug(`Image ${index + 1} uploaded successfully`);
              return { success: true, attachment };
            } else {
              logger.warn(`Image ${index + 1} failed to upload`);
              return { success: false, error: `Imagen ${index + 1}` };
            }
          } catch (error) {
            logger.warn(`Image ${index + 1} upload error:`, error);
            return { success: false, error: `Imagen ${index + 1}` };
          }
        });

        const results = await Promise.allSettled(uploadPromises);

        // Process results
        results.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            if (result.value.success) {
              attachments.push(result.value.attachment!);
            } else {
              uploadErrors.push(result.value.error);
            }
          } else {
            uploadErrors.push(`Imagen ${index + 1}`);
            logger.warn(`Image ${index + 1} promise rejected:`, result.reason);
          }
        });
      } catch (error) {
        logger.error(`Error during image upload batch: ${error}`);
        uploadErrors.push('Error inesperado durante la carga');
      } finally {
        setIsUploadingImages(false);
        setPendingImages([]);
      }

      // Handle upload results
      if (uploadErrors.length > 0 && attachments.length === 0) {
        // All uploads failed
        Alert.alert(
          'Error de carga',
          `No se pudo subir ninguna imagen: ${uploadErrors.join(', ')}. ¿Quieres enviar solo el mensaje de texto?`,
          [
            {
              text: 'Cancelar',
              style: 'cancel',
              onPress: () => setMessage(messageText),
            },
            {
              text: 'Enviar texto',
              onPress: () => {
                if (messageText) {
                  sendMessage(messageText, activeConversationId || undefined, []);
                }
              },
            },
          ]
        );
        return;
      } else if (uploadErrors.length > 0) {
        // Some uploads failed
        Alert.alert(
          'Carga parcial',
          `Algunas imágenes no se pudieron subir: ${uploadErrors.join(', ')}. Se enviará el mensaje con las imágenes exitosas.`
        );
      }
    }

    // Send message with attachments (empty array if no images or all failed)
    const finalMessage = messageText || (attachments.length > 0 ? 'Imagen adjunta' : '');
    if (finalMessage) {
      await sendMessage(finalMessage, activeConversationId || undefined, attachments);
    }
  };

  const handleImagePicker = async (source: 'camera' | 'library') => {
    try {
      const permissionResult =
        source === 'camera'
          ? await ImagePicker.requestCameraPermissionsAsync()
          : await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (!permissionResult.granted) {
        Alert.alert(
          'Permisos necesarios',
          `Necesitamos acceso a tu ${source === 'camera' ? 'cámara' : 'galería'} para enviar fotos`
        );
        return;
      }

      const result =
        source === 'camera'
          ? await ImagePicker.launchCameraAsync({
              mediaTypes: ['images'],
              quality: 0.8,
            })
          : await ImagePicker.launchImageLibraryAsync({
              mediaTypes: ['images'],
              quality: 0.8,
              allowsMultipleSelection: true,
              selectionLimit: 3,
            });

      if (!result.canceled && result.assets) {
        const compressedImages = await Promise.all(
          result.assets.map(async asset => {
            const compressedBase64 = await ImageProcessor.compressForUpload(asset.uri, false);
            return `data:image/jpeg;base64,${compressedBase64}`;
          })
        );
        setPendingImages([...pendingImages, ...compressedImages]);
      }
    } catch (error) {
      logger.error(
        `Error selecting images: ${error instanceof Error ? error.message : String(error)}`
      );
      Alert.alert('Error', 'No se pudo seleccionar la imagen');
    }
  };

  const removePendingImage = (index: number) => {
    setPendingImages(pendingImages.filter((_, i) => i !== index));
  };

  const generateConversationTitle = (type: string, data: Record<string, unknown> = {}): string => {
    switch (type) {
      case 'client':
        return `Consulta sobre ${data?.name || 'cliente'}`;
      case 'service':
        return `Servicio del ${new Date(typeof data?.serviceDate === 'string' || typeof data?.serviceDate === 'number' ? data.serviceDate : Date.now()).toLocaleDateString()}`;
      case 'formula':
        return `Fórmula: ${data?.technique || 'coloración'}`;
      case 'inventory':
        return `Consulta de inventario`;
      default:
        return 'Nueva conversación';
    }
  };

  const handleCreateNewConversation = async () => {
    const newConv = await createConversation({ title: 'Nueva conversación' });
    if (newConv) {
      setActiveConversation(newConv.id);
      // Cerrar sidebar en móviles para mostrar la nueva conversación
      if (!IS_TABLET) {
        setTimeout(() => setShowSidebar(false), 100);
      }
    }
  };

  const handleSelectConversation = (id: string) => {
    setActiveConversation(id);
    if (!IS_TABLET) {
      setShowSidebar(false);
    }
  };

  const renderMessage = (msg: ChatMessage) => {
    const isUser = msg.role === 'user';

    return (
      <View
        key={msg.id}
        style={[
          styles.messageContainer,
          isUser ? styles.userMessageContainer : styles.assistantMessageContainer,
        ]}
      >
        <View style={[styles.messageBubble, isUser ? styles.userBubble : styles.assistantBubble]}>
          {msg.content && <Markdown style={getChatMarkdownStyles(isUser)}>{msg.content}</Markdown>}

          {msg.attachments && msg.attachments.length > 0 && (
            <View style={styles.attachmentsContainer}>
              {msg.attachments.map((attachment, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.attachmentThumbnail}
                  onPress={() => setSelectedImage(attachment.url)}
                >
                  <Image
                    source={{ uri: attachment.thumbnailUrl || attachment.url }}
                    style={styles.attachmentImage}
                    resizeMode="cover"
                  />
                  {attachment.uploadStatus === 'uploading' && (
                    <View style={styles.uploadingOverlay}>
                      <ActivityIndicator size="small" color={Colors.light.surface} />
                    </View>
                  )}
                </TouchableOpacity>
              ))}
            </View>
          )}

          {!msg.synced && (
            <View style={styles.pendingIndicator}>
              <ActivityIndicator size="small" color={Colors.light.primary} />
            </View>
          )}
        </View>
        <Text style={styles.messageTime}>
          {new Date(msg.createdAt).toLocaleTimeString('es-ES', {
            hour: '2-digit',
            minute: '2-digit',
          })}
        </Text>
      </View>
    );
  };

  const currentMessages = activeConversationId ? messages[activeConversationId] || [] : [];

  return (
    <KeyboardAvoidingView
      style={styles.mainContainer}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={isModal ? 0 : insets.top}
    >
      {/* Sidebar for tablets */}
      {IS_TABLET && showSidebar && (
        <View style={styles.sidebar}>
          <ConversationsList
            conversations={conversations}
            activeConversationId={activeConversationId}
            isLoading={isLoading}
            onSelectConversation={handleSelectConversation}
            onArchiveConversation={archiveConversation}
            onNewConversation={handleCreateNewConversation}
            onToggleFavorite={toggleFavorite}
          />
        </View>
      )}

      {/* BottomSheet for mobile */}
      {!IS_TABLET && (
        <BottomSheet
          visible={showSidebar}
          onClose={() => setShowSidebar(false)}
          height={undefined}
          scrollable={false}
          contentHandlesScroll={true}
          disableSwipeToClose={true}
        >
          <ConversationsList
            conversations={conversations}
            activeConversationId={activeConversationId}
            isLoading={isLoading}
            onSelectConversation={handleSelectConversation}
            onArchiveConversation={archiveConversation}
            onNewConversation={handleCreateNewConversation}
            onToggleFavorite={toggleFavorite}
          />
        </BottomSheet>
      )}

      <View style={[styles.container, IS_TABLET && showSidebar && styles.containerWithSidebar]}>
        {/* Header */}
        <View style={[styles.header, isModal && styles.modalHeader]}>
          <View style={styles.headerLeft}>
            {!IS_TABLET && (
              <TouchableOpacity onPress={() => setShowSidebar(true)} style={styles.menuButton}>
                <Menu size={24} color={Colors.light.text} />
              </TouchableOpacity>
            )}
            <MessageCircle size={24} color={Colors.light.primary} />
            <View style={styles.headerTextContainer}>
              <Text style={styles.headerTitle}>Asistente Salonier</Text>
              {contextType && (
                <Text style={styles.headerSubtitle}>
                  {contextType === 'client' && 'Consulta sobre cliente'}
                  {contextType === 'service' && 'Consulta sobre servicio'}
                  {contextType === 'formula' && 'Consulta sobre fórmula'}
                  {contextType === 'inventory' && 'Consulta de inventario'}
                </Text>
              )}
            </View>
          </View>
          {isModal && onClose && (
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color={Colors.light.text} />
            </TouchableOpacity>
          )}
        </View>

        {/* Messages */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          contentContainerStyle={styles.messagesContent}
          showsVerticalScrollIndicator={false}
        >
          {isLoading && currentMessages.length === 0 ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={Colors.light.primary} />
              <Text style={styles.loadingText}>Cargando conversación...</Text>
            </View>
          ) : currentMessages.length === 0 ? (
            <View style={styles.emptyContainer}>
              <MessageCircle size={48} color={Colors.light.textSecondary} />
              <Text style={styles.emptyTitle}>¡Hola! Soy tu asistente experto</Text>
              <Text style={styles.emptyText}>
                Puedo ayudarte con fórmulas, diagnósticos, técnicas de aplicación y cualquier
                consulta profesional sobre colorimetría.
              </Text>
              {contextType && (
                <BaseCard style={styles.contextCard}>
                  <View style={styles.contextHeader}>
                    <AlertCircle size={16} color={Colors.light.primary} />
                    <Text style={styles.contextTitle}>Contexto activo</Text>
                  </View>
                  <Text style={styles.contextText}>
                    {contextType === 'client' && `Cliente: ${contextData?.name || 'Sin nombre'}`}
                    {contextType === 'service' &&
                      `Servicio del ${new Date(typeof contextData?.serviceDate === 'string' || typeof contextData?.serviceDate === 'number' ? contextData.serviceDate : Date.now()).toLocaleDateString()}`}
                    {contextType === 'formula' &&
                      `Técnica: ${contextData?.technique || 'Sin especificar'}`}
                    {contextType === 'inventory' && 'Consultando tu inventario actual'}
                  </Text>
                </BaseCard>
              )}
            </View>
          ) : (
            currentMessages.map(renderMessage)
          )}
          {isSending && (
            <View style={[styles.messageContainer, styles.assistantMessageContainer]}>
              <View style={[styles.messageBubble, styles.assistantBubble, styles.typingBubble]}>
                <ActivityIndicator size="small" color={Colors.light.text} />
                <Text style={styles.typingText}>Escribiendo...</Text>
              </View>
            </View>
          )}
        </ScrollView>

        {/* Input */}
        <View
          style={[
            styles.inputContainer,
            {
              paddingBottom: isModal ? spacing.md : insets.bottom + spacing.sm,
            },
          ]}
        >
          {/* Pending images preview */}
          {pendingImages.length > 0 && (
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.pendingImagesContainer}
            >
              {pendingImages.map((uri, index) => (
                <View key={index} style={styles.pendingImageWrapper}>
                  <Image source={{ uri }} style={styles.pendingImage} />
                  <TouchableOpacity
                    style={styles.removePendingImage}
                    onPress={() => removePendingImage(index)}
                  >
                    <X size={16} color={Colors.light.surface} />
                  </TouchableOpacity>
                </View>
              ))}
            </ScrollView>
          )}

          <View style={styles.inputWrapper}>
            <TouchableOpacity
              style={styles.imageButton}
              onPress={() => handleImagePicker('camera')}
              disabled={isSending || isUploadingImages}
            >
              <Camera size={24} color={Colors.light.primary} />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.imageButton}
              onPress={() => handleImagePicker('library')}
              disabled={isSending || isUploadingImages}
            >
              <ImageIcon size={24} color={Colors.light.primary} />
            </TouchableOpacity>

            <TextInput
              style={styles.input}
              value={message}
              onChangeText={setMessage}
              placeholder="Escribe tu consulta..."
              placeholderTextColor={Colors.light.textSecondary}
              multiline
              // maxHeight={100} // Not supported in TextInput props
              editable={!isSending && !isUploadingImages}
            />

            <TouchableOpacity
              style={[
                styles.sendButton,
                ((!message.trim() && pendingImages.length === 0) ||
                  isSending ||
                  isUploadingImages) &&
                  styles.sendButtonDisabled,
              ]}
              onPress={handleSend}
              disabled={
                (!message.trim() && pendingImages.length === 0) || isSending || isUploadingImages
              }
            >
              {isUploadingImages ? (
                <ActivityIndicator size="small" color={Colors.light.surface} />
              ) : (
                <Send size={20} color={Colors.light.surface} />
              )}
            </TouchableOpacity>
          </View>
        </View>

        {/* Image viewer modal */}
        <Modal
          visible={!!selectedImage}
          transparent
          animationType="fade"
          onRequestClose={() => setSelectedImage(null)}
        >
          <TouchableOpacity
            style={styles.imageViewerContainer}
            activeOpacity={1}
            onPress={() => setSelectedImage(null)}
          >
            <View style={styles.imageViewerContent}>
              {selectedImage && (
                <Image
                  source={{ uri: selectedImage }}
                  style={styles.fullImage}
                  resizeMode="contain"
                />
              )}
              <TouchableOpacity
                style={styles.closeImageViewer}
                onPress={() => setSelectedImage(null)}
              >
                <X size={24} color={Colors.light.surface} />
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        </Modal>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  containerWithSidebar: {
    marginLeft: SIDEBAR_WIDTH,
  },
  sidebar: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: SIDEBAR_WIDTH,
    backgroundColor: Colors.light.surface,
    borderRightWidth: 1,
    borderRightColor: Colors.light.border,
    zIndex: 1,
  },
  menuButton: {
    marginRight: spacing.sm,
    padding: spacing.xs,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: spacing.md,
    backgroundColor: Colors.light.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  modalHeader: {
    borderTopLeftRadius: radius.xl,
    borderTopRightRadius: radius.xl,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerTextContainer: {
    marginLeft: spacing.sm,
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  headerSubtitle: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginTop: 2,
  },
  closeButton: {
    padding: spacing.xs,
  },
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    padding: spacing.md,
    paddingBottom: spacing.xl,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  loadingText: {
    marginTop: spacing.md,
    fontSize: typography.sizes.lg,
    color: Colors.light.textSecondary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xl,
    paddingHorizontal: spacing.xl,
  },
  emptyTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginTop: spacing.md,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: typography.sizes.lg,
    color: Colors.light.textSecondary,
    marginTop: spacing.sm,
    textAlign: 'center',
    lineHeight: 22,
  },
  contextCard: {
    marginTop: spacing.xl,
    padding: spacing.md,
  },
  contextHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  contextTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.primary,
    marginLeft: spacing.xs,
  },
  contextText: {
    fontSize: typography.sizes.lg,
    color: Colors.light.text,
  },
  messageContainer: {
    marginBottom: spacing.md,
  },
  userMessageContainer: {
    alignItems: 'flex-end',
  },
  assistantMessageContainer: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: '80%',
    padding: spacing.md,
    borderRadius: radius.lg,
  },
  userBubble: {
    backgroundColor: Colors.light.primary,
  },
  assistantBubble: {
    backgroundColor: Colors.light.surface,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  messageText: {
    fontSize: typography.sizes.lg,
    lineHeight: 22,
  },
  userMessageText: {
    color: Colors.light.surface,
  },
  assistantMessageText: {
    color: Colors.light.text,
  },
  messageTime: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
    marginTop: spacing.xs,
    marginHorizontal: spacing.xs,
  },
  pendingIndicator: {
    position: 'absolute',
    bottom: -spacing.xs,
    right: -spacing.xs,
  },
  typingBubble: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typingText: {
    marginLeft: spacing.sm,
    fontSize: typography.sizes.lg,
    color: Colors.light.textSecondary,
  },
  inputContainer: {
    backgroundColor: Colors.light.surface,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
    paddingHorizontal: spacing.md,
    paddingTop: spacing.md,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  input: {
    flex: 1,
    backgroundColor: Colors.light.background,
    borderRadius: radius.lg,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: typography.sizes.lg,
    color: Colors.light.text,
    minHeight: 44,
    maxHeight: 100,
  },
  sendButton: {
    marginLeft: spacing.sm,
    width: 44,
    height: 44,
    borderRadius: radius.full,
    backgroundColor: Colors.light.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: Colors.light.textSecondary,
    opacity: 0.5,
  },
  imageButton: {
    marginRight: spacing.sm,
    padding: spacing.xs,
  },
  attachmentsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: spacing.sm,
    gap: spacing.xs,
  },
  attachmentThumbnail: {
    width: 150,
    height: 150,
    borderRadius: radius.md,
    overflow: 'hidden',
  },
  attachmentImage: {
    width: '100%',
    height: '100%',
  },
  uploadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: Colors.light.darkGray + '80',
    justifyContent: 'center',
    alignItems: 'center',
  },
  pendingImagesContainer: {
    marginBottom: spacing.sm,
    maxHeight: 80,
  },
  pendingImageWrapper: {
    marginRight: spacing.sm,
    position: 'relative',
  },
  pendingImage: {
    width: 70,
    height: 70,
    borderRadius: radius.md,
  },
  removePendingImage: {
    position: 'absolute',
    top: -spacing.xs,
    right: -spacing.xs,
    backgroundColor: Colors.light.error,
    borderRadius: radius.full,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageViewerContainer: {
    flex: 1,
    backgroundColor: Colors.light.backgroundDark + 'E6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageViewerContent: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullImage: {
    width: '100%',
    height: '80%',
  },
  closeImageViewer: {
    position: 'absolute',
    top: 50,
    right: 20,
    backgroundColor: Colors.light.darkGray + '80',
    borderRadius: radius.full,
    padding: spacing.sm,
  },
});

// Helper function to create chat markdown styles
const getChatMarkdownStyles = (isUser: boolean) => ({
  body: {
    ...styles.messageText,
    ...(isUser ? styles.userMessageText : styles.assistantMessageText),
  },
  strong: {
    fontWeight: '700',
    color: isUser ? 'white' : Colors.light.text,
  },
  em: {
    fontStyle: 'italic',
    color: isUser ? 'white' : Colors.light.text,
  },
  bullet_list: {
    marginVertical: spacing.xs,
  },
  bullet_list_icon: {
    color: isUser ? 'white' : Colors.light.text,
    fontSize: typography.sizes.lg,
  },
  paragraph: {
    marginVertical: spacing.xs / 2,
  },
  heading1: {
    fontSize: typography.sizes.lg,
    fontWeight: '700',
    marginVertical: spacing.sm,
    color: isUser ? 'white' : Colors.light.text,
  },
  heading2: {
    fontSize: typography.sizes.lg * 1.1,
    fontWeight: '600',
    marginVertical: spacing.sm,
    color: isUser ? 'white' : Colors.light.text,
  },
  code_inline: {
    backgroundColor: isUser ? 'rgba(255,255,255,0.2)' : Colors.light.background,
    paddingHorizontal: spacing.xs / 2,
    paddingVertical: 2,
    borderRadius: 4,
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
    fontSize: typography.sizes.sm,
  },
});
