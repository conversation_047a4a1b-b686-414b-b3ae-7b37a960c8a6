import React, { useState, useRef, useEffect, useCallback } from 'react';
import { logger } from '@/utils/logger';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Alert,
  Vibration,
} from 'react-native';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import * as Haptics from 'expo-haptics';
import * as ImagePicker from 'expo-image-picker';
import {
  Camera,
  FlipHorizontal,
  Grid3X3,
  Check,
  X,
  RefreshCw,
  Image as ImageIcon,
} from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import Colors from '@/constants/colors';
import { spacing } from '@/constants/theme';

const { width: _SCREEN_WIDTH, height: _SCREEN_HEIGHT } = Dimensions.get('window');

interface CameraCaptureProps {
  onCapture: (uri: string) => void;
  onCancel: () => void;
  captureMode?: 'single' | 'multiple';
  maxPhotos?: number;
  showGuides?: boolean;
  analysisContext?: 'hair-analysis' | 'color-reference' | 'result';
}

type LightQuality = 'excellent' | 'good' | 'fair' | 'poor';
type CaptureGuide = {
  title: string;
  description: string;
  icon: string;
  position: { top?: number; bottom?: number; left?: number; right?: number };
};

const CAPTURE_GUIDES: Record<string, CaptureGuide[]> = {
  'hair-analysis': [
    {
      title: 'Vista frontal',
      description: 'Enfoca la línea del cabello y raíces',
      icon: '1️⃣',
      position: { top: 120, left: 60 },
    },
    {
      title: 'Vista lateral',
      description: 'Captura medios y longitudes',
      icon: '2️⃣',
      position: { top: 120, right: 60 },
    },
    {
      title: 'Vista puntas',
      description: 'Enfoca las puntas del cabello',
      icon: '3️⃣',
      position: { bottom: 180, left: '50%' as const },
    },
  ],
  'color-reference': [
    {
      title: 'Color uniforme',
      description: 'Busca una zona con color parejo',
      icon: '🎨',
      position: { top: 240, left: '50%' as const },
    },
  ],
  result: [
    {
      title: 'Resultado final',
      description: 'Captura el resultado completo',
      icon: '✨',
      position: { top: 180, left: '50%' as const },
    },
  ],
};

export const CameraCapture: React.FC<CameraCaptureProps> = ({
  onCapture,
  onCancel,
  captureMode = 'single',
  maxPhotos = 3,
  showGuides = true,
  analysisContext = 'hair-analysis',
}) => {
  const [permission, requestPermission] = useCameraPermissions();
  const [facing, setFacing] = useState<CameraType>('back');
  const [capturedPhotos, setCapturedPhotos] = useState<string[]>([]);
  const [lightQuality, setLightQuality] = useState<LightQuality>('good');
  const [showGridLines, setShowGridLines] = useState(true);
  const [lastCapturedPhoto, setLastCapturedPhoto] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  const cameraRef = useRef<CameraView>(null);
  const captureButtonScale = useSharedValue(1);
  const flashOpacity = useSharedValue(0);

  const guides = CAPTURE_GUIDES[analysisContext] || [];

  // Detectar calidad de luz (simulado)
  useEffect(() => {
    const lightTimer = setInterval(() => {
      // En una implementación real, usarías el sensor de luz o análisis de imagen
      const qualities: LightQuality[] = ['excellent', 'good', 'fair'];
      const randomQuality = qualities[Math.floor(Math.random() * qualities.length)];
      setLightQuality(randomQuality);
    }, 2000);

    return () => clearInterval(lightTimer);
  }, []);

  const animatedCaptureButtonStyle = useAnimatedStyle(() => ({
    transform: [{ scale: captureButtonScale.value }],
  }));

  const animatedFlashStyle = useAnimatedStyle(() => ({
    opacity: flashOpacity.value,
  }));

  const handleCapturePress = useCallback(async () => {
    if (!cameraRef.current) return;

    try {
      // Haptic feedback
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // Animate capture button
      captureButtonScale.value = withSequence(
        withTiming(0.8, { duration: 100 }),
        withTiming(1, { duration: 100 })
      );

      // Flash effect
      flashOpacity.value = withSequence(
        withTiming(1, { duration: 100 }),
        withTiming(0, { duration: 200 })
      );

      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: false,
      });

      if (photo) {
        setLastCapturedPhoto(photo.uri);
        setShowPreview(true);

        // Vibrate for confirmation
        Vibration.vibrate(50);
      }
    } catch (error) {
      logger.error('Error taking picture:', error);
      Alert.alert('Error', 'No se pudo capturar la foto');
    }
  }, [captureButtonScale, flashOpacity]);

  const handleConfirmCapture = useCallback(() => {
    if (!lastCapturedPhoto) return;

    if (captureMode === 'multiple') {
      setCapturedPhotos(prev => [...prev, lastCapturedPhoto]);

      if (capturedPhotos.length + 1 >= maxPhotos) {
        onCapture(lastCapturedPhoto); // Return last photo or array
      } else {
        setShowPreview(false);
        setLastCapturedPhoto(null);
      }
    } else {
      onCapture(lastCapturedPhoto);
    }
  }, [lastCapturedPhoto, captureMode, capturedPhotos.length, maxPhotos, onCapture]);

  const handleRetakePhoto = useCallback(() => {
    setShowPreview(false);
    setLastCapturedPhoto(null);
  }, []);

  const handlePickFromGallery = useCallback(async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        onCapture(result.assets[0].uri);
      }
    } catch (error) {
      logger.error('Error picking image:', error);
    }
  }, [onCapture]);

  if (!permission) {
    return <View style={styles.container} />;
  }

  if (!permission.granted) {
    return (
      <View style={styles.permissionContainer}>
        <Camera size={64} color={Colors.light.textSecondary} />
        <Text style={styles.permissionTitle}>Acceso a cámara requerido</Text>
        <Text style={styles.permissionText}>
          Para analizar el cabello necesitamos acceso a tu cámara
        </Text>
        <TouchableOpacity style={styles.permissionButton} onPress={requestPermission}>
          <Text style={styles.permissionButtonText}>Permitir cámara</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const getLightQualityColor = () => {
    switch (lightQuality) {
      case 'excellent':
        return Colors.light.success;
      case 'good':
        return Colors.light.warning;
      case 'fair':
        return Colors.light.warning;
      case 'poor':
        return Colors.light.error;
    }
  };

  const getLightQualityText = () => {
    switch (lightQuality) {
      case 'excellent':
        return '✨ Iluminación excelente';
      case 'good':
        return '☀️ Buena iluminación';
      case 'fair':
        return '⚠️ Iluminación regular';
      case 'poor':
        return '🌙 Poca luz - muévete a zona más iluminada';
    }
  };

  if (showPreview && lastCapturedPhoto) {
    return (
      <View style={styles.container}>
        <View style={styles.previewContainer}>
          <Text style={styles.previewTitle}>¿Te gusta la foto?</Text>

          <View style={styles.previewActions}>
            <TouchableOpacity style={styles.retakeButton} onPress={handleRetakePhoto}>
              <RefreshCw size={20} color={Colors.light.textLight} />
              <Text style={styles.retakeButtonText}>Repetir</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.confirmButton} onPress={handleConfirmCapture}>
              <Check size={20} color={Colors.light.textLight} />
              <Text style={styles.confirmButtonText}>Usar esta</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <CameraView ref={cameraRef} style={styles.camera} facing={facing}>
        {/* Flash overlay */}
        <Animated.View style={[styles.flashOverlay, animatedFlashStyle]} />

        {/* Light quality indicator */}
        <View style={styles.lightQualityContainer}>
          <View style={[styles.lightQualityIndicator, { backgroundColor: getLightQualityColor() }]}>
            <Text style={styles.lightQualityText}>{getLightQualityText()}</Text>
          </View>
        </View>

        {/* Grid lines */}
        {showGridLines && (
          <View style={styles.gridContainer}>
            <View style={styles.gridLine} />
            <View style={[styles.gridLine, styles.gridLineVertical]} />
            <View style={[styles.gridLine, styles.gridLineBottom]} />
            <View style={[styles.gridLine, styles.gridLineVertical, styles.gridLineRight]} />
          </View>
        )}

        {/* Capture guides */}
        {showGuides &&
          guides.map((guide, index) => (
            <View key={index} style={[styles.captureGuide, guide.position]}>
              <Text style={styles.guideIcon}>{guide.icon}</Text>
              <Text style={styles.guideTitle}>{guide.title}</Text>
              <Text style={styles.guideDescription}>{guide.description}</Text>
            </View>
          ))}

        {/* Top controls */}
        <View style={styles.topControls}>
          <TouchableOpacity style={styles.controlButton} onPress={onCancel}>
            <X size={24} color={Colors.light.textLight} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.controlButton}
            onPress={() => setShowGridLines(!showGridLines)}
          >
            <Grid3X3
              size={24}
              color={showGridLines ? Colors.light.primary : Colors.light.textLight}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.controlButton}
            onPress={() => setFacing(facing === 'back' ? 'front' : 'back')}
          >
            <FlipHorizontal size={24} color={Colors.light.textLight} />
          </TouchableOpacity>
        </View>

        {/* Bottom controls */}
        <View style={styles.bottomControls}>
          <TouchableOpacity style={styles.galleryButton} onPress={handlePickFromGallery}>
            <ImageIcon size={24} color={Colors.light.textLight} />
          </TouchableOpacity>

          <Animated.View style={animatedCaptureButtonStyle}>
            <TouchableOpacity style={styles.captureButton} onPress={handleCapturePress}>
              <LinearGradient
                colors={[Colors.light.primary, Colors.light.secondary]}
                style={styles.captureButtonGradient}
              >
                <Camera size={32} color={Colors.light.textLight} />
              </LinearGradient>
            </TouchableOpacity>
          </Animated.View>

          <View style={styles.placeholder} />
        </View>
      </CameraView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.common.black,
  },
  camera: {
    flex: 1,
  },

  // Permission styles
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
    backgroundColor: Colors.light.background,
  },
  permissionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.light.text,
    marginTop: spacing.lg,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  permissionText: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.xl,
    lineHeight: 24,
  },
  permissionButton: {
    backgroundColor: Colors.light.primary,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    borderRadius: 12,
  },
  permissionButtonText: {
    color: Colors.light.textLight,
    fontSize: 16,
    fontWeight: '600',
  },

  // Flash overlay
  flashOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: Colors.light.background,
    pointerEvents: 'none',
  },

  // Light quality
  lightQualityContainer: {
    position: 'absolute',
    top: 80,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  lightQualityIndicator: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    borderRadius: 20,
    maxWidth: '90%',
  },
  lightQualityText: {
    color: Colors.light.textLight,
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },

  // Grid lines
  gridContainer: {
    ...StyleSheet.absoluteFillObject,
    pointerEvents: 'none',
  },
  gridLine: {
    position: 'absolute',
    backgroundColor: Colors.light.backgroundWithOpacity,
  },
  gridLineVertical: {
    width: 1,
    height: '100%',
    left: '33.33%',
  },
  gridLineBottom: {
    top: '66.66%',
  },
  gridLineRight: {
    left: '66.66%',
  },

  // Capture guides
  captureGuide: {
    position: 'absolute',
    backgroundColor: Colors.common.shadowColor + 'B3', // 0.7 opacity
    borderRadius: 8,
    padding: spacing.sm,
    alignItems: 'center',
    maxWidth: 120,
  },
  guideIcon: {
    fontSize: 24,
    marginBottom: 4,
  },
  guideTitle: {
    color: Colors.light.textLight,
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  guideDescription: {
    color: Colors.light.backgroundOpacity90 + 'CC', // 0.8 opacity on white
    fontSize: 10,
    textAlign: 'center',
    marginTop: 2,
  },

  // Controls
  topControls: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
  },
  controlButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: Colors.common.shadowColor + '80', // 0.5 opacity
    justifyContent: 'center',
    alignItems: 'center',
  },

  bottomControls: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  galleryButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: Colors.common.shadowColor + '80', // 0.5 opacity
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  captureButtonGradient: {
    flex: 1,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholder: {
    width: 44,
    height: 44,
  },

  // Preview
  previewContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.common.black,
    padding: spacing.xl,
  },
  previewTitle: {
    color: Colors.light.textLight,
    fontSize: 24,
    fontWeight: '600',
    marginBottom: spacing.xl,
    textAlign: 'center',
  },
  previewActions: {
    flexDirection: 'row',
    gap: spacing.lg,
  },
  retakeButton: {
    backgroundColor: Colors.light.textSecondary,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
    gap: spacing.xs,
  },
  retakeButtonText: {
    color: Colors.light.textLight,
    fontSize: 16,
    fontWeight: '600',
  },
  confirmButton: {
    backgroundColor: Colors.light.success,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
    gap: spacing.xs,
  },
  confirmButtonText: {
    color: Colors.light.textLight,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CameraCapture;
