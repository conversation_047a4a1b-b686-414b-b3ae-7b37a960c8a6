import React from 'react';
import { StyleSheet, Text, View, TouchableOpacity, Modal, ScrollView } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  interpolate,
} from 'react-native-reanimated';
import { ChevronDown, Check, CheckCircle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import {
  hairColorMap,
  temperatureColors,
  getTemperatureFromReflect,
  getIndicatorType,
} from '@/constants/hair-colors';
import HairVisualIndicator from './HairVisualIndicator';

interface DiagnosisSelectorProps {
  label: string;
  value: string;
  options: string[] | number[];
  onValueChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  isFromAI?: boolean;
  showColorIndicator?: boolean;
  showTemperatureIndicator?: boolean;
}

export default function DiagnosisSelector({
  label,
  value,
  options,
  onValueChange,
  placeholder = 'Seleccionar',
  required = false,
  isFromAI = false,
  showColorIndicator: _showColorIndicator = false,
  showTemperatureIndicator = false,
}: DiagnosisSelectorProps) {
  const [isModalVisible, setIsModalVisible] = React.useState(false);
  const [hasBeenEdited, setHasBeenEdited] = React.useState(false);
  const fadeAnim = useSharedValue(0);
  const borderColorAnim = useSharedValue(0);
  const scaleAnim = useSharedValue(0.95);

  // Auto-detect indicator type based on label
  const indicatorType = React.useMemo(() => getIndicatorType(label), [label]);

  // Debug log
  React.useEffect(() => {
    if (value) {
      // Debug logging removed for production
    }
  }, [value, label]);

  // Animate when value appears from AI
  React.useEffect(() => {
    if (value && isFromAI && !hasBeenEdited) {
      fadeAnim.value = withTiming(1, { duration: 300 });
      borderColorAnim.value = withTiming(1, { duration: 300 });
      scaleAnim.value = withSpring(1, { damping: 8, stiffness: 40 });
    }
  }, [value, isFromAI, hasBeenEdited, fadeAnim, borderColorAnim, scaleAnim]);

  // Animated styles using useAnimatedStyle
  const animatedContainerStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scaleAnim.value }],
    borderColor: interpolate(
      borderColorAnim.value,
      [0, 1],
      [Colors.light.border, Colors.light.success]
    ),
    backgroundColor: interpolate(
      borderColorAnim.value,
      [0, 1],
      [Colors.light.surface, Colors.light.success + '08']
    ),
  }));

  const animatedCheckStyle = useAnimatedStyle(() => ({
    opacity: fadeAnim.value,
  }));

  const handleSelect = (option: string | number) => {
    const value =
      typeof option === 'number' && option % 1 !== 0 ? option.toFixed(1) : option.toString();
    onValueChange(value);
    setIsModalVisible(false);
    setHasBeenEdited(true);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>
        {label} {required && <Text style={styles.required}>*</Text>}
      </Text>

      <View>
        <Animated.View
          style={[
            styles.selectorWrapper,
            value && isFromAI && !hasBeenEdited ? animatedContainerStyle : {},
          ]}
        >
          <TouchableOpacity style={styles.selector} onPress={() => setIsModalVisible(true)}>
            <View style={styles.selectorContent}>
              {indicatorType !== 'none' && value && (
                <HairVisualIndicator type={indicatorType} value={value} size="small" />
              )}
              {showTemperatureIndicator && value && indicatorType === 'color' && (
                <View
                  style={[
                    styles.temperatureIndicator,
                    {
                      backgroundColor: temperatureColors[getTemperatureFromReflect(value)],
                    },
                  ]}
                />
              )}
              <Text style={[styles.selectorText, !value && styles.placeholderText]}>
                {value || placeholder}
              </Text>
            </View>
            {value && isFromAI && !hasBeenEdited ? (
              <Animated.View style={[styles.animatedCheckContainer, animatedCheckStyle]}>
                <CheckCircle size={20} color={Colors.light.success} />
              </Animated.View>
            ) : (
              <ChevronDown size={20} color={Colors.light.gray} />
            )}
          </TouchableOpacity>
        </Animated.View>
        {indicatorType === 'color' && value && hairColorMap[value] && (
          <View style={[styles.colorLine, { backgroundColor: hairColorMap[value] }]} />
        )}
      </View>

      <Modal
        visible={isModalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setIsModalVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setIsModalVisible(false)}
        >
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{label}</Text>
            </View>

            <ScrollView showsVerticalScrollIndicator={true} keyboardShouldPersistTaps="handled">
              {options.map((item, index) => {
                const itemStr =
                  typeof item === 'number' && item % 1 !== 0 ? item.toFixed(1) : item.toString();
                const valueStr = value || '';

                return (
                  <React.Fragment key={item.toString()}>
                    <TouchableOpacity
                      style={[styles.option, valueStr === itemStr && styles.selectedOption]}
                      onPress={() => handleSelect(item)}
                    >
                      <View style={styles.optionContent}>
                        {indicatorType !== 'none' && (
                          <HairVisualIndicator type={indicatorType} value={itemStr} size="small" />
                        )}
                        {showTemperatureIndicator && indicatorType === 'color' && (
                          <View
                            style={[
                              styles.temperatureIndicator,
                              {
                                backgroundColor:
                                  temperatureColors[getTemperatureFromReflect(itemStr)],
                              },
                            ]}
                          />
                        )}
                        <Text
                          style={[
                            styles.optionText,
                            valueStr === itemStr && styles.selectedOptionText,
                          ]}
                        >
                          {itemStr}
                        </Text>
                      </View>
                      {valueStr === itemStr && <Check size={20} color={Colors.light.primary} />}
                    </TouchableOpacity>
                    {index < options.length - 1 && <View style={styles.separator} />}
                  </React.Fragment>
                );
              })}
            </ScrollView>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  required: {
    color: Colors.light.error,
  },
  selectorWrapper: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.light.border,
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  selectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 12,
  },
  temperatureIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  colorLine: {
    height: 2,
    marginTop: -2,
    marginHorizontal: 12,
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  },
  selectorText: {
    fontSize: 16,
    color: Colors.light.text,
    flex: 1,
  },
  placeholderText: {
    color: Colors.light.gray,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: Colors.common.shadowColor + '80', // 0.5 opacity
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: Colors.light.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '70%',
  },
  modalHeader: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    textAlign: 'center',
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 12,
  },
  selectedOption: {
    backgroundColor: Colors.light.primaryTransparent10,
  },
  optionText: {
    fontSize: 16,
    color: Colors.light.text,
  },
  selectedOptionText: {
    color: Colors.light.primary,
    fontWeight: '500',
  },
  separator: {
    height: 1,
    backgroundColor: Colors.light.border,
    marginHorizontal: 16,
  },
  animatedCheckContainer: {
    // Container for the animated check icon - no additional styling needed
  },
});
