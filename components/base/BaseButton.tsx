import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  Animated,
  ActivityIndicator,
} from 'react-native';
import { LucideIcon } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { radius, spacing, typography, shadows, components } from '@/constants/theme';
import * as Haptics from 'expo-haptics';
import { a11y } from '@/utils/accessibility';

interface BaseButtonProps {
  onPress: () => void;
  title?: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'dark';
  size?: 'sm' | 'md' | 'lg';
  icon?: LucideIcon;
  iconPosition?: 'left' | 'right';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  haptic?: boolean;
  accessibilityLabel?: string;
  accessibilityHint?: string;
}

export const BaseButton: React.FC<BaseButtonProps> = ({
  onPress,
  title,
  variant = 'primary',
  size = 'md',
  icon: Icon,
  iconPosition = 'left',
  disabled = false,
  loading = false,
  fullWidth = false,
  style,
  textStyle,
  haptic = true,
  accessibilityLabel,
  accessibilityHint,
}) => {
  const scaleAnim = React.useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.97,
      useNativeDriver: true,
      speed: 20,
      bounciness: 0,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      speed: 20,
      bounciness: 0,
    }).start();
  };

  const handlePress = () => {
    if (haptic && !disabled && !loading) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    onPress();
  };

  const getVariantStyle = () => {
    switch (variant) {
      case 'primary':
        return styles.primary;
      case 'secondary':
        return styles.secondary;
      case 'outline':
        return styles.outline;
      case 'ghost':
        return styles.ghost;
      case 'dark':
        return styles.dark;
      default:
        return styles.primary;
    }
  };

  const getSizeStyle = () => {
    switch (size) {
      case 'sm':
        return styles.sm;
      case 'md':
        return styles.md;
      case 'lg':
        return styles.lg;
      default:
        return styles.md;
    }
  };

  const buttonStyle = [
    styles.base,
    getVariantStyle(),
    getSizeStyle(),
    fullWidth && styles.fullWidth,
    (disabled || loading) && styles.disabled,
    style,
  ];

  const textColor =
    variant === 'primary' || variant === 'dark'
      ? Colors.light.textLight
      : variant === 'outline' || variant === 'ghost'
        ? Colors.light.primary
        : Colors.light.text;

  const iconColor = textColor;
  const iconSize = size === 'sm' ? 16 : size === 'md' ? 20 : 24;

  // Generate accessibility label if not provided
  const defaultLabel = loading ? 'Cargando' : title || 'Botón';

  const a11yProps = a11y.button(accessibilityLabel || defaultLabel, accessibilityHint);

  return (
    <TouchableOpacity
      onPress={handlePress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      disabled={disabled || loading}
      activeOpacity={1}
      {...a11yProps}
      accessibilityState={{
        disabled: disabled || loading,
        busy: loading,
      }}
    >
      <Animated.View style={[buttonStyle, { transform: [{ scale: scaleAnim }] }]}>
        {loading ? (
          <ActivityIndicator size="small" color={textColor} />
        ) : (
          <>
            {Icon && iconPosition === 'left' && (
              <Icon size={iconSize} color={iconColor} style={styles.leftIcon} />
            )}
            {title && (
              <Text
                style={[
                  styles.text,
                  {
                    fontSize:
                      size === 'sm'
                        ? typography.sizes.sm
                        : size === 'md'
                          ? typography.sizes.base
                          : typography.sizes.lg,
                    color: textColor,
                  },
                  textStyle,
                ]}
              >
                {title}
              </Text>
            )}
            {Icon && iconPosition === 'right' && (
              <Icon size={iconSize} color={iconColor} style={styles.rightIcon} />
            )}
          </>
        )}
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  base: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: radius.md,
    ...shadows.sm,
  },

  // Sizes
  sm: {
    height: components.button.height.sm,
    paddingHorizontal: components.button.paddingHorizontal.sm,
  },
  md: {
    height: components.button.height.md,
    paddingHorizontal: components.button.paddingHorizontal.md,
  },
  lg: {
    height: components.button.height.lg,
    paddingHorizontal: components.button.paddingHorizontal.lg,
  },

  // Variants
  primary: {
    backgroundColor: Colors.light.primary,
  },
  secondary: {
    backgroundColor: Colors.light.secondary,
  },
  outline: {
    backgroundColor: Colors.common.transparent,
    borderWidth: 1,
    borderColor: Colors.light.primary,
  },
  ghost: {
    backgroundColor: Colors.common.transparent,
  },
  dark: {
    backgroundColor: Colors.light.text,
  },

  // Text sizes
  text: {
    fontWeight: typography.weights.semibold,
  },

  // States
  fullWidth: {
    width: '100%',
  },
  disabled: {
    opacity: 0.5,
  },
  leftIcon: {
    marginRight: spacing.sm,
  },
  rightIcon: {
    marginLeft: spacing.sm,
  },
});
