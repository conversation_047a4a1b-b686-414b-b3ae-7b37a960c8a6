import React, { useEffect, useRef } from 'react';
import { View, Text, ActivityIndicator, StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withRepeat,
  interpolate,
  Easing,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import Colors from '@/constants/colors';
import { typography, spacing } from '@/constants/theme';
import { a11y } from '@/utils/accessibility';

type LoadingVariant = 'spinner' | 'skeleton' | 'pulse' | 'shimmer' | 'contextual';
type LoadingContext = 'photo-analysis' | 'formulation' | 'ai-processing' | 'sync' | 'generic';

interface LoadingStateProps {
  message?: string;
  fullScreen?: boolean;
  variant?: LoadingVariant;
  context?: LoadingContext;
  progress?: number; // 0-100
  estimatedDuration?: number; // seconds
  showProgress?: boolean;
}

const CONTEXTUAL_MESSAGES = {
  'photo-analysis': [
    'Detectando color de cabello...',
    'Analizando estructura capilar...',
    'Calculando niveles por zona...',
    'Generando diagnóstico...',
  ],
  formulation: [
    'Consultando base de datos química...',
    'Optimizando proporciones...',
    'Validando compatibilidad...',
    'Preparando fórmula final...',
  ],
  'ai-processing': [
    'Procesando con IA avanzada...',
    'Analizando patrones complejos...',
    'Validando resultados...',
    'Finalizando análisis...',
  ],
  sync: ['Sincronizando datos...', 'Actualizando servidor...', 'Verificando cambios...'],
  generic: ['Cargando...', 'Procesando...', 'Casi listo...'],
};

export function LoadingState({
  message,
  fullScreen = true,
  variant = 'spinner',
  context = 'generic',
  progress,
  estimatedDuration,
  showProgress = false,
}: LoadingStateProps) {
  const fadeAnim = useSharedValue(0);
  const scaleAnim = useSharedValue(0.8);
  const shimmerAnim = useSharedValue(0);
  const progressAnim = useSharedValue(0);

  const messageIndex = useRef(0);
  const [currentMessage, setCurrentMessage] = React.useState(
    message || CONTEXTUAL_MESSAGES[context][0]
  );

  useEffect(() => {
    // Entrada suave
    fadeAnim.value = withTiming(1, { duration: 300 });
    scaleAnim.value = withTiming(1, {
      duration: 400,
      easing: Easing.out(Easing.back(1.2)),
    });

    // Animación shimmer continua
    shimmerAnim.value = withRepeat(
      withTiming(1, { duration: 1500, easing: Easing.linear }),
      -1,
      false
    );

    // Progress animation si se especifica
    if (progress !== undefined) {
      progressAnim.value = withTiming(progress / 100, { duration: 500 });
    }
  }, [progress, fadeAnim, scaleAnim, shimmerAnim, progressAnim]);

  // Rotación de mensajes contextuales
  useEffect(() => {
    if (!message && context !== 'generic') {
      const messages = CONTEXTUAL_MESSAGES[context];
      const interval = setInterval(
        () => {
          messageIndex.current = (messageIndex.current + 1) % messages.length;
          setCurrentMessage(messages[messageIndex.current]);
        },
        estimatedDuration ? (estimatedDuration * 1000) / messages.length : 3000
      );

      return () => clearInterval(interval);
    }
  }, [message, context, estimatedDuration]);

  const containerAnimStyle = useAnimatedStyle(() => ({
    opacity: fadeAnim.value,
    transform: [{ scale: scaleAnim.value }],
  }));

  const shimmerAnimStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateX: interpolate(
          shimmerAnim.value,
          [0, 1],
          [-Dimensions.get('window').width, Dimensions.get('window').width]
        ),
      },
    ],
  }));

  const progressAnimStyle = useAnimatedStyle(() => ({
    width: `${interpolate(progressAnim.value, [0, 1], [0, 100])}%`,
  }));

  const renderLoadingIndicator = () => {
    switch (variant) {
      case 'skeleton':
        return (
          <View style={styles.skeletonContainer}>
            {[...Array(3)].map((_, i) => (
              <View key={i} style={styles.skeletonLine}>
                <LinearGradient
                  colors={['#f0f0f0', '#e0e0e0', '#f0f0f0']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={StyleSheet.absoluteFill}
                />
                <Animated.View style={[styles.shimmerOverlay, shimmerAnimStyle]} />
              </View>
            ))}
          </View>
        );

      case 'pulse':
        return (
          <Animated.View style={[styles.pulseContainer, containerAnimStyle]}>
            <View style={styles.pulseRing} />
            <View style={[styles.pulseRing, styles.pulseRingDelay]} />
            <View style={styles.pulseCore} />
          </Animated.View>
        );

      case 'shimmer':
        return (
          <View style={styles.shimmerContainer}>
            <View style={styles.shimmerBlock}>
              <Animated.View style={[styles.shimmerGradient, shimmerAnimStyle]} />
            </View>
          </View>
        );

      case 'contextual':
        return (
          <View style={styles.contextualContainer}>
            <ActivityIndicator size="large" color={Colors.light.primary} />
            <View style={styles.contextIcon}>
              {context === 'photo-analysis' && <Text style={styles.contextEmoji}>📸</Text>}
              {context === 'formulation' && <Text style={styles.contextEmoji}>⚗️</Text>}
              {context === 'ai-processing' && <Text style={styles.contextEmoji}>🧠</Text>}
              {context === 'sync' && <Text style={styles.contextEmoji}>☁️</Text>}
            </View>
          </View>
        );

      default:
        return (
          <ActivityIndicator
            size="large"
            color={Colors.light.primary}
            accessibilityElementsHidden={true}
          />
        );
    }
  };

  return (
    <Animated.View
      style={[styles.container, fullScreen && styles.fullScreen, containerAnimStyle]}
      {...a11y.loading(currentMessage)}
    >
      {renderLoadingIndicator()}

      <Text style={styles.message} accessibilityLiveRegion="polite">
        {currentMessage}
      </Text>

      {showProgress && progress !== undefined && (
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <Animated.View style={[styles.progressFill, progressAnimStyle]} />
          </View>
          <Text style={styles.progressText}>{Math.round(progress)}%</Text>
        </View>
      )}

      {estimatedDuration && !showProgress && (
        <Text style={styles.estimateText}>Tiempo estimado: ~{estimatedDuration}s</Text>
      )}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  fullScreen: {
    flex: 1,
    backgroundColor: Colors.light.surface,
  },
  message: {
    marginTop: spacing.md,
    ...typography.body,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    fontWeight: '500',
  },

  // Skeleton styles
  skeletonContainer: {
    width: '80%',
    alignItems: 'center',
  },
  skeletonLine: {
    height: 16,
    backgroundColor: Colors.light.border,
    borderRadius: 8,
    marginVertical: 4,
    width: '100%',
    overflow: 'hidden',
  },
  shimmerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.light.surfaceTransparent || 'rgba(255,255,255,0.8)',
    width: 100,
  },

  // Pulse styles
  pulseContainer: {
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
  pulseRing: {
    position: 'absolute',
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.light.primary + '20',
    transform: [{ scale: 1.0 }],
  },
  pulseRingDelay: {
    animationDelay: '0.5s',
  },
  pulseCore: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light.primary,
  },

  // Shimmer styles
  shimmerContainer: {
    width: 200,
    height: 40,
  },
  shimmerBlock: {
    width: '100%',
    height: '100%',
    backgroundColor: Colors.light.border,
    borderRadius: 8,
    overflow: 'hidden',
  },
  shimmerGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: 100,
    backgroundColor: Colors.light.surfaceTransparent || 'rgba(255,255,255,0.8)',
  },

  // Contextual styles
  contextualContainer: {
    position: 'relative',
    alignItems: 'center',
  },
  contextIcon: {
    position: 'absolute',
    top: -10,
    right: -10,
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  contextEmoji: {
    fontSize: 12,
  },

  // Progress styles
  progressContainer: {
    width: '80%',
    marginTop: spacing.md,
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: Colors.light.border,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.light.primary,
    borderRadius: 2,
  },
  progressText: {
    marginTop: spacing.xs,
    fontSize: 12,
    color: Colors.light.textSecondary,
    fontWeight: '600',
  },
  estimateText: {
    marginTop: spacing.xs,
    fontSize: 12,
    color: Colors.light.textSecondary,
    fontStyle: 'italic',
  },
});
