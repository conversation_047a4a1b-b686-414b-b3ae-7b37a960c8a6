import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import Colors from '@/constants/colors';
import { HairZone, HairZoneDisplay } from '@/types/hair-diagnosis';

interface ZoneAnalysisDisplayProps {
  currentZone: HairZone;
  onZoneChange: (zone: HairZone) => void;
  completedZones?: Partial<Record<HairZone, boolean>>;
  showCompletionIndicators?: boolean;
}

export const ZoneAnalysisDisplay: React.FC<ZoneAnalysisDisplayProps> = ({
  currentZone,
  onZoneChange,
  completedZones = {},
  showCompletionIndicators = false,
}) => {
  return (
    <View style={styles.zoneTabsContainer}>
      {Object.values(HairZone).map(zone => (
        <TouchableOpacity
          key={zone}
          style={[
            styles.zoneTab,
            currentZone === zone && styles.activeZoneTab,
            currentZone === zone && styles.activeZoneTabBackground,
          ]}
          onPress={() => onZoneChange(zone)}
        >
          <Text style={[styles.zoneTabText, currentZone === zone && styles.activeZoneTabText]}>
            {HairZoneDisplay[zone]}
          </Text>
          {showCompletionIndicators && completedZones[zone] && (
            <View style={[styles.zoneCompleteIndicator, styles.indicatorMargin]} />
          )}
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  zoneTabsContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.light.surface,
    borderRadius: 16,
    marginBottom: 24,
    padding: 4,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.03,
    shadowRadius: 4,
    elevation: 1,
  },
  zoneTab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 12,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  activeZoneTab: {
    backgroundColor: Colors.light.surface,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 3,
    elevation: 2,
  },
  zoneTabText: {
    fontSize: 15,
    color: Colors.light.gray,
    fontWeight: '500',
  },
  activeZoneTabText: {
    color: Colors.light.primary,
    fontWeight: '700',
  },
  zoneCompleteIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.light.success,
  },
  activeZoneTabBackground: {
    backgroundColor: Colors.light.surface,
  },
  indicatorMargin: {
    marginLeft: 6,
  },
});
