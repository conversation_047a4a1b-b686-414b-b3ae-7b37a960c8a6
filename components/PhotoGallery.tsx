import React from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  Image,
  Animated,
} from 'react-native';
import { Camera, X, AlertTriangle, CheckCircle, Upload } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { CapturedPhoto, PhotoQuality, PHOTO_GUIDES } from '@/types/photo-capture';

interface PhotoGalleryProps {
  photos: CapturedPhoto[];
  onAddPhoto?: () => void;
  onCameraCapture?: () => void;
  onRemovePhoto: (photoId: string) => void;
  onRetakePhoto?: (photo: CapturedPhoto) => void;
  maxPhotos?: number;
  showQuality?: boolean;
}

export default function PhotoGallery({
  photos,
  onAddPhoto,
  onCameraCapture,
  onRemovePhoto,
  onRetakePhoto,
  maxPhotos = 5,
  showQuality: _showQuality = false,
}: PhotoGalleryProps) {
  const scaleAnim = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      tension: 50,
      friction: 7,
    }).start();
  }, [photos.length, scaleAnim]);

  const _getQualityColor = (quality?: string) => {
    switch (quality) {
      case 'good':
        return Colors.light.success;
      case 'fair':
        return Colors.light.warning;
      case 'poor':
        return Colors.light.error;
      default:
        return Colors.light.gray;
    }
  };

  const getQualityIcon = (quality?: PhotoQuality) => {
    const overallQuality = quality?.overall ?? 50; // Default to 50 if undefined
    if (overallQuality >= 80) {
      return <CheckCircle size={16} color={Colors.light.success} />;
    } else if (overallQuality >= 60) {
      return <AlertTriangle size={16} color={Colors.light.warning} />;
    } else {
      return <AlertTriangle size={16} color={Colors.light.error} />;
    }
  };

  const renderPhotoSlot = (photo: CapturedPhoto | null, index: number) => {
    const guide = photo ? PHOTO_GUIDES.find(g => g.angle === photo.angle) : PHOTO_GUIDES[index];

    if (!photo && photos.length < maxPhotos) {
      // Empty slot
      return (
        <TouchableOpacity key={`empty-${index}`} style={styles.photoSlot} onPress={onAddPhoto}>
          <View style={styles.emptySlot}>
            <Text style={styles.slotIcon}>{guide?.icon || '📷'}</Text>
            <Text style={styles.slotLabel}>{guide?.label || 'Agregar'}</Text>
            {guide?.required && (
              <View style={styles.requiredBadge}>
                <Text style={styles.requiredText}>Requerida</Text>
              </View>
            )}
          </View>
        </TouchableOpacity>
      );
    }

    if (photo) {
      // Filled slot
      return (
        <Animated.View
          key={photo.id}
          style={[
            styles.photoSlot,
            {
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <Image source={{ uri: photo.uri }} style={styles.photoImage} />

          <View style={styles.photoOverlay}>
            <View style={styles.photoHeader}>
              <View style={styles.qualityIndicator}>{getQualityIcon(photo.quality)}</View>
              <TouchableOpacity style={styles.removeButton} onPress={() => onRemovePhoto(photo.id)}>
                <X size={16} color="white" />
              </TouchableOpacity>
            </View>

            <View style={styles.photoFooter}>
              <Text style={styles.photoLabel}>{guide?.label}</Text>
              {(!photo.quality || photo.quality.overall < 60) && (
                <TouchableOpacity style={styles.retakeButton} onPress={() => onRetakePhoto(photo)}>
                  <Camera size={12} color="white" />
                  <Text style={styles.retakeText}>Mejorar</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </Animated.View>
      );
    }

    return null;
  };

  const renderProgressIndicator = () => {
    const requiredCount = PHOTO_GUIDES.filter(g => g.required).length;
    const capturedRequired = photos.filter(p => {
      const guide = PHOTO_GUIDES.find(g => g.angle === p.angle);
      return guide?.required;
    }).length;

    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressHeader}>
          <Text style={styles.progressTitle}>
            {photos.length} de {requiredCount}-{maxPhotos} fotos
          </Text>
          <Text style={styles.progressSubtitle}>
            {capturedRequired}/{requiredCount} requeridas
          </Text>
        </View>
        <View style={styles.progressBar}>
          <View
            style={[styles.progressFill, { width: `${(capturedRequired / requiredCount) * 100}%` }]}
          />
        </View>
      </View>
    );
  };

  const renderActionButtons = () => (
    <View style={styles.actionButtonsContainer}>
      {onCameraCapture && (
        <TouchableOpacity
          style={[styles.actionButton, styles.cameraButton]}
          onPress={() => onCameraCapture()}
        >
          <Camera size={20} color="white" />
          <Text style={styles.actionButtonText}>Tomar Foto</Text>
        </TouchableOpacity>
      )}

      {onAddPhoto && (
        <TouchableOpacity
          style={[styles.actionButton, styles.uploadButton]}
          onPress={() => onAddPhoto()}
        >
          <Upload size={20} color={Colors.light.primary} />
          <Text style={[styles.actionButtonText, styles.uploadButtonText]}>Subir Fotos</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      {renderProgressIndicator()}

      {/* Action buttons */}
      {renderActionButtons()}

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Render captured photos first */}
        {photos.map((photo, index) => renderPhotoSlot(photo, index))}

        {/* Render empty slots for remaining guides */}
        {photos.length < maxPhotos &&
          PHOTO_GUIDES.slice(photos.length, maxPhotos).map((_, index) =>
            renderPhotoSlot(null, photos.length + index)
          )}

        {/* Add more button if not at max */}
        {photos.length < maxPhotos &&
          photos.length >= PHOTO_GUIDES.filter(g => g.required).length && (
            <TouchableOpacity style={[styles.photoSlot, styles.addMoreSlot]} onPress={onAddPhoto}>
              <Camera size={24} color={Colors.light.primary} />
              <Text style={styles.addMoreText}>Agregar más</Text>
            </TouchableOpacity>
          )}
      </ScrollView>

      <Text style={styles.helpText}>
        Desliza para ver todas las fotos • Toca para agregar o editar
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  progressContainer: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  progressSubtitle: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  progressBar: {
    height: 6,
    backgroundColor: Colors.light.border,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.light.primary,
    borderRadius: 3,
  },
  scrollContent: {
    paddingHorizontal: 4,
    gap: 12,
  },
  photoSlot: {
    width: 120,
    height: 160,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: Colors.light.surface,
  },
  emptySlot: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.light.border,
    borderStyle: 'dashed',
    borderRadius: 12,
    padding: 12,
  },
  slotIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  slotLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.text,
    textAlign: 'center',
  },
  requiredBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: Colors.light.primaryTransparent20,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  requiredText: {
    fontSize: 10,
    fontWeight: '600',
    color: Colors.light.primary,
  },
  photoImage: {
    width: '100%',
    height: '100%',
  },
  photoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.common.shadowColor + '4D', // 0.3 opacity
    justifyContent: 'space-between',
    padding: 8,
  },
  photoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  qualityIndicator: {
    backgroundColor: Colors.light.backgroundOpacity90,
    borderRadius: 12,
    padding: 4,
  },
  removeButton: {
    backgroundColor: Colors.common.shadowColor + '80', // 0.5 opacity
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoFooter: {
    gap: 4,
  },
  photoLabel: {
    color: Colors.light.textLight,
    fontSize: 12,
    fontWeight: '600',
    textShadowColor: Colors.common.shadowColor + '80', // 0.5 opacity
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  retakeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.warning,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
    alignSelf: 'flex-start',
  },
  retakeText: {
    color: Colors.light.textLight,
    fontSize: 11,
    fontWeight: '500',
  },
  addMoreSlot: {
    borderWidth: 2,
    borderColor: Colors.light.primary + '30',
    borderStyle: 'solid',
    backgroundColor: Colors.light.primaryTransparent10,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  addMoreText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.primary,
  },
  helpText: {
    fontSize: 12,
    color: Colors.light.gray,
    textAlign: 'center',
    marginTop: 12,
    fontStyle: 'italic',
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    gap: 8,
  },
  cameraButton: {
    backgroundColor: Colors.light.primary,
  },
  uploadButton: {
    backgroundColor: Colors.common.transparent,
    borderWidth: 1,
    borderColor: Colors.light.primary,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textLight,
  },
  uploadButtonText: {
    color: Colors.light.primary,
  },
});
