import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Animated,
  Image,
  Modal,
  Dimensions,
} from 'react-native';
import {
  Info,
  Eye,
  Brain,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Lightbulb,
  Target,
  Shield,
  Edit3,
} from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import Colors from '@/constants/colors';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface AIDecision {
  field: string;
  value: string | number;
  confidence: number;
  reasoning: string[];
  evidence: Evidence[];
  alternativeOptions?: AlternativeOption[];
  canOverride: boolean;
}

interface Evidence {
  type: 'visual' | 'historical' | 'chemical' | 'statistical';
  description: string;
  weight: number; // 0-1, how much this influenced the decision
  imageRegion?: ImageRegion;
}

interface ImageRegion {
  x: number;
  y: number;
  width: number;
  height: number;
  label: string;
}

interface AlternativeOption {
  value: string | number;
  confidence: number;
  reasoning: string;
}

interface ExplainableAIProps {
  decisions: AIDecision[];
  imageUrl?: string;
  onOverride?: (field: string, newValue: string | number, justification: string) => void;
  onAcceptAll?: () => void;
}

export const ExplainableAI: React.FC<ExplainableAIProps> = ({
  decisions,
  imageUrl,
  onOverride,
  onAcceptAll,
}) => {
  const [expandedDecisions, setExpandedDecisions] = useState<Set<string>>(new Set());
  const [showImageOverlay, setShowImageOverlay] = useState(false);
  const [selectedRegion, setSelectedRegion] = useState<ImageRegion | null>(null);
  const [_overrideModal, _setOverrideModal] = useState<{
    field: string;
    alternatives: AlternativeOption[];
  } | null>(null);

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, scaleAnim]);

  const toggleDecision = (field: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    const newExpanded = new Set(expandedDecisions);
    if (newExpanded.has(field)) {
      newExpanded.delete(field);
    } else {
      newExpanded.add(field);
    }
    setExpandedDecisions(newExpanded);
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return Colors.light.success;
    if (confidence >= 70) return Colors.light.warning;
    return Colors.light.error;
  };

  const getConfidenceLabel = (confidence: number) => {
    if (confidence >= 90) return 'Alta confianza';
    if (confidence >= 70) return 'Confianza media';
    return 'Baja confianza';
  };

  const renderDecisionCard = (decision: AIDecision) => {
    const isExpanded = expandedDecisions.has(decision.field);
    const confidenceColor = getConfidenceColor(decision.confidence);

    return (
      <Animated.View
        key={decision.field}
        style={[
          styles.decisionCard,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <TouchableOpacity
          style={styles.decisionHeader}
          onPress={() => toggleDecision(decision.field)}
        >
          <View style={styles.decisionTitleRow}>
            <Brain size={20} color={Colors.light.primary} />
            <Text style={styles.decisionField}>{decision.field}</Text>
            {decision.canOverride && (
              <TouchableOpacity
                style={styles.overrideButton}
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                  _setOverrideModal({
                    field: decision.field,
                    alternatives: decision.alternativeOptions || [],
                  });
                }}
              >
                <Edit3 size={16} color={Colors.light.primary} />
              </TouchableOpacity>
            )}
          </View>

          <View style={styles.decisionValueRow}>
            <Text style={styles.decisionValue}>{decision.value}</Text>
            <View style={[styles.confidenceBadge, { backgroundColor: confidenceColor + '20' }]}>
              <Text style={[styles.confidenceText, { color: confidenceColor }]}>
                {decision.confidence}%
              </Text>
            </View>
            {isExpanded ? (
              <ChevronUp size={20} color={Colors.light.textSecondary} />
            ) : (
              <ChevronDown size={20} color={Colors.light.textSecondary} />
            )}
          </View>
        </TouchableOpacity>

        {isExpanded && (
          <Animated.View style={styles.decisionDetails}>
            {/* Reasoning Section */}
            <View style={styles.reasoningSection}>
              <View style={styles.sectionHeader}>
                <Lightbulb size={16} color={Colors.light.primary} />
                <Text style={styles.sectionTitle}>Razonamiento de IA</Text>
              </View>

              {decision.reasoning.map((reason, index) => (
                <View key={index} style={styles.reasonItem}>
                  <Text style={styles.bulletPoint}>•</Text>
                  <Text style={styles.reasonText}>{reason}</Text>
                </View>
              ))}
            </View>

            {/* Evidence Section */}
            <View style={styles.evidenceSection}>
              <View style={styles.sectionHeader}>
                <Target size={16} color={Colors.light.primary} />
                <Text style={styles.sectionTitle}>Evidencia Analizada</Text>
              </View>

              {decision.evidence.map((evidence, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.evidenceItem}
                  onPress={() => {
                    if (evidence.imageRegion) {
                      setSelectedRegion(evidence.imageRegion);
                      setShowImageOverlay(true);
                    }
                  }}
                >
                  <View style={styles.evidenceIcon}>
                    {evidence.type === 'visual' && <Eye size={14} color={Colors.light.primary} />}
                    {evidence.type === 'chemical' && (
                      <Shield size={14} color={Colors.light.success} />
                    )}
                    {evidence.type === 'historical' && (
                      <Info size={14} color={Colors.light.warning} />
                    )}
                    {evidence.type === 'statistical' && (
                      <Brain size={14} color={Colors.light.primary} />
                    )}
                  </View>

                  <View style={styles.evidenceContent}>
                    <Text style={styles.evidenceDescription}>{evidence.description}</Text>
                    <View style={styles.evidenceWeight}>
                      <View style={[styles.weightBar, { width: `${evidence.weight * 100}%` }]} />
                    </View>
                  </View>

                  {evidence.imageRegion && (
                    <Eye size={16} color={Colors.light.primary} style={styles.viewIcon} />
                  )}
                </TouchableOpacity>
              ))}
            </View>

            {/* Alternative Options */}
            {decision.alternativeOptions && decision.alternativeOptions.length > 0 && (
              <View style={styles.alternativesSection}>
                <View style={styles.sectionHeader}>
                  <Info size={16} color={Colors.light.textSecondary} />
                  <Text style={styles.sectionTitle}>Opciones Alternativas</Text>
                </View>

                {decision.alternativeOptions.map((alt, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.alternativeItem}
                    onPress={() => {
                      if (onOverride) {
                        onOverride(decision.field, alt.value, alt.reasoning);
                      }
                    }}
                  >
                    <Text style={styles.alternativeValue}>{alt.value}</Text>
                    <Text
                      style={[
                        styles.alternativeConfidence,
                        { color: getConfidenceColor(alt.confidence) },
                      ]}
                    >
                      {alt.confidence}%
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}

            {/* Confidence Explanation */}
            <View style={styles.confidenceExplanation}>
              <View style={[styles.confidenceIndicator, { backgroundColor: confidenceColor }]} />
              <Text style={styles.confidenceLabel}>{getConfidenceLabel(decision.confidence)}</Text>
              {decision.confidence < 70 && (
                <Text style={styles.confidenceWarning}>Recomendamos revisión manual</Text>
              )}
            </View>
          </Animated.View>
        )}
      </Animated.View>
    );
  };

  const renderImageOverlay = () => {
    if (!imageUrl || !showImageOverlay) return null;

    return (
      <Modal
        visible={showImageOverlay}
        transparent
        animationType="fade"
        onRequestClose={() => setShowImageOverlay(false)}
      >
        <View style={styles.imageOverlayContainer}>
          <TouchableOpacity
            style={styles.imageOverlayBackdrop}
            onPress={() => setShowImageOverlay(false)}
          />

          <View style={styles.imageOverlayContent}>
            <Image source={{ uri: imageUrl }} style={styles.overlayImage} resizeMode="contain" />

            {selectedRegion && (
              <View
                style={[
                  styles.highlightRegion,
                  {
                    left: selectedRegion.x,
                    top: selectedRegion.y,
                    width: selectedRegion.width,
                    height: selectedRegion.height,
                  },
                ]}
              >
                <View style={styles.regionLabel}>
                  <Text style={styles.regionLabelText}>{selectedRegion.label}</Text>
                </View>
              </View>
            )}

            <TouchableOpacity style={styles.closeButton} onPress={() => setShowImageOverlay(false)}>
              <Text style={styles.closeButtonText}>Cerrar</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Brain size={24} color={Colors.light.primary} />
        <Text style={styles.headerTitle}>Análisis de IA Explicable</Text>
      </View>

      {/* Summary Stats */}
      <View style={styles.summaryContainer}>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryValue}>{decisions.length}</Text>
          <Text style={styles.summaryLabel}>Decisiones</Text>
        </View>

        <View style={styles.summaryItem}>
          <Text style={styles.summaryValue}>
            {Math.round(decisions.reduce((acc, d) => acc + d.confidence, 0) / decisions.length)}%
          </Text>
          <Text style={styles.summaryLabel}>Confianza Media</Text>
        </View>

        <View style={styles.summaryItem}>
          <Text style={styles.summaryValue}>{decisions.filter(d => d.canOverride).length}</Text>
          <Text style={styles.summaryLabel}>Editables</Text>
        </View>
      </View>

      {/* Decision Cards */}
      <ScrollView style={styles.decisionsContainer} showsVerticalScrollIndicator={false}>
        {decisions.map(renderDecisionCard)}
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.actionButton, styles.secondaryButton]}
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            // Expand all decisions
            setExpandedDecisions(new Set(decisions.map(d => d.field)));
          }}
        >
          <Eye size={20} color={Colors.light.primary} />
          <Text style={styles.secondaryButtonText}>Ver Todo</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.primaryButton]}
          onPress={() => {
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            onAcceptAll?.();
          }}
        >
          <CheckCircle size={20} color="white" />
          <Text style={styles.primaryButtonText}>Aceptar Todo</Text>
        </TouchableOpacity>
      </View>

      {/* Image Overlay */}
      {renderImageOverlay()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    backgroundColor: Colors.light.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.surface,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.light.text,
    marginLeft: 12,
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 16,
    backgroundColor: Colors.light.surface,
    marginBottom: 8,
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryValue: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.light.primary,
  },
  summaryLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginTop: 4,
  },
  decisionsContainer: {
    flex: 1,
    padding: 16,
  },
  decisionCard: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    marginBottom: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: Colors.light.surface,
  },
  decisionHeader: {
    padding: 16,
  },
  decisionTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  decisionField: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginLeft: 8,
    flex: 1,
  },
  overrideButton: {
    padding: 4,
  },
  decisionValueRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  decisionValue: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    flex: 1,
  },
  confidenceBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  confidenceText: {
    fontSize: 12,
    fontWeight: '600',
  },
  decisionDetails: {
    borderTopWidth: 1,
    borderTopColor: Colors.light.surface,
  },
  reasoningSection: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.surface,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginLeft: 8,
  },
  reasonItem: {
    flexDirection: 'row',
    marginBottom: 8,
    paddingLeft: 8,
  },
  bulletPoint: {
    fontSize: 14,
    color: Colors.light.primary,
    marginRight: 8,
  },
  reasonText: {
    fontSize: 14,
    color: Colors.light.text,
    flex: 1,
    lineHeight: 20,
  },
  evidenceSection: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.surface,
  },
  evidenceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingLeft: 8,
  },
  evidenceIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.light.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  evidenceContent: {
    flex: 1,
  },
  evidenceDescription: {
    fontSize: 14,
    color: Colors.light.text,
    marginBottom: 4,
  },
  evidenceWeight: {
    height: 4,
    backgroundColor: Colors.light.surface,
    borderRadius: 2,
    overflow: 'hidden',
  },
  weightBar: {
    height: '100%',
    backgroundColor: Colors.light.primary,
    borderRadius: 2,
  },
  viewIcon: {
    marginLeft: 8,
  },
  alternativesSection: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.surface,
  },
  alternativeItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    marginBottom: 8,
  },
  alternativeValue: {
    fontSize: 14,
    color: Colors.light.text,
    flex: 1,
  },
  alternativeConfidence: {
    fontSize: 12,
    fontWeight: '600',
  },
  confidenceExplanation: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  confidenceIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  confidenceLabel: {
    fontSize: 14,
    color: Colors.light.text,
    flex: 1,
  },
  confidenceWarning: {
    fontSize: 12,
    color: Colors.light.warning,
    fontStyle: 'italic',
  },
  actionButtons: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: Colors.light.surface,
    borderTopWidth: 1,
    borderTopColor: Colors.light.surface,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  primaryButton: {
    backgroundColor: Colors.light.primary,
  },
  secondaryButton: {
    backgroundColor: Colors.light.surface,
  },
  primaryButtonText: {
    color: Colors.light.surface,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  secondaryButtonText: {
    color: Colors.light.primary,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  imageOverlayContainer: {
    flex: 1,
    backgroundColor: Colors.light.overlay || 'rgba(0,0,0,0.9)',
  },
  imageOverlayBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  imageOverlayContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlayImage: {
    width: SCREEN_WIDTH - 40,
    height: SCREEN_WIDTH - 40,
  },
  highlightRegion: {
    position: 'absolute',
    borderWidth: 2,
    borderColor: Colors.light.primary,
    borderStyle: 'dashed',
  },
  regionLabel: {
    position: 'absolute',
    top: -20,
    left: 0,
    backgroundColor: Colors.light.primary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  regionLabelText: {
    color: Colors.light.surface,
    fontSize: 12,
    fontWeight: '600',
  },
  closeButton: {
    position: 'absolute',
    bottom: 40,
    paddingHorizontal: 24,
    paddingVertical: 12,
    backgroundColor: Colors.light.primary,
    borderRadius: 24,
  },
  closeButtonText: {
    color: Colors.light.surface,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ExplainableAI;
