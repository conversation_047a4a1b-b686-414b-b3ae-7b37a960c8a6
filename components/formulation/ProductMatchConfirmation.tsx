import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  TextInput,
} from 'react-native';
import { X, Search, Check, AlertCircle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius } from '@/constants/theme';
import { useInventoryStore } from '@/stores/inventory-store';
import { Product } from '@/types/inventory';

interface ProductMatchConfirmationProps {
  visible: boolean;
  onClose: () => void;
  aiProductName: string;
  suggestedMatch?: {
    product: Product;
    confidence: number;
    matchType: 'exact' | 'partial' | 'fuzzy';
  };
  onConfirm: (productId: string) => void;
  onCreateNew: () => void;
}

export const ProductMatchConfirmation: React.FC<ProductMatchConfirmationProps> = ({
  visible,
  onClose,
  aiProductName,
  suggestedMatch,
  onConfirm,
  onCreateNew,
}) => {
  const { products, searchProducts, saveProductMapping } = useInventoryStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProductId, setSelectedProductId] = useState(suggestedMatch?.product.id || '');

  const searchResults =
    searchQuery.length > 0
      ? searchProducts(searchQuery)
      : suggestedMatch
        ? [
            suggestedMatch.product,
            ...products.filter(p => p.id !== suggestedMatch.product.id).slice(0, 4),
          ]
        : products.slice(0, 5);

  const handleConfirm = async () => {
    if (selectedProductId) {
      // Save the mapping for future use
      await saveProductMapping({
        aiProductName,
        inventoryProductId: selectedProductId,
        confidence: suggestedMatch?.confidence || 100,
        context: {
          matchType: suggestedMatch?.matchType || 'manual',
          confirmedAt: new Date().toISOString(),
        },
      });

      onConfirm(selectedProductId);
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return Colors.light.success;
    if (confidence >= 70) return Colors.light.warning;
    return Colors.light.error;
  };

  const getMatchTypeLabel = (type: string) => {
    switch (type) {
      case 'exact':
        return 'Coincidencia exacta';
      case 'partial':
        return 'Coincidencia parcial';
      case 'fuzzy':
        return 'Coincidencia aproximada';
      default:
        return 'Sin coincidencia';
    }
  };

  return (
    <Modal visible={visible} transparent animationType="slide" onRequestClose={onClose}>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.title}>Confirmar Producto</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color={Colors.light.text} />
            </TouchableOpacity>
          </View>

          <View style={styles.aiProductSection}>
            <Text style={styles.sectionLabel}>Producto en la fórmula:</Text>
            <Text style={styles.aiProductName}>{aiProductName}</Text>

            {suggestedMatch && (
              <View style={styles.suggestionInfo}>
                <View style={styles.matchTypeBadge}>
                  <Text style={styles.matchTypeText}>
                    {getMatchTypeLabel(suggestedMatch.matchType)}
                  </Text>
                </View>
                <View
                  style={[
                    styles.confidenceBadge,
                    {
                      backgroundColor: getConfidenceColor(suggestedMatch.confidence) + '20',
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.confidenceText,
                      { color: getConfidenceColor(suggestedMatch.confidence) },
                    ]}
                  >
                    {suggestedMatch.confidence}% confianza
                  </Text>
                </View>
              </View>
            )}
          </View>

          <View style={styles.divider} />

          <View style={styles.searchSection}>
            <View style={styles.searchBar}>
              <Search size={20} color={Colors.light.gray} />
              <TextInput
                style={styles.searchInput}
                placeholder="Buscar en inventario..."
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholderTextColor={Colors.light.gray}
              />
            </View>
          </View>

          <ScrollView style={styles.productList} showsVerticalScrollIndicator={false}>
            {searchResults.map(product => (
              <TouchableOpacity
                key={product.id}
                style={[
                  styles.productItem,
                  selectedProductId === product.id && styles.productItemSelected,
                ]}
                onPress={() => setSelectedProductId(product.id)}
              >
                <View style={styles.productInfo}>
                  <Text style={styles.productName}>{product.displayName || product.name}</Text>
                  <View style={styles.productDetails}>
                    <Text style={styles.productBrand}>{product.brand}</Text>
                    {product.line && (
                      <>
                        <Text style={styles.separator}>•</Text>
                        <Text style={styles.productLine}>{product.line}</Text>
                      </>
                    )}
                    {product.shade && (
                      <>
                        <Text style={styles.separator}>•</Text>
                        <Text style={styles.productShade}>{product.shade}</Text>
                      </>
                    )}
                  </View>
                  <Text style={styles.stockInfo}>
                    Stock: {product.currentStock} {product.unitType}
                  </Text>
                </View>
                {selectedProductId === product.id && (
                  <Check size={24} color={Colors.light.primary} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>

          <View style={styles.actions}>
            <TouchableOpacity style={[styles.button, styles.secondaryButton]} onPress={onCreateNew}>
              <Text style={styles.secondaryButtonText}>Crear Nuevo Producto</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.button,
                styles.primaryButton,
                !selectedProductId && styles.buttonDisabled,
              ]}
              onPress={handleConfirm}
              disabled={!selectedProductId}
            >
              <Text style={styles.primaryButtonText}>Confirmar Selección</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.infoBox}>
            <AlertCircle size={16} color={Colors.light.primary} />
            <Text style={styles.infoText}>Esta asociación se recordará para futuras fórmulas</Text>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: Colors.light.modalOverlay,
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: Colors.light.surface,
    borderTopLeftRadius: radius.xl,
    borderTopRightRadius: radius.xl,
    maxHeight: '85%',
    paddingBottom: spacing.xl,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  title: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  closeButton: {
    padding: spacing.xs,
  },
  aiProductSection: {
    padding: spacing.lg,
  },
  sectionLabel: {
    fontSize: typography.sizes.sm,
    color: Colors.light.gray,
    marginBottom: spacing.xs,
  },
  aiProductName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
    marginBottom: spacing.sm,
  },
  suggestionInfo: {
    flexDirection: 'row',
    gap: spacing.sm,
    marginTop: spacing.sm,
  },
  matchTypeBadge: {
    backgroundColor: Colors.light.primary + '10',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: radius.sm,
  },
  matchTypeText: {
    fontSize: typography.sizes.xs,
    color: Colors.light.primary,
  },
  confidenceBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: radius.sm,
  },
  confidenceText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium,
  },
  divider: {
    height: 1,
    backgroundColor: Colors.light.border,
    marginHorizontal: spacing.lg,
  },
  searchSection: {
    padding: spacing.lg,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderRadius: radius.md,
    paddingHorizontal: spacing.md,
    gap: spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
    paddingVertical: spacing.md,
  },
  productList: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  productItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderRadius: radius.md,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderWidth: 2,
    borderColor: Colors.common.transparent,
  },
  productItemSelected: {
    borderColor: Colors.light.primary,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  productDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  productBrand: {
    fontSize: typography.sizes.sm,
    color: Colors.light.gray,
  },
  productLine: {
    fontSize: typography.sizes.sm,
    color: Colors.light.gray,
  },
  productShade: {
    fontSize: typography.sizes.sm,
    color: Colors.light.primary,
    fontWeight: typography.weights.medium,
  },
  separator: {
    fontSize: typography.sizes.sm,
    color: Colors.light.lightGray,
    marginHorizontal: spacing.xs,
  },
  stockInfo: {
    fontSize: typography.sizes.xs,
    color: Colors.light.gray,
  },
  actions: {
    padding: spacing.lg,
    gap: spacing.sm,
  },
  button: {
    paddingVertical: spacing.md,
    borderRadius: radius.md,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: Colors.light.primary,
  },
  secondaryButton: {
    backgroundColor: Colors.common.transparent,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  buttonDisabled: {
    opacity: 0.5,
  },
  primaryButtonText: {
    color: Colors.light.textLight,
    fontWeight: typography.weights.semibold,
    fontSize: typography.sizes.base,
  },
  secondaryButtonText: {
    color: Colors.light.primary,
    fontWeight: typography.weights.medium,
    fontSize: typography.sizes.base,
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary + '10',
    marginHorizontal: spacing.lg,
    padding: spacing.md,
    borderRadius: radius.md,
    gap: spacing.sm,
  },
  infoText: {
    flex: 1,
    fontSize: typography.sizes.sm,
    color: Colors.light.primary,
  },
});
