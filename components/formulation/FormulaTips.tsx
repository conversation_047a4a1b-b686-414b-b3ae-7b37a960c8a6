import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import {
  AlertTriangle,
  Info,
  CheckCircle,
  Lightbulb,
  Thermometer,
  Clock,
} from 'lucide-react-native';
import { ContextualTip } from '@/types/visual-formulation';
import { ViabilityAnalysis } from '@/types/formulation';
import Colors from '@/constants/colors';

interface FormulaTipsProps {
  analysis: {
    hasGreyHair?: boolean;
    averageDepthLevel?: number;
    averageLevel?: number;
    zonePhysicalAnalysis?: {
      roots?: { damage?: string };
      mids?: { damage?: string };
      ends?: { damage?: string };
    };
  }; // Análisis del diagnóstico actual
  technique: string; // Técnica seleccionada
  targetLevel: number; // Nivel objetivo del servicio
  viabilityAnalysis?: ViabilityAnalysis | null; // Análisis de viabilidad
}

export default function FormulaTips({
  analysis,
  technique,
  targetLevel,
  viabilityAnalysis,
}: FormulaTipsProps) {
  // Generar consejos contextuales dinámicos
  const generateContextualTips = React.useMemo(() => {
    const tips: ContextualTip[] = [];

    // Consejos basados en canas
    if (analysis?.hasGreyHair || analysis?.averageDepthLevel <= 3) {
      tips.push({
        id: 'grey-coverage',
        type: 'warning',
        title: 'Cobertura de Canas',
        description:
          'Asegurate de usar fórmula con buena cobertura de canas. Considera pre-pigmentación si hay más de 50% de canas.',
      });
    }

    // Consejos basados en técnica
    if (technique === 'balayage' || technique === 'highlights') {
      tips.push({
        id: 'strand-test',
        type: 'tip',
        title: 'Prueba de Mecha',
        description:
          'Siempre realiza prueba de mecha antes de aplicar. Para balayage, prueba en sección menos visible.',
      });
    }

    // Consejos por daño capilar
    if (
      analysis?.zonePhysicalAnalysis?.roots?.damage === 'Severo' ||
      analysis?.zonePhysicalAnalysis?.mids?.damage === 'Severo' ||
      analysis?.zonePhysicalAnalysis?.ends?.damage === 'Severo'
    ) {
      tips.push({
        id: 'damaged-hair',
        type: 'warning',
        title: 'Cabello Dañado',
        description:
          'Usa oxidante de menor volumen y considera tratamiento reconstrutivo antes del servicio.',
      });
    }

    // Consejos por gran diferencia de nivel - usar información del análisis de viabilidad
    const currentLevel = analysis?.averageDepthLevel || analysis?.averageLevel || 5;
    const levelDifference = Math.abs(targetLevel - currentLevel);
    const estimatedSessions = viabilityAnalysis?.factors?.estimatedSessions || 1;

    if (levelDifference >= 3) {
      // Usar las sesiones reales calculadas por el análisis de viabilidad
      const sessionText =
        estimatedSessions > 1
          ? `Considera hacer en ${estimatedSessions} sesiones para mantener integridad capilar.`
          : 'Proceso viable en una sesión con cuidados especiales.';

      tips.push({
        id: 'lightening',
        type: 'warning',
        title: 'Aclarado Intenso',
        description: `Diferencia de ${levelDifference} niveles. ${sessionText}`,
      });
    }

    // Agregar warnings del análisis de viabilidad si existen
    if (viabilityAnalysis?.warnings && viabilityAnalysis.warnings.length > 0) {
      viabilityAnalysis.warnings.forEach((warning, index) => {
        tips.push({
          id: `viability-warning-${index}`,
          type: 'warning',
          title: 'Advertencia de Colorimetría',
          description: warning,
        });
      });
    }

    // Consejos generales de técnica
    if (technique === 'full_color') {
      tips.push({
        id: 'application-order',
        type: 'info',
        title: 'Orden de Aplicación',
        description:
          'Aplica primero en medios y puntas, deja raíces para el final para control de temperatura.',
      });
    }

    // Consejo de temperatura
    tips.push({
      id: 'temperature',
      type: 'info',
      title: 'Control de Temperatura',
      description:
        'Mantén ambiente entre 20-25°C. Evita fuentes de calor directo durante procesamiento.',
    });

    return tips;
  }, [analysis, technique, targetLevel, viabilityAnalysis]);

  const tips = generateContextualTips;
  const getIcon = (type: ContextualTip['type']) => {
    const iconProps = { size: 20, color: 'white' };

    switch (type) {
      case 'warning':
        return <AlertTriangle {...iconProps} />;
      case 'info':
        return <Info {...iconProps} />;
      case 'success':
        return <CheckCircle {...iconProps} />;
      case 'tip':
      default:
        return <Lightbulb {...iconProps} />;
    }
  };

  const getIconByTitle = (title: string) => {
    const iconProps = { size: 20, color: 'white' };

    if (title.toLowerCase().includes('temperatura')) {
      return <Thermometer {...iconProps} />;
    }
    if (title.toLowerCase().includes('tiempo') || title.toLowerCase().includes('control')) {
      return <Clock {...iconProps} />;
    }
    return null;
  };

  const getStyles = (type: ContextualTip['type']) => {
    switch (type) {
      case 'warning':
        return {
          container: styles.warningContainer,
          iconBg: styles.warningIconBg,
          title: styles.warningTitle,
        };
      case 'info':
        return {
          container: styles.infoContainer,
          iconBg: styles.infoIconBg,
          title: styles.infoTitle,
        };
      case 'success':
        return {
          container: styles.successContainer,
          iconBg: styles.successIconBg,
          title: styles.successTitle,
        };
      case 'tip':
      default:
        return {
          container: styles.tipContainer,
          iconBg: styles.tipIconBg,
          title: styles.tipTitle,
        };
    }
  };

  const groupedTips = tips.reduce(
    (acc, tip) => {
      if (!acc[tip.type]) {
        acc[tip.type] = [];
      }
      acc[tip.type].push(tip);
      return acc;
    },
    {} as Record<ContextualTip['type'], ContextualTip[]>
  );

  const typeOrder: ContextualTip['type'][] = ['warning', 'info', 'tip', 'success'];

  return (
    <View style={styles.container}>
      {typeOrder.map(type => {
        const tipsOfType = groupedTips[type];
        if (!tipsOfType || tipsOfType.length === 0) return null;

        return (
          <View key={type} style={styles.typeSection}>
            {tipsOfType.map(tip => {
              const tipStyles = getStyles(tip.type);
              const customIcon = getIconByTitle(tip.title);

              return (
                <View key={tip.id} style={[styles.tipCard, tipStyles.container]}>
                  <View style={[styles.iconContainer, tipStyles.iconBg]}>
                    {customIcon || getIcon(tip.type)}
                  </View>
                  <View style={styles.textContainer}>
                    <Text style={[styles.tipTitle, tipStyles.title]}>{tip.title}</Text>
                    <Text style={styles.tipDescription}>{tip.description}</Text>
                  </View>
                </View>
              );
            })}
          </View>
        );
      })}

      {/* Quick Tips Grid */}
      <View style={styles.quickTipsGrid}>
        <View style={styles.quickTip}>
          <View style={styles.quickTipIcon}>
            <Thermometer size={16} color={Colors.light.primary} />
          </View>
          <Text style={styles.quickTipText}>20-25°C</Text>
        </View>
        <View style={styles.quickTip}>
          <View style={styles.quickTipIcon}>
            <Clock size={16} color={Colors.light.primary} />
          </View>
          <Text style={styles.quickTipText}>Revisar c/10min</Text>
        </View>
        <View style={styles.quickTip}>
          <View style={styles.quickTipIcon}>
            <CheckCircle size={16} color={Colors.light.primary} />
          </View>
          <Text style={styles.quickTipText}>Prueba mechón</Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    gap: 10,
  },
  typeSection: {
    gap: 10,
  },
  tipCard: {
    flexDirection: 'row',
    padding: 15,
    borderRadius: 12,
    borderLeftWidth: 4,
  },
  warningContainer: {
    backgroundColor: Colors.light.warningBackground,
    borderLeftColor: Colors.light.warning,
  },
  infoContainer: {
    backgroundColor: Colors.light.primaryTransparent10,
    borderLeftColor: Colors.light.info,
  },
  successContainer: {
    backgroundColor: Colors.light.successTransparent15,
    borderLeftColor: Colors.light.success,
  },
  tipContainer: {
    backgroundColor: Colors.light.accentTransparent15,
    borderLeftColor: Colors.light.accent,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  warningIconBg: {
    backgroundColor: Colors.light.warning,
  },
  infoIconBg: {
    backgroundColor: Colors.light.info,
  },
  successIconBg: {
    backgroundColor: Colors.light.success,
  },
  tipIconBg: {
    backgroundColor: Colors.light.accent,
  },
  textContainer: {
    flex: 1,
  },
  tipTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  warningTitle: {
    color: Colors.light.warningText,
  },
  infoTitle: {
    color: Colors.light.text,
  },
  successTitle: {
    color: Colors.light.success,
  },
  tipDescription: {
    fontSize: 13,
    color: Colors.light.gray,
    lineHeight: 18,
  },
  quickTipsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 15,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  quickTip: {
    alignItems: 'center',
  },
  quickTipIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 5,
  },
  quickTipText: {
    fontSize: 11,
    color: Colors.light.gray,
    fontWeight: '500',
  },
});
