import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  LayoutAnimation,
  Platform,
  UIManager,
} from 'react-native';
import { ChevronDown, ChevronUp, Droplet, Palette, Zap, Package } from 'lucide-react-native';
import { ZoneFormula, FormulaIngredient } from '@/types/visual-formulation';
import { HairZone } from '@/types/hair-diagnosis';
import Colors from '@/constants/colors';

// Enable LayoutAnimation on Android
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

interface ZoneFormulaCardsProps {
  zones: ZoneFormula[];
}

export default function ZoneFormulaCards({ zones }: ZoneFormulaCardsProps) {
  const [expandedZones, setExpandedZones] = useState<string[]>([HairZone.ROOTS, 'global']);

  const toggleZone = (zoneId: string) => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setExpandedZones(prev =>
      prev.includes(zoneId) ? prev.filter(z => z !== zoneId) : [...prev, zoneId]
    );
  };

  const getZoneColor = (zone: string) => {
    switch (zone) {
      case HairZone.ROOTS:
        return '#ff6b6b';
      case HairZone.MIDS:
        return '#4ecdc4';
      case HairZone.ENDS:
        return '#f59e0b';
      case 'global':
        return '#667eea';
      default:
        return Colors.light.primary;
    }
  };

  const getIngredientIcon = (type: FormulaIngredient['icon']) => {
    const iconProps = { size: 20, color: 'white' };
    switch (type) {
      case 'tube':
        return <Palette {...iconProps} />;
      case 'bottle':
        return <Droplet {...iconProps} />;
      case 'dropper':
        return <Zap {...iconProps} />;
      case 'jar':
        return <Package {...iconProps} />;
      default:
        return <Palette {...iconProps} />;
    }
  };

  const getIngredientColor = (type: FormulaIngredient['type']) => {
    switch (type) {
      case 'color':
        return '#ff6b6b';
      case 'developer':
        return '#4ecdc4';
      case 'additive':
        return '#a855f7';
      case 'treatment':
        return '#10b981';
      default:
        return Colors.light.gray;
    }
  };

  const calculateTotalAmount = (ingredients: FormulaIngredient[]) => {
    return ingredients.reduce((sum, ing) => {
      // Convert everything to grams for calculation
      if (ing.unit === 'ml') return sum + ing.amount; // Assume 1ml = 1g for simplicity
      if (ing.unit === 'oz') return sum + ing.amount * 28.35;
      if (ing.unit === 'fl oz') return sum + ing.amount * 29.57;
      return sum + ing.amount;
    }, 0);
  };

  const renderIngredient = (ingredient: FormulaIngredient, totalAmount: number) => {
    const percentage = (ingredient.amount / totalAmount) * 100;

    return (
      <View key={`${ingredient.name}-${ingredient.amount}`} style={styles.ingredient}>
        <View style={styles.ingredientHeader}>
          <View
            style={[
              styles.ingredientIcon,
              { backgroundColor: getIngredientColor(ingredient.type) },
            ]}
          >
            {getIngredientIcon(ingredient.icon)}
          </View>
          <View style={styles.ingredientInfo}>
            <Text style={styles.ingredientName}>{ingredient.name}</Text>
            {ingredient.code && <Text style={styles.ingredientCode}>{ingredient.code}</Text>}
          </View>
          <Text style={styles.ingredientAmount}>
            {ingredient.amount}
            {ingredient.unit}
          </Text>
        </View>

        <View style={styles.proportionBar}>
          <Animated.View
            style={[
              styles.proportionFill,
              {
                width: `${percentage}%`,
                backgroundColor: getIngredientColor(ingredient.type),
              },
            ]}
          >
            <Text style={styles.proportionText}>{percentage.toFixed(0)}%</Text>
          </Animated.View>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {zones.map(zone => {
        const isExpanded = expandedZones.includes(zone.zone);
        const totalAmount = calculateTotalAmount(zone.ingredients);

        return (
          <View key={zone.zone} style={styles.zoneCard}>
            <TouchableOpacity
              style={styles.zoneHeader}
              onPress={() => toggleZone(zone.zone)}
              activeOpacity={0.7}
            >
              <View style={styles.zoneHeaderLeft}>
                <View style={[styles.zoneIndicator, { backgroundColor: getZoneColor(zone.zone) }]}>
                  <Text style={styles.zoneNumber}>
                    {zone.zone === HairZone.ROOTS
                      ? '1'
                      : zone.zone === HairZone.MIDS
                        ? '2'
                        : zone.zone === HairZone.ENDS
                          ? '3'
                          : 'G'}
                  </Text>
                </View>
                <View>
                  <Text style={styles.zoneTitle}>{zone.title}</Text>
                  {zone.mixingRatio && (
                    <Text style={styles.zoneMixingRatio}>Proporción: {zone.mixingRatio}</Text>
                  )}
                </View>
              </View>
              {isExpanded ? (
                <ChevronUp size={20} color={Colors.light.gray} />
              ) : (
                <ChevronDown size={20} color={Colors.light.gray} />
              )}
            </TouchableOpacity>

            {isExpanded && (
              <View style={styles.zoneContent}>
                {zone.ingredients.map(ingredient => renderIngredient(ingredient, totalAmount))}

                {zone.processingTime && (
                  <View style={styles.processingTimeContainer}>
                    <Text style={styles.processingTimeLabel}>Tiempo de procesamiento:</Text>
                    <Text style={styles.processingTimeValue}>{zone.processingTime} min</Text>
                  </View>
                )}

                {zone.specialNotes && zone.specialNotes.length > 0 && (
                  <View style={styles.notesContainer}>
                    {zone.specialNotes.map((note, index) => (
                      <Text key={index} style={styles.noteText}>
                        • {note}
                      </Text>
                    ))}
                  </View>
                )}
              </View>
            )}
          </View>
        );
      })}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    gap: 15,
  },
  zoneCard: {
    backgroundColor: Colors.light.background,
    borderRadius: 15,
    overflow: 'hidden',
    shadowColor: Colors.common.shadowColor,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  zoneHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    backgroundColor: Colors.light.timelineBackground,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.progressBarBackground,
  },
  zoneHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  zoneIndicator: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  zoneNumber: {
    color: Colors.light.textLight,
    fontSize: 18,
    fontWeight: 'bold',
  },
  zoneTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  zoneMixingRatio: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 2,
  },
  zoneContent: {
    padding: 15,
  },
  ingredient: {
    marginBottom: 15,
  },
  ingredientHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  ingredientIcon: {
    width: 40,
    height: 40,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  ingredientInfo: {
    flex: 1,
  },
  ingredientName: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.text,
  },
  ingredientCode: {
    fontSize: 12,
    color: Colors.light.gray,
  },
  ingredientAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.darkGray,
  },
  proportionBar: {
    height: 25,
    backgroundColor: Colors.light.progressBarBackground,
    borderRadius: 12.5,
    overflow: 'hidden',
  },
  proportionFill: {
    height: '100%',
    borderRadius: 12.5,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 40,
  },
  proportionText: {
    color: Colors.light.textLight,
    fontSize: 12,
    fontWeight: '600',
  },
  processingTimeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 10,
    marginTop: 10,
    borderTopWidth: 1,
    borderTopColor: Colors.light.progressBarBackground,
  },
  processingTimeLabel: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  processingTimeValue: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.primary,
  },
  notesContainer: {
    marginTop: 10,
    padding: 10,
    backgroundColor: Colors.light.timelineBackground,
    borderRadius: 8,
  },
  noteText: {
    fontSize: 12,
    color: Colors.light.gray,
    lineHeight: 18,
  },
});
