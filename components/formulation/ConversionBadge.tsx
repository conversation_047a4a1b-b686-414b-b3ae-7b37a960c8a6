import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { ArrowRight, AlertCircle } from 'lucide-react-native';
import { ConversionInfo } from '@/types/visual-formulation';
import Colors from '@/constants/colors';

interface ConversionBadgeProps {
  conversion: ConversionInfo;
}

export default function ConversionBadge({ conversion }: ConversionBadgeProps) {
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return Colors.light.success;
    if (confidence >= 70) return Colors.light.warning;
    return Colors.light.error;
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.conversionIcon}>
          <Text style={styles.conversionIconText}>🔄</Text>
        </View>
        <Text style={styles.title}>FÓRMULA CONVERTIDA</Text>
      </View>

      <View style={styles.conversionFlow}>
        <View style={styles.brandBlock}>
          <Text style={styles.brandLabel}>Original</Text>
          <Text style={styles.brandName}>{conversion.originalBrand}</Text>
          <Text style={styles.brandLine}>{conversion.originalLine}</Text>
          <Text style={styles.formulaCode}>{conversion.originalFormula}</Text>
        </View>

        <ArrowRight size={24} color={Colors.light.textLight} style={styles.arrow} />

        <View style={styles.brandBlock}>
          <Text style={styles.brandLabel}>Adaptado</Text>
          <Text style={styles.brandName}>{conversion.targetBrand}</Text>
          <Text style={styles.brandLine}>{conversion.targetLine}</Text>
        </View>
      </View>

      <View style={styles.confidenceContainer}>
        <Text style={styles.confidenceLabel}>Nivel de confianza</Text>
        <View style={styles.confidenceBar}>
          <View
            style={[
              styles.confidenceFill,
              {
                width: `${conversion.confidence}%`,
                backgroundColor: getConfidenceColor(conversion.confidence),
              },
            ]}
          />
        </View>
        <Text style={[styles.confidenceText, { color: getConfidenceColor(conversion.confidence) }]}>
          {conversion.confidence}%
        </Text>
      </View>

      {conversion.adjustments.length > 0 && (
        <View style={styles.adjustmentsContainer}>
          <Text style={styles.adjustmentsTitle}>Ajustes realizados:</Text>
          {conversion.adjustments.map((adjustment, index) => (
            <Text key={index} style={styles.adjustmentText}>
              • {adjustment}
            </Text>
          ))}
        </View>
      )}

      {conversion.warnings && conversion.warnings.length > 0 && (
        <View style={styles.warningsContainer}>
          <AlertCircle size={16} color={Colors.light.warning} />
          <View style={styles.warningsTextContainer}>
            {conversion.warnings.map((warning, index) => (
              <Text key={index} style={styles.warningText}>
                {warning}
              </Text>
            ))}
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.backgroundOpacity95,
    marginTop: 15,
    padding: 15,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.light.backgroundWithOpacity,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  conversionIcon: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: Colors.light.primaryTransparent20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  conversionIconText: {
    fontSize: 16,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.textLight,
    letterSpacing: 0.5,
  },
  conversionFlow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    marginBottom: 15,
  },
  brandBlock: {
    alignItems: 'center',
    flex: 1,
  },
  brandLabel: {
    fontSize: 11,
    color: Colors.light.backgroundOpacity70,
    marginBottom: 2,
  },
  brandName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textLight,
  },
  brandLine: {
    fontSize: 13,
    color: Colors.light.backgroundOpacity90,
  },
  formulaCode: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.textLight,
    marginTop: 2,
    backgroundColor: Colors.light.primaryTransparent20,
    paddingHorizontal: 10,
    paddingVertical: 2,
    borderRadius: 10,
  },
  arrow: {
    marginHorizontal: 10,
  },
  confidenceContainer: {
    marginBottom: 10,
  },
  confidenceLabel: {
    fontSize: 11,
    color: Colors.light.backgroundOpacity70,
    marginBottom: 5,
  },
  confidenceBar: {
    height: 6,
    backgroundColor: Colors.light.primaryTransparent20,
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 5,
  },
  confidenceFill: {
    height: '100%',
    borderRadius: 3,
  },
  confidenceText: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'right',
  },
  adjustmentsContainer: {
    backgroundColor: Colors.light.primaryTransparent10,
    padding: 10,
    borderRadius: 8,
    marginBottom: 10,
  },
  adjustmentsTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.backgroundOpacity90,
    marginBottom: 5,
  },
  adjustmentText: {
    fontSize: 11,
    color: Colors.light.textSecondary,
    lineHeight: 16,
  },
  warningsContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.light.warningTransparent15,
    padding: 10,
    borderRadius: 8,
    alignItems: 'flex-start',
  },
  warningsTextContainer: {
    flex: 1,
    marginLeft: 8,
  },
  warningText: {
    fontSize: 11,
    color: Colors.light.textLight,
    lineHeight: 16,
  },
});
