# CLAUDE.md - Formulation Components

## 🎯 Propósito

Componentes para visualización y manipulación de fórmulas químicas de coloración capilar. Incluye displays interactivos, calculadoras de proporciones, validación en tiempo real y instrucciones paso a paso.

## 📁 Componentes Principales

### Visualización de Fórmulas

- `FormulaDisplay.tsx` - Display principal de fórmula
- `EnhancedFormulationView.tsx` - Vista mejorada con interacciones
- `FormulaVisualization.tsx` - Visualización gráfica de mezclas
- `ZoneFormulaCards.tsx` - Fórmulas por zonas del cabello

### Interacción y Ajustes

- `QuickAdjustPanel.tsx` - Panel de ajustes rápidos
- `ProportionCalculator.tsx` - Calculadora de proporciones
- `ProductMatchConfirmation.tsx` - Confirmación de mapeo de productos
- `ConversionBadge.tsx` - Badge de conversión entre marcas

### Instrucciones y Flujo

- `InstructionsFlow.tsx` - Flujo de instrucciones paso a paso
- `InteractiveTimeline.tsx` - Timeline interactivo del proceso
- `ApplicationDiagram.tsx` - Diagrama de aplicación por zonas
- `StepDetailCard.tsx` - Detalle de cada paso

### Materiales y Costos

- `MaterialsSummaryCard.tsx` - Resumen de materiales necesarios
- `FormulaTips.tsx` - Tips y recomendaciones
- `ColorTransitionVisual.tsx` - Visualización de transición de color

## 🧪 Formula Display (FormulaDisplay.tsx)

### Estructura de Datos

```typescript
interface DisplayFormula {
  id: string;
  colors: ColorComponent[];
  developer: DeveloperComponent;
  additives?: AdditiveComponent[];
  instructions: ProcessingInstructions;
  metadata: FormulaMetadata;
}

interface ColorComponent {
  id: string;
  brand: string;
  line: string;
  shade: string;
  quantity: number; // ml o g
  percentage: number; // % del total
  cost?: number;
  isAvailable: boolean;
  mappingConfidence?: number;
}
```

### Display Principal

```typescript
export function FormulaDisplay({ formula, onEdit, readonly = false }: Props) {
  const [editMode, setEditMode] = useState(false);
  const [adjustments, setAdjustments] = useState<Adjustments>({});

  // Calcular totales en tiempo real
  const totals = useMemo(() => {
    const totalVolume = formula.colors.reduce((sum, c) => sum + c.quantity, 0)
                      + formula.developer.quantity;
    const totalCost = calculateTotalCost(formula);

    return { totalVolume, totalCost };
  }, [formula]);

  // Validar proporciones
  const validation = useMemo(() => {
    return validateFormula(formula);
  }, [formula]);

  return (
    <View style={styles.container}>
      {/* Header con información básica */}
      <FormulaHeader
        formula={formula}
        totals={totals}
        validation={validation}
        onEditToggle={() => setEditMode(!editMode)}
        readonly={readonly}
      />

      {/* Lista de componentes */}
      <View style={styles.components}>
        {formula.colors.map(color => (
          <ColorComponentCard
            key={color.id}
            component={color}
            editable={editMode}
            onAdjust={(id, adjustment) => handleAdjustment(id, adjustment)}
          />
        ))}

        <DeveloperCard
          developer={formula.developer}
          editable={editMode}
          onAdjust={handleDeveloperAdjust}
        />
      </View>

      {/* Panel de ajustes rápidos */}
      {editMode && (
        <QuickAdjustPanel
          formula={formula}
          onQuickAdjust={handleQuickAdjust}
        />
      )}

      {/* Validación y warnings */}
      <ValidationPanel validation={validation} />
    </View>
  );
}
```

### Validación en Tiempo Real

```typescript
function validateFormula(formula: DisplayFormula): FormulaValidation {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 1. Validar proporciones
  const totalColor = formula.colors.reduce((sum, c) => sum + c.quantity, 0);
  const developerRatio = formula.developer.quantity / totalColor;

  if (developerRatio < 0.5 || developerRatio > 2.0) {
    errors.push(`Ratio color:developer incorrecto (${developerRatio.toFixed(2)}:1)`);
  }

  // 2. Validar disponibilidad de productos
  const unavailableProducts = formula.colors.filter(c => !c.isAvailable);
  if (unavailableProducts.length > 0) {
    warnings.push(`${unavailableProducts.length} productos no disponibles en inventario`);
  }

  // 3. Validar mapping confidence
  const lowConfidenceProducts = formula.colors.filter(
    c => c.mappingConfidence && c.mappingConfidence < 70
  );
  if (lowConfidenceProducts.length > 0) {
    warnings.push('Algunos productos tienen bajo nivel de confianza en el mapeo');
  }

  // 4. Validar totales
  const totalPercentage = formula.colors.reduce((sum, c) => sum + c.percentage, 0);
  if (Math.abs(totalPercentage - 100) > 0.1) {
    errors.push('Los porcentajes no suman 100%');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    confidence: calculateOverallConfidence(formula),
  };
}
```

## ⚡ Quick Adjust Panel

### Ajustes Rápidos

```typescript
interface QuickAdjustment {
  type: 'increase' | 'decrease' | 'neutralize' | 'intensify';
  target: 'all' | 'color' | 'developer' | 'specific';
  amount: number;
  unit: 'ml' | 'g' | 'percentage';
}

export function QuickAdjustPanel({ formula, onQuickAdjust }: Props) {
  const quickAdjustments: QuickAdjustment[] = [
    { type: 'increase', target: 'all', amount: 5, unit: 'ml' },
    { type: 'decrease', target: 'all', amount: 5, unit: 'ml' },
    { type: 'neutralize', target: 'color', amount: 10, unit: 'percentage' },
    { type: 'intensify', target: 'color', amount: 10, unit: 'percentage' }
  ];

  const handleAdjustment = (adjustment: QuickAdjustment) => {
    const adjustedFormula = applyQuickAdjustment(formula, adjustment);

    // Validar antes de aplicar
    const validation = validateFormula(adjustedFormula);
    if (validation.isValid) {
      onQuickAdjust(adjustedFormula);
    } else {
      showValidationErrors(validation.errors);
    }
  };

  return (
    <View style={styles.panel}>
      <Text style={styles.title}>Ajustes Rápidos</Text>

      <View style={styles.adjustmentGrid}>
        {quickAdjustments.map(adjustment => (
          <TouchableOpacity
            key={`${adjustment.type}_${adjustment.target}`}
            style={styles.adjustmentButton}
            onPress={() => handleAdjustment(adjustment)}
          >
            <Icon name={getAdjustmentIcon(adjustment.type)} />
            <Text>{getAdjustmentLabel(adjustment)}</Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Slider para ajustes custom */}
      <View style={styles.customAdjust}>
        <Text>Ajuste Custom:</Text>
        <Slider
          minimumValue={-20}
          maximumValue={20}
          value={0}
          onValueChange={handleCustomAdjustment}
          step={1}
        />
      </View>
    </View>
  );
}
```

## 📊 Proportion Calculator

### Calculadora Interactiva

```typescript
export function ProportionCalculator({
  baseFormula,
  targetVolume,
  onRecalculate
}: Props) {

  const [scaleFactor, setScaleFactor] = useState(1);
  const [customVolumes, setCustomVolumes] = useState<Record<string, number>>({});

  // Calcular proporciones escaladas
  const scaledFormula = useMemo(() => {
    return scaleFormula(baseFormula, scaleFactor, customVolumes);
  }, [baseFormula, scaleFactor, customVolumes]);

  // Validar que las proporciones se mantengan
  const proportionValidation = useMemo(() => {
    return validateProportions(baseFormula, scaledFormula);
  }, [baseFormula, scaledFormula]);

  return (
    <View style={styles.calculator}>
      <View style={styles.header}>
        <Text style={styles.title}>Calculadora de Proporciones</Text>
        <Text style={styles.subtitle}>
          Volumen objetivo: {targetVolume}ml
        </Text>
      </View>

      {/* Control de escala */}
      <View style={styles.scaleControl}>
        <Text>Factor de Escala:</Text>
        <View style={styles.scaleButtons}>
          <TouchableOpacity onPress={() => setScaleFactor(0.5)}>
            <Text>1/2</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => setScaleFactor(1)}>
            <Text>1x</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => setScaleFactor(1.5)}>
            <Text>1.5x</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={() => setScaleFactor(2)}>
            <Text>2x</Text>
          </TouchableOpacity>
        </View>

        {/* Slider para ajuste fino */}
        <Slider
          minimumValue={0.1}
          maximumValue={5}
          value={scaleFactor}
          onValueChange={setScaleFactor}
          step={0.1}
        />
      </View>

      {/* Comparación lado a lado */}
      <View style={styles.comparison}>
        <View style={styles.column}>
          <Text style={styles.columnTitle}>Original</Text>
          <FormulaList formula={baseFormula} />
        </View>

        <View style={styles.column}>
          <Text style={styles.columnTitle}>Escalado</Text>
          <FormulaList formula={scaledFormula} />

          {/* Indicador de validación */}
          <ValidationIndicator validation={proportionValidation} />
        </View>
      </View>

      {/* Botón de aplicar */}
      <TouchableOpacity
        style={[
          styles.applyButton,
          !proportionValidation.isValid && styles.disabledButton
        ]}
        onPress={() => onRecalculate(scaledFormula)}
        disabled={!proportionValidation.isValid}
      >
        <Text style={styles.applyButtonText}>
          Aplicar Proporciones
        </Text>
      </TouchableOpacity>
    </View>
  );
}

function scaleFormula(
  formula: DisplayFormula,
  factor: number,
  customVolumes: Record<string, number>
): DisplayFormula {

  return {
    ...formula,
    colors: formula.colors.map(color => ({
      ...color,
      quantity: customVolumes[color.id] || (color.quantity * factor),
      percentage: calculatePercentage(color, factor)
    })),
    developer: {
      ...formula.developer,
      quantity: formula.developer.quantity * factor
    }
  };
}
```

## 🎨 Color Transition Visual

### Visualización de Transformación

```typescript
export function ColorTransitionVisual({
  beforeColor,
  afterColor,
  steps
}: Props) {

  const transitionSteps = useMemo(() => {
    return generateTransitionSteps(beforeColor, afterColor, steps);
  }, [beforeColor, afterColor, steps]);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Transformación de Color</Text>

      {/* Barra de progreso visual */}
      <View style={styles.progressBar}>
        {transitionSteps.map((step, index) => (
          <View
            key={index}
            style={[
              styles.colorStep,
              { backgroundColor: step.color }
            ]}
          />
        ))}
      </View>

      {/* Información de colores */}
      <View style={styles.colorInfo}>
        <View style={styles.colorBox}>
          <View
            style={[
              styles.colorSwatch,
              { backgroundColor: beforeColor.hex }
            ]}
          />
          <Text>Antes</Text>
          <Text style={styles.colorDetails}>
            Nivel {beforeColor.level} - {beforeColor.tone}
          </Text>
        </View>

        <Icon name="arrow-right" style={styles.arrow} />

        <View style={styles.colorBox}>
          <View
            style={[
              styles.colorSwatch,
              { backgroundColor: afterColor.hex }
            ]}
          />
          <Text>Después</Text>
          <Text style={styles.colorDetails}>
            Nivel {afterColor.level} - {afterColor.tone}
          </Text>
        </View>
      </View>

      {/* Análisis de la transformación */}
      <View style={styles.analysis}>
        <Text style={styles.analysisTitle}>Análisis de Transformación</Text>
        <Text>Diferencia de nivel: {afterColor.level - beforeColor.level}</Text>
        <Text>Tipo de proceso: {determineProcessType(beforeColor, afterColor)}</Text>
        <Text>Dificultad: {calculateDifficulty(beforeColor, afterColor)}</Text>
      </View>
    </View>
  );
}

function generateTransitionSteps(
  before: HairColor,
  after: HairColor,
  stepCount: number
): ColorStep[] {

  const steps: ColorStep[] = [];

  for (let i = 0; i <= stepCount; i++) {
    const progress = i / stepCount;

    // Interpolación RGB
    const r = Math.round(before.rgb.r + (after.rgb.r - before.rgb.r) * progress);
    const g = Math.round(before.rgb.g + (after.rgb.g - before.rgb.g) * progress);
    const b = Math.round(before.rgb.b + (after.rgb.b - before.rgb.b) * progress);

    steps.push({
      progress,
      color: `rgb(${r}, ${g}, ${b})`,
      level: before.level + (after.level - before.level) * progress,
      description: generateStepDescription(progress, before, after)
    });
  }

  return steps;
}
```

## 📋 Instructions Flow

### Flujo Paso a Paso

```typescript
export function InstructionsFlow({
  formula,
  onStepComplete,
  onProcessComplete
}: Props) {

  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const [timer, setTimer] = useState<NodeJS.Timeout | null>(null);

  const instructions = useMemo(() => {
    return generateInstructions(formula);
  }, [formula]);

  const handleStepComplete = (stepIndex: number) => {
    setCompletedSteps(prev => new Set([...prev, stepIndex]));
    onStepComplete(stepIndex);

    // Auto-avanzar al siguiente paso
    if (stepIndex < instructions.length - 1) {
      setCurrentStep(stepIndex + 1);
    } else {
      onProcessComplete();
    }
  };

  return (
    <ScrollView style={styles.container}>
      {/* Progress indicator */}
      <View style={styles.progressHeader}>
        <Text style={styles.title}>Instrucciones de Aplicación</Text>
        <Text style={styles.progress}>
          Paso {currentStep + 1} de {instructions.length}
        </Text>
        <ProgressBar
          progress={(currentStep + 1) / instructions.length}
          color={Colors.primary}
        />
      </View>

      {/* Lista de pasos */}
      {instructions.map((instruction, index) => (
        <StepCard
          key={index}
          instruction={instruction}
          index={index}
          isActive={index === currentStep}
          isCompleted={completedSteps.has(index)}
          onComplete={() => handleStepComplete(index)}
        />
      ))}

      {/* Timer global */}
      <TimerCard
        totalTime={formula.processingTime}
        onTimeUp={() => alert('¡Tiempo de procesamiento completado!')}
      />
    </ScrollView>
  );
}

function generateInstructions(formula: DisplayFormula): ProcessingInstruction[] {
  const instructions: ProcessingInstruction[] = [];

  // 1. Preparación
  instructions.push({
    id: 'prep',
    title: 'Preparación',
    description: 'Reunir todos los materiales y preparar el área de trabajo',
    duration: 5,
    materials: extractMaterials(formula),
    steps: [
      'Colocar capa protectora',
      'Reunir productos según fórmula',
      'Preparar utensilios de mezcla',
      'Verificar alergias del cliente'
    ]
  });

  // 2. Mezcla
  instructions.push({
    id: 'mix',
    title: 'Mezcla de Productos',
    description: 'Mezclar los componentes en el orden correcto',
    duration: 10,
    materials: ['Bowl de mezcla', 'Pincel'],
    steps: generateMixingSteps(formula)
  });

  // 3. Aplicación
  instructions.push({
    id: 'apply',
    title: 'Aplicación',
    description: 'Aplicar la mezcla siguiendo el patrón recomendado',
    duration: 20,
    materials: ['Pinceles', 'Separadores'],
    steps: generateApplicationSteps(formula)
  });

  // 4. Procesamiento
  instructions.push({
    id: 'process',
    title: 'Tiempo de Procesamiento',
    description: `Dejar actuar durante ${formula.processingTime} minutos`,
    duration: formula.processingTime,
    materials: ['Timer'],
    steps: [
      'Controlar tiempo con timer',
      'Verificar progreso cada 10 minutos',
      'Mantener temperatura ambiente'
    ]
  });

  // 5. Enjuague
  instructions.push({
    id: 'rinse',
    title: 'Enjuague y Finalización',
    description: 'Enjuagar completamente y aplicar tratamiento',
    duration: 15,
    materials: ['Champú', 'Acondicionador'],
    steps: [
      'Enjuagar con agua tibia',
      'Aplicar champú neutralizante',
      'Aplicar acondicionador',
      'Secar y evaluar resultado'
    ]
  });

  return instructions;
}
```

## 🔧 Patterns & Performance

### Optimización de Re-renders

```typescript
// Usar React.memo para componentes pesados
export const FormulaDisplay = React.memo(
  function FormulaDisplay(props: Props) {
    // Component implementation
  },
  (prevProps, nextProps) => {
    // Custom comparison para evitar re-renders innecesarios
    return (
      prevProps.formula.id === nextProps.formula.id &&
      prevProps.readonly === nextProps.readonly &&
      JSON.stringify(prevProps.formula.colors) === JSON.stringify(nextProps.formula.colors)
    );
  }
);

// Usar useMemo para cálculos costosos
const calculatedTotals = useMemo(() => {
  return heavyCalculation(formula);
}, [formula.colors, formula.developer]);

// Usar useCallback para funciones
const handleAdjustment = useCallback((id: string, adjustment: number) => {
  setFormula(prev => adjustFormulaComponent(prev, id, adjustment));
}, []);
```

### Validación de Props

```typescript
interface FormulaDisplayProps {
  formula: DisplayFormula;
  onEdit?: (formula: DisplayFormula) => void;
  readonly?: boolean;
  showCosts?: boolean;
  enableQuickAdjust?: boolean;
}

// Validación runtime con PropTypes o Zod
const FormulaDisplaySchema = z.object({
  formula: z.object({
    colors: z.array(ColorComponentSchema),
    developer: DeveloperComponentSchema,
    processingTime: z.number().min(1).max(120),
  }),
  readonly: z.boolean().optional(),
});
```

## 🤖 Agentes Recomendados

### Para este módulo usar:

**whimsy-injector** - Micro-interacciones en componentes de formulación

- Animaciones fluidas en sliders de proporciones
- Haptic feedback en botones de ajuste rápido
- Transiciones suaves en validación de fórmulas
- Usar cuando la funcionalidad base esté completa

**ui-designer** - Diseño de nuevos componentes de formulación

- Componentes consistentes para display de fórmulas
- Sistemas de colores para indicadores de confianza
- Accessibility en componentes complejos
- PROACTIVAMENTE usar para nuevas pantallas

**colorimetry-expert** - Validación técnica de fórmulas

- Revisar cálculos químicos en displays
- Validar terminología profesional
- Verificar precisión en visualizaciones de color
- PROACTIVAMENTE usar al revisar displays de fórmulas

**frontend-developer** - Implementación de componentes complejos

- Lógica de validación en tiempo real
- Optimización de re-renders en componentes pesados
- Patterns offline-first para fórmulas
- PROACTIVAMENTE usar para nuevas features

### 💡 Ejemplos de Uso

```bash
# Añadir micro-interacciones a sliders
Task: Use whimsy-injector to add smooth haptic feedback to QuickAdjustPanel sliders

# Optimizar rendimiento de FormulaDisplay
Task: Use frontend-developer to optimize FormulaDisplay re-renders with React.memo

# Validar nueva visualización de color
Task: Use colorimetry-expert to review ColorTransitionVisual accuracy
```

## 🔌 MCPs Disponibles

### Funciones MCP útiles:

**Para análisis de código:**

- `mcp__serena__get_symbols_overview` - Entender estructura de componentes
- `mcp__serena__find_symbol` - Localizar componentes específicos
- `mcp__serena__find_referencing_symbols` - Ver dónde se usan componentes

**Para navegación y modificación:**

- `mcp__serena__search_for_pattern` - Buscar patrones en componentes
- `mcp__serena__replace_symbol_body` - Actualizar implementaciones
- `mcp__serena__insert_after_symbol` - Añadir nuevos componentes

**Para documentación:**

- `mcp__context7__resolve_library_id` - Buscar docs de React Native
- `mcp__context7__get_library_docs` - Obtener docs de componentes UI

### 📝 Ejemplos MCP

```bash
# Analizar estructura de FormulaDisplay
mcp__serena__get_symbols_overview: "components/formulation/FormulaDisplay.tsx"

# Encontrar todas las referencias a QuickAdjustPanel
mcp__serena__find_referencing_symbols: "QuickAdjustPanel" in "components/formulation/QuickAdjustPanel.tsx"

# Buscar todos los useMemo en componentes
mcp__serena__search_for_pattern: "useMemo" in "components/formulation/"

# Obtener docs de React Native Slider
mcp__context7__get_library_docs: "/react-native-community/react-native-slider"
```

## 🔗 Archivos Relacionados

- `../../stores/service-draft-store.ts` - Estado de la fórmula en servicio
- `../../utils/parseFormula.ts` - Parser de fórmulas
- `../../utils/professional-colorimetry.ts` - Cálculos químicos
- `../../services/brandConversionService.ts` - Conversión entre marcas
- `../../types/formulation.ts` - Tipos de fórmulas

---

**⚡ Recuerda:** La visualización de fórmulas es crítica para el profesional. Debe ser clara, precisa y permitir ajustes seguros en tiempo real. Usa agentes especializados para optimizar tanto la funcionalidad como la experiencia de usuario.
