import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { ArrowRight } from 'lucide-react-native';
import { ColorTransition } from '@/types/visual-formulation';
import Colors from '@/constants/colors';

interface ColorTransitionVisualProps {
  transition: ColorTransition;
  brand?: string;
}

export default function ColorTransitionVisual({ transition, brand }: ColorTransitionVisualProps) {
  const getDifficultyColor = (difficulty: ColorTransition['difficulty']) => {
    switch (difficulty) {
      case 'easy':
        return Colors.light.success;
      case 'moderate':
        return Colors.light.warning;
      case 'challenging':
        return Colors.light.secondary;
      case 'complex':
        return Colors.light.error;
      default:
        return Colors.light.gray;
    }
  };

  const getDifficultyText = (difficulty: ColorTransition['difficulty']) => {
    switch (difficulty) {
      case 'easy':
        return 'Cambio Simple';
      case 'moderate':
        return 'Cambio Moderado';
      case 'challenging':
        return 'Cambio Desafiante';
      case 'complex':
        return 'Cambio Complejo';
      default:
        return 'Cambio';
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>TRANSFORMACIÓN DE COLOR</Text>
        {brand && <Text style={styles.brand}>{brand}</Text>}
      </View>

      <View style={styles.transitionContainer}>
        {/* Current Color */}
        <View style={styles.colorBlock}>
          <View
            style={[
              styles.colorCircle,
              { backgroundColor: transition.current.hex || Colors.light.darkGray },
            ]}
          >
            <Text style={styles.levelText}>{transition.current.level}</Text>
          </View>
          <Text style={styles.colorLabel}>ACTUAL</Text>
          <Text style={styles.toneText}>{transition.current.tone}</Text>
        </View>

        {/* Arrow */}
        <View style={styles.arrowContainer}>
          <ArrowRight size={40} color={Colors.light.primary} />
        </View>

        {/* Target Color */}
        <View style={styles.colorBlock}>
          <View
            style={[
              styles.colorCircle,
              { backgroundColor: transition.target.hex || Colors.light.secondary },
            ]}
          >
            <Text style={styles.levelText}>{transition.target.level}</Text>
          </View>
          <Text style={styles.colorLabel}>OBJETIVO</Text>
          <Text style={styles.toneText}>{transition.target.tone}</Text>
        </View>
      </View>

      {/* Difficulty Indicator */}
      <View style={styles.difficultyContainer}>
        <View
          style={[
            styles.difficultyBadge,
            { backgroundColor: getDifficultyColor(transition.difficulty) },
          ]}
        >
          <Text style={styles.difficultyText}>{getDifficultyText(transition.difficulty)}</Text>
        </View>
        {transition.sessions && transition.sessions > 1 && (
          <Text style={styles.sessionsText}>Recomendado en {transition.sessions} sesiones</Text>
        )}
      </View>

      {/* Level Difference Bar */}
      <View style={styles.levelBarContainer}>
        <Text style={styles.levelBarLabel}>Cambio de Nivel</Text>
        <View style={styles.levelBar}>
          <LinearGradient
            colors={[Colors.light.primary, Colors.light.secondary]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={[
              styles.levelBarFill,
              {
                width: `${Math.min(Math.abs(transition.target.level - transition.current.level) * 10, 100)}%`,
              },
            ]}
          />
        </View>
        <Text style={styles.levelDiffText}>
          {Math.abs(transition.target.level - transition.current.level).toFixed(1)} niveles
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.card,
    borderRadius: 20,
    padding: 20,
    shadowColor: Colors.light.shadowColor,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.darkGray,
    letterSpacing: 0.5,
  },
  brand: {
    fontSize: 12,
    color: Colors.light.gray,
  },
  transitionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  colorBlock: {
    alignItems: 'center',
    flex: 1,
  },
  colorCircle: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.light.shadowColor,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 10,
    elevation: 8,
    marginBottom: 10,
  },
  levelText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.textLight,
    textShadowColor: Colors.light.shadowColorWithOpacity,
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  colorLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.gray,
    marginBottom: 5,
  },
  toneText: {
    fontSize: 14,
    color: Colors.light.text,
    textAlign: 'center',
  },
  arrowContainer: {
    paddingHorizontal: 10,
  },
  difficultyContainer: {
    alignItems: 'center',
    marginBottom: 15,
  },
  difficultyBadge: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
    marginBottom: 5,
  },
  difficultyText: {
    color: Colors.light.textLight,
    fontWeight: '600',
    fontSize: 14,
  },
  sessionsText: {
    fontSize: 12,
    color: Colors.light.gray,
    fontStyle: 'italic',
  },
  levelBarContainer: {
    marginTop: 10,
  },
  levelBarLabel: {
    fontSize: 12,
    color: Colors.light.gray,
    marginBottom: 5,
  },
  levelBar: {
    height: 8,
    backgroundColor: Colors.light.border,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 5,
  },
  levelBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  levelDiffText: {
    fontSize: 12,
    color: Colors.light.text,
    textAlign: 'right',
  },
});
