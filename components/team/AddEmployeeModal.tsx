import React, { useState } from 'react';
import { logger } from '@/utils/logger';
import {
  Modal,
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Switch,
} from 'react-native';
import { X, Eye, EyeOff, Wand2 } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius, shadows } from '@/constants/theme';
import { useTeamStore } from '@/stores/team-store';
import {
  PERMISSIONS,
  PERMISSION_LABELS,
  PERMISSION_DESCRIPTIONS,
  DEFAULT_EMPLOYEE_PERMISSIONS,
  type Permission,
} from '@/types/permissions';

interface AddEmployeeModalProps {
  visible: boolean;
  onClose: () => void;
  salonId: string;
  createdBy: string;
}

export default function AddEmployeeModal({
  visible,
  onClose,
  salonId,
  createdBy: _createdBy,
}: AddEmployeeModalProps) {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [permissions, setPermissions] = useState<Permission[]>([...DEFAULT_EMPLOYEE_PERMISSIONS]);
  const [isLoading, setIsLoading] = useState(false);

  const { addMember, getMemberByEmail, hashPassword } = useTeamStore();

  const generatePassword = () => {
    const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789!@#';
    let newPassword = '';
    for (let i = 0; i < 12; i++) {
      newPassword += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setPassword(newPassword);
    setShowPassword(true);
  };

  const togglePermission = (permission: Permission) => {
    setPermissions(prev =>
      prev.includes(permission) ? prev.filter(p => p !== permission) : [...prev, permission]
    );
  };

  const handleSubmit = async () => {
    // Validación
    if (!name.trim() || !email.trim() || !password.trim()) {
      Alert.alert('Error', 'Por favor completa todos los campos obligatorios');
      return;
    }

    // Verificar si el email ya existe
    if (getMemberByEmail(email)) {
      Alert.alert('Error', 'Ya existe un usuario con ese email');
      return;
    }

    setIsLoading(true);

    try {
      // Hashear la contraseña antes de guardar
      const passwordHash = await hashPassword(password);

      addMember({
        name: name.trim(),
        email: email.trim().toLowerCase(),
        passwordHash,
        role: 'Colorista',
        isOwner: false,
        permissions,
        salonId,
        status: 'active',
      });

      Alert.alert(
        'Usuario Creado',
        `${name.trim()} ha sido añadido al equipo.\n\nUsuario: ${email.trim().toLowerCase()}\nContraseña: ${password}\n\nGuarda estas credenciales de forma segura.`,
        [{ text: 'OK', onPress: onClose }]
      );

      // Limpiar formulario
      setName('');
      setEmail('');
      setPassword('');
      setPermissions([...DEFAULT_EMPLOYEE_PERMISSIONS]);
    } catch (error) {
      Alert.alert('Error', 'No se pudo crear el usuario. Intenta de nuevo.');
      logger.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Nuevo Empleado</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <X size={24} color={Colors.light.text} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Información Básica</Text>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Nombre*</Text>
              <TextInput
                style={styles.input}
                value={name}
                onChangeText={setName}
                placeholder="María García"
                autoCapitalize="words"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Email*</Text>
              <TextInput
                style={styles.input}
                value={email}
                onChangeText={setEmail}
                placeholder="<EMAIL>"
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Contraseña*</Text>
              <View style={styles.passwordContainer}>
                <TextInput
                  style={styles.passwordInput}
                  value={password}
                  onChangeText={setPassword}
                  placeholder="••••••••"
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                <TouchableOpacity
                  style={styles.passwordToggle}
                  onPress={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff size={20} color={Colors.light.textSecondary} />
                  ) : (
                    <Eye size={20} color={Colors.light.textSecondary} />
                  )}
                </TouchableOpacity>
                <TouchableOpacity style={styles.generateButton} onPress={generatePassword}>
                  <Wand2 size={20} color={Colors.light.primary} />
                </TouchableOpacity>
              </View>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>¿Qué puede hacer {name || 'este empleado'}?</Text>
            <Text style={styles.sectionSubtitle}>
              Selecciona los permisos que tendrá en la aplicación
            </Text>

            {Object.values(PERMISSIONS).map(permission => (
              <TouchableOpacity
                key={permission}
                style={styles.permissionRow}
                onPress={() => togglePermission(permission)}
                activeOpacity={0.7}
              >
                <View style={styles.permissionInfo}>
                  <Text style={styles.permissionLabel}>{PERMISSION_LABELS[permission]}</Text>
                  <Text style={styles.permissionDescription}>
                    {PERMISSION_DESCRIPTIONS[permission]}
                  </Text>
                </View>
                <Switch
                  value={permissions.includes(permission)}
                  onValueChange={() => togglePermission(permission)}
                  trackColor={{ false: Colors.light.border, true: Colors.light.primary }}
                />
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity style={styles.cancelButton} onPress={onClose} disabled={isLoading}>
            <Text style={styles.cancelButtonText}>Cancelar</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.submitButton, isLoading && styles.disabledButton]}
            onPress={handleSubmit}
            disabled={isLoading}
          >
            <Text style={styles.submitButtonText}>
              {isLoading ? 'Creando...' : 'Crear Usuario'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.backgroundSecondary,
  },
  title: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
  },
  closeButton: {
    padding: spacing.xs,
  },
  content: {
    flex: 1,
  },
  section: {
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.backgroundSecondary,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  sectionSubtitle: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginBottom: spacing.lg,
  },
  inputGroup: {
    marginBottom: spacing.md,
  },
  label: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  input: {
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: radius.md,
    padding: spacing.md,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: radius.md,
  },
  passwordInput: {
    flex: 1,
    padding: spacing.md,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  passwordToggle: {
    padding: spacing.md,
  },
  generateButton: {
    padding: spacing.md,
    borderLeftWidth: 1,
    borderLeftColor: '#E5E5E5',
  },
  permissionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.backgroundSecondary,
  },
  permissionInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  permissionLabel: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
    marginBottom: 4,
  },
  permissionDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  footer: {
    flexDirection: 'row',
    padding: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: '#F5F5F7',
    gap: spacing.md,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: Colors.light.backgroundSecondary,
    paddingVertical: spacing.md,
    borderRadius: radius.md,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
  },
  submitButton: {
    flex: 1,
    backgroundColor: Colors.light.primary,
    paddingVertical: spacing.md,
    borderRadius: radius.md,
    alignItems: 'center',
    ...shadows.sm,
  },
  submitButtonText: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.textLight,
  },
  disabledButton: {
    opacity: 0.6,
  },
});
