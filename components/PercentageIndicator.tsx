import React from 'react';
import { StyleSheet, Text, View, Animated } from 'react-native';
import Colors from '@/constants/colors';

interface PercentageIndicatorProps {
  value: number;
  label: string;
  isFromAI?: boolean;
}

export default function PercentageIndicator({
  value,
  label,
  isFromAI = false,
}: PercentageIndicatorProps) {
  const progressAnim = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    Animated.timing(progressAnim, {
      toValue: value / 100,
      duration: 600,
      useNativeDriver: false,
    }).start();
  }, [value, progressAnim]);

  // Pre-calculate animated width to avoid inline interpolation
  const animatedWidth = progressAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '100%'],
  });

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.label}>{label}</Text>
        <Text style={[styles.value, isFromAI && styles.valueFromAI]}>{value}%</Text>
      </View>
      <View style={styles.progressBar}>
        <Animated.View style={[styles.progressFill, { width: animatedWidth }]} />
        <View style={styles.dotsContainer}>
          {[0, 25, 50, 75, 100].map(milestone => (
            <View key={milestone} style={[styles.dot, value >= milestone && styles.dotActive]} />
          ))}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'baseline',
    marginBottom: 8,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
  },
  value: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.light.text,
  },
  valueFromAI: {
    color: Colors.light.success,
  },
  progressBar: {
    height: 4,
    backgroundColor: Colors.light.border,
    borderRadius: 2,
    position: 'relative',
    overflow: 'hidden',
  },
  progressFill: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    backgroundColor: Colors.light.primary,
    borderRadius: 2,
  },
  dotsContainer: {
    position: 'absolute',
    top: -2,
    left: 0,
    right: 0,
    bottom: -2,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: -2,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.light.border,
    borderWidth: 2,
    borderColor: Colors.light.background,
  },
  dotActive: {
    backgroundColor: Colors.light.primary,
  },
});
