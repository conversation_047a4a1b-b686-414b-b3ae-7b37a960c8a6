import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '@/constants/colors';
import { ViabilityAnalysis } from '@/types/formulation';

interface ViabilityIndicatorProps {
  analysis: ViabilityAnalysis | null;
  loading?: boolean;
}

export default function ViabilityIndicator({ analysis, loading }: ViabilityIndicatorProps) {
  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingCard}>
          <Text style={styles.loadingText}>Analizando viabilidad...</Text>
        </View>
      </View>
    );
  }

  if (!analysis) return null;

  const getScoreConfig = () => {
    switch (analysis.score) {
      case 'safe':
        return {
          icon: <Ionicons name="shield-checkmark-outline" size={24} color={Colors.light.success} />,
          color: Colors.light.success,
          bgColor: Colors.light.success + '15',
          title: 'Viable',
          subtitle: 'Realizable en una única sesión',
        };
      case 'caution':
        return {
          icon: <Ionicons name="alert-triangle-outline" size={24} color={Colors.light.warning} />,
          color: Colors.light.warning,
          bgColor: Colors.light.warning + '15',
          title: 'Requiere 2 Fases',
          subtitle: 'Se requieren cuidados especiales',
        };
      case 'risky':
        return {
          icon: <Ionicons name="close-circle-outline" size={24} color={Colors.light.error} />,
          color: Colors.light.error,
          bgColor: Colors.light.error + '15',
          title: 'No Recomendado',
          subtitle: 'Alto riesgo sin tratamiento previo',
        };
    }
  };

  const config = getScoreConfig();

  return (
    <View style={styles.container}>
      <View style={[styles.mainCard, { backgroundColor: config.bgColor }]}>
        <View style={styles.header}>
          {config.icon}
          <View style={styles.headerText}>
            <Text style={[styles.title, { color: config.color }]}>{config.title}</Text>
            <Text style={styles.subtitle}>{config.subtitle}</Text>
          </View>
        </View>

        <View style={styles.factors}>
          <View style={styles.factorRow}>
            <Text style={styles.factorLabel}>Diferencia de niveles:</Text>
            <Text style={styles.factorValue}>{analysis.factors.levelDifference} niveles</Text>
          </View>
          <View style={styles.factorRow}>
            <Text style={styles.factorLabel}>Salud capilar:</Text>
            <Text style={styles.factorValue}>
              {analysis.factors.hairHealth === 'good'
                ? 'Buena'
                : analysis.factors.hairHealth === 'fair'
                  ? 'Regular'
                  : 'Comprometida'}
            </Text>
          </View>
          {analysis.factors.estimatedSessions > 1 && (
            <View style={styles.factorRow}>
              <Text style={styles.factorLabel}>Sesiones estimadas:</Text>
              <Text style={styles.factorValue}>{analysis.factors.estimatedSessions}</Text>
            </View>
          )}
        </View>

        {analysis.warnings.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Ionicons name="warning-outline" size={16} color={config.color} />
              <Text style={[styles.sectionTitle, { color: config.color }]}>Advertencias</Text>
            </View>
            {analysis.warnings.map((warning, index) => (
              <Text key={index} style={styles.warningText}>
                • {warning}
              </Text>
            ))}
          </View>
        )}

        {analysis.recommendations.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Ionicons name="information-circle-outline" size={16} color={Colors.light.primary} />
              <Text style={[styles.sectionTitle, { color: Colors.light.primary }]}>
                Recomendaciones
              </Text>
            </View>
            {analysis.recommendations.map((rec, index) => (
              <Text key={index} style={styles.recommendationText}>
                • {rec}
              </Text>
            ))}
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  loadingCard: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  mainCard: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.light.borderTransparent,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerText: {
    marginLeft: 12,
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  subtitle: {
    fontSize: 14,
    color: Colors.light.gray,
    marginTop: 2,
  },
  factors: {
    backgroundColor: Colors.light.backgroundOpacity90,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  factorRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  factorLabel: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  factorValue: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.text,
  },
  section: {
    marginTop: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  warningText: {
    fontSize: 14,
    color: Colors.light.text,
    marginLeft: 24,
    marginBottom: 4,
    lineHeight: 20,
  },
  recommendationText: {
    fontSize: 14,
    color: Colors.light.text,
    marginLeft: 24,
    marginBottom: 4,
    lineHeight: 20,
  },
});
