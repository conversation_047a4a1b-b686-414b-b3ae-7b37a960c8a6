/**
 * Coloration Loading State - Loading específico para procesos de coloración
 * Incluye animaciones delightful específicas del contexto de belleza profesional
 */

import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withRepeat,
  withSequence,
  withSpring,
  interpolate,
  Easing,
  withDelay,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { Palette, Droplets, Sparkles, Scissors } from 'lucide-react-native';
import Colors from '@/constants/colors';

type ColorationContext = 'analyzing-photo' | 'generating-formula' | 'mixing-colors' | 'processing';

interface ColorationLoadingStateProps {
  message?: string;
  context?: ColorationContext;
  visible?: boolean;
}

const SCREEN_WIDTH = Dimensions.get('window').width;

const CONTEXTUAL_ANIMATIONS = {
  'analyzing-photo': {
    message: 'Analizando estructura capilar...',
    icon: Scissors,
    colors: ['#8B5CF6', '#A78BFA', '#C4B5FD'],
    duration: 2000,
  },
  'generating-formula': {
    message: 'Creando fórmula personalizada...',
    icon: Palette,
    colors: ['#EF4444', '#F97316', '#EAB308'],
    duration: 2500,
  },
  'mixing-colors': {
    message: 'Mezclando pigmentos perfectos...',
    icon: Droplets,
    colors: ['#06B6D4', '#3B82F6', '#6366F1'],
    duration: 3000,
  },
  processing: {
    message: 'Procesando con IA avanzada...',
    icon: Sparkles,
    colors: ['#10B981', '#14B8A6', '#06B6D4'],
    duration: 1800,
  },
};

export const ColorationLoadingState: React.FC<ColorationLoadingStateProps> = ({
  message,
  context = 'processing',
  visible = true,
}) => {
  // Animation values
  const fadeIn = useSharedValue(0);
  const dropletY = useSharedValue(-20);
  const dropletOpacity = useSharedValue(0);
  const mixingRotation = useSharedValue(0);
  const colorFlow = useSharedValue(0);
  const sparkleScale = useSharedValue(0);
  const brushStroke = useSharedValue(0);

  const contextConfig = CONTEXTUAL_ANIMATIONS[context];

  useEffect(() => {
    if (visible) {
      // Main fade in
      fadeIn.value = withSpring(1, { damping: 15 });

      // Context-specific animations
      switch (context) {
        case 'analyzing-photo':
          // Scissor cutting animation
          brushStroke.value = withRepeat(
            withSequence(withTiming(1, { duration: 800 }), withTiming(0, { duration: 200 })),
            -1,
            false
          );
          break;

        case 'generating-formula':
          // Palette color mixing
          mixingRotation.value = withRepeat(
            withTiming(360, {
              duration: contextConfig.duration,
              easing: Easing.linear,
            }),
            -1,
            false
          );
          colorFlow.value = withRepeat(withTiming(1, { duration: 2000 }), -1, true);
          break;

        case 'mixing-colors':
          // Droplets falling
          dropletY.value = withRepeat(
            withSequence(withTiming(40, { duration: 1000 }), withTiming(-20, { duration: 100 })),
            -1,
            false
          );
          dropletOpacity.value = withRepeat(
            withSequence(
              withDelay(200, withTiming(1, { duration: 800 })),
              withTiming(0, { duration: 100 })
            ),
            -1,
            false
          );
          break;

        default:
          // Sparkle animation
          sparkleScale.value = withRepeat(
            withSequence(withSpring(1.2, { damping: 8 }), withSpring(0.8, { damping: 8 })),
            -1,
            true
          );
      }
    } else {
      fadeIn.value = withTiming(0, { duration: 300 });
    }
  }, [
    visible,
    context,
    fadeIn,
    brushStroke,
    mixingRotation,
    colorFlow,
    dropletY,
    dropletOpacity,
    sparkleScale,
    contextConfig.duration,
  ]);

  // Animated styles
  const containerStyle = useAnimatedStyle(() => ({
    opacity: fadeIn.value,
    transform: [{ scale: fadeIn.value }],
  }));

  const dropletStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: dropletY.value }],
    opacity: dropletOpacity.value,
  }));

  const mixingStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${mixingRotation.value}deg` }],
  }));

  const colorFlowStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateX: interpolate(colorFlow.value, [0, 1], [-SCREEN_WIDTH * 0.3, SCREEN_WIDTH * 0.3]),
      },
    ],
  }));

  const sparkleStyle = useAnimatedStyle(() => ({
    transform: [{ scale: sparkleScale.value }],
  }));

  const brushStrokeStyle = useAnimatedStyle(() => ({
    opacity: interpolate(brushStroke.value, [0, 1], [0.3, 1]),
    transform: [{ scaleX: interpolate(brushStroke.value, [0, 1], [0.5, 1]) }],
  }));

  if (!visible) return null;

  const IconComponent = contextConfig.icon;

  return (
    <Animated.View style={[styles.container, containerStyle]}>
      {/* Background with context colors */}
      <LinearGradient
        colors={[
          `${contextConfig.colors[0]}15`,
          `${contextConfig.colors[1]}10`,
          `${contextConfig.colors[2]}05`,
        ]}
        style={StyleSheet.absoluteFill}
      />

      {/* Color flow animation for generating formula */}
      {context === 'generating-formula' && (
        <View style={styles.colorFlowContainer}>
          {contextConfig.colors.map((color, index) => (
            <Animated.View
              key={index}
              style={[
                styles.colorFlow,
                colorFlowStyle,
                {
                  backgroundColor: color,
                  animationDelay: `${index * 200}ms`,
                  top: 30 + index * 15,
                },
              ]}
            />
          ))}
        </View>
      )}

      {/* Main animation container */}
      <View style={styles.animationContainer}>
        {/* Analyzing photo - brush stroke */}
        {context === 'analyzing-photo' && (
          <Animated.View style={[styles.brushContainer, brushStrokeStyle]}>
            <View style={styles.brushStroke} />
            <Animated.View style={mixingStyle}>
              <IconComponent size={32} color={contextConfig.colors[0]} />
            </Animated.View>
          </Animated.View>
        )}

        {/* Generating formula - rotating palette */}
        {context === 'generating-formula' && (
          <Animated.View style={[styles.paletteContainer, mixingStyle]}>
            <IconComponent size={40} color={contextConfig.colors[1]} />
            <View style={styles.colorDots}>
              {contextConfig.colors.map((color, index) => (
                <View
                  key={index}
                  style={[
                    styles.colorDot,
                    {
                      backgroundColor: color,
                      animationDelay: `${index * 100}ms`,
                    },
                  ]}
                />
              ))}
            </View>
          </Animated.View>
        )}

        {/* Mixing colors - falling droplets */}
        {context === 'mixing-colors' && (
          <View style={styles.mixingContainer}>
            <IconComponent size={36} color={contextConfig.colors[0]} />
            {[0, 1, 2].map(index => (
              <Animated.View
                key={index}
                style={[
                  styles.droplet,
                  dropletStyle,
                  {
                    left: 20 + index * 15,
                    backgroundColor: contextConfig.colors[index],
                    animationDelay: `${index * 300}ms`,
                  },
                ]}
              />
            ))}
          </View>
        )}

        {/* Processing - sparkles */}
        {context === 'processing' && (
          <Animated.View style={sparkleStyle}>
            <IconComponent size={38} color={contextConfig.colors[1]} />
            <View style={styles.sparkleRing}>
              {[0, 1, 2, 3, 4, 5].map(index => (
                <View
                  key={index}
                  style={[
                    styles.miniSparkle,
                    {
                      transform: [{ rotate: `${index * 60}deg` }, { translateX: 25 }],
                    },
                  ]}
                />
              ))}
            </View>
          </Animated.View>
        )}
      </View>

      {/* Message */}
      <Text style={styles.message}>{message || contextConfig.message}</Text>

      {/* Professional touch */}
      <Text style={styles.subtitle}>✨ Tecnología IA avanzada para coloristas profesionales</Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    backgroundColor: Colors.light.loadingBackground,
  },

  colorFlowContainer: {
    position: 'absolute',
    top: '40%',
    width: '120%',
    height: 100,
  },

  colorFlow: {
    position: 'absolute',
    width: 60,
    height: 4,
    borderRadius: 2,
  },

  animationContainer: {
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    position: 'relative',
  },

  brushContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  brushStroke: {
    position: 'absolute',
    width: 60,
    height: 3,
    backgroundColor: Colors.light.brushPurple,
    borderRadius: 2,
    marginBottom: 10,
  },

  paletteContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },

  colorDots: {
    position: 'absolute',
    width: 60,
    height: 60,
  },

  colorDot: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
  },

  mixingContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },

  droplet: {
    position: 'absolute',
    width: 6,
    height: 8,
    borderRadius: 3,
    top: -20,
  },

  sparkleRing: {
    position: 'absolute',
    width: 60,
    height: 60,
  },

  miniSparkle: {
    position: 'absolute',
    width: 4,
    height: 4,
    backgroundColor: Colors.light.sparkleYellow,
    borderRadius: 2,
  },

  message: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    color: Colors.light.text,
    marginBottom: 8,
  },

  subtitle: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default ColorationLoadingState;
