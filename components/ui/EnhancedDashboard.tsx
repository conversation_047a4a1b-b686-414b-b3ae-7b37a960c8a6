/**
 * Enhanced Dashboard Component
 * Rediseño del dashboard principal con micro-interacciones profesionales
 * y mejor jerarquía visual optimizada para thumb zone navigation
 */

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Dimensions, Pressable, Platform } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withSequence,
  FadeIn,
  FadeInDown,
  SlideInRight,
  Layout,
  interpolate,
  Extrapolation,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Zap,
  Users,
  Package,
  BarChart3,
  Sparkles,
  TrendingUp,
  Shield,
  Award,
  Calendar,
  Clock,
  Target,
  ChevronRight,
} from 'lucide-react-native';
import * as Haptics from 'expo-haptics';

import DesignSystem from '@/constants/DesignSystem';
import { SkeletonLoader } from './EnhancedLoadingStates';
import { shadows, radius, typography } from '@/constants/theme';
import { commonStyles } from '@/styles/commonStyles';

const { colors, typography, spacing, radius, shadows, responsive: _responsive } = DesignSystem;
const { width: _SCREEN_WIDTH, height: _SCREEN_HEIGHT } = Dimensions.get('window');

interface DashboardMetrics {
  servicesToday: number;
  totalClients: number;
  averageSatisfaction: number;
  monthlyRevenue: number;
  pendingServices: number;
  isLoading: boolean;
  // New fields for real data
  newClientsThisWeek?: number;
  aiEfficiency?: number;
  servicesTodayChange?: number;
}

interface EnhancedDashboardProps {
  metrics: DashboardMetrics;
  onNewService: () => void;
  onNavigateToClients: () => void;
  onNavigateToInventory: () => void;
  onNavigateToReports: () => void;
  userName?: string;
  salonName?: string;
}

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

export const EnhancedDashboard: React.FC<EnhancedDashboardProps> = ({
  metrics,
  onNewService,
  onNavigateToClients,
  onNavigateToInventory,
  onNavigateToReports,
  userName = 'Profesional',
  salonName = 'Salonier',
}) => {
  const [currentTime, setCurrentTime] = useState(new Date());

  // Animation values
  const _headerScale = useSharedValue(1);
  const heroButtonScale = useSharedValue(1);
  const scrollY = useSharedValue(0);

  useEffect(() => {
    // Update time every minute
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  // Animated header that scales based on scroll
  const headerAnimatedStyle = useAnimatedStyle(() => {
    const scale = interpolate(scrollY.value, [0, 100], [1, 0.9], Extrapolation.CLAMP);

    const opacity = interpolate(scrollY.value, [0, 150], [1, 0.8], Extrapolation.CLAMP);

    return {
      transform: [{ scale }],
      opacity,
    };
  });

  const handleScroll = (event: { nativeEvent: { contentOffset: { y: number } } }) => {
    scrollY.value = event.nativeEvent.contentOffset.y;
  };

  const handleHeroButtonPress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);

    heroButtonScale.value = withSequence(withSpring(0.95), withSpring(1.05), withSpring(1));

    setTimeout(onNewService, 100);
  };

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return '¡Buenos días';
    if (hour < 18) return '¡Buenas tardes';
    return '¡Buenas noches';
  };

  const heroButtonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: heroButtonScale.value }],
  }));

  return (
    <ScrollView
      style={styles.container}
      showsVerticalScrollIndicator={false}
      onScroll={handleScroll}
      scrollEventThrottle={16}
    >
      {/* Animated Header */}
      <Animated.View style={[styles.header, headerAnimatedStyle]}>
        <LinearGradient
          colors={[colors.primary + '10', 'transparent']}
          style={styles.headerGradient}
        >
          <Animated.View entering={FadeIn.delay(100)} style={styles.headerContent}>
            <View style={styles.welcomeSection}>
              <Text style={styles.greeting}>
                {getGreeting()}, {userName}! 👋
              </Text>
              <Text style={styles.salonName}>{salonName}</Text>
              <Text style={styles.dateTime}>
                {currentTime.toLocaleDateString('es-ES', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                })}
              </Text>
            </View>

            {/* Quick Stats Bar */}
            <Animated.View entering={SlideInRight.delay(200)} style={styles.quickStatsBar}>
              <QuickStat
                icon={Zap}
                value={metrics.servicesToday}
                label="Hoy"
                color={colors.primary}
                isLoading={metrics.isLoading}
              />
              <QuickStat
                icon={Clock}
                value={metrics.pendingServices}
                label="Pendientes"
                color={colors.warning}
                isLoading={metrics.isLoading}
              />
              <QuickStat
                icon={TrendingUp}
                value={`${Math.round(metrics.averageSatisfaction * 20)}%`}
                label="Satisfacción"
                color={colors.success}
                isLoading={metrics.isLoading}
              />
            </Animated.View>
          </Animated.View>
        </LinearGradient>
      </Animated.View>

      {/* Hero Action */}
      <Animated.View entering={FadeInDown.delay(300)} style={styles.heroSection}>
        <AnimatedPressable
          style={[styles.heroButton, heroButtonAnimatedStyle]}
          onPress={handleHeroButtonPress}
        >
          <LinearGradient
            colors={[colors.primary, colors.primaryDark]}
            style={styles.heroButtonGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <View style={styles.heroButtonContent}>
              <View style={styles.heroIcon}>
                <Sparkles size={32} color={colors.text.inverse} />
              </View>

              <View style={styles.heroText}>
                <Text style={styles.heroTitle}>Nuevo Servicio</Text>
                <Text style={styles.heroSubtitle}>Diagnóstico IA + Fórmula personalizada</Text>
              </View>

              <View style={styles.heroArrow}>
                <ChevronRight size={24} color={colors.text.inverse} />
              </View>
            </View>
          </LinearGradient>
        </AnimatedPressable>
      </Animated.View>

      {/* Quick Actions Grid */}
      <Animated.View
        entering={FadeIn.delay(400)}
        layout={Layout.springify()}
        style={styles.quickActionsSection}
      >
        <Text style={styles.sectionTitle}>Acciones Rápidas</Text>

        <View style={styles.actionsGrid}>
          <ActionCard
            icon={Users}
            title="Clientes"
            subtitle={`${metrics.totalClients} registrados`}
            onPress={onNavigateToClients}
            delay={0}
            color={colors.secondary}
          />

          <ActionCard
            icon={Package}
            title="Inventario"
            subtitle="Gestionar productos"
            onPress={onNavigateToInventory}
            delay={100}
            color={colors.info}
          />

          <ActionCard
            icon={BarChart3}
            title="Reportes"
            subtitle="Análisis y métricas"
            onPress={onNavigateToReports}
            delay={200}
            color={colors.success}
          />

          <ActionCard
            icon={Calendar}
            title="Agenda"
            subtitle="Próximas citas"
            onPress={() => {}} // TODO: Navigate to calendar
            delay={300}
            color={colors.warning}
          />
        </View>
      </Animated.View>

      {/* Metrics Dashboard */}
      <Animated.View entering={FadeIn.delay(500)} style={styles.metricsSection}>
        <Text style={styles.sectionTitle}>Resumen del Día</Text>

        <View style={styles.metricsGrid}>
          <MetricCard
            title="Servicios Completados"
            value={metrics.servicesToday}
            subtitle={`vs ayer ${metrics.servicesTodayChange >= 0 ? '+' : ''}${metrics.servicesTodayChange || 0}`}
            icon={Zap}
            color={colors.primary}
            trend="up"
            isLoading={metrics.isLoading}
          />

          <MetricCard
            title="Nuevos Clientes"
            value={metrics.newClientsThisWeek || 0}
            subtitle="esta semana"
            icon={Users}
            color={colors.secondary}
            trend="up"
            isLoading={metrics.isLoading}
          />

          <MetricCard
            title="Satisfacción Promedio"
            value={`${Math.round(metrics.averageSatisfaction * 20)}%`}
            subtitle="últimos 30 días"
            icon={Award}
            color={colors.success}
            trend="stable"
            isLoading={metrics.isLoading}
          />

          <MetricCard
            title="Eficiencia IA"
            value={`${Math.round(metrics.aiEfficiency || 95)}%`}
            subtitle="precisión diagnósticos"
            icon={Target}
            color={colors.info}
            trend="up"
            isLoading={metrics.isLoading}
          />
        </View>
      </Animated.View>

      {/* Professional Features */}
      <Animated.View entering={FadeIn.delay(600)} style={styles.featuresSection}>
        <Text style={styles.sectionTitle}>Características Premium</Text>

        <FeatureHighlight
          icon={Shield}
          title="Privacidad Total"
          description="Análisis local. Las imágenes nunca salen de tu dispositivo."
          accent={colors.success}
        />

        <FeatureHighlight
          icon={Sparkles}
          title="IA Generativa 100%"
          description="Cada fórmula es única, creada específicamente para tu cliente."
          accent={colors.primary}
        />

        <FeatureHighlight
          icon={TrendingUp}
          title="Mejora Continua"
          description="El sistema aprende y se perfecciona con cada actualización."
          accent={colors.info}
        />
      </Animated.View>
    </ScrollView>
  );
};

// Quick Stat Component
const QuickStat: React.FC<{
  icon: React.ComponentType<{ size: number; color: string }>;
  value: number | string;
  label: string;
  color: string;
  isLoading: boolean;
}> = ({ icon: Icon, value, label, color, isLoading }) => (
  <View style={styles.quickStat}>
    <View style={[styles.quickStatIcon, { backgroundColor: color + '15' }]}>
      <Icon size={16} color={color} />
    </View>
    {isLoading ? (
      <SkeletonLoader width={30} height={18} style={commonStyles.marginBottom2} />
    ) : (
      <Text style={[styles.quickStatValue, { color }]}>{value}</Text>
    )}
    <Text style={styles.quickStatLabel}>{label}</Text>
  </View>
);

// Action Card Component
const ActionCard: React.FC<{
  icon: React.ComponentType<{ size: number; color: string }>;
  title: string;
  subtitle: string;
  onPress: () => void;
  delay: number;
  color: string;
}> = ({ icon: Icon, title, subtitle, onPress, delay, color }) => {
  const scale = useSharedValue(1);

  const handlePress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    scale.value = withSequence(withSpring(0.95), withSpring(1));

    setTimeout(onPress, 50);
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  return (
    <Animated.View entering={FadeIn.delay(delay)} style={[styles.actionCardWrapper]}>
      <AnimatedPressable style={[styles.actionCard, animatedStyle]} onPress={handlePress}>
        <View style={[styles.actionIcon, { backgroundColor: color + '15' }]}>
          <Icon size={24} color={color} />
        </View>
        <Text style={styles.actionTitle}>{title}</Text>
        <Text style={styles.actionSubtitle}>{subtitle}</Text>
      </AnimatedPressable>
    </Animated.View>
  );
};

// Metric Card Component
const MetricCard: React.FC<{
  title: string;
  value: string | number;
  subtitle: string;
  icon: React.ComponentType<{ size: number; color: string }>;
  color: string;
  trend: 'up' | 'down' | 'stable';
  isLoading: boolean;
}> = ({ title, value, subtitle, icon: Icon, color, trend, isLoading }) => (
  <Animated.View
    entering={FadeInDown.delay(Math.random() * 300)}
    layout={Layout.springify()}
    style={styles.metricCard}
  >
    <View style={styles.metricHeader}>
      <View style={[styles.metricIcon, { backgroundColor: color + '15' }]}>
        <Icon size={20} color={color} />
      </View>
      <TrendIndicator trend={trend} />
    </View>

    {isLoading ? (
      <SkeletonLoader width="60%" height={24} style={commonStyles.marginVertical8} />
    ) : (
      <Text style={styles.metricValue}>{value}</Text>
    )}

    <Text style={styles.metricTitle}>{title}</Text>
    <Text style={styles.metricSubtitle}>{subtitle}</Text>
  </Animated.View>
);

// Trend Indicator Component
const TrendIndicator: React.FC<{ trend: 'up' | 'down' | 'stable' }> = ({ trend }) => {
  const getColor = () => {
    switch (trend) {
      case 'up':
        return colors.success;
      case 'down':
        return colors.error;
      case 'stable':
        return colors.text.secondary;
    }
  };

  const getIcon = () => {
    switch (trend) {
      case 'up':
        return '↗';
      case 'down':
        return '↘';
      case 'stable':
        return '→';
    }
  };

  return (
    <View style={[styles.trendIndicator, { backgroundColor: getColor() + '15' }]}>
      <Text style={[styles.trendIcon, { color: getColor() }]}>{getIcon()}</Text>
    </View>
  );
};

// Feature Highlight Component
const FeatureHighlight: React.FC<{
  icon: React.ComponentType<{ size: number; color: string }>;
  title: string;
  description: string;
  accent: string;
}> = ({ icon: Icon, title, description, accent }) => (
  <Animated.View
    entering={FadeIn.delay(Math.random() * 200)}
    style={[styles.featureCard, { borderLeftColor: accent }]}
  >
    <View style={[styles.featureIcon, { backgroundColor: accent + '15' }]}>
      <Icon size={24} color={accent} />
    </View>

    <View style={styles.featureContent}>
      <Text style={styles.featureTitle}>{title}</Text>
      <Text style={styles.featureDescription}>{description}</Text>
    </View>
  </Animated.View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },

  header: {
    marginBottom: spacing['4'],
  },

  headerGradient: {
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingBottom: spacing['6'],
  },

  headerContent: {
    paddingHorizontal: spacing['6'],
  },

  welcomeSection: {
    marginBottom: spacing['6'],
  },

  greeting: {
    fontSize: typography.sizes['3xl'],
    fontWeight: typography.weights.extrabold,
    color: colors.text.primary,
    marginBottom: spacing['1'],
  },

  salonName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.medium,
    color: colors.primary,
    marginBottom: spacing['1'],
  },

  dateTime: {
    fontSize: typography.sizes.base,
    color: colors.text.secondary,
    fontWeight: typography.weights.regular,
  },

  quickStatsBar: {
    flexDirection: 'row',
    backgroundColor: colors.card,
    borderRadius: radius.xl,
    padding: spacing['4'],
    justifyContent: 'space-around',
    ...shadows.sm,
  },

  quickStat: {
    alignItems: 'center',
  },

  quickStatIcon: {
    width: 32,
    height: 32,
    borderRadius: radius.full,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing['1'],
  },

  quickStatValue: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    marginBottom: spacing['0.5'],
  },

  quickStatLabel: {
    fontSize: typography.sizes.xs,
    color: colors.text.secondary,
    fontWeight: typography.weights.medium,
  },

  heroSection: {
    paddingHorizontal: spacing['6'],
    marginBottom: spacing['8'],
  },

  heroButton: {
    borderRadius: radius.xl,
    overflow: 'hidden',
    ...shadows.lg,
  },

  heroButtonGradient: {
    flex: 1,
    paddingVertical: spacing['6'],
    paddingHorizontal: spacing['6'],
  },

  heroButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  heroIcon: {
    width: 64,
    height: 64,
    borderRadius: radius.xl,
    backgroundColor: Colors.light.backgroundOpacity20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing['4'],
  },

  heroText: {
    flex: 1,
  },

  heroTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text.inverse,
    marginBottom: spacing['1'],
  },

  heroSubtitle: {
    fontSize: typography.sizes.base,
    color: colors.text.inverse,
    opacity: 0.9,
  },

  heroArrow: {
    marginLeft: spacing['4'],
  },

  quickActionsSection: {
    paddingHorizontal: spacing['6'],
    marginBottom: spacing['8'],
  },

  sectionTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text.primary,
    marginBottom: spacing['4'],
  },

  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -spacing['2'],
  },

  actionCardWrapper: {
    width: '50%',
    paddingHorizontal: spacing['2'],
    marginBottom: spacing['4'],
  },

  actionCard: {
    backgroundColor: colors.card,
    borderRadius: radius.xl,
    padding: spacing['4'],
    alignItems: 'center',
    minHeight: 120,
    justifyContent: 'center',
    ...shadows.sm,
  },

  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: radius.full,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing['3'],
  },

  actionTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: colors.text.primary,
    marginBottom: spacing['1'],
    textAlign: 'center',
  },

  actionSubtitle: {
    fontSize: typography.sizes.sm,
    color: colors.text.secondary,
    textAlign: 'center',
  },

  metricsSection: {
    paddingHorizontal: spacing['6'],
    marginBottom: spacing['8'],
  },

  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -spacing['2'],
  },

  metricCard: {
    width: '50%',
    paddingHorizontal: spacing['2'],
    marginBottom: spacing['4'],
  },

  metricHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing['3'],
  },

  metricIcon: {
    width: 40,
    height: 40,
    borderRadius: radius.full,
    alignItems: 'center',
    justifyContent: 'center',
  },

  trendIndicator: {
    width: 24,
    height: 24,
    borderRadius: radius.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },

  trendIcon: {
    fontSize: 12,
    fontWeight: typography.weights.bold,
  },

  metricValue: {
    fontSize: typography.sizes['2xl'],
    fontWeight: typography.weights.extrabold,
    color: colors.text.primary,
    marginBottom: spacing['1'],
  },

  metricTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: colors.text.primary,
    marginBottom: spacing['0.5'],
  },

  metricSubtitle: {
    fontSize: typography.sizes.xs,
    color: colors.text.secondary,
  },

  featuresSection: {
    paddingHorizontal: spacing['6'],
    paddingBottom: spacing['8'],
  },

  featureCard: {
    flexDirection: 'row',
    backgroundColor: colors.card,
    borderRadius: radius.xl,
    padding: spacing['5'],
    marginBottom: spacing['4'],
    borderLeftWidth: 4,
    alignItems: 'center',
    ...shadows.sm,
  },

  featureIcon: {
    width: 56,
    height: 56,
    borderRadius: radius.full,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing['4'],
  },

  featureContent: {
    flex: 1,
  },

  featureTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: colors.text.primary,
    marginBottom: spacing['1'],
  },

  featureDescription: {
    fontSize: typography.sizes.sm,
    color: colors.text.secondary,
    lineHeight: typography.sizes.sm * typography.lineHeight.relaxed,
  },
});
