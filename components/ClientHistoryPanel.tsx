import React, { useState } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView } from 'react-native';
import {
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  Star,
  Shield,
  Eye,
  EyeOff,
} from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useClientHistoryStore } from '@/stores/client-history-store';

interface ClientHistoryPanelProps {
  clientId: string;
  onRecommendationApply?: (recommendation: string) => void;
}

export default function ClientHistoryPanel({
  clientId,
  onRecommendationApply,
}: ClientHistoryPanelProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'history' | 'warnings'>('overview');

  const {
    getClientProfile,
    getRecommendationsForClient,
    getWarningsForClient,
    getCompatibleFormulas,
    initializeClientProfile,
  } = useClientHistoryStore();

  // Initialize profile if it doesn't exist
  React.useEffect(() => {
    initializeClientProfile(clientId);
  }, [clientId, initializeClientProfile]);

  const profile = getClientProfile(clientId);
  const recommendations = getRecommendationsForClient(clientId);
  const warnings = getWarningsForClient(clientId);
  const compatibleFormulas = getCompatibleFormulas(clientId);

  if (!profile) {
    return null;
  }

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'alto':
        return Colors.light.error;
      case 'medio':
        return Colors.light.warning;
      default:
        return Colors.light.success;
    }
  };

  const getRiskIcon = (risk: string) => {
    switch (risk) {
      case 'alto':
        return <AlertTriangle size={16} color={Colors.light.error} />;
      case 'medio':
        return <Clock size={16} color={Colors.light.warning} />;
      default:
        return <CheckCircle size={16} color={Colors.light.success} />;
    }
  };

  const renderOverview = () => (
    <View style={styles.tabContent}>
      {/* Real-time data indicator */}
      <View style={styles.realTimeIndicator}>
        <CheckCircle size={12} color={Colors.light.success} />
        <Text style={styles.realTimeText}>Datos en tiempo real</Text>
      </View>

      {profile.totalServices === 0 && (
        <View style={styles.firstServiceBanner}>
          <Text style={styles.firstServiceTitle}>Primer servicio del cliente</Text>
          <Text style={styles.firstServiceSubtext}>
            Se comenzará a registrar el historial a partir de este servicio
          </Text>
        </View>
      )}

      <View style={styles.statsGrid}>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>
            {profile.totalServices === 0 ? '-' : profile.totalServices}
          </Text>
          <Text style={styles.statLabel}>
            {profile.totalServices === 0 ? 'Sin servicios previos' : 'Servicios'}
          </Text>
        </View>
        <View style={styles.statCard}>
          <View style={styles.statRow}>
            <Star size={14} color={Colors.light.warning} />
            <Text style={styles.statNumber}>
              {profile.averageSatisfaction ? profile.averageSatisfaction.toFixed(1) : '-'}
            </Text>
          </View>
          <Text style={styles.statLabel}>
            {profile.averageSatisfaction ? 'Satisfacción' : 'Sin datos'}
          </Text>
        </View>
        <View style={styles.statCard}>
          <View style={styles.statRow}>
            {getRiskIcon(profile.riskLevel)}
            <Text style={[styles.statNumber, { color: getRiskColor(profile.riskLevel) }]}>
              {profile.riskLevel.toUpperCase()}
            </Text>
          </View>
          <Text style={styles.statLabel}>Riesgo</Text>
        </View>
      </View>

      {recommendations.length > 0 ? (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recomendaciones IA</Text>
          {recommendations.slice(0, 3).map((rec, index) => (
            <TouchableOpacity
              key={index}
              style={styles.recommendationItem}
              onPress={() => onRecommendationApply?.(rec)}
            >
              <TrendingUp size={16} color={Colors.light.accent} />
              <Text style={styles.recommendationText}>{rec}</Text>
            </TouchableOpacity>
          ))}
        </View>
      ) : (
        profile.totalServices === 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Recomendaciones IA</Text>
            <View style={styles.emptyState}>
              <TrendingUp size={24} color={Colors.light.gray} />
              <Text style={styles.emptyStateText}>
                Las recomendaciones aparecerán después del primer servicio
              </Text>
            </View>
          </View>
        )
      )}

      {compatibleFormulas.length > 0 ? (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Fórmulas Exitosas</Text>
          {compatibleFormulas.slice(0, 2).map((formula, _index) => (
            <View key={formula.id} style={styles.formulaItem}>
              <View style={styles.formulaHeader}>
                <Text style={styles.formulaDate}>{formula.date}</Text>
                <View style={styles.satisfactionBadge}>
                  <Star size={12} color={Colors.light.warning} />
                  <Text style={styles.satisfactionText}>{formula.satisfaction}</Text>
                </View>
              </View>
              <Text style={styles.formulaText} numberOfLines={2}>
                {formula.formula}
              </Text>
              <Text style={styles.formulaBrand}>
                {formula.brand} - {formula.line}
              </Text>
            </View>
          ))}
        </View>
      ) : (
        profile.totalServices === 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Fórmulas Exitosas</Text>
            <View style={styles.emptyState}>
              <Star size={24} color={Colors.light.gray} />
              <Text style={styles.emptyStateText}>Las fórmulas exitosas se mostrarán aquí</Text>
            </View>
          </View>
        )
      )}
    </View>
  );

  const renderHistory = () => (
    <View style={styles.tabContent}>
      {profile.hairEvolution.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Evolución Capilar</Text>
          {profile.hairEvolution.slice(0, 3).map((evolution, index) => (
            <View key={index} style={styles.evolutionItem}>
              <Text style={styles.evolutionDate}>{evolution.date}</Text>
              <View style={styles.evolutionDetails}>
                <Text style={styles.evolutionText}>Nivel: {evolution.level}</Text>
                <Text style={styles.evolutionText}>Porosidad: {evolution.porosity}</Text>
                <Text style={styles.evolutionText}>Daño: {evolution.damage}</Text>
              </View>
              {evolution.notes && <Text style={styles.evolutionNotes}>{evolution.notes}</Text>}
            </View>
          ))}
        </View>
      )}

      {profile.preferences.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Preferencias del Cliente</Text>
          {profile.preferences.map((pref, index) => (
            <View key={index} style={styles.preferenceItem}>
              <Text style={styles.preferenceCategory}>{pref.category}</Text>
              <Text style={styles.preferenceValue}>{pref.value}</Text>
              <View
                style={[
                  styles.priorityBadge,
                  {
                    backgroundColor:
                      pref.priority === 'alta'
                        ? Colors.light.error + '20'
                        : pref.priority === 'media'
                          ? Colors.light.warning + '20'
                          : Colors.light.success + '20',
                  },
                ]}
              >
                <Text
                  style={[
                    styles.priorityText,
                    {
                      color:
                        pref.priority === 'alta'
                          ? Colors.light.error
                          : pref.priority === 'media'
                            ? Colors.light.warning
                            : Colors.light.success,
                    },
                  ]}
                >
                  {pref.priority}
                </Text>
              </View>
            </View>
          ))}
        </View>
      )}
    </View>
  );

  const renderWarnings = () => (
    <View style={styles.tabContent}>
      {warnings.length > 0 ? (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Alertas Activas</Text>
          {warnings.map((warning, index) => (
            <View key={index} style={styles.warningItem}>
              <AlertTriangle size={16} color={Colors.light.error} />
              <Text style={styles.warningText}>{warning}</Text>
            </View>
          ))}
        </View>
      ) : (
        <View style={styles.noWarnings}>
          <CheckCircle size={32} color={Colors.light.success} />
          <Text style={styles.noWarningsText}>No hay alertas activas</Text>
          <Text style={styles.noWarningsSubtext}>Cliente sin restricciones conocidas</Text>
        </View>
      )}

      {profile.allergies.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Alergias Registradas</Text>
          {profile.allergies.map((allergy, index) => (
            <View key={index} style={styles.allergyItem}>
              <View
                style={[
                  styles.severityBadge,
                  {
                    backgroundColor:
                      allergy.severity === 'severa'
                        ? Colors.light.error + '20'
                        : allergy.severity === 'moderada'
                          ? Colors.light.warning + '20'
                          : Colors.light.success + '20',
                  },
                ]}
              >
                <Text
                  style={[
                    styles.severityText,
                    {
                      color:
                        allergy.severity === 'severa'
                          ? Colors.light.error
                          : allergy.severity === 'moderada'
                            ? Colors.light.warning
                            : Colors.light.success,
                    },
                  ]}
                >
                  {allergy.severity}
                </Text>
              </View>
              <View style={styles.allergyInfo}>
                <Text style={styles.allergySubstance}>{allergy.substance}</Text>
                <Text style={styles.allergyDate}>Detectada: {allergy.dateDetected}</Text>
                {allergy.notes && <Text style={styles.allergyNotes}>{allergy.notes}</Text>}
              </View>
            </View>
          ))}
        </View>
      )}

      {profile.patchTests.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tests de Parche</Text>
          {profile.patchTests.slice(0, 3).map((test, _index) => (
            <View key={test.id} style={styles.patchTestItem}>
              <View style={styles.patchTestHeader}>
                <Text style={styles.patchTestDate}>{test.date}</Text>
                <View
                  style={[
                    styles.resultBadge,
                    {
                      backgroundColor:
                        test.result === 'negativo'
                          ? Colors.light.success + '20'
                          : test.result === 'positivo'
                            ? Colors.light.error + '20'
                            : Colors.light.warning + '20',
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.resultText,
                      {
                        color:
                          test.result === 'negativo'
                            ? Colors.light.success
                            : test.result === 'positivo'
                              ? Colors.light.error
                              : Colors.light.warning,
                      },
                    ]}
                  >
                    {test.result}
                  </Text>
                </View>
              </View>
              <Text style={styles.patchTestProducts}>Productos: {test.products.join(', ')}</Text>
            </View>
          ))}
        </View>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.header} onPress={() => setIsExpanded(!isExpanded)}>
        <View style={styles.headerLeft}>
          <Shield size={20} color={Colors.light.primary} />
          <Text style={styles.headerTitle}>Historial del Cliente</Text>
          {warnings.length > 0 && (
            <View style={styles.warningBadge}>
              <Text style={styles.warningBadgeText}>{warnings.length}</Text>
            </View>
          )}
        </View>
        {isExpanded ? (
          <EyeOff size={20} color={Colors.light.gray} />
        ) : (
          <Eye size={20} color={Colors.light.gray} />
        )}
      </TouchableOpacity>

      {isExpanded && (
        <View style={styles.expandedContent}>
          <View style={styles.tabs}>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'overview' && styles.activeTab]}
              onPress={() => setActiveTab('overview')}
            >
              <Text style={[styles.tabText, activeTab === 'overview' && styles.activeTabText]}>
                Resumen
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'history' && styles.activeTab]}
              onPress={() => setActiveTab('history')}
            >
              <Text style={[styles.tabText, activeTab === 'history' && styles.activeTabText]}>
                Historial
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'warnings' && styles.activeTab]}
              onPress={() => setActiveTab('warnings')}
            >
              <Text style={[styles.tabText, activeTab === 'warnings' && styles.activeTabText]}>
                Alertas
              </Text>
              {warnings.length > 0 && (
                <View style={styles.tabBadge}>
                  <Text style={styles.tabBadgeText}>{warnings.length}</Text>
                </View>
              )}
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.scrollContent} showsVerticalScrollIndicator={false}>
            {activeTab === 'overview' && renderOverview()}
            {activeTab === 'history' && renderHistory()}
            {activeTab === 'warnings' && renderWarnings()}
          </ScrollView>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    marginBottom: 20,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  warningBadge: {
    backgroundColor: Colors.light.error,
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  warningBadgeText: {
    color: Colors.light.textLight,
    fontSize: 12,
    fontWeight: '600',
  },
  expandedContent: {
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  tabs: {
    flexDirection: 'row',
    backgroundColor: Colors.light.surface,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 4,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: Colors.light.primary,
    backgroundColor: Colors.light.background,
  },
  tabText: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  activeTabText: {
    color: Colors.light.primary,
    fontWeight: '600',
  },
  tabBadge: {
    backgroundColor: Colors.light.error,
    borderRadius: 8,
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabBadgeText: {
    color: Colors.light.textLight,
    fontSize: 10,
    fontWeight: '600',
  },
  scrollContent: {
    maxHeight: 300,
  },
  tabContent: {
    padding: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  statCard: {
    flex: 1,
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  statRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statNumber: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.text,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 4,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  recommendationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.accent + '10',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    gap: 8,
  },
  recommendationText: {
    flex: 1,
    fontSize: 13,
    color: Colors.light.text,
    lineHeight: 18,
  },
  formulaItem: {
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  formulaHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  formulaDate: {
    fontSize: 12,
    color: Colors.light.gray,
  },
  satisfactionBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  satisfactionText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.warning,
  },
  formulaText: {
    fontSize: 13,
    color: Colors.light.text,
    marginBottom: 4,
  },
  formulaBrand: {
    fontSize: 12,
    color: Colors.light.primary,
    fontWeight: '500',
  },
  evolutionItem: {
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  evolutionDate: {
    fontSize: 12,
    color: Colors.light.gray,
    marginBottom: 4,
  },
  evolutionDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 4,
  },
  evolutionText: {
    fontSize: 12,
    color: Colors.light.text,
  },
  evolutionNotes: {
    fontSize: 12,
    color: Colors.light.gray,
    fontStyle: 'italic',
  },
  preferenceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    gap: 8,
  },
  preferenceCategory: {
    fontSize: 12,
    color: Colors.light.gray,
    textTransform: 'uppercase',
    fontWeight: '600',
    minWidth: 60,
  },
  preferenceValue: {
    flex: 1,
    fontSize: 13,
    color: Colors.light.text,
  },
  priorityBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  noWarnings: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  noWarningsText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.success,
    marginTop: 8,
  },
  noWarningsSubtext: {
    fontSize: 14,
    color: Colors.light.gray,
    marginTop: 4,
  },
  warningItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.error + '10',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    gap: 8,
  },
  warningText: {
    flex: 1,
    fontSize: 13,
    color: Colors.light.error,
    fontWeight: '500',
  },
  allergyItem: {
    flexDirection: 'row',
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    gap: 12,
  },
  severityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    alignSelf: 'flex-start',
  },
  severityText: {
    fontSize: 11,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  allergyInfo: {
    flex: 1,
  },
  allergySubstance: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 2,
  },
  allergyDate: {
    fontSize: 12,
    color: Colors.light.gray,
    marginBottom: 2,
  },
  allergyNotes: {
    fontSize: 12,
    color: Colors.light.gray,
    fontStyle: 'italic',
  },
  patchTestItem: {
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  patchTestHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  patchTestDate: {
    fontSize: 12,
    color: Colors.light.gray,
  },
  resultBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  resultText: {
    fontSize: 11,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  patchTestProducts: {
    fontSize: 12,
    color: Colors.light.text,
  },
  realTimeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: Colors.light.success + '10',
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  realTimeText: {
    fontSize: 11,
    color: Colors.light.success,
    fontWeight: '600',
  },
  firstServiceBanner: {
    backgroundColor: Colors.light.primary + '10',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  firstServiceTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.primary,
    marginBottom: 4,
  },
  firstServiceSubtext: {
    fontSize: 12,
    color: Colors.light.gray,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 20,
    gap: 8,
  },
  emptyStateText: {
    fontSize: 13,
    color: Colors.light.gray,
    textAlign: 'center',
  },
});
