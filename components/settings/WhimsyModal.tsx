/**
 * Whimsy Settings Modal - Configuración de micro-interacciones y detalles delightful
 * Permite a los usuarios personalizar su experiencia de animaciones y efectos
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  Switch,
  ScrollView,
  StyleSheet,
  Alert,
  Dimensions,
} from 'react-native';
import { X, Sparkles, Volume2, Settings, RotateCcw, Zap } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withSequence,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import Colors from '@/constants/colors';
import { typography, spacing, radius } from '@/constants/theme';
import { useWhimsyStore, useAchievements } from '@/stores/whimsy-store';
import { EnhancedButton } from '@/components/ui/EnhancedButton';

interface WhimsyModalProps {
  visible: boolean;
  onClose: () => void;
}

const { width: _SCREEN_WIDTH } = Dimensions.get('window');

export const WhimsyModal: React.FC<WhimsyModalProps> = ({ visible, onClose }) => {
  const whimsyStore = useWhimsyStore();
  const achievements = useAchievements();
  const [_activeDemo, _setActiveDemo] = useState<string | null>(null);

  // Animation values for demos
  const heartScale = useSharedValue(0);
  const sparkleRotation = useSharedValue(0);
  const rippleScale = useSharedValue(0);
  const rippleOpacity = useSharedValue(0);

  const demoHeart = () => {
    if (!whimsyStore.enableCelebrationAnimations) return;

    heartScale.value = withSequence(
      withSpring(1.5, { damping: 8 }),
      withSpring(0, { damping: 12 })
    );

    if (whimsyStore.enableHapticFeedback) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
  };

  const demoSparkle = () => {
    sparkleRotation.value = withSpring(sparkleRotation.value + 180, {
      damping: 10,
    });
    if (whimsyStore.enableHapticFeedback) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const demoRipple = () => {
    if (!whimsyStore.enableRippleEffects) return;

    rippleScale.value = 0;
    rippleOpacity.value = 0.6;
    rippleScale.value = withSpring(2, { damping: 15 });
    rippleOpacity.value = withSpring(0, { damping: 20 });
  };

  const resetToDefaults = () => {
    Alert.alert(
      'Restaurar configuración',
      '¿Quieres restaurar todas las configuraciones de micro-interacciones a sus valores por defecto?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Restaurar',
          style: 'destructive',
          onPress: () => {
            whimsyStore.resetToDefaults();
            if (whimsyStore.enableHapticFeedback) {
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            }
          },
        },
      ]
    );
  };

  const heartStyle = useAnimatedStyle(() => ({
    transform: [{ scale: heartScale.value }],
    opacity: heartScale.value,
  }));

  const sparkleStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${sparkleRotation.value}deg` }],
  }));

  const rippleStyle = useAnimatedStyle(() => ({
    transform: [{ scale: rippleScale.value }],
    opacity: rippleOpacity.value,
  }));

  const SettingRow: React.FC<{
    title: string;
    description: string;
    value: boolean;
    onValueChange: (value: boolean) => void;
    icon?: React.ReactNode;
    demoAction?: () => void;
  }> = ({ title, description, value, onValueChange, icon, demoAction }) => (
    <View style={styles.settingRow}>
      <View style={styles.settingInfo}>
        {icon && <View style={styles.settingIcon}>{icon}</View>}
        <View style={styles.settingText}>
          <Text style={styles.settingTitle}>{title}</Text>
          <Text style={styles.settingDescription}>{description}</Text>
        </View>
      </View>
      <View style={styles.settingControls}>
        {demoAction && (
          <TouchableOpacity onPress={demoAction} style={styles.demoButton} disabled={!value}>
            <Text style={[styles.demoButtonText, !value && styles.demoButtonTextDisabled]}>
              Demo
            </Text>
          </TouchableOpacity>
        )}
        <Switch
          value={value}
          onValueChange={onValueChange}
          trackColor={{
            false: Colors.light.border,
            true: Colors.light.primary,
          }}
          thumbColor="white"
        />
      </View>
    </View>
  );

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <LinearGradient colors={['#f8fafc', '#f1f5f9']} style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <X size={24} color={Colors.light.gray} />
          </TouchableOpacity>
          <Text style={styles.title}>✨ Configuración Delightful</Text>
          <TouchableOpacity onPress={resetToDefaults} style={styles.resetButton}>
            <RotateCcw size={20} color={Colors.light.primary} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Animations Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>🎬 Animaciones</Text>

            <SettingRow
              title="Breathing Cards"
              description="Respiración sutil en las tarjetas del dashboard"
              value={whimsyStore.enableBreathingAnimations}
              onValueChange={value =>
                whimsyStore.updateSettings({ enableBreathingAnimations: value })
              }
              icon={<Zap size={20} color={Colors.light.primary} />}
            />

            <SettingRow
              title="Efectos Ripple"
              description="Ondas al presionar botones"
              value={whimsyStore.enableRippleEffects}
              onValueChange={value => whimsyStore.updateSettings({ enableRippleEffects: value })}
              icon={
                <View style={styles.rippleDemo}>
                  <Animated.View style={[styles.demoRipple, rippleStyle]} />
                </View>
              }
              demoAction={demoRipple}
            />

            <SettingRow
              title="Celebraciones"
              description="Animaciones de éxito y logros"
              value={whimsyStore.enableCelebrationAnimations}
              onValueChange={value =>
                whimsyStore.updateSettings({
                  enableCelebrationAnimations: value,
                })
              }
              icon={
                <View style={styles.heartDemo}>
                  <Animated.Text style={[styles.demoHeart, heartStyle]}>❤️</Animated.Text>
                </View>
              }
              demoAction={demoHeart}
            />
          </View>

          {/* Feedback Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>📳 Feedback Táctil</Text>

            <SettingRow
              title="Haptic Feedback"
              description="Vibración sutil en interacciones"
              value={whimsyStore.enableHapticFeedback}
              onValueChange={value => whimsyStore.updateSettings({ enableHapticFeedback: value })}
              icon={<Settings size={20} color={Colors.light.secondary} />}
            />
          </View>

          {/* Loading Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>⚡ Estados de Carga</Text>

            <SettingRow
              title="Loading Contextual"
              description="Animaciones específicas por contexto"
              value={whimsyStore.enableContextualLoadingStates}
              onValueChange={value =>
                whimsyStore.updateSettings({
                  enableContextualLoadingStates: value,
                })
              }
            />

            <SettingRow
              title="Mensajes Técnicos"
              description="Mostrar pasos detallados del proceso"
              value={whimsyStore.showLoadingMessages}
              onValueChange={value => whimsyStore.updateSettings({ showLoadingMessages: value })}
            />
          </View>

          {/* Easter Eggs Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>🥚 Easter Eggs</Text>

            <SettingRow
              title="Secretos Ocultos"
              description="Sorpresas para usuarios curiosos"
              value={whimsyStore.enableEasterEggs}
              onValueChange={value => whimsyStore.updateSettings({ enableEasterEggs: value })}
              icon={
                <Animated.View style={sparkleStyle}>
                  <Sparkles size={20} color={Colors.light.warning} />
                </Animated.View>
              }
              demoAction={demoSparkle}
            />
          </View>

          {/* Future Features */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>🔮 Próximamente</Text>

            <View style={[styles.settingRow, styles.disabledRow]}>
              <View style={styles.settingInfo}>
                <View style={styles.settingIcon}>
                  <Volume2 size={20} color={Colors.light.textSecondary} />
                </View>
                <View style={styles.settingText}>
                  <Text style={[styles.settingTitle, styles.disabledText]}>Efectos de Sonido</Text>
                  <Text style={[styles.settingDescription, styles.disabledText]}>
                    Audio sutil para acciones importantes
                  </Text>
                </View>
              </View>
              <View style={styles.settingControls}>
                <Text style={styles.comingSoonText}>Próximamente</Text>
              </View>
            </View>
          </View>

          {/* Achievements Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>🏆 Logros Desbloqueados</Text>
            <View style={styles.achievementsContainer}>
              <View style={styles.achievementStats}>
                <View style={styles.stat}>
                  <Text style={styles.statNumber}>{achievements.progress.services}</Text>
                  <Text style={styles.statLabel}>Servicios</Text>
                </View>
                <View style={styles.stat}>
                  <Text style={styles.statNumber}>{achievements.progress.perfectRatings}</Text>
                  <Text style={styles.statLabel}>5 Estrellas</Text>
                </View>
                <View style={styles.stat}>
                  <Text style={styles.statNumber}>{achievements.progress.easterEggs}</Text>
                  <Text style={styles.statLabel}>Secretos</Text>
                </View>
              </View>
              <Text style={styles.achievementProgress}>
                {achievements.unlocked.length} de {achievements.available.length} logros
                desbloqueados
              </Text>
            </View>
          </View>

          {/* Performance Note */}
          <View style={styles.performanceNote}>
            <Text style={styles.performanceText}>
              💡 Tip: Las animaciones se optimizan automáticamente según el rendimiento de tu
              dispositivo
            </Text>
          </View>
        </ScrollView>

        {/* Footer */}
        <View style={styles.footer}>
          <EnhancedButton
            title="¡Perfecto!"
            variant="primary"
            onPress={onClose}
            icon={Sparkles}
            hapticFeedback="medium"
            fullWidth
          />
        </View>
      </LinearGradient>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    paddingTop: 50,
    backgroundColor: Colors.light.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.borderGray,
  },
  closeButton: {
    padding: spacing.xs,
  },
  resetButton: {
    padding: spacing.xs,
  },
  title: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  section: {
    marginVertical: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.md,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.light.background,
    padding: spacing.md,
    borderRadius: radius.lg,
    marginBottom: spacing.sm,
    shadowColor: Colors.common.shadowColor,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
  },
  disabledRow: {
    opacity: 0.6,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    marginRight: spacing.md,
    width: 24,
    alignItems: 'center',
    position: 'relative',
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  disabledText: {
    color: Colors.light.textSecondary,
  },
  settingControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  demoButton: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    backgroundColor: Colors.light.primary + '15',
    borderRadius: radius.sm,
  },
  demoButtonText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium,
    color: Colors.light.primary,
  },
  demoButtonTextDisabled: {
    color: Colors.light.textSecondary,
  },
  comingSoonText: {
    fontSize: typography.sizes.xs,
    fontStyle: 'italic',
    color: Colors.light.textSecondary,
  },

  // Demo animations
  rippleDemo: {
    width: 20,
    height: 20,
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  demoRipple: {
    position: 'absolute',
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: Colors.light.primary + '40',
  },
  heartDemo: {
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  demoHeart: {
    fontSize: 16,
  },

  // Achievements
  achievementsContainer: {
    backgroundColor: Colors.light.background,
    padding: spacing.md,
    borderRadius: radius.lg,
    shadowColor: Colors.common.shadowColor,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
  },
  achievementStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: spacing.sm,
  },
  stat: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.primary,
  },
  statLabel: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
  },
  achievementProgress: {
    textAlign: 'center',
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    fontStyle: 'italic',
  },

  performanceNote: {
    backgroundColor: Colors.light.primary + '10',
    padding: spacing.md,
    borderRadius: radius.lg,
    marginVertical: spacing.lg,
  },
  performanceText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.text,
    textAlign: 'center',
    lineHeight: 20,
  },

  footer: {
    padding: spacing.lg,
    backgroundColor: Colors.light.background,
    borderTopWidth: 1,
    borderTopColor: Colors.light.borderGray,
  },
});

export default WhimsyModal;
