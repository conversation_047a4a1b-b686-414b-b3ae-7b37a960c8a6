import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet } from 'react-native';
import {
  professionalHairColorBrands,
  getColorLinesByBrandId,
  validateBrandLines,
  getBrandLinesStats,
  getBrandsByPopularity,
  getRecommendedBrandsByRegion,
  getBrandTechnicalInfo,
} from '@/constants/reference-data/brands-data';
import Colors from '@/constants/colors';

/**
 * Componente de debug para verificar la clasificación de líneas de marcas
 * Solo para desarrollo - no incluir en producción
 */
export const BrandLinesDebug: React.FC = () => {
  const [selectedBrandId, setSelectedBrandId] = useState<string>('salerm');
  const [showValidation, setShowValidation] = useState(false);

  const validation = validateBrandLines();
  const stats = getBrandLinesStats();
  const selectedBrand = professionalHairColorBrands.find(b => b.id === selectedBrandId);
  const colorLines = selectedBrand ? getColorLinesByBrandId(selectedBrand.id) : [];
  const allLines = selectedBrand ? selectedBrand.lines : [];
  const _brandsByPopularity = getBrandsByPopularity();
  const _spanishBrands = getRecommendedBrandsByRegion('Spain');
  const technicalInfo = selectedBrand ? getBrandTechnicalInfo(selectedBrand.id) : null;

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Debug: Líneas de Coloración</Text>

      {/* Estadísticas generales */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Estadísticas Generales</Text>
        <Text style={styles.stat}>Total de líneas: {stats.total}</Text>
        <Text style={styles.stat}>Líneas de coloración: {stats.colorLines}</Text>
        <Text style={styles.stat}>Líneas de tratamiento: {stats.treatmentLines}</Text>
        <Text style={styles.stat}>Líneas de styling: {stats.stylingLines}</Text>
        <Text style={styles.stat}>Líneas de decoloración: {stats.bleachingLines}</Text>
        <Text style={styles.stat}>Líneas de oxidante: {stats.developerLines}</Text>
        <Text style={styles.stat}>Otras líneas: {stats.otherLines}</Text>
      </View>

      {/* Validación */}
      <View style={styles.section}>
        <TouchableOpacity style={styles.button} onPress={() => setShowValidation(!showValidation)}>
          <Text style={styles.buttonText}>{showValidation ? 'Ocultar' : 'Mostrar'} Validación</Text>
        </TouchableOpacity>

        {showValidation && (
          <View style={styles.validation}>
            <Text
              style={[styles.validationStatus, validation.valid ? styles.valid : styles.invalid]}
            >
              {validation.valid
                ? '✅ Todas las líneas están correctamente configuradas'
                : '❌ Hay problemas en la configuración'}
            </Text>
            {validation.issues.map((issue, index) => (
              <Text key={index} style={styles.issue}>
                {issue}
              </Text>
            ))}
          </View>
        )}
      </View>

      {/* Selector de marca */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Seleccionar Marca para Análisis</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {professionalHairColorBrands.slice(0, 10).map(brand => (
            <TouchableOpacity
              key={brand.id}
              style={[
                styles.brandButton,
                selectedBrandId === brand.id && styles.brandButtonSelected,
              ]}
              onPress={() => setSelectedBrandId(brand.id)}
            >
              <Text
                style={[
                  styles.brandButtonText,
                  selectedBrandId === brand.id && styles.brandButtonTextSelected,
                ]}
              >
                {brand.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Análisis de marca seleccionada */}
      {selectedBrand && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{selectedBrand.name}</Text>
          <Text style={styles.brandInfo}>País: {selectedBrand.country}</Text>
          <Text style={styles.brandInfo}>Total de líneas: {allLines.length}</Text>
          <Text style={styles.brandInfo}>Líneas de coloración: {colorLines.length}</Text>

          {technicalInfo && (
            <View style={styles.technicalInfo}>
              <Text style={styles.subsectionTitle}>Información Técnica:</Text>
              <Text style={styles.techDetail}>
                Sistema de numeración: {technicalInfo.numberingSystem}
              </Text>
              <Text style={styles.techDetail}>
                Volumen máximo oxidante: {technicalInfo.maxDeveloperVolume} vol
              </Text>
              <Text style={styles.techDetail}>Características especiales:</Text>
              {technicalInfo.specialFeatures.map((feature, index) => (
                <Text key={index} style={styles.techFeature}>
                  • {feature}
                </Text>
              ))}
              {technicalInfo.compatibleBrands && (
                <>
                  <Text style={styles.techDetail}>Marcas compatibles:</Text>
                  {technicalInfo.compatibleBrands.map((brandId, index) => (
                    <Text key={index} style={styles.techFeature}>
                      • {brandId}
                    </Text>
                  ))}
                </>
              )}
            </View>
          )}

          <Text style={styles.subsectionTitle}>Todas las líneas:</Text>
          {allLines.map(line => (
            <View
              key={line.id}
              style={[styles.lineItem, line.isColorLine ? styles.colorLine : styles.nonColorLine]}
            >
              <Text style={styles.lineName}>{line.name}</Text>
              <Text style={styles.lineCategory}>
                {line.category || 'Sin categoría'} |{' '}
                {line.isColorLine ? 'Coloración' : 'No coloración'}
              </Text>
              {line.description && <Text style={styles.lineDescription}>{line.description}</Text>}
            </View>
          ))}

          <Text style={styles.subsectionTitle}>Solo líneas de coloración (filtradas):</Text>
          {colorLines.length > 0 ? (
            colorLines.map(line => (
              <View key={line.id} style={styles.colorLineItem}>
                <Text style={styles.lineName}>{line.name}</Text>
                <Text style={styles.lineCategory}>{line.category}</Text>
                {line.description && <Text style={styles.lineDescription}>{line.description}</Text>}
              </View>
            ))
          ) : (
            <Text style={styles.noColorLines}>
              ⚠️ Esta marca no tiene líneas de coloración configuradas
            </Text>
          )}
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.light.text,
    marginBottom: 20,
    textAlign: 'center',
  },
  section: {
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 12,
  },
  subsectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginTop: 16,
    marginBottom: 8,
  },
  stat: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 4,
  },
  button: {
    backgroundColor: Colors.light.primary,
    borderRadius: 6,
    paddingVertical: 8,
    paddingHorizontal: 16,
    alignSelf: 'flex-start',
  },
  buttonText: {
    color: 'white',
    fontWeight: '600',
  },
  validation: {
    marginTop: 12,
  },
  validationStatus: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  valid: {
    color: 'green',
  },
  invalid: {
    color: 'red',
  },
  issue: {
    fontSize: 14,
    color: 'red',
    marginBottom: 4,
  },
  brandButton: {
    backgroundColor: Colors.light.border,
    borderRadius: 6,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 8,
  },
  brandButtonSelected: {
    backgroundColor: Colors.light.primary,
  },
  brandButtonText: {
    fontSize: 14,
    color: Colors.light.text,
  },
  brandButtonTextSelected: {
    color: 'white',
  },
  brandInfo: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 4,
  },
  lineItem: {
    borderRadius: 6,
    padding: 12,
    marginBottom: 8,
    borderWidth: 1,
  },
  colorLine: {
    backgroundColor: '#e8f5e8',
    borderColor: '#4caf50',
  },
  nonColorLine: {
    backgroundColor: '#fff3e0',
    borderColor: '#ff9800',
  },
  colorLineItem: {
    backgroundColor: '#e8f5e8',
    borderRadius: 6,
    padding: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#4caf50',
  },
  lineName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  lineCategory: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginBottom: 4,
  },
  lineDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontStyle: 'italic',
  },
  noColorLines: {
    fontSize: 14,
    color: 'orange',
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 16,
  },
  technicalInfo: {
    backgroundColor: '#f0f8ff',
    borderRadius: 6,
    padding: 12,
    marginTop: 12,
    borderWidth: 1,
    borderColor: '#2196f3',
  },
  techDetail: {
    fontSize: 14,
    color: Colors.light.text,
    marginBottom: 4,
    fontWeight: '500',
  },
  techFeature: {
    fontSize: 13,
    color: Colors.light.textSecondary,
    marginLeft: 8,
    marginBottom: 2,
  },
});

export default BrandLinesDebug;
