import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { CheckCircle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useInventoryStore } from '@/stores/inventory-store';
import { ProductMatcherService } from '@/services/productMatcherService';
import { Product } from '@/types/inventory';

interface ProductNameHelperProps {
  value: string;
  onChangeText: (text: string) => void;
  onSelectSuggestion?: (product: Partial<Product>) => void;
  placeholder?: string;
  brand?: string;
  type?: string;
}

interface Suggestion {
  type: 'existing' | 'standard';
  product?: Product;
  standardName?: string;
  similarity?: number;
}

export const ProductNameHelper: React.FC<ProductNameHelperProps> = ({
  value,
  onChangeText,
  onSelectSuggestion,
  placeholder = 'Ej: Koleston 7/43',
  brand,
  type,
}) => {
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const { products } = useInventoryStore();

  useEffect(() => {
    if (value.length < 3) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    const findSuggestions = () => {
      const similarProducts: Suggestion[] = [];

      // Buscar productos existentes similares
      products.forEach(product => {
        const similarity = ProductMatcherService.calculateSmartSimilarity(
          value,
          product.displayName || product.name
        );

        if (similarity >= 50) {
          similarProducts.push({
            type: 'existing',
            product,
            similarity,
          });
        }
      });

      // Ordenar por similitud
      similarProducts.sort((a, b) => (b.similarity || 0) - (a.similarity || 0));

      // Sugerir formato estándar si parece un producto incompleto
      const standardSuggestions = getStandardSuggestions(value, brand, type);

      // Combinar sugerencias
      const allSuggestions = [...similarProducts.slice(0, 3), ...standardSuggestions];

      setSuggestions(allSuggestions);
      setShowSuggestions(allSuggestions.length > 0);
    };

    const debounceTimer = setTimeout(findSuggestions, 300);
    return () => clearTimeout(debounceTimer);
  }, [value, products, brand, type]);

  const getStandardSuggestions = (input: string, brand?: string, type?: string): Suggestion[] => {
    const suggestions: Suggestion[] = [];
    const lower = input.toLowerCase();

    // Detectar si el usuario está escribiendo un tinte con tono
    const tintMatch = input.match(/(\d{1,2})[\/\-\.,]?(\d{0,2})/);
    if (tintMatch && (type === 'color' || lower.includes('tint') || lower.includes('color'))) {
      const level = tintMatch[1];
      const tone = tintMatch[2];

      // Sugerir formato estándar para tintes conocidos
      if (brand?.toLowerCase().includes('wella') || lower.includes('koleston')) {
        suggestions.push({
          type: 'standard',
          standardName: `Wella Koleston Perfect ${level}/${tone || '0'}`,
        });
      }
      if (brand?.toLowerCase().includes('loreal') || lower.includes('majirel')) {
        suggestions.push({
          type: 'standard',
          standardName: `L'Oréal Professionnel Majirel ${level}.${tone || '0'}`,
        });
      }
    }

    // Detectar oxidantes
    const volumeMatch = input.match(/(\d{1,2})\s*(?:vol|%)/i);
    if (
      volumeMatch &&
      (type === 'developer' || lower.includes('oxid') || lower.includes('developer'))
    ) {
      const volume = volumeMatch[1];

      if (brand?.toLowerCase().includes('wella')) {
        suggestions.push({
          type: 'standard',
          standardName: `Wella Welloxon Perfect ${volume} vol`,
        });
      }
      if (brand?.toLowerCase().includes('loreal')) {
        suggestions.push({
          type: 'standard',
          standardName: `L'Oréal Oxydant Creme ${volume} vol`,
        });
      }
    }

    return suggestions;
  };

  const handleSelectSuggestion = (suggestion: Suggestion) => {
    if (suggestion.type === 'existing' && suggestion.product) {
      // Seleccionar producto existente
      onChangeText(suggestion.product.displayName || suggestion.product.name);

      if (onSelectSuggestion) {
        onSelectSuggestion({
          name: suggestion.product.name,
          displayName: suggestion.product.displayName,
          brand: suggestion.product.brand,
          line: suggestion.product.line,
          type: suggestion.product.type,
          shade: suggestion.product.shade,
          barcode: suggestion.product.barcode,
        });
      }
    } else if (suggestion.standardName) {
      // Usar nombre estándar sugerido
      onChangeText(suggestion.standardName);
    }

    setShowSuggestions(false);
  };

  const renderSuggestion = ({ item }: { item: Suggestion }) => {
    if (item.type === 'existing' && item.product) {
      return (
        <TouchableOpacity
          style={styles.suggestionItem}
          onPress={() => handleSelectSuggestion(item)}
        >
          <View style={styles.suggestionContent}>
            <Text style={styles.suggestionText}>
              {item.product.displayName || item.product.name}
            </Text>
            <Text style={styles.suggestionSubtext}>
              Producto existente • {Math.round(item.similarity || 0)}% similar
            </Text>
          </View>
          <CheckCircle size={16} color={Colors.light.success} />
        </TouchableOpacity>
      );
    } else {
      return (
        <TouchableOpacity
          style={styles.suggestionItem}
          onPress={() => handleSelectSuggestion(item)}
        >
          <View style={styles.suggestionContent}>
            <Text style={styles.suggestionText}>{item.standardName}</Text>
            <Text style={styles.suggestionSubtext}>Formato sugerido</Text>
          </View>
        </TouchableOpacity>
      );
    }
  };

  return (
    <View style={styles.container}>
      <TextInput
        style={styles.input}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor={Colors.light.gray}
        onFocus={() => value.length >= 3 && setShowSuggestions(true)}
        onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
      />

      {showSuggestions && suggestions.length > 0 && (
        <View style={styles.suggestionsContainer}>
          <ScrollView
            style={styles.suggestionsList}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
            nestedScrollEnabled={true}
          >
            {suggestions.map((item, index) => (
              <View key={`${item.type}-${index}`}>{renderSuggestion({ item })}</View>
            ))}
          </ScrollView>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    zIndex: 1,
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
    color: Colors.light.text,
    backgroundColor: Colors.light.surface,
  },
  suggestionsContainer: {
    position: 'absolute',
    top: 52,
    left: 0,
    right: 0,
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
    maxHeight: 200,
    shadowColor: Colors.light.shadowColor,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  suggestionsList: {
    borderRadius: 12,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border + '30',
  },
  suggestionContent: {
    flex: 1,
  },
  suggestionText: {
    fontSize: 15,
    color: Colors.light.text,
    fontWeight: '500',
  },
  suggestionSubtext: {
    fontSize: 12,
    color: Colors.light.gray,
    marginTop: 2,
  },
});
