# CLAUDE.md - Inventory Components

## 🎯 Propósito

Componentes para gestión de inventario con sistema jerárquico estructurado: **Brand → Line → Type → Shade**. Integra mapeo de productos AI, conversión entre marcas y tracking de consumo en tiempo real.

## 🏗️ Sistema de Inventario Estructurado

### Jerarquía de Productos

```
Brand (Marca)
├── Line (Línea)
│   ├── Type (Tipo/Categoría)
│   │   ├── Shade (Tono/Color)
│   │   ├── Shade
│   │   └── ...
│   ├── Type
│   └── ...
├── Line
└── ...
```

### Ejemplo Práctico

```
Wella
├── Koleston Perfect
│   ├── Color (Permanent)
│   │   ├── 7/1 (Rubio Ceniza)
│   │   ├── 8/0 (Rubio Claro Natural)
│   │   └── 9/16 (Rubio Muy Claro Ceniza Violeta)
│   ├── Developer (Oxidante)
│   │   ├── 10 Vol (3%)
│   │   ├── 20 Vol (6%)
│   │   └── 30 Vol (9%)
│   └── Additive (Aditivo)
└── Illumina Color
    ├── Color (Semi-permanent)
    └── Care (Tratamiento)
```

## 🗂️ Componentes Principales

### 1. BrandSelector.tsx

**Propósito:** Selector de marca con autocompletado y frecuencia de uso.

```typescript
interface BrandSelectorProps {
  selectedBrand: string | null;
  onBrandChange: (brand: string) => void;
  showFrequent?: boolean; // Mostrar marcas más usadas
  allowCustom?: boolean;  // Permitir marcas personalizadas
}

// Uso típico
<BrandSelector
  selectedBrand={selectedBrand}
  onBrandChange={setBrand}
  showFrequent={true}
/>
```

**Características:**

- Autocomplete con fuzzy search
- Marcas recientes y frecuentes al top
- Validación de marcas soportadas
- Integración con brand expertise system

### 2. LineSelector.tsx

**Propósito:** Selector de línea dependiente de la marca seleccionada.

```typescript
interface LineSelectorProps {
  brand: string;
  selectedLine: string | null;
  onLineChange: (line: string) => void;
  disabled?: boolean;
}
```

**Características:**

- Filtrado dinámico por marca
- Líneas más populares primero
- Validación de compatibilidad brand-line

### 3. TypeSelector.tsx

**Propósito:** Selector de tipo/categoría con mapeo español-inglés.

```typescript
// Mapeo obligatorio de categorías
const CATEGORY_MAPPING = {
  tinte: 'color',
  oxidante: 'developer',
  decolorante: 'bleach',
  matizador: 'toner',
  tratamiento: 'treatment',
  aditivo: 'additive',
};
```

**Características:**

- UI en español, DB en inglés
- Iconos descriptivos por tipo
- Validación de tipos soportados por línea

### 4. ProductMappingModal.tsx

**Propósito:** Modal para mapear productos AI con inventario local.

```typescript
interface ProductMappingProps {
  aiProduct: {
    brand: string;
    name: string;
    category: string;
    quantity?: string;
  };
  onMapping: (inventoryProduct: Product) => void;
  onSkip: () => void;
}
```

**Flujo de Mapeo:**

1. AI genera fórmula con productos genéricos
2. Sistema busca matches en inventario local
3. Si no encuentra match exacto, abre modal
4. Usuario mapea o crea nuevo producto
5. Mapping se guarda para futuras fórmulas

### 5. StockMovementsModal.tsx

**Propósito:** Registro y visualización de movimientos de stock.

```typescript
interface StockMovement {
  type: 'in' | 'out' | 'adjustment' | 'consumption';
  quantity: number;
  reason: string;
  referenceId?: string; // ID del servicio si es consumo
  timestamp: string;
  userId: string;
}
```

**Tipos de Movimientos:**

- `in`: Entrada (compras, devoluciones)
- `out`: Salida manual
- `adjustment`: Ajuste de inventario
- `consumption`: Consumo en servicio (automático)

## 🔄 Sistema de Conversión entre Marcas

### Uso del BrandConversionService

```typescript
import { brandConversionService } from '@/services/brandConversionService';

// Convertir fórmula entre marcas
const convertFormula = async (formula: ColorFormula, targetBrand: string) => {
  const conversion = await brandConversionService.convert(
    formula,
    targetBrand,
    'standard' // línea objetivo
  );

  if (conversion.confidence < 70) {
    // Mostrar warning de baja confianza
    showConversionWarning(conversion);
  }

  return conversion.targetFormula;
};
```

### Componente ConversionBadge

```typescript
<ConversionBadge
  confidence={conversion.confidence}
  originalBrand="Wella"
  targetBrand="L'Oréal"
  adjustments={conversion.adjustments}
/>
```

## 📊 Sistema de Consumo y Stock

### Validación de Stock

```typescript
// SIEMPRE validar stock antes de permitir consumo
const validateStock = (productId: string, requestedQuantity: number): boolean => {
  const product = inventory.getProduct(productId);
  if (!product) return false;

  // NUNCA permitir stock negativo
  return product.stock >= requestedQuantity;
};

// Registro de consumo
const recordConsumption = async (
  consumptions: Array<{ productId: string; quantity: number }>,
  serviceId: string,
  clientName: string
) => {
  // Validar TODOS los productos antes de registrar NINGUNO
  for (const { productId, quantity } of consumptions) {
    if (!validateStock(productId, quantity)) {
      throw new Error(`Insufficient stock for product ${productId}`);
    }
  }

  // Si pasa validación, registrar todos
  await inventoryStore.consumeProducts(consumptions, serviceId, clientName);
};
```

### Alertas de Stock Bajo

```typescript
interface LowStockAlertProps {
  products: LowStockProduct[];
  onReorder: (productId: string) => void;
  onAdjustMinimum: (productId: string, newMinimum: number) => void;
}

// Lógica de alerta
const checkLowStock = (): LowStockProduct[] => {
  return products.filter(product => {
    const percentage = (product.stock / product.minimumStock) * 100;
    return percentage <= 20; // Alerta cuando queda 20% o menos
  });
};
```

## 🎨 Filtros y Búsqueda

### InventoryFilters.tsx

```typescript
interface FilterState {
  stockStatus: 'all' | 'low' | 'out' | 'ok';
  categories: string[];
  brands: string[];
  searchQuery: string;
  priceRange: [number, number];
}

// Implementación de filtros
const applyFilters = (products: Product[], filters: FilterState): Product[] => {
  return products
    .filter(p => filterByStockStatus(p, filters.stockStatus))
    .filter(p => filterByCategories(p, filters.categories))
    .filter(p => filterByBrands(p, filters.brands))
    .filter(p => filterBySearch(p, filters.searchQuery))
    .filter(p => filterByPriceRange(p, filters.priceRange));
};
```

### Búsqueda Inteligente

```typescript
// Fuzzy search en múltiples campos
const searchProducts = (query: string): Product[] => {
  const searchFields = ['name', 'brand', 'line', 'type', 'shade', 'code'];
  return products.filter(product =>
    searchFields.some(field => fuzzyMatch(product[field]?.toLowerCase(), query.toLowerCase()))
  );
};
```

## 📋 Pricing y Configuración

### PricingSetupModal.tsx

```typescript
interface PricingConfig {
  costPrice: number; // Precio de costo
  sellingPrice: number; // Precio de venta
  margin: number; // Margen de ganancia %
  currency: string; // Moneda (EUR, USD, etc.)
  taxRate: number; // Tasa de impuestos %
}

// Cálculo automático de precios
const calculateMargin = (cost: number, selling: number): number => {
  return ((selling - cost) / cost) * 100;
};

const calculateSellingPrice = (cost: number, margin: number): number => {
  return cost * (1 + margin / 100);
};
```

## 🔧 Patrones de Implementación

### Carga Lazy de Datos

```typescript
// Cargar productos por demanda para mejor performance
const [visibleProducts, setVisibleProducts] = useState<Product[]>([]);
const [page, setPage] = useState(0);

const loadMoreProducts = useCallback(() => {
  const nextPage = products.slice(page * PAGE_SIZE, (page + 1) * PAGE_SIZE);
  setVisibleProducts(prev => [...prev, ...nextPage]);
  setPage(prev => prev + 1);
}, [products, page]);
```

### Optimistic Updates

```typescript
// UI updates inmediatos, sync en background
const updateProduct = async (id: string, updates: Partial<Product>) => {
  // 1. Update UI inmediatamente
  setProducts(prev => prev.map(p => (p.id === id ? { ...p, ...updates } : p)));

  // 2. Sync con backend
  try {
    await inventoryStore.updateProduct(id, updates);
  } catch (error) {
    // Revertir en caso de error
    setProducts(prev => prev.map(p => (p.id === id ? originalProduct : p)));
    showErrorToast('Failed to update product');
  }
};
```

## 🚨 Errores Comunes y Soluciones

### 1. Stock negativo

```bash
Error: "Stock went negative"
Solución: SIEMPRE validar antes de consumo
```

### 2. Mapeo de categorías incorrecto

```bash
Error: "Category not found in database"
Solución: Usar CATEGORY_MAPPING para español → inglés
```

### 3. Performance con muchos productos

```bash
Error: "List renders slowly"
Solución: Virtualización con FlatList, lazy loading
```

### 4. Conversión entre marcas fallida

```bash
Error: "Conversion not available"
Solución: Verificar with hasConversion() antes de intentar
```

## 📊 Métricas Importantes

### KPIs de Inventario

- **Stock accuracy:** 99.9%
- **Productos con stock bajo:** <5%
- **Tiempo de búsqueda:** <200ms
- **Success rate de conversión:** >85%

### Optimizaciones

- Pagination: 50 productos por página
- Search debounce: 300ms
- Cache de conversiones: 24h TTL
- Lazy loading activado a partir de 100+ productos

## 🤖 Agentes Recomendados

### Para este módulo usar:

**database-architect** - Optimización de queries de inventario

- Optimizar queries de productos (>500ms)
- Diseñar índices para búsquedas frecuentes
- Análisis de performance en filtros complejos
- PROACTIVAMENTE usar para queries lentas

**data-migration-specialist** - Cambios de schema en productos

- Migraciones zero-downtime para nuevas columnas
- Transformaciones de datos en estructuras de productos
- Migración de datos legacy a nueva jerarquía
- MUST BE USED antes de cambios de schema

**frontend-developer** - Componentes de inventario complejos

- Virtualización de listas grandes de productos
- Implementación de filtros avanzados
- Optimización de re-renders en componentes pesados
- PROACTIVAMENTE usar para nuevas features

**ui-designer** - Interfaz de gestión de inventario

- Diseño de componentes de búsqueda y filtrado
- Indicadores visuales de stock bajo
- Patterns de accessibility para tablas grandes
- PROACTIVAMENTE usar para rediseño de flujos

**performance-benchmarker** - Optimización de rendimiento

- Análisis de performance en listas grandes
- Optimización de FPS en filtros en tiempo real
- Memory usage en componentes de inventario
- PROACTIVAMENTE usar cuando se detecte lentitud

### 💡 Ejemplos de Uso

```bash
# Optimizar query lenta de productos
Task: Use database-architect to analyze and optimize slow products filtering query

# Virtualizar lista de productos grande
Task: Use frontend-developer to implement FlatList virtualization for 1000+ products

# Rediseñar flujo de mapeo de productos
Task: Use ui-designer to improve ProductMappingModal UX flow

# Migrar estructura de productos
Task: Use data-migration-specialist to migrate products table to new brand hierarchy
```

## 🔌 MCPs Disponibles

### Funciones MCP útiles:

**Para base de datos:**

- `mcp__supabase__list_tables` - Inspeccionar schema de products
- `mcp__supabase__execute_sql` - Queries directas para análisis
- `mcp__supabase__get_advisors` - Recomendaciones de performance
- `mcp__supabase__apply_migration` - Aplicar cambios de schema

**Para análisis de código:**

- `mcp__serena__get_symbols_overview` - Estructura de stores y servicios
- `mcp__serena__find_symbol` - Localizar funciones específicas
- `mcp__serena__search_for_pattern` - Buscar patrones en validaciones

**Para diagnósticos:**

- `mcp__ide__getDiagnostics` - Errores TypeScript en tipos
- `mcp__supabase__get_logs` - Logs de operaciones de inventario

### 📝 Ejemplos MCP

```bash
# Analizar performance de tabla products
mcp__supabase__list_tables
mcp__supabase__get_advisors: "performance"
mcp__supabase__execute_sql: "EXPLAIN SELECT * FROM products WHERE brand = 'Wella'"

# Explorar inventory-store
mcp__serena__get_symbols_overview: "stores/inventory-store.ts"
mcp__serena__find_symbol: "addProduct"

# Buscar validaciones de stock
mcp__serena__search_for_pattern: "validateStock" in "components/inventory/"

# Verificar tipos de inventario
mcp__ide__getDiagnostics: "types/inventory.ts"
```

### 🔄 Combinaciones Recomendadas

**Análisis de Performance:**

1. `database-architect` + `mcp__supabase__get_advisors`
2. `performance-benchmarker` + `mcp__ide__getDiagnostics`

**Desarrollo de Features:**

1. `frontend-developer` + `mcp__serena__find_symbol`
2. `ui-designer` + `mcp__context7__get_library_docs`

## 🔗 Archivos Relacionados

- `../../stores/inventory-store.ts` - Estado global del inventario
- `../../services/brandConversionService.ts` - Conversión entre marcas
- `../../services/inventoryConsumptionService.ts` - Tracking de consumo
- `../../constants/product-mappings.ts` - Mapeos de categorías
- `../../types/inventory.ts` - Tipos TypeScript

---

**⚡ Recuerda:** El inventario es crítico para el negocio. Siempre validar stock y mantener consistencia de datos. Usa `database-architect` para optimización de queries y `data-migration-specialist` para cambios de schema.
