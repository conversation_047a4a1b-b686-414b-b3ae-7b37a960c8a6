import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
} from 'react-native';
import { X, Plus, Info, Check, Euro } from 'lucide-react-native';
import { router } from 'expo-router';
import Colors from '@/constants/colors';
import { commonStyles } from '@/styles/commonStyles';
import { useInventoryStore } from '@/stores/inventory-store';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { Product } from '@/types/inventory';

interface PricingSetupModalProps {
  visible: boolean;
  onClose: () => void;
  onComplete: () => void;
}

interface ProductTemplate {
  name: string;
  brand: string;
  category: Product['category'];
  unitType: Product['unitType'];
  defaultSize: number;
  suggestedPrice: number;
}

const DEFAULT_PRODUCTS: ProductTemplate[] = [
  // Oxidantes
  {
    name: 'Oxidante 10 Vol (3%)',
    brand: 'Genérico',
    category: 'oxidante',
    unitType: 'ml',
    defaultSize: 1000,
    suggestedPrice: 5,
  },
  {
    name: 'Oxidante 20 Vol (6%)',
    brand: 'Genérico',
    category: 'oxidante',
    unitType: 'ml',
    defaultSize: 1000,
    suggestedPrice: 5,
  },
  {
    name: 'Oxidante 30 Vol (9%)',
    brand: 'Genérico',
    category: 'oxidante',
    unitType: 'ml',
    defaultSize: 1000,
    suggestedPrice: 5,
  },
  {
    name: 'Oxidante 40 Vol (12%)',
    brand: 'Genérico',
    category: 'oxidante',
    unitType: 'ml',
    defaultSize: 1000,
    suggestedPrice: 5,
  },

  // Decolorantes
  {
    name: 'Polvo Decolorante Azul',
    brand: 'Genérico',
    category: 'decolorante',
    unitType: 'g',
    defaultSize: 500,
    suggestedPrice: 15,
  },
  {
    name: 'Polvo Decolorante Blanco',
    brand: 'Genérico',
    category: 'decolorante',
    unitType: 'g',
    defaultSize: 500,
    suggestedPrice: 15,
  },

  // Tratamientos
  {
    name: 'Olaplex No.1',
    brand: 'Olaplex',
    category: 'tratamiento',
    unitType: 'ml',
    defaultSize: 100,
    suggestedPrice: 80,
  },
  {
    name: 'Olaplex No.2',
    brand: 'Olaplex',
    category: 'tratamiento',
    unitType: 'ml',
    defaultSize: 500,
    suggestedPrice: 120,
  },
  {
    name: 'Protector de Cuero Cabelludo',
    brand: 'Genérico',
    category: 'tratamiento',
    unitType: 'ml',
    defaultSize: 250,
    suggestedPrice: 12,
  },
];

export default function PricingSetupModal({
  visible,
  onClose,
  onComplete,
}: PricingSetupModalProps) {
  const { addProduct, initializeWithDefaults: _initializeWithDefaults } = useInventoryStore();
  const { formatCurrency } = useSalonConfigStore();

  const [step, setStep] = useState(1);
  const [selectedProducts, setSelectedProducts] = useState<number[]>([0, 1, 2, 3, 4, 5]); // Default selections
  const [productPrices, setProductPrices] = useState<Record<number, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // Custom product form removed - using main inventory screen instead

  useEffect(() => {
    // Initialize prices with suggested values
    const initialPrices: Record<number, string> = {};
    DEFAULT_PRODUCTS.forEach((product, index) => {
      initialPrices[index] = product.suggestedPrice.toString();
    });
    setProductPrices(initialPrices);
  }, []);

  const toggleProductSelection = (index: number) => {
    if (selectedProducts.includes(index)) {
      setSelectedProducts(selectedProducts.filter(i => i !== index));
    } else {
      setSelectedProducts([...selectedProducts, index]);
    }
  };

  const calculateUnitPrice = (totalPrice: string, size: number, _unitType: string): number => {
    const price = parseFloat(totalPrice) || 0;
    return price / size;
  };

  const handleSaveProducts = async () => {
    setIsLoading(true);

    try {
      // Save selected default products in parallel for better performance
      const productCreationPromises = selectedProducts.map(async index => {
        const template = DEFAULT_PRODUCTS[index];
        const purchasePrice = parseFloat(productPrices[index] || '0');

        if (purchasePrice > 0) {
          return addProduct({
            name: template.name,
            brand: template.brand,
            category: template.category,
            currentStock: 0, // Start with 0, user will add stock later
            minStock: template.unitType === 'ml' ? 200 : 100,
            unitType: template.unitType,
            unitSize: template.defaultSize,
            purchasePrice,
            costPerUnit: calculateUnitPrice(
              productPrices[index],
              template.defaultSize,
              template.unitType
            ),
            isActive: true,
          });
        }
        return Promise.resolve();
      });

      await Promise.all(productCreationPromises);

      setIsLoading(false);
      onComplete();
      Alert.alert(
        'Configuración Completada',
        'Los productos se han configurado correctamente. Ahora puedes gestionar tu inventario.',
        [{ text: 'OK', onPress: onClose }]
      );
    } catch {
      setIsLoading(false);
      Alert.alert('Error', 'No se pudieron guardar los productos. Por favor, intenta de nuevo.');
    }
  };

  // Removed handleAddCustomProduct - using main inventory screen instead

  const renderStep1 = () => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>Selecciona los productos que utilizas</Text>
      <Text style={styles.stepDescription}>
        Estos son los productos más comunes en coloración. Selecciona los que tienes en tu salón.
      </Text>

      <ScrollView style={styles.productList} showsVerticalScrollIndicator={false}>
        {DEFAULT_PRODUCTS.map((product, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.productItem,
              selectedProducts.includes(index) && styles.productItemSelected,
            ]}
            onPress={() => toggleProductSelection(index)}
          >
            <View style={styles.productInfo}>
              <Text style={styles.productName}>{product.name}</Text>
              <Text style={styles.productDetails}>
                {product.brand} • {product.defaultSize}
                {product.unitType}
              </Text>
            </View>
            <View style={styles.checkbox}>
              {selectedProducts.includes(index) && <Check size={16} color={Colors.light.primary} />}
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>

      <TouchableOpacity
        style={styles.customProductButton}
        onPress={() => {
          onClose(); // Close the modal first
          router.push('/inventory/new'); // Navigate to the main inventory form
        }}
      >
        <Plus size={20} color={Colors.light.primary} />
        <Text style={styles.customProductButtonText}>Agregar producto personalizado</Text>
      </TouchableOpacity>
    </View>
  );

  const renderStep2 = () => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>Configura los precios de compra</Text>
      <Text style={styles.stepDescription}>
        Ingresa el precio que pagas por cada producto. El sistema calculará automáticamente el costo
        por unidad.
      </Text>

      <ScrollView style={styles.priceList} showsVerticalScrollIndicator={false}>
        {selectedProducts.map(index => {
          const product = DEFAULT_PRODUCTS[index];
          const price = productPrices[index] || '';
          const unitPrice = price
            ? calculateUnitPrice(price, product.defaultSize, product.unitType)
            : 0;

          return (
            <View key={index} style={styles.priceItem}>
              <View style={styles.priceItemHeader}>
                <Text style={styles.priceItemName}>{product.name}</Text>
                <Text style={styles.priceItemSize}>
                  {product.defaultSize}
                  {product.unitType}
                </Text>
              </View>

              <View style={styles.priceInputContainer}>
                <View style={styles.priceInputWrapper}>
                  <Euro size={16} color={Colors.light.gray} style={styles.euroIcon} />
                  <TextInput
                    style={styles.priceInput}
                    value={price}
                    onChangeText={text => setProductPrices({ ...productPrices, [index]: text })}
                    placeholder="0.00"
                    keyboardType="decimal-pad"
                    placeholderTextColor={Colors.light.gray}
                  />
                </View>

                {unitPrice > 0 && (
                  <Text style={styles.unitPrice}>
                    {formatCurrency(unitPrice)}/{product.unitType}
                  </Text>
                )}
              </View>
            </View>
          );
        })}
      </ScrollView>

      <View style={styles.infoBox}>
        <Info size={16} color={Colors.light.primary} />
        <Text style={styles.infoText}>
          Los precios por unidad se usarán para calcular el costo exacto de cada servicio
        </Text>
      </View>
    </View>
  );

  // Removed renderCustomProductForm - using main inventory screen instead

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <X size={24} color={Colors.light.gray} />
          </TouchableOpacity>
          <Text style={styles.title}>Configuración de Precios</Text>
          <View style={commonStyles.width24} />
        </View>

        <View style={styles.progressBar}>
          <View style={[styles.progressFill, { width: `${step * 50}%` }]} />
        </View>

        {step === 1 ? renderStep1() : renderStep2()}

        <View style={styles.footer}>
          {step > 1 && (
            <TouchableOpacity style={styles.backButton} onPress={() => setStep(step - 1)}>
              <Text style={styles.backButtonText}>Atrás</Text>
            </TouchableOpacity>
          )}

          {step === 1 ? (
            <TouchableOpacity
              style={[styles.nextButton, selectedProducts.length === 0 && styles.buttonDisabled]}
              onPress={() => selectedProducts.length > 0 && setStep(2)}
              disabled={selectedProducts.length === 0}
            >
              <Text style={styles.nextButtonText}>Siguiente</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={styles.nextButton}
              onPress={handleSaveProducts}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text style={styles.nextButtonText}>Finalizar</Text>
              )}
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
  },
  progressBar: {
    height: 4,
    backgroundColor: Colors.light.lightGray,
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.light.primary,
  },
  stepContent: {
    flex: 1,
    padding: 20,
  },
  stepTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 16,
    color: Colors.light.gray,
    marginBottom: 24,
    lineHeight: 22,
  },
  productList: {
    flex: 1,
  },
  productItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: Colors.light.border,
  },
  productItemSelected: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.primary + '05',
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 4,
  },
  productDetails: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.light.border,
    alignItems: 'center',
    justifyContent: 'center',
  },
  customProductButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderWidth: 2,
    borderColor: Colors.light.primary,
    borderStyle: 'dashed',
    borderRadius: 12,
    marginTop: 16,
  },
  customProductButtonText: {
    fontSize: 16,
    color: Colors.light.primary,
    fontWeight: '600',
    marginLeft: 8,
  },
  priceList: {
    flex: 1,
  },
  priceItem: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  priceItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  priceItemName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    flex: 1,
  },
  priceItemSize: {
    fontSize: 14,
    color: Colors.light.gray,
  },
  priceInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  priceInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    paddingHorizontal: 12,
    flex: 1,
    marginRight: 12,
  },
  euroIcon: {
    marginRight: 8,
  },
  priceInput: {
    flex: 1,
    height: 44,
    fontSize: 16,
    color: Colors.light.text,
  },
  unitPrice: {
    fontSize: 14,
    color: Colors.light.accent,
    fontWeight: '600',
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary + '10',
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
  },
  infoText: {
    fontSize: 14,
    color: Colors.light.primary,
    marginLeft: 12,
    flex: 1,
    lineHeight: 20,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  backButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  backButtonText: {
    fontSize: 16,
    color: Colors.light.gray,
    fontWeight: '600',
  },
  nextButton: {
    backgroundColor: Colors.light.primary,
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 24,
    minWidth: 120,
    alignItems: 'center',
  },
  nextButtonText: {
    fontSize: 16,
    color: Colors.light.textLight,
    fontWeight: '600',
  },
  buttonDisabled: {
    opacity: 0.5,
  },
});
