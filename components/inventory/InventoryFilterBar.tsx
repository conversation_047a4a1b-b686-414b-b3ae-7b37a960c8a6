import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet, Modal } from 'react-native';
import { X, ChevronDown, AlertCircle, Check } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius, shadows } from '@/constants/theme';
import { useInventoryStore } from '@/stores/inventory-store';
import { useRegionalUnits } from '@/hooks/useRegionalUnits';

interface SortOption {
  value: 'name' | 'stock' | 'price' | 'brand' | 'usage';
  label: string;
}

interface GroupOption {
  value: 'none' | 'brand' | 'line' | 'category' | 'type';
  label: string;
}

const sortOptions: SortOption[] = [
  { value: 'name', label: 'Nombre' },
  { value: 'stock', label: 'Stock' },
  { value: 'price', label: 'Precio' },
  { value: 'brand', label: 'Marca' },
  { value: 'usage', label: 'Uso frecuente' },
];

const groupOptions: GroupOption[] = [
  { value: 'none', label: 'Sin agrupar' },
  { value: 'brand', label: 'Por marca' },
  { value: 'line', label: 'Por línea' },
  { value: 'category', label: 'Por categoría' },
  { value: 'type', label: 'Por tipo' },
];

export const InventoryFilterBar: React.FC = () => {
  const [showSortModal, setShowSortModal] = useState(false);
  const [showGroupModal, setShowGroupModal] = useState(false);

  const {
    products,
    activeFilters,
    sortBy,
    groupBy,
    setFilter,
    setSortBy,
    setGroupBy,
    resetFilters,
    getFilteredAndSortedProducts,
  } = useInventoryStore();

  const { colorTerm: _colorTerm, developerTerm: _developerTerm } = useRegionalUnits();

  // Calculate counts for quick filters
  const lowStockCount = products.filter(
    p => p.currentStock <= p.minStock && p.currentStock > 0
  ).length;
  const outOfStockCount = products.filter(p => p.currentStock === 0).length;
  const filteredCount = getFilteredAndSortedProducts().length;

  // Check if any filters are active
  const hasActiveFilters =
    activeFilters.stockStatus !== 'all' ||
    activeFilters.categories.length > 0 ||
    activeFilters.brands.length > 0;

  const handleStockStatusChange = (status: typeof activeFilters.stockStatus) => {
    setFilter('stockStatus', activeFilters.stockStatus === status ? 'all' : status);
  };

  const renderSortModal = () => (
    <Modal
      visible={showSortModal}
      transparent
      animationType="fade"
      onRequestClose={() => setShowSortModal(false)}
    >
      <TouchableOpacity
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={() => setShowSortModal(false)}
      >
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>Ordenar por</Text>
          {sortOptions.map(option => (
            <TouchableOpacity
              key={option.value}
              style={styles.modalOption}
              onPress={() => {
                setSortBy(option.value);
                setShowSortModal(false);
              }}
            >
              <Text
                style={[
                  styles.modalOptionText,
                  sortBy === option.value && styles.modalOptionTextActive,
                ]}
              >
                {option.label}
              </Text>
              {sortBy === option.value && <Check size={20} color={Colors.light.primary} />}
            </TouchableOpacity>
          ))}
        </View>
      </TouchableOpacity>
    </Modal>
  );

  const renderGroupModal = () => (
    <Modal
      visible={showGroupModal}
      transparent
      animationType="fade"
      onRequestClose={() => setShowGroupModal(false)}
    >
      <TouchableOpacity
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={() => setShowGroupModal(false)}
      >
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>Agrupar productos</Text>
          {groupOptions.map(option => (
            <TouchableOpacity
              key={option.value}
              style={styles.modalOption}
              onPress={() => {
                setGroupBy(option.value);
                setShowGroupModal(false);
              }}
            >
              <Text
                style={[
                  styles.modalOptionText,
                  groupBy === option.value && styles.modalOptionTextActive,
                ]}
              >
                {option.label}
              </Text>
              {groupBy === option.value && <Check size={20} color={Colors.light.primary} />}
            </TouchableOpacity>
          ))}
        </View>
      </TouchableOpacity>
    </Modal>
  );

  return (
    <>
      <View style={styles.container}>
        {/* Quick Filters */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.filtersRow}
          contentContainerStyle={styles.filtersContent}
        >
          {/* Stock Status Filters */}
          {lowStockCount > 0 && (
            <TouchableOpacity
              style={[
                styles.filterChip,
                activeFilters.stockStatus === 'low' && styles.filterChipActive,
              ]}
              onPress={() => handleStockStatusChange('low')}
            >
              <AlertCircle
                size={16}
                color={activeFilters.stockStatus === 'low' ? 'white' : Colors.light.warning}
              />
              <Text
                style={[
                  styles.filterChipText,
                  activeFilters.stockStatus === 'low' && styles.filterChipTextActive,
                ]}
              >
                Stock bajo
              </Text>
              <View
                style={[
                  styles.filterChipBadge,
                  activeFilters.stockStatus === 'low' && styles.filterChipBadgeActive,
                ]}
              >
                <Text
                  style={[
                    styles.filterChipBadgeText,
                    activeFilters.stockStatus === 'low' && styles.filterChipBadgeTextActive,
                  ]}
                >
                  {lowStockCount}
                </Text>
              </View>
            </TouchableOpacity>
          )}

          {outOfStockCount > 0 && (
            <TouchableOpacity
              style={[
                styles.filterChip,
                activeFilters.stockStatus === 'out' && styles.filterChipActive,
              ]}
              onPress={() => handleStockStatusChange('out')}
            >
              <X
                size={16}
                color={activeFilters.stockStatus === 'out' ? 'white' : Colors.light.error}
              />
              <Text
                style={[
                  styles.filterChipText,
                  activeFilters.stockStatus === 'out' && styles.filterChipTextActive,
                ]}
              >
                Sin stock
              </Text>
              <View
                style={[
                  styles.filterChipBadge,
                  activeFilters.stockStatus === 'out' && styles.filterChipBadgeActive,
                ]}
              >
                <Text
                  style={[
                    styles.filterChipBadgeText,
                    activeFilters.stockStatus === 'out' && styles.filterChipBadgeTextActive,
                  ]}
                >
                  {outOfStockCount}
                </Text>
              </View>
            </TouchableOpacity>
          )}

          {/* Sort and Group Controls */}
          <TouchableOpacity style={styles.controlChip} onPress={() => setShowSortModal(true)}>
            <Text style={styles.controlLabel}>Ordenar</Text>
            <ChevronDown size={16} color={Colors.light.primary} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.controlChip} onPress={() => setShowGroupModal(true)}>
            <Text style={styles.controlLabel}>Agrupar</Text>
            <ChevronDown size={16} color={Colors.light.primary} />
          </TouchableOpacity>

          {/* Clear filters */}
          {hasActiveFilters && (
            <TouchableOpacity style={styles.clearChip} onPress={resetFilters}>
              <X size={16} color={Colors.light.primary} />
              <Text style={styles.clearChipText}>Limpiar</Text>
            </TouchableOpacity>
          )}
        </ScrollView>

        {/* Results Count */}
        <View style={styles.resultsCount}>
          <Text style={styles.resultsText}>
            {filteredCount} {filteredCount === 1 ? 'producto' : 'productos'}
          </Text>
        </View>
      </View>

      {renderSortModal()}
      {renderGroupModal()}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    flexDirection: 'row',
    alignItems: 'center',
    height: 48,
  },
  filtersRow: {
    flex: 1,
  },
  filtersContent: {
    paddingHorizontal: spacing.lg,
    gap: spacing.sm,
    alignItems: 'center',
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs + 2,
    borderRadius: radius.full,
    gap: spacing.xs,
    marginRight: spacing.sm,
  },
  filterChipActive: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  filterChipText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
  },
  filterChipTextActive: {
    color: Colors.light.textLight,
  },
  filterChipBadge: {
    backgroundColor: Colors.light.surface,
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: radius.sm,
  },
  filterChipBadgeActive: {
    backgroundColor: Colors.light.backgroundOpacity20,
  },
  filterChipBadgeText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.semibold,
    color: Colors.light.textSecondary,
  },
  filterChipBadgeTextActive: {
    color: Colors.light.textLight,
  },
  controlChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.primary + '40',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs + 2,
    borderRadius: radius.full,
    gap: spacing.xs,
    marginRight: spacing.sm,
  },
  controlLabel: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.primary,
  },
  clearChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary + '10',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs + 2,
    borderRadius: radius.full,
    gap: spacing.xs,
    marginRight: spacing.sm,
  },
  clearChipText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.primary,
  },
  resultsCount: {
    paddingHorizontal: spacing.lg,
  },
  resultsText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    fontWeight: typography.weights.medium,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: Colors.light.modalOverlay,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: Colors.light.background,
    borderRadius: radius.lg,
    padding: spacing.lg,
    width: '80%',
    maxWidth: 320,
    ...shadows.lg,
  },
  modalTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.md,
  },
  modalOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  modalOptionText: {
    fontSize: typography.sizes.base,
    color: Colors.light.text,
  },
  modalOptionTextActive: {
    color: Colors.light.primary,
    fontWeight: typography.weights.medium,
  },
});
