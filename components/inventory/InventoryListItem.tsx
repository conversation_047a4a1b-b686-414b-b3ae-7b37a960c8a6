import React, { useMemo } from 'react';
import { StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { Package, Edit, Trash2, AlertCircle, TrendingUp } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius, shadows } from '@/constants/theme';
import { Product } from '@/types/inventory';
import { useInventoryStore } from '@/stores/inventory-store';

interface InventoryListItemProps {
  item: Product;
  canManageInventory: boolean;
  canViewCosts: boolean;
  formatCurrency: (amount: number) => string;
  formatVolume: (amount: number) => string;
  formatWeight: (amount: number) => string;
  getUnitLabel: (type: 'volume' | 'weight') => string;
  onDelete: (id: string, name: string) => void;
}

const InventoryListItem = React.memo<InventoryListItemProps>(
  ({
    item,
    canManageInventory,
    canViewCosts,
    formatCurrency,
    formatVolume,
    formatWeight,
    getUnitLabel,
    onDelete,
  }) => {
    const { getFrequentlyUsedProducts } = useInventoryStore();

    const isLowStock = item.currentStock <= item.minStock;

    // Check if product is frequently used
    const isFrequentlyUsed = useMemo(() => {
      const frequentProducts = getFrequentlyUsedProducts(20);
      return frequentProducts.some(p => p.id === item.id);
    }, [item.id, getFrequentlyUsedProducts]);

    // Calculate stock percentage for visual indicator
    const stockPercentage = useMemo(() => {
      if (item.minStock === 0) return 100;
      return Math.min((item.currentStock / (item.minStock * 2)) * 100, 100);
    }, [item.currentStock, item.minStock]);

    const handleStock = () => {
      router.push(`/inventory/${item.id}`);
    };

    const handleEdit = () => {
      router.push(`/inventory/edit/${item.id}`);
    };

    return (
      <View style={[styles.productCard, isLowStock && styles.lowStockCard]}>
        <View style={styles.cardHeader}>
          <View style={styles.productInfo}>
            <Text style={styles.productName} numberOfLines={2}>
              {item.displayName || item.name}
            </Text>
            <View style={styles.brandCategoryRow}>
              <Text style={styles.productBrand}>{item.brand}</Text>
              {item.line && (
                <>
                  <Text style={styles.separator}> • </Text>
                  <Text style={styles.productLine}>{item.line}</Text>
                </>
              )}
              {item.shade && (
                <>
                  <Text style={styles.separator}> • </Text>
                  <Text style={styles.productShade}>{item.shade}</Text>
                </>
              )}
            </View>
          </View>
          <View style={styles.badgesContainer}>
            {isFrequentlyUsed && (
              <View style={styles.frequentBadge}>
                <TrendingUp size={14} color="white" />
              </View>
            )}
            {isLowStock && (
              <View style={styles.lowStockBadge}>
                <AlertCircle size={16} color="white" />
              </View>
            )}
          </View>
        </View>

        <View style={styles.cardDetails}>
          <View style={styles.stockInfo}>
            <Text style={styles.detailLabel}>Stock:</Text>
            <Text style={[styles.detailValue, isLowStock && styles.lowStockText]}>
              {item.unitType === 'ml' || item.unitType === 'fl oz'
                ? formatVolume(item.currentStock)
                : formatWeight(item.currentStock)}
              {item.unitSize && item.currentStock >= item.unitSize && (
                <Text style={styles.packageCount}>
                  {' '}
                  ({Math.floor(item.currentStock / item.unitSize)} envases)
                </Text>
              )}
            </Text>
          </View>

          {/* Stock level visual indicator */}
          <View style={styles.stockBarContainer}>
            <View
              style={[
                styles.stockBar,
                {
                  width: `${stockPercentage}%`,
                  backgroundColor: isLowStock ? Colors.light.warning : Colors.light.success,
                },
              ]}
            />
          </View>

          {canViewCosts && item.costPerUnit > 0 && (
            <View style={styles.priceInfo}>
              <Text style={styles.detailLabel}>Precio:</Text>
              <Text style={styles.detailValue}>
                {formatCurrency(item.costPerUnit)}/
                {getUnitLabel(
                  item.unitType === 'ml' || item.unitType === 'fl oz' ? 'volume' : 'weight'
                )}
              </Text>
            </View>
          )}
        </View>

        <View style={styles.cardActions}>
          <TouchableOpacity style={[styles.cardButton, styles.stockButton]} onPress={handleStock}>
            <Package size={16} color={Colors.light.primary} />
            <Text style={styles.cardButtonText}>Stock</Text>
          </TouchableOpacity>
          {canManageInventory && (
            <>
              <TouchableOpacity style={[styles.cardButton, styles.editButton]} onPress={handleEdit}>
                <Edit size={16} color={Colors.light.primary} />
                <Text style={styles.cardButtonText}>Editar</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.cardButton, styles.deleteButton]}
                onPress={() => onDelete(item.id, item.name)}
              >
                <Trash2 size={16} color={Colors.light.error} />
              </TouchableOpacity>
            </>
          )}
        </View>
      </View>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison for better performance
    return (
      prevProps.item.id === nextProps.item.id &&
      prevProps.item.name === nextProps.item.name &&
      prevProps.item.brand === nextProps.item.brand &&
      prevProps.item.category === nextProps.item.category &&
      prevProps.item.currentStock === nextProps.item.currentStock &&
      prevProps.item.minStock === nextProps.item.minStock &&
      prevProps.item.costPerUnit === nextProps.item.costPerUnit &&
      prevProps.item.unitSize === nextProps.item.unitSize &&
      prevProps.item.unitType === nextProps.item.unitType &&
      prevProps.canManageInventory === nextProps.canManageInventory &&
      prevProps.canViewCosts === nextProps.canViewCosts
    );
  }
);

InventoryListItem.displayName = 'InventoryListItem';

const styles = StyleSheet.create({
  productCard: {
    backgroundColor: Colors.light.background,
    borderRadius: radius.lg,
    padding: spacing.lg,
    marginBottom: spacing.md,
    marginHorizontal: spacing.xs,
    ...shadows.md,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  lowStockCard: {
    borderColor: Colors.light.warning,
    borderWidth: 2,
    backgroundColor: Colors.light.warning + '05',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
    lineHeight: typography.sizes.lg * 1.3,
  },
  brandCategoryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  productBrand: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary, // Better contrast than textSecondary
    fontWeight: typography.weights.semibold,
  },
  productLine: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  productShade: {
    fontSize: typography.sizes.sm,
    color: Colors.light.primary,
    fontWeight: typography.weights.semibold,
  },
  separator: {
    fontSize: typography.sizes.sm,
    color: Colors.light.border,
    marginHorizontal: spacing.xs,
  },
  productCategory: {
    fontSize: typography.sizes.sm,
    color: Colors.light.primary,
  },
  badgesContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  lowStockBadge: {
    backgroundColor: Colors.light.warning,
    padding: spacing.xs + 2,
    borderRadius: radius.full,
    ...shadows.sm,
  },
  frequentBadge: {
    backgroundColor: Colors.light.primary,
    padding: spacing.xs + 2,
    borderRadius: radius.full,
    ...shadows.sm,
  },
  cardDetails: {
    marginTop: spacing.md,
    marginBottom: spacing.md,
  },
  stockInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  priceInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.xs,
  },
  detailLabel: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginRight: spacing.sm,
    fontWeight: typography.weights.medium,
  },
  detailValue: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  lowStockText: {
    color: Colors.light.warning,
    fontWeight: typography.weights.semibold,
  },
  stockBarContainer: {
    height: 8,
    backgroundColor: Colors.light.surface,
    borderRadius: radius.full,
    overflow: 'hidden',
    marginTop: spacing.xs,
    marginBottom: spacing.sm,
  },
  stockBar: {
    height: '100%',
    borderRadius: radius.full,
  },
  cardActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  cardButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.sm + 2,
    borderRadius: radius.md,
    gap: spacing.xs,
    borderWidth: 1,
  },
  stockButton: {
    backgroundColor: Colors.light.primary + '10',
    borderColor: Colors.light.primary + '20',
  },
  editButton: {
    backgroundColor: Colors.light.background,
    borderColor: Colors.light.border,
  },
  deleteButton: {
    backgroundColor: Colors.light.error + '10',
    borderColor: Colors.light.error + '20',
    flex: 0,
    paddingHorizontal: spacing.md,
  },
  cardButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.primary,
  },
  packageCount: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
    fontWeight: typography.weights.normal,
  },
});

export default InventoryListItem;
