/**
 * CRITERIOS PARA LÍNEAS FORMULABLES
 *
 * Una línea es FORMULABLE si puede generar fórmulas de coloración profesional:
 *
 * ✅ FORMULABLES:
 * - hair-color: Coloración permanente, semipermanente, demi-permanente
 * - bleaching: Decolorantes, aclaradores, lighteners (para procesos de aclarado)
 *
 * ❌ NO FORMULABLES:
 * - treatment: Tratamientos, mascarillas, reconstructores
 * - styling: Productos de peinado, geles, mousses
 * - developer: Oxidantes, reveladores (son complementos, no productos principales)
 * - other: Otros productos no relacionados con coloración
 *
 * NOTA: Los desarrolladores son necesarios para la formulación pero no son
 * el producto principal. Se incluyen automáticamente según la marca/línea seleccionada.
 */
export interface ProductLine {
  id: string;
  name: string;
  description?: string;
  category: 'hair-color' | 'treatment' | 'styling' | 'bleaching' | 'developer' | 'other';
  isColorLine?: boolean; // Helper para filtrado rápido - TRUE solo para líneas formulables
}

export interface Brand {
  id: string;
  name: string;
  country: string;
  description?: string;
  lines: ProductLine[];
}

export const professionalHairColorBrands: Brand[] = [
  // German Brands
  {
    id: 'wella',
    name: 'Wella Professionals',
    country: 'Germany',
    description: 'Leading professional hair color brand',
    lines: [
      {
        id: 'koleston-perfect',
        name: 'Koleston Perfect',
        description: 'Permanent color - ME+ technology with metal purifier',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'illumina-color',
        name: 'Illumina Color',
        description: 'Permanent color - Translucent color with Microlight technology',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'color-touch',
        name: 'Color Touch',
        description: 'Demi-permanent color - Ammonia-free formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'shinefinity',
        name: 'Shinefinity',
        description: 'Demi-permanent color - Acidic pH glaze with zero lift',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'schwarzkopf',
    name: 'Schwarzkopf Professional',
    country: 'Germany',
    description: 'German engineering for hair color',
    lines: [
      {
        id: 'igora-royal',
        name: 'IGORA ROYAL',
        description: 'Permanent color - High-performance standard formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'igora-royal-absolutes',
        name: 'IGORA ROYAL Absolutes',
        description: 'Permanent color - Gray coverage for mature hair',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'igora-royal-highlifts',
        name: 'IGORA ROYAL Highlifts',
        description: 'Permanent color - High-lift blonde with Bonder technology',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'igora-zero-amm',
        name: 'IGORA ZERO AMM',
        description: 'Permanent color - Ammonia-free vegan formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'igora-vibrance',
        name: 'IGORA VIBRANCE',
        description: 'Demi-permanent color - Moisturizing liquid or cream formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'blondme-colour',
        name: 'BLONDME Colour',
        description: 'Permanent color - Specialized blonde system',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'goldwell',
    name: 'Goldwell',
    country: 'Germany',
    description: 'German precision in color',
    lines: [
      {
        id: 'topchic',
        name: 'Topchic',
        description: 'Permanent color - Intelligent color system',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'topchic-zero',
        name: 'Topchic Zero',
        description: 'Permanent color - Ammonia-free formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'elumen',
        name: 'Elumen',
        description: 'Permanent color - Non-oxidative direct pigments',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'colorance',
        name: 'Colorance',
        description: 'Demi-permanent color - Acidic or alkaline pH options',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'kadus',
    name: 'Kadus Professional',
    country: 'Germany',
    description: 'Creative color solutions with German precision',
    lines: [
      {
        id: 'kadus-color',
        name: 'Kadus Color',
        description: 'Permanent hair color with vibrant results',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'fervidol',
        name: 'Fervidol',
        description: 'Brilliant permanent color with intense shine',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'visible-repair',
        name: 'Visible Repair',
        description: 'Reconstructive treatment for damaged hair',
        category: 'treatment',
        isColorLine: false,
      },
      {
        id: 'kadus-lightener',
        name: 'Kadus Lightener',
        description: 'Professional lightening powder',
        category: 'bleaching',
        isColorLine: true,
      },
    ],
  },

  // French Brands
  {
    id: 'loreal',
    name: "L'Oréal Professionnel",
    country: 'France',
    description: 'Professional hair color innovation',
    lines: [
      {
        id: 'majirel',
        name: 'Majirel',
        description: 'Permanent color - Low ammonia standard formula with Ionène G + Incell',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'inoa',
        name: 'iNOA',
        description: 'Permanent color - Ammonia-free with Oil Delivery System (60% oils)',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'dia-light',
        name: 'Dia Light',
        description: 'Demi-permanent color - Acidic pH 6.3 toner for shine and tone',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'dia-color',
        name: 'Dia Color',
        description: 'Demi-permanent color - Alkaline formula for enhanced coverage',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'eugene-perma',
    name: 'Eugène Perma',
    country: 'France',
    description: 'French professional hair care',
    lines: [
      {
        id: 'carmen',
        name: 'Carmen',
        description: 'Professional color line',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'solaris',
        name: 'Solaris',
        description: 'Lightening products',
        category: 'bleaching',
        isColorLine: false,
      },
      {
        id: 'artiste',
        name: 'Artiste',
        description: 'Creative color',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'phyto',
    name: 'Phyto Professional',
    country: 'France',
    description: 'Botanical hair color',
    lines: [
      {
        id: 'phytocolor',
        name: 'Phytocolor',
        description: 'Botanical permanent color',
      },
      {
        id: 'phytocolorbox',
        name: 'Phytocolor Box',
        description: 'Home color kit',
      },
    ],
  },

  // Italian Brands
  {
    id: 'alfaparf',
    name: 'Alfaparf Milano',
    country: 'Italy',
    description: 'Italian professional excellence in hair color',
    lines: [
      {
        id: 'evolution-of-the-color3',
        name: 'Evolution of the Color³',
        description: 'Permanent color - With Hyaluronic Acid',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'precious-nature-hair-color',
        name: 'Precious Nature Hair Color',
        description: 'Permanent color - Ammonia-free formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'color-wear',
        name: 'Color Wear',
        description: 'Demi-permanent color - Tone-on-tone vegan formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'color-wear-gloss-toner',
        name: 'Color Wear Gloss Toner',
        description: 'Demi-permanent color - Liquid toner',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'inebrya',
    name: 'Inebrya',
    country: 'Italy',
    description: 'Italian innovation in hair color',
    lines: [
      {
        id: 'color',
        name: 'Color',
        description: 'Permanent hair color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'bionic',
        name: 'Bionic',
        description: 'Ammonia-free color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'bleaching-powder',
        name: 'Bleaching Powder',
        description: 'Lightening products',
      },
      {
        id: 'color-perfect',
        name: 'Color Perfect',
        description: 'Professional color line',
      },
    ],
  },
  {
    id: 'framesi',
    name: 'Framesi',
    country: 'Italy',
    description: 'Italian luxury hair color',
    lines: [
      {
        id: 'framcolor',
        name: 'FramColor',
        description: 'Permanent hair color',
      },
      {
        id: 'eclectic-care',
        name: 'Eclectic Care',
        description: 'Ammonia-free color',
      },
      {
        id: 'decolor-b',
        name: 'Decolor B',
        description: 'Lightening powder',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'hair-treatment',
        name: 'Hair Treatment',
        description: 'Color care system',
      },
    ],
  },
  {
    id: 'davines',
    name: 'Davines',
    country: 'Italy',
    description: 'Italian sustainable professional hair color',
    lines: [
      {
        id: 'mask-with-vibrachrom',
        name: 'Mask with Vibrachrom',
        description: 'Permanent color - Standard formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'a-new-colour',
        name: 'A New Colour',
        description: 'Permanent color - Ammonia-free formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'view',
        name: 'View',
        description: 'Demi-permanent color - Acidic pH with natural origin',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'finest-pigments',
        name: 'Finest Pigments',
        description: 'Semi-permanent color',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'kemon',
    name: 'Kemon',
    country: 'Italy',
    description: 'Italian professional hair color',
    lines: [
      {
        id: 'kroma',
        name: 'Kroma',
        description: 'Permanent color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'nayo',
        name: 'Nayo',
        description: 'Ammonia-free color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'lunex',
        name: 'Lunex',
        description: 'Lightening system',
        category: 'bleaching',
        isColorLine: false,
      },
    ],
  },
  {
    id: 'selective',
    name: 'Selective Professional',
    country: 'Italy',
    description: 'Italian color innovation',
    lines: [
      {
        id: 'colorevo',
        name: 'ColorEvo',
        description: 'Permanent hair color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'reverso',
        name: 'Reverso',
        description: 'Hair color remover',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'decolor',
        name: 'Decolor',
        description: 'Lightening powder',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },

  // Spanish Brands
  {
    id: 'salerm',
    name: 'Salerm Cosmetics',
    country: 'Spain',
    description: 'Spanish professional hair care and color innovation',
    lines: [
      {
        id: 'salermvison',
        name: 'Salermvison',
        description: 'Permanent color - Standard formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'biokera-natura-color',
        name: 'Biokera Natura Color',
        description: 'Permanent color - PPD-free, Resorcinol-free, organic formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'salermix',
        name: 'Salermix',
        description: 'Permanent color - Contrast tones',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'hi-repair',
        name: 'Hi Repair',
        description: 'Reconstructive treatment system',
        category: 'treatment',
        isColorLine: false,
      },
      {
        id: 'technique',
        name: 'Technique',
        description: 'Professional styling and finishing products',
        category: 'styling',
        isColorLine: false,
      },
      {
        id: 'biokera-natura',
        name: 'Biokera Natura',
        description: 'Natural active ingredients treatment line',
        category: 'treatment',
        isColorLine: false,
      },
      {
        id: 'color-reverse',
        name: 'Color Reverse',
        description: 'Professional color remover system',
        category: 'other',
        isColorLine: false,
      },
    ],
  },
  {
    id: 'arkhe',
    name: 'Arkhé Cosmetics',
    country: 'Spain',
    description: 'Premium Spanish brand revolutionizing professional hair color',
    lines: [
      {
        id: 'color-pure',
        name: 'Color Pure',
        description: 'Revolutionary professional hair coloration system',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'vibrant-well-aging',
        name: 'Vibrant Well-Aging Booster',
        description: 'Pro-age hair treatment for mature hair',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },
  {
    id: 'lendan',
    name: 'Lendan',
    country: 'Spain',
    description: 'Spanish innovation in professional hair color',
    lines: [
      {
        id: 'activia-plant',
        name: 'Activia Plant',
        description: 'Permanent color - Low ammonia formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'activia-plant-absolutes',
        name: 'Activia Plant Absolutes',
        description: 'Permanent color - PPD-free, Resorcinol-free formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'bond-colour-plex',
        name: 'Bond Colour Plex',
        description: 'Permanent/Demi-permanent color - Oil-based with integrated Bonder',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'tahe',
    name: 'Tahe Professional',
    country: 'Spain',
    description: 'Spanish professional hair care and color',
    lines: [
      {
        id: 'organic-care',
        name: 'Organic Care Color',
        description: 'Natural organic professional hair color system',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'lumiere',
        name: 'Lumiere',
        description: 'Professional lightening system up to 7 levels',
        category: 'bleaching',
        isColorLine: true,
      },
      {
        id: 'magic',
        name: 'Magic Color',
        description: 'Professional permanent color line',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'botanic',
        name: 'Botanic Tricology',
        description: 'Botanical hair treatment and care line',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },

  // American Brands
  {
    id: 'j-beverly-hills',
    name: 'J Beverly Hills',
    country: 'USA',
    description: 'Luxury professional hair color',
    lines: [
      {
        id: 'j-beverly-hills-colour',
        name: 'J Beverly Hills Colour',
        description: 'Permanent/Demi-permanent color - Multi-use formula <1% ammonia',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'liquid-colour',
        name: 'Liquid Colour',
        description: 'Demi-permanent color - Ammonia-free liquid gloss',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'matrix',
    name: 'Matrix',
    country: 'USA',
    description: 'Professional color innovation with ColorGrip technology',
    lines: [
      {
        id: 'socolor-pre-bonded',
        name: 'SoColor Pre-Bonded',
        description: 'Permanent color - With integrated Bonder technology',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'coil-color',
        name: 'Coil Color',
        description: 'Permanent color - Ammonia-free for curly hair',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'socolor-sync-alkaline',
        name: 'SoColor Sync (Alkaline)',
        description: 'Demi-permanent color - Alkaline pre-matched formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'socolor-sync-acidic',
        name: 'SoColor Sync (Acidic)',
        description: 'Demi-permanent color - Acidic pH toner pre-matched',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'socolor-sync-5-minute-fast-toner',
        name: 'SoColor Sync 5-Minute Fast Toner',
        description: 'Demi-permanent color - Express 5-minute toner',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'redken',
    name: 'Redken',
    country: 'USA',
    description: 'Science-based hair color with acidic pH technology',
    lines: [
      {
        id: 'color-gels-lacquers',
        name: 'Color Gels Lacquers',
        description: 'Permanent color - Liquid formula for precise application',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'color-gels-lacquers-10-minute',
        name: 'Color Gels Lacquers 10 Minute',
        description: 'Permanent color - Express 10-minute color service',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'chromatics',
        name: 'Chromatics',
        description: 'Permanent color - Ammonia-free with oil base',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'cover-fusion',
        name: 'Cover Fusion',
        description: 'Permanent color - Gray coverage with low ammonia',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'shades-eq-gloss',
        name: 'Shades EQ Gloss',
        description: 'Demi-permanent color - Acidic pH toner',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'shades-eq-bonder-inside',
        name: 'Shades EQ Bonder Inside',
        description: 'Demi-permanent color - Acidic pH toner with Bonder technology',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'joico',
    name: 'Joico',
    country: 'USA',
    description: 'Quadramine complex technology for hair reconstruction',
    lines: [
      {
        id: 'lumishine-permanent-creme-color',
        name: 'LumiShine Permanent Crème Color',
        description: 'Permanent color - With ArgiPlex™ Bonder technology',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'lumi10',
        name: 'Lumi10',
        description: 'Permanent color - Express 10-minute color service',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'youthlock',
        name: 'YouthLock',
        description: 'Permanent color - Gray coverage with collagen',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'vero-k-pak-permanent-creme-color',
        name: 'Vero K-PAK Permanent Crème Color',
        description: 'Permanent color - Reconstructive with Quadramine® Complex',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'lumishine-demi-permanent',
        name: 'LumiShine Demi-Permanent Liquid/DD Crème',
        description: 'Demi-permanent color - Available in liquid or cream formula',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'aveda',
    name: 'Aveda',
    country: 'USA',
    description: 'Plant-based professional color',
    lines: [
      {
        id: 'full-spectrum',
        name: 'Full Spectrum',
        description: 'Permanent hair color',
      },
      {
        id: 'enlightener',
        name: 'Enlightener',
        description: 'Lightening powder',
      },
      {
        id: 'demi-plus',
        name: 'Demi+',
        description: 'Demi-permanent color',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'paul-mitchell',
    name: 'Paul Mitchell',
    country: 'USA',
    description: 'Cruelty-free professional color',
    lines: [
      {
        id: 'the-color-xg',
        name: 'The Color XG',
        description: 'Permanent color - Vegan DYESMART® System',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'coversmart-color-xg',
        name: 'CoverSmart (Color XG)',
        description: 'Permanent color - Gray coverage vegan formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'the-color-10',
        name: 'THE COLOR 10',
        description: 'Permanent color - Express 10-minute color service',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'the-demi',
        name: 'The Demi',
        description: 'Demi-permanent color - Ammonia-free vegan formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'crema-xg',
        name: 'Crema XG',
        description: 'Demi-permanent color - Matched with Color XG',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'pravana',
    name: 'Pravana',
    country: 'USA',
    description: 'Vivid fashion color specialists',
    lines: [
      {
        id: 'chromasilk-creme-color',
        name: 'ChromaSilk Creme Color',
        description: 'Permanent color - With silk and keratin',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'chromasilk-hi-lifts',
        name: 'ChromaSilk HI LIFTS',
        description: 'Permanent color - High-lift blonde',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'chromasilk-vivids-everlasting',
        name: 'ChromaSilk VIVIDS Everlasting',
        description: 'Permanent color - Hybrid permanent/fantasy',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'chromasilk-hydragloss',
        name: 'ChromaSilk HydraGloss',
        description: 'Demi-permanent color - Gel toner',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'chromasilk-express-tones',
        name: 'ChromaSilk Express Tones',
        description: 'Demi-permanent color - Express ammonia-free toner',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'revlon',
    name: 'Revlon Professional',
    country: 'USA',
    description: 'Professional color expertise',
    lines: [
      {
        id: 'revlonissimo',
        name: 'Revlonissimo',
        description: 'High-performance color',
      },
      {
        id: 'young-color-excel',
        name: 'Young Color Excel',
        description: 'Ammonia-free color',
      },
      {
        id: 'nutri-color',
        name: 'Nutri Color',
        description: 'Conditioning color',
      },
      {
        id: 'colorsmetique',
        name: 'Colorsmetique',
        description: 'Permanent color',
      },
    ],
  },
  {
    id: 'clairol',
    name: 'Clairol Professional',
    country: 'USA',
    description: 'Professional color innovation',
    lines: [
      {
        id: 'premium-creme',
        name: 'Premium Creme',
        description: 'Permanent hair color',
      },
      {
        id: 'jazzing',
        name: 'Jazzing',
        description: 'Temporary hair color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'bw2',
        name: 'BW2',
        description: 'Lightening powder',
        category: 'bleaching',
        isColorLine: false,
      },
      {
        id: 'soy4plex',
        name: 'Soy4Plex',
        description: 'Conditioning color',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },
  {
    id: 'kenra',
    name: 'Kenra Professional',
    country: 'USA',
    description: 'Professional hair color',
    lines: [
      {
        id: 'kenra-color-permanent',
        name: 'Kenra Color Permanent',
        description: 'Permanent color - Low ammonia formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'studio-stylist-express',
        name: 'Studio Stylist Express',
        description: 'Permanent color - Express 10-minute color service',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'kenra-color-demi-permanent',
        name: 'Kenra Color Demi-Permanent',
        description: 'Demi-permanent color - Ammonia-free formula',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'guy-tang-mydentity',
    name: 'Guy Tang #Mydentity',
    country: 'USA',
    description: 'Celebrity colorist professional line',
    lines: [
      {
        id: 'permanent',
        name: 'Permanent',
        description: 'Permanent color - Pre-mixed tones',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'demi-permanent',
        name: 'Demi-Permanent',
        description: 'Demi-permanent color - Pre-mixed tones',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'reflect-liquid-demi',
        name: 'REFLECT Liquid Demi',
        description: 'Demi-permanent color - Liquid toner',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'x-press-toners',
        name: 'X-Press Toners',
        description: 'Demi-permanent color - Express toner',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },

  // Japanese Brands
  {
    id: 'milbon',
    name: 'Milbon',
    country: 'Japan',
    description: 'Japanese hair color technology',
    lines: [
      {
        id: 'ordeve',
        name: 'Ordeve',
        description: 'Permanent hair color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'addicthy',
        name: 'Addicthy',
        description: 'Fashion color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'deesses',
        name: 'Deesses',
        description: 'Hair care line',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },
  {
    id: 'lebel',
    name: 'Lebel Cosmetics',
    country: 'Japan',
    description: 'Japanese professional hair care',
    lines: [
      {
        id: 'materia',
        name: 'Materia',
        description: 'Hair color line',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'luquias',
        name: 'Luquias',
        description: 'Premium color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'theo',
        name: 'Theo',
        description: 'Scalp care color',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },
  {
    id: 'shiseido',
    name: 'Shiseido Professional',
    country: 'Japan',
    description: 'Japanese beauty innovation',
    lines: [
      {
        id: 'primience',
        name: 'Primience',
        description: 'Hair color system',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'crystallizing',
        name: 'Crystallizing',
        description: 'Straightening color',
      },
    ],
  },

  // Dutch Brands
  {
    id: 'keune',
    name: 'Keune',
    country: 'Netherlands',
    description: 'Dutch premium professional hair color and care',
    lines: [
      {
        id: 'tinta-color',
        name: 'Tinta Color',
        description: 'Permanent color - With silk protein',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'so-pure-color',
        name: 'So Pure Color',
        description: 'Permanent color - Ammonia-free organic formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'semi-color',
        name: 'Semi Color',
        description: 'Demi-permanent color - Ammonia-free formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'bleaching-powder',
        name: 'Bleaching Powder',
        description: 'Lightening products',
        category: 'bleaching',
        isColorLine: true,
      },
    ],
  },

  // British Brands
  {
    id: 'tigi',
    name: 'TIGI Professional',
    country: 'United Kingdom',
    description: 'British creative hair color',
    lines: [
      {
        id: 'creative',
        name: 'Creative',
        description: 'Fashion color line',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'colour',
        name: 'Colour',
        description: 'Permanent hair color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'blonde',
        name: 'Blonde',
        description: 'Lightening system',
        category: 'bleaching',
        isColorLine: false,
      },
    ],
  },
  {
    id: 'wella-uk',
    name: 'Wella UK',
    country: 'United Kingdom',
    description: 'British Wella division',
    lines: [
      {
        id: 'professionals',
        name: 'Professionals',
        description: 'UK professional line',
      },
    ],
  },

  // Australian Brands
  {
    id: 'kevin-murphy',
    name: 'Kevin Murphy',
    country: 'Australia',
    description: 'Australian luxury hair color',
    lines: [
      {
        id: 'color-me',
        name: 'Color.Me',
        description: 'Fashion color line',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'blonde-angel',
        name: 'Blonde.Angel',
        description: 'Lightening treatment',
      },
    ],
  },

  // Canadian Brands
  {
    id: 'schwarzkopf-canada',
    name: 'Schwarzkopf Canada',
    country: 'Canada',
    description: 'Canadian professional division',
    lines: [
      {
        id: 'igora-ca',
        name: 'Igora CA',
        description: 'Canadian color line',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },

  // Brazilian Brands
  {
    id: 'amend',
    name: 'Amend',
    country: 'Brazil',
    description: 'Brazilian professional hair care',
    lines: [
      {
        id: 'color',
        name: 'Color',
        description: 'Professional color line',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'expertise',
        name: 'Expertise',
        description: 'Premium color',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'felps',
    name: 'Felps Professional',
    country: 'Brazil',
    description: 'Brazilian hair color innovation',
    lines: [
      {
        id: 'color',
        name: 'Color',
        description: 'Permanent hair color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'omega',
        name: 'Omega',
        description: 'Hair treatment color',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },

  // Korean Brands
  {
    id: 'mise-en-scene',
    name: 'Mise En Scene',
    country: 'South Korea',
    description: 'Korean professional hair color',
    lines: [
      {
        id: 'hello-bubble',
        name: 'Hello Bubble',
        description: 'Foam hair color',
      },
      {
        id: 'perfect',
        name: 'Perfect',
        description: 'Professional color',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },

  // Russian Brands
  {
    id: 'estel',
    name: 'Estel Professional',
    country: 'Russia',
    description: 'Russian professional hair color',
    lines: [
      {
        id: 'de-luxe',
        name: 'De Luxe',
        description: 'Premium color line',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'essex',
        name: 'Essex',
        description: 'Professional color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'princess',
        name: 'Princess Essex',
        description: 'Ammonia-free color',
      },
      {
        id: 'haute-couture',
        name: 'Haute Couture',
        description: 'Fashion color',
      },
    ],
  },
  {
    id: 'kapous',
    name: 'Kapous Professional',
    country: 'Russia',
    description: 'Russian hair color brand',
    lines: [
      {
        id: 'hyaluronic',
        name: 'Hyaluronic',
        description: 'Hyaluronic acid color',
      },
      {
        id: 'magic-keratin',
        name: 'Magic Keratin',
        description: 'Keratin color',
      },
      {
        id: 'non-ammonia',
        name: 'Non Ammonia',
        description: 'Ammonia-free color',
      },
    ],
  },

  // Polish Brands
  {
    id: 'indola',
    name: 'Indola',
    country: 'Poland',
    description: 'Polish professional hair color',
    lines: [
      {
        id: 'permanent',
        name: 'Permanent Caring Color',
        description: 'Caring permanent color',
      },
      {
        id: 'rapid',
        name: 'Rapid Blond',
        description: 'Fast lightening',
        category: 'bleaching',
        isColorLine: false,
      },
      {
        id: 'profession',
        name: 'Profession',
        description: 'Professional color line',
      },
    ],
  },

  // Czech Brands
  {
    id: 'subrina',
    name: 'Subrina Professional',
    country: 'Czech Republic',
    description: 'Czech professional hair color',
    lines: [
      {
        id: 'unique',
        name: 'Unique',
        description: 'Professional color line',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'mad-touch',
        name: 'Mad Touch',
        description: 'Fashion color',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },

  // Swedish Brands
  {
    id: 'maria-nila',
    name: 'Maria Nila',
    country: 'Sweden',
    description: 'Swedish sustainable hair color',
    lines: [
      {
        id: 'colour-refresh',
        name: 'Colour Refresh',
        description: 'Color depositing mask',
      },
      {
        id: 'pure-color',
        name: 'Pure Color',
        description: 'Vegan hair color',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },

  // Norwegian Brands
  {
    id: 'cutrin',
    name: 'Cutrin',
    country: 'Norway',
    description: 'Nordic professional hair color',
    lines: [
      {
        id: 'aurora',
        name: 'Aurora',
        description: 'Permanent color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'reflection',
        name: 'Reflection',
        description: 'Demi-permanent color',
      },
    ],
  },

  // Additional International Brands
  {
    id: 'fanola',
    name: 'Fanola',
    country: 'Italy',
    description: 'Italian professional hair care',
    lines: [
      {
        id: 'color',
        name: 'Color',
        description: 'Permanent hair color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'no-yellow',
        name: 'No Yellow',
        description: 'Anti-yellow shampoo',
      },
      {
        id: 'botugen',
        name: 'Botugen',
        description: 'Reconstructive treatment',
      },
    ],
  },
  {
    id: 'lisap',
    name: 'Lisap Milano',
    country: 'Italy',
    description: 'Italian hair color innovation',
    lines: [
      {
        id: 'lisaplex',
        name: 'LisapLex',
        description: 'Bond builder system',
        category: 'treatment',
        isColorLine: false,
      },
      {
        id: 'easy-absolute',
        name: 'Easy Absolute',
        description: 'Ammonia-free color',
      },
      {
        id: 'splendor',
        name: 'Splendor',
        description: 'Permanent color',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'bbcos',
    name: 'BBCos',
    country: 'Italy',
    description: 'Italian professional hair color',
    lines: [
      {
        id: 'keratin',
        name: 'Keratin Color',
        description: 'Keratin-enriched color',
      },
      {
        id: 'innovation',
        name: 'Innovation Evo',
        description: 'Advanced color technology',
      },
    ],
  },
  {
    id: 'farmavita',
    name: 'Farmavita',
    country: 'Italy',
    description: 'Italian hair color expertise',
    lines: [
      {
        id: 'life-color',
        name: 'Life Color Plus',
        description: 'Permanent hair color',
      },
      {
        id: 'bleach',
        name: 'Bleach',
        description: 'Lightening powder',
        category: 'bleaching',
        isColorLine: false,
      },
    ],
  },
  {
    id: 'vitality',
    name: "Vitality's",
    country: 'Italy',
    description: 'Italian natural hair color',
    lines: [
      {
        id: 'tone-intense',
        name: 'Tone Intense',
        description: 'Intensive color',
      },
      {
        id: 'art',
        name: 'Art',
        description: 'Creative color line',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'echosline',
    name: 'Echosline',
    country: 'Italy',
    description: 'Italian professional hair care',
    lines: [
      {
        id: 'color',
        name: 'Color',
        description: 'Professional color line',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'fashion',
        name: 'Fashion Color',
        description: 'Trend colors',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'green-light',
    name: 'Green Light',
    country: 'Italy',
    description: 'Italian eco-friendly hair color',
    lines: [
      {
        id: 'color',
        name: 'Color',
        description: 'Eco-friendly color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'keratin',
        name: 'Keratin',
        description: 'Keratin color line',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },
  {
    id: 'hair-company',
    name: 'Hair Company',
    country: 'Italy',
    description: 'Italian hair color innovation',
    lines: [
      {
        id: 'inimitable',
        name: 'Inimitable',
        description: 'Premium color line',
      },
      {
        id: 'professional',
        name: 'Professional',
        description: 'Professional color',
      },
    ],
  },
  {
    id: 'oyster',
    name: 'Oyster Cosmetics',
    country: 'Italy',
    description: 'Italian professional hair color',
    lines: [
      {
        id: 'perlacolor',
        name: 'Perlacolor',
        description: 'Pearl-enriched color',
      },
      {
        id: 'perlaplus',
        name: 'Perlaplus',
        description: 'Advanced color system',
      },
    ],
  },
  {
    id: 'dikson',
    name: 'Dikson',
    country: 'Italy',
    description: 'Italian hair color tradition',
    lines: [
      {
        id: 'color',
        name: 'Color',
        description: 'Traditional color line',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'drop-color',
        name: 'Drop Color',
        description: 'Liquid color',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'bionike',
    name: 'BioNike',
    country: 'Italy',
    description: 'Italian dermatological hair color',
    lines: [
      {
        id: 'shine-on',
        name: 'Shine On',
        description: 'Gentle hair color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'defence',
        name: 'Defence',
        description: 'Sensitive scalp color',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'cotril',
    name: 'Cotril',
    country: 'Italy',
    description: 'Italian professional hair care',
    lines: [
      {
        id: 'color',
        name: 'Color',
        description: 'Professional color line',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'creative',
        name: 'Creative Walk',
        description: 'Fashion color',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'maxima',
    name: 'Maxima',
    country: 'Italy',
    description: 'Italian hair color solutions',
    lines: [
      {
        id: 'color',
        name: 'Color',
        description: 'Professional color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'bleach',
        name: 'Bleach',
        description: 'Lightening system',
        category: 'bleaching',
        isColorLine: false,
      },
    ],
  },
  {
    id: 'nouvelle',
    name: 'Nouvelle',
    country: 'Italy',
    description: 'Italian color innovation',
    lines: [
      {
        id: 'hair-color',
        name: 'Hair Color',
        description: 'Professional color line',
      },
      {
        id: 'touch',
        name: 'Touch',
        description: 'Quick color touch-up',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'periche',
    name: 'Periche Professional',
    country: 'Spain',
    description: 'Spanish professional hair color',
    lines: [
      {
        id: 'color',
        name: 'Color',
        description: 'Permanent hair color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'cybercolor',
        name: 'Cybercolor',
        description: 'Advanced color technology',
      },
    ],
  },
  {
    id: 'montibello',
    name: 'Montibello',
    country: 'Spain',
    description: 'Spanish hair color expertise',
    lines: [
      {
        id: 'cromatone',
        name: 'Cromatone',
        description: 'Permanent color - Standard formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'denuee',
        name: 'Dénuée',
        description: 'Permanent color - Ammonia-free water-based formula',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'eclat',
        name: 'Éclat',
        description: 'Demi-permanent color - Acidic pH toner',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'kativa',
    name: 'Kativa',
    country: 'Spain',
    description: 'Spanish natural hair care',
    lines: [
      {
        id: 'keratin',
        name: 'Keratin',
        description: 'Keratin color treatment',
      },
      {
        id: 'collagen',
        name: 'Collagen',
        description: 'Anti-aging color',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'exitenn',
    name: 'Exitenn',
    country: 'Spain',
    description: 'Spanish professional hair color',
    lines: [
      {
        id: 'color',
        name: 'Color',
        description: 'Professional color line',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'fashion',
        name: 'Fashion Color',
        description: 'Trend colors',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'nirvel',
    name: 'Nirvel Professional',
    country: 'Spain',
    description: 'Spanish hair color innovation',
    lines: [
      {
        id: 'color',
        name: 'Color',
        description: 'Professional color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'artx',
        name: 'ArtX',
        description: 'Creative color line',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'postquam',
    name: 'Postquam Professional',
    country: 'Spain',
    description: 'Spanish professional hair care',
    lines: [
      {
        id: 'color',
        name: 'Color',
        description: 'Professional color line',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'keratin',
        name: 'Keratin',
        description: 'Keratin color treatment',
      },
    ],
  },
  {
    id: 'eugene-color',
    name: 'Eugène Color',
    country: 'France',
    description: 'French color expertise',
    lines: [
      {
        id: 'professional',
        name: 'Professional',
        description: 'Professional color line',
      },
      {
        id: 'fashion',
        name: 'Fashion',
        description: 'Fashion color',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'subtil',
    name: 'Subtil',
    country: 'France',
    description: 'French professional hair color',
    lines: [
      {
        id: 'color',
        name: 'Color',
        description: 'Professional color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'blonde',
        name: 'Blonde',
        description: 'Lightening system',
        category: 'bleaching',
        isColorLine: false,
      },
    ],
  },
  {
    id: 'ducastel',
    name: 'Ducastel Subtil',
    country: 'France',
    description: 'French hair color tradition',
    lines: [
      {
        id: 'subtil',
        name: 'Subtil',
        description: 'Traditional color line',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'design',
        name: 'Design',
        description: 'Creative color',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'schwarzkopf-igora',
    name: 'Schwarzkopf Igora',
    country: 'Germany',
    description: 'German Igora specialist line',
    lines: [
      {
        id: 'royal-absolutes',
        name: 'Royal Absolutes',
        description: 'Mature hair color',
      },
      {
        id: 'royal-disheveled',
        name: 'Royal Disheveled',
        description: 'Fashion color',
      },
    ],
  },
  {
    id: 'lanza',
    name: 'Lanza',
    country: 'USA',
    description: 'American healing hair color',
    lines: [
      {
        id: 'healing',
        name: 'Healing',
        description: 'Healing color system',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'trauma',
        name: 'Trauma Treatment',
        description: 'Reconstructive color',
      },
    ],
  },
  {
    id: 'rusk',
    name: 'Rusk',
    country: 'USA',
    description: 'American professional hair color',
    lines: [
      {
        id: 'deepshine',
        name: 'Deepshine',
        description: 'Color-enhancing system',
      },
      {
        id: 'color',
        name: 'Color',
        description: 'Professional color line',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'chi',
    name: 'CHI',
    country: 'USA',
    description: 'American ionic hair color',
    lines: [
      {
        id: 'ionic',
        name: 'Ionic',
        description: 'Ionic color system',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'color',
        name: 'Color',
        description: 'Professional color',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'sebastian',
    name: 'Sebastian Professional',
    country: 'USA',
    description: 'American creative hair color',
    lines: [
      {
        id: 'cellophanes',
        name: 'Cellophanes',
        description: 'Shine color treatment',
      },
      {
        id: 'color',
        name: 'Color',
        description: 'Professional color line',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },
  {
    id: 'tigi-bed-head',
    name: 'TIGI Bed Head',
    country: 'United Kingdom',
    description: 'British creative color',
    lines: [
      {
        id: 'colour',
        name: 'Colour',
        description: 'Creative color line',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'dumb-blonde',
        name: 'Dumb Blonde',
        description: 'Blonde care system',
      },
    ],
  },
  {
    id: 'osmo',
    name: 'Osmo',
    country: 'United Kingdom',
    description: 'British professional hair color',
    lines: [
      {
        id: 'color',
        name: 'Color',
        description: 'Professional color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'ikon',
        name: 'Ikon',
        description: 'Fashion color line',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },

  // Additional American Brands
  {
    id: 'ion',
    name: 'Ion',
    country: 'USA',
    description: 'Professional vibrant colors at Sally Beauty',
    lines: [
      {
        id: 'permanent',
        name: 'Permanent Brights',
        description: 'Vibrant permanent hair color',
      },
      {
        id: 'demi',
        name: 'Demi Permanent',
        description: 'Long-lasting demi-permanent color',
      },
      {
        id: 'brilliance',
        name: 'Color Brilliance',
        description: 'Brilliant shine formula',
      },
      {
        id: 'intensive',
        name: 'Intensive Shine',
        description: 'High-gloss color system',
      },
      {
        id: 'liquid',
        name: 'Liquid Hair Color',
        description: 'Easy-application liquid formula',
      },
    ],
  },
  {
    id: 'arctic-fox',
    name: 'Arctic Fox',
    country: 'USA',
    description: 'Semi-permanent vegan hair color',
    lines: [
      {
        id: 'semi-permanent',
        name: 'Semi-Permanent',
        description: 'Vegan & cruelty-free colors',
      },
      {
        id: 'diluter',
        name: 'Diluter',
        description: 'Mix to create pastel shades',
      },
      {
        id: 'virgin-pink',
        name: 'Virgin Pink Collection',
        description: 'Pink tone variations',
      },
      {
        id: 'aquamarine',
        name: 'Aquamarine Collection',
        description: 'Blue-green shades',
      },
    ],
  },
  {
    id: 'madison-reed',
    name: 'Madison Reed',
    country: 'USA',
    description: 'Professional-grade ammonia-free color',
    lines: [
      {
        id: 'radiant',
        name: 'Radiant Hair Color',
        description: 'Ammonia-free permanent color',
      },
      {
        id: 'root-touch-up',
        name: 'Root Touch Up',
        description: 'Quick root coverage',
      },
      {
        id: 'color-reviving',
        name: 'Color Reviving Gloss',
        description: 'Semi-permanent gloss treatment',
      },
    ],
  },
  {
    id: 'igk',
    name: 'IGK',
    country: 'USA',
    description: 'Modern professional hair color',
    lines: [
      {
        id: 'permanent',
        name: 'Permanent Color Kit',
        description: 'Salon-quality permanent color',
      },
      {
        id: 'foamo',
        name: 'Foamo',
        description: 'Foam-based color system',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'direct-dye',
        name: 'Direct Dye',
        description: 'Vibrant temporary colors',
      },
    ],
  },

  // Additional Brazilian Brands
  {
    id: 'cadiveu',
    name: 'Cadiveu Professional',
    country: 'Brazil',
    description: 'Brazilian professional hair solutions',
    lines: [
      {
        id: 'buriti',
        name: 'Buriti Mechas',
        description: 'Highlighting system with buriti oil',
      },
      {
        id: 'superclear',
        name: 'Superclear',
        description: 'High-lift lightening powder',
      },
      {
        id: 'color',
        name: 'Professional Color',
        description: 'Permanent color line',
      },
    ],
  },
  {
    id: 'truss',
    name: 'Truss Professional',
    country: 'Brazil',
    description: 'High-performance Brazilian hair color',
    lines: [
      {
        id: 'color',
        name: 'Professional Color',
        description: 'Advanced color technology',
      },
      {
        id: 'blond',
        name: 'Specific Blond',
        description: 'Specialized blonde treatments',
      },
      {
        id: 'nano',
        name: 'Nano Regeneration',
        description: 'Color with regenerative treatment',
      },
    ],
  },

  // Mexican Brands
  {
    id: 'recamier',
    name: 'Recamier Professional',
    country: 'Mexico',
    description: 'Mexican professional hair care',
    lines: [
      {
        id: 'saloon-in',
        name: 'SaloonIn',
        description: 'Professional permanent color',
      },
      {
        id: 'keratina',
        name: 'Keratina Color',
        description: 'Keratin-infused color',
      },
      {
        id: 'argan',
        name: 'Argan Color',
        description: 'Argan oil enriched color',
      },
    ],
  },
  {
    id: 'issue',
    name: 'Issue Professional',
    country: 'Mexico',
    description: 'Latin American professional color',
    lines: [
      {
        id: 'colorissue',
        name: 'Colorissue',
        description: 'Permanent hair color',
      },
      {
        id: 'deco',
        name: 'Deco',
        description: 'Lightening products',
        category: 'bleaching',
        isColorLine: false,
      },
      {
        id: 'fantasy',
        name: 'Fantasy Colors',
        description: 'Fashion color collection',
      },
    ],
  },

  // Argentinian Brands
  {
    id: 'fidelite',
    name: 'Fidelité',
    country: 'Argentina',
    description: 'Argentinian professional hair color',
    lines: [
      {
        id: 'coloracion',
        name: 'Coloración Permanente',
        description: 'Permanent color system',
      },
      {
        id: 'nutri-color',
        name: 'Nutri Color',
        description: 'Nourishing color treatment',
      },
      {
        id: 'blonde',
        name: 'Blonde Expert',
        description: 'Blonde specialist line',
      },
    ],
  },

  // Colombian Brands
  {
    id: 'revlon-colombia',
    name: 'Revlon Professional Colombia',
    country: 'Colombia',
    description: 'Colombian division of Revlon Professional',
    lines: [
      {
        id: 'revlonissimo',
        name: 'Revlonissimo Colombia',
        description: 'Adapted for Latin hair',
      },
      {
        id: 'young-color',
        name: 'Young Color',
        description: 'Ammonia-free option',
      },
    ],
  },

  // Chilean Brands
  {
    id: 'saloon-in-chile',
    name: 'Saloon In',
    country: 'Chile',
    description: 'Chilean professional hair color',
    lines: [
      {
        id: 'color-cream',
        name: 'Color Cream',
        description: 'Creamy permanent color',
      },
      {
        id: 'lightening',
        name: 'Lightening System',
        description: 'Professional bleaching',
      },
      {
        id: 'toner',
        name: 'Toner Collection',
        description: 'Toning products',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },

  // Additional Asian Brands
  {
    id: 'napla',
    name: 'Napla',
    country: 'Japan',
    description: 'Japanese hair color innovation',
    lines: [
      {
        id: 'caretect',
        name: 'Caretect',
        description: 'Care-focused color system',
      },
      {
        id: 'n-color',
        name: 'N. Color',
        description: 'Natural color collection',
      },
      {
        id: 'bleach',
        name: 'Bleach Powder',
        description: 'Gentle lightening',
        category: 'bleaching',
        isColorLine: false,
      },
    ],
  },
  {
    id: 'hoyu',
    name: 'Hoyu Professional',
    country: 'Japan',
    description: 'Japanese color technology leader',
    lines: [
      {
        id: 'promaster',
        name: 'Promaster Color Care',
        description: 'Professional color system',
      },
      {
        id: 'somarca',
        name: 'Somarca',
        description: 'Fashion color line',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'glamage',
        name: 'Glamage',
        description: 'Premium color collection',
      },
    ],
  },

  // African Brands
  {
    id: 'dark-and-lovely',
    name: 'Dark & Lovely Professional',
    country: 'South Africa',
    description: 'Professional products for textured hair',
    lines: [
      {
        id: 'fade-resist',
        name: 'Fade Resist',
        description: 'Long-lasting color for textured hair',
      },
      {
        id: 'go-intense',
        name: 'Go Intense',
        description: 'Ultra vibrant colors',
      },
      {
        id: 'precision',
        name: 'Precision Color',
        description: 'Precise color application',
      },
    ],
  },
  {
    id: 'ors',
    name: 'ORS Professional',
    country: 'South Africa',
    description: 'Olive oil-based professional color',
    lines: [
      {
        id: 'olive-oil',
        name: 'Olive Oil Color',
        description: 'Nourishing color system',
      },
      {
        id: 'professional',
        name: 'Professional Line',
        description: 'Salon-grade products',
      },
    ],
  },

  // Additional European Brands
  {
    id: 'alter-ego',
    name: 'Alter Ego Italy',
    country: 'Italy',
    description: 'Italian professional excellence',
    lines: [
      {
        id: 'technofruit',
        name: 'TechnoFruit Color',
        description: 'Fruit acid technology',
      },
      {
        id: 'blondego',
        name: 'BlondEgo',
        description: 'Blonde specialist range',
      },
      {
        id: 'color-ego',
        name: 'ColorEgo',
        description: 'Permanent color system',
      },
    ],
  },
  {
    id: 'be-hair',
    name: 'Be Hair',
    country: 'Italy',
    description: 'Italian sustainable hair color',
    lines: [
      {
        id: 'be-color',
        name: 'Be Color',
        description: 'Eco-friendly permanent color',
      },
      {
        id: '12-minute',
        name: '12 Minute',
        description: 'Fast-acting color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'plex',
        name: 'Plex System',
        description: 'Bond-building color',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },

  // Turkish Brands
  {
    id: 'maxx-deluxe',
    name: 'Maxx Deluxe',
    country: 'Turkey',
    description: 'Turkish professional hair color',
    lines: [
      {
        id: 'premium',
        name: 'Premium Color',
        description: 'High-quality permanent color',
      },
      {
        id: 'lightening',
        name: 'Lightening Powder',
        description: 'Professional bleaching',
      },
      {
        id: 'toner',
        name: 'Toner Series',
        description: 'Toning collection',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },

  // Indian Brands
  {
    id: 'streax',
    name: 'Streax Professional',
    country: 'India',
    description: 'Indian professional hair color',
    lines: [
      {
        id: 'professional',
        name: 'Professional Color',
        description: 'Salon-grade color',
      },
      {
        id: 'insta-shine',
        name: 'Insta Shine',
        description: 'Instant shine color',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'hair-serum',
        name: 'Color Serum',
        description: 'Serum-based color',
        category: 'treatment',
        isColorLine: false,
      },
    ],
  },

  {
    id: 'fanola-professional',
    name: 'Fanola Professional',
    country: 'Italy',
    description: 'Italian professional hair color, blonde specialist',
    lines: [
      {
        id: 'fanola-color',
        name: 'Fanola Color',
        description: 'Professional permanent color system',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'polvere-decolorante',
        name: 'Polvere Decolorante',
        description: 'Professional bleaching powder up to 8 levels',
        category: 'bleaching',
        isColorLine: true,
      },
      {
        id: 'no-yellow-shampoo',
        name: 'No Yellow Shampoo',
        description: 'Anti-yellow toning system for blondes',
        category: 'hair-color',
        isColorLine: true,
      },
    ],
  },

  {
    id: 'tec-italy-professional',
    name: 'TEC Italy Professional',
    country: 'Italy',
    description: 'Italian professional color innovation',
    lines: [
      {
        id: 'designer-color',
        name: 'Designer Color',
        description: 'Professional color with real tonalities and vibrant reflects',
        category: 'hair-color',
        isColorLine: true,
      },
      {
        id: 'lumina-lightener',
        name: 'Lumina Lightener',
        description: 'Professional lightening system',
        category: 'bleaching',
        isColorLine: true,
      },
    ],
  },
];

export const getLinesByBrandId = (brandId: string): ProductLine[] => {
  const brand = professionalHairColorBrands.find(b => b.id === brandId);
  return brand ? brand.lines : [];
};

/**
 * Obtiene solo las líneas FORMULABLES de una marca
 * (líneas que pueden generar fórmulas de coloración)
 */
export const getColorLinesByBrandId = (brandId: string): ProductLine[] => {
  const brand = professionalHairColorBrands.find(b => b.id === brandId);
  return brand ? brand.lines.filter(line => line.isColorLine === true) : [];
};

/**
 * Valida si una línea debería ser formulable según su categoría
 */
export const isFormulableLine = (line: ProductLine): boolean => {
  return line.category === 'hair-color' || line.category === 'bleaching';
};

/**
 * Obtiene marcas que tienen al menos una línea formulable
 */
export const getBrandsWithFormulableLines = (): Brand[] => {
  return professionalHairColorBrands.filter(brand =>
    brand.lines.some(line => line.isColorLine === true)
  );
};

export const getBrandById = (brandId: string): Brand | undefined => {
  return professionalHairColorBrands.find(b => b.id === brandId);
};

export const searchBrands = (query: string): Brand[] => {
  if (!query.trim()) return professionalHairColorBrands;

  const lowercaseQuery = query.toLowerCase();
  return professionalHairColorBrands.filter(
    brand =>
      brand.name.toLowerCase().includes(lowercaseQuery) ||
      brand.country.toLowerCase().includes(lowercaseQuery) ||
      brand.lines.some(line => line.name.toLowerCase().includes(lowercaseQuery))
  );
};

/**
 * Busca marcas solo entre aquellas que tienen líneas formulables
 */
export const searchFormulableBrands = (query: string): Brand[] => {
  const brandsWithFormulableLines = getBrandsWithFormulableLines();

  if (!query.trim()) return brandsWithFormulableLines;

  const lowercaseQuery = query.toLowerCase();
  return brandsWithFormulableLines.filter(
    brand =>
      brand.name.toLowerCase().includes(lowercaseQuery) ||
      brand.country.toLowerCase().includes(lowercaseQuery) ||
      brand.lines.some(line => line.isColorLine && line.name.toLowerCase().includes(lowercaseQuery))
  );
};

/**
 * Valida que todas las líneas tengan la estructura correcta
 * y detecta inconsistencias en la categorización
 */
export const validateBrandLines = (): { valid: boolean; issues: string[]; warnings: string[] } => {
  const issues: string[] = [];
  const warnings: string[] = [];

  professionalHairColorBrands.forEach(brand => {
    brand.lines.forEach(line => {
      // Errores críticos
      if (!line.category) {
        issues.push(`${brand.name} - ${line.name}: Falta categoría`);
      }
      if (line.isColorLine === undefined) {
        issues.push(`${brand.name} - ${line.name}: Falta isColorLine`);
      }

      // Advertencias de inconsistencias
      if (line.category && line.isColorLine !== undefined) {
        const shouldBeFormulable = isFormulableLine(line);
        if (shouldBeFormulable && !line.isColorLine) {
          warnings.push(
            `${brand.name} - ${line.name}: Debería ser formulable (categoría: ${line.category})`
          );
        }
        if (!shouldBeFormulable && line.isColorLine) {
          warnings.push(
            `${brand.name} - ${line.name}: No debería ser formulable (categoría: ${line.category})`
          );
        }
      }
    });

    // Advertencia si la marca no tiene líneas formulables
    const formulableLines = brand.lines.filter(line => line.isColorLine === true);
    if (formulableLines.length === 0) {
      warnings.push(`${brand.name}: No tiene líneas formulables configuradas`);
    }
  });

  return {
    valid: issues.length === 0,
    issues,
    warnings,
  };
};

/**
 * Obtiene estadísticas de líneas por categoría
 */
export const getBrandLinesStats = () => {
  const stats = {
    total: 0,
    colorLines: 0,
    treatmentLines: 0,
    stylingLines: 0,
    bleachingLines: 0,
    developerLines: 0,
    otherLines: 0,
    brandsWithoutColorLines: 0,
    brandsWithColorLines: 0,
  };

  professionalHairColorBrands.forEach(brand => {
    const brandColorLines = brand.lines.filter(line => line.isColorLine === true);
    if (brandColorLines.length > 0) {
      stats.brandsWithColorLines++;
    } else {
      stats.brandsWithoutColorLines++;
    }

    brand.lines.forEach(line => {
      stats.total++;
      if (line.isColorLine) {
        stats.colorLines++;
      }
      switch (line.category) {
        case 'treatment':
          stats.treatmentLines++;
          break;
        case 'styling':
          stats.stylingLines++;
          break;
        case 'bleaching':
          stats.bleachingLines++;
          break;
        case 'developer':
          stats.developerLines++;
          break;
        case 'other':
          stats.otherLines++;
          break;
      }
    });
  });

  return stats;
};

/**
 * Obtiene marcas ordenadas por popularidad/importancia internacional
 */
export const getBrandsByPopularity = (): Brand[] => {
  // Orden de popularidad basado en presencia internacional y uso profesional
  const popularityOrder = [
    // Tier 1: Marcas globales premium
    'wella',
    'loreal',
    'schwarzkopf',
    'redken',
    'matrix',
    'goldwell',

    // Tier 2: Marcas internacionales reconocidas
    'alfaparf',
    'joico',
    'pravana',
    'revlon',
    'indola',
    'keune-professional',

    // Tier 3: Marcas españolas y europeas
    'salerm',
    'arkhe',
    'lendan',
    'tahe',
    'davines-professional',
    'inebrya',
    'fanola-professional',
    'tec-italy-professional',

    // Tier 4: Marcas americanas especializadas
    'paul-mitchell',
    'kenra',
    'aveda',
    'sebastian-professional',

    // Tier 5: Marcas asiáticas y emergentes
    'milbon',
    'shiseido',
    'napla',
    'hoyu',

    // Tier 6: Marcas regionales importantes
    'lowell',
    'felps',
    'kativa',
    'cadiveu',
    'truss',
  ];

  const orderedBrands: Brand[] = [];
  const remainingBrands = [...professionalHairColorBrands];

  // Agregar marcas en orden de popularidad
  popularityOrder.forEach(brandId => {
    const brandIndex = remainingBrands.findIndex(b => b.id === brandId);
    if (brandIndex !== -1) {
      orderedBrands.push(remainingBrands[brandIndex]);
      remainingBrands.splice(brandIndex, 1);
    }
  });

  // Agregar marcas restantes al final
  orderedBrands.push(...remainingBrands);

  return orderedBrands;
};

/**
 * Obtiene marcas recomendadas por región
 */
export const getRecommendedBrandsByRegion = (region: string): Brand[] => {
  const regionalRecommendations: Record<string, string[]> = {
    Europe: [
      'wella',
      'loreal',
      'schwarzkopf',
      'goldwell',
      'salerm',
      'arkhe',
      'davines-professional',
      'keune-professional',
    ],
    'North America': ['redken', 'matrix', 'paul-mitchell', 'kenra', 'joico', 'pravana', 'aveda'],
    'South America': ['loreal', 'wella', 'alfaparf', 'lowell', 'felps', 'kativa', 'cadiveu'],
    Asia: ['milbon', 'shiseido', 'napla', 'hoyu', 'wella', 'loreal', 'schwarzkopf'],
    Spain: ['salerm', 'arkhe', 'lendan', 'tahe', 'wella', 'loreal', 'schwarzkopf'],
    Italy: [
      'alfaparf',
      'davines-professional',
      'inebrya',
      'fanola-professional',
      'tec-italy-professional',
    ],
    Germany: ['wella', 'schwarzkopf', 'goldwell', 'kadus'],
    France: ['loreal', 'eugene-perma', 'phyto', 'subtil'],
    Netherlands: ['keune-professional', 'wella', 'loreal'],
    USA: ['redken', 'matrix', 'paul-mitchell', 'joico', 'pravana', 'kenra', 'aveda'],
    Brazil: ['lowell', 'felps', 'cadiveu', 'truss', 'amend'],
    Japan: ['milbon', 'shiseido', 'napla', 'hoyu', 'lebel'],
  };

  const recommendedIds = regionalRecommendations[region] || [];
  return recommendedIds
    .map(id => professionalHairColorBrands.find(b => b.id === id))
    .filter(Boolean) as Brand[];
};

export const getBrandsByCountry = (country: string): Brand[] => {
  return professionalHairColorBrands.filter(brand => brand.country === country);
};

export const getAllCountries = (): string[] => {
  const countries = [...new Set(professionalHairColorBrands.map(brand => brand.country))];
  return countries.sort();
};

/**
 * Información técnica por marca
 */
export interface BrandTechnicalInfo {
  numberingSystem: string;
  maxDeveloperVolume: number;
  specialFeatures: string[];
  compatibleBrands?: string[];
}

export const getBrandTechnicalInfo = (brandId: string): BrandTechnicalInfo | null => {
  const technicalData: Record<string, BrandTechnicalInfo> = {
    wella: {
      numberingSystem: 'International system with / (e.g., 8/38)',
      maxDeveloperVolume: 40,
      specialFeatures: ['ME+ technology', 'Microlight technology', 'Anti-yellow molecules'],
      compatibleBrands: ['schwarzkopf', 'goldwell'],
    },
    loreal: {
      numberingSystem: 'Point system with . (e.g., 8.3)',
      maxDeveloperVolume: 40,
      specialFeatures: ['Ionène G + Incell', 'Oil Delivery System', 'Carmilane micro-pigments'],
      compatibleBrands: ['redken', 'matrix'],
    },
    schwarzkopf: {
      numberingSystem: 'Dash system with - (e.g., 8-4)',
      maxDeveloperVolume: 40,
      specialFeatures: ['Fibreplex technology', 'BlondMe bonding', 'Royal Absolutes'],
      compatibleBrands: ['wella', 'goldwell'],
    },
    salerm: {
      numberingSystem: 'Traditional European system',
      maxDeveloperVolume: 40,
      specialFeatures: ['Biokera natural oils', 'Zero ammonia-free', 'Vison coverage'],
      compatibleBrands: ['wella', 'loreal'],
    },
    arkhe: {
      numberingSystem: 'Modern European system',
      maxDeveloperVolume: 40,
      specialFeatures: [
        'Color Pure technology',
        'Well-aging formulas',
        'Premium Spanish innovation',
      ],
      compatibleBrands: ['salerm', 'wella'],
    },
    redken: {
      numberingSystem: 'American system with letters (e.g., 8N)',
      maxDeveloperVolume: 40,
      specialFeatures: ['Acidic pH technology', 'Chromatics ultra-rich', 'Science-based formulas'],
      compatibleBrands: ['matrix', 'loreal'],
    },
    matrix: {
      numberingSystem: 'American system with letters (e.g., 8N)',
      maxDeveloperVolume: 40,
      specialFeatures: ['SoColor technology', 'ColorSync deposit-only', 'Light Master system'],
      compatibleBrands: ['redken', 'loreal'],
    },
    joico: {
      numberingSystem: 'American system with letters (e.g., 8N)',
      maxDeveloperVolume: 40,
      specialFeatures: ['Quadramine complex', 'ArgiPlex technology', 'SmartRelease system'],
      compatibleBrands: ['redken', 'matrix'],
    },
    alfaparf: {
      numberingSystem: 'European system with . (e.g., 8.3)',
      maxDeveloperVolume: 40,
      specialFeatures: ['Hyaluronic Acid', 'Urban Defense Pro', 'Organic extracts'],
      compatibleBrands: ['wella', 'loreal'],
    },
    kadus: {
      numberingSystem: 'European system with / (e.g., 8/3)',
      maxDeveloperVolume: 40,
      specialFeatures: ['German precision', 'Vibrant results', 'Creative solutions'],
      compatibleBrands: ['wella', 'schwarzkopf'],
    },
  };

  return technicalData[brandId] || null;
};

/**
 * Obtiene marcas compatibles para conversiones
 */
export const getCompatibleBrands = (brandId: string): Brand[] => {
  const techInfo = getBrandTechnicalInfo(brandId);
  if (!techInfo?.compatibleBrands) return [];

  return techInfo.compatibleBrands
    .map(id => professionalHairColorBrands.find(b => b.id === id))
    .filter(Boolean) as Brand[];
};
