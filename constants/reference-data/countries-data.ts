import { CountryInfo, CountryCode } from '@/types/regional';

export const countriesData: CountryInfo[] = [
  // ========== EUROPE ==========
  {
    code: 'ES',
    name: 'Spain',
    localName: 'España',
    flag: '🇪🇸',
    config: {
      countryCode: 'ES',
      countryName: 'España',
      region: 'Europe',
      measurementSystem: 'metric',
      currency: 'EUR',
      currencySymbol: '€',
      language: 'es',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '24h',
      decimalSeparator: ',',
      thousandsSeparator: '.',
      volumeUnit: 'ml',
      weightUnit: 'g',
      developerTerminology: 'oxidante',
      colorTerminology: 'tinte',
      requiresAllergyTest: false,
      maxDeveloperVolume: 40,
    },
  },
  {
    code: 'FR',
    name: 'France',
    localName: 'France',
    flag: '🇫🇷',
    config: {
      countryCode: 'FR',
      countryName: 'France',
      region: 'Europe',
      measurementSystem: 'metric',
      currency: 'EUR',
      currencySymbol: '€',
      language: 'fr',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '24h',
      decimalSeparator: ',',
      thousandsSeparator: ' ',
      volumeUnit: 'ml',
      weightUnit: 'g',
      developerTerminology: 'révélateur',
      colorTerminology: 'couleur',
      requiresAllergyTest: true,
      maxDeveloperVolume: 30,
    },
  },
  {
    code: 'IT',
    name: 'Italy',
    localName: 'Italia',
    flag: '🇮🇹',
    config: {
      countryCode: 'IT',
      countryName: 'Italia',
      region: 'Europe',
      measurementSystem: 'metric',
      currency: 'EUR',
      currencySymbol: '€',
      language: 'it',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '24h',
      decimalSeparator: ',',
      thousandsSeparator: '.',
      volumeUnit: 'ml',
      weightUnit: 'g',
      developerTerminology: 'ossidante',
      colorTerminology: 'tinta',
      requiresAllergyTest: false,
      maxDeveloperVolume: 40,
    },
  },
  {
    code: 'DE',
    name: 'Germany',
    localName: 'Deutschland',
    flag: '🇩🇪',
    config: {
      countryCode: 'DE',
      countryName: 'Deutschland',
      region: 'Europe',
      measurementSystem: 'metric',
      currency: 'EUR',
      currencySymbol: '€',
      language: 'de',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '24h',
      decimalSeparator: ',',
      thousandsSeparator: '.',
      volumeUnit: 'ml',
      weightUnit: 'g',
      developerTerminology: 'oxidant',
      colorTerminology: 'farbe',
      requiresAllergyTest: true,
      maxDeveloperVolume: 30,
    },
  },
  {
    code: 'GB',
    name: 'United Kingdom',
    localName: 'United Kingdom',
    flag: '🇬🇧',
    config: {
      countryCode: 'GB',
      countryName: 'United Kingdom',
      region: 'Europe',
      measurementSystem: 'imperial',
      currency: 'GBP',
      currencySymbol: '£',
      language: 'en',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '12h',
      decimalSeparator: '.',
      thousandsSeparator: ',',
      volumeUnit: 'ml',
      weightUnit: 'g',
      developerTerminology: 'developer',
      colorTerminology: 'color',
      requiresAllergyTest: false,
      maxDeveloperVolume: 40,
    },
  },
  {
    code: 'PT',
    name: 'Portugal',
    localName: 'Portugal',
    flag: '🇵🇹',
    config: {
      countryCode: 'PT',
      countryName: 'Portugal',
      region: 'Europe',
      measurementSystem: 'metric',
      currency: 'EUR',
      currencySymbol: '€',
      language: 'pt',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '24h',
      decimalSeparator: ',',
      thousandsSeparator: ' ',
      volumeUnit: 'ml',
      weightUnit: 'g',
      developerTerminology: 'oxidante',
      colorTerminology: 'coloração',
      requiresAllergyTest: false,
      maxDeveloperVolume: 40,
    },
  },

  // ========== NORTH AMERICA ==========
  {
    code: 'US',
    name: 'United States',
    localName: 'United States',
    flag: '🇺🇸',
    config: {
      countryCode: 'US',
      countryName: 'United States',
      region: 'North America',
      measurementSystem: 'imperial',
      currency: 'USD',
      currencySymbol: '$',
      language: 'en',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: '12h',
      decimalSeparator: '.',
      thousandsSeparator: ',',
      volumeUnit: 'fl oz',
      weightUnit: 'oz',
      developerTerminology: 'developer',
      colorTerminology: 'color',
      requiresAllergyTest: false,
      maxDeveloperVolume: 40,
    },
  },
  {
    code: 'CA',
    name: 'Canada',
    localName: 'Canada',
    flag: '🇨🇦',
    config: {
      countryCode: 'CA',
      countryName: 'Canada',
      region: 'North America',
      measurementSystem: 'metric',
      currency: 'CAD',
      currencySymbol: '$',
      language: 'en',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: '12h',
      decimalSeparator: '.',
      thousandsSeparator: ',',
      volumeUnit: 'ml',
      weightUnit: 'g',
      developerTerminology: 'developer',
      colorTerminology: 'color',
      requiresAllergyTest: false,
      maxDeveloperVolume: 40,
    },
  },
  {
    code: 'MX',
    name: 'Mexico',
    localName: 'México',
    flag: '🇲🇽',
    config: {
      countryCode: 'MX',
      countryName: 'México',
      region: 'North America',
      measurementSystem: 'metric',
      currency: 'MXN',
      currencySymbol: '$',
      language: 'es',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '24h',
      decimalSeparator: '.',
      thousandsSeparator: ',',
      volumeUnit: 'ml',
      weightUnit: 'g',
      developerTerminology: 'peróxido',
      colorTerminology: 'tinte',
      requiresAllergyTest: false,
      maxDeveloperVolume: 40,
    },
  },

  // ========== CENTRAL AMERICA ==========
  {
    code: 'GT',
    name: 'Guatemala',
    localName: 'Guatemala',
    flag: '🇬🇹',
    config: {
      countryCode: 'GT',
      countryName: 'Guatemala',
      region: 'Central America',
      measurementSystem: 'metric',
      currency: 'USD',
      currencySymbol: '$',
      language: 'es',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '12h',
      decimalSeparator: '.',
      thousandsSeparator: ',',
      volumeUnit: 'ml',
      weightUnit: 'g',
      developerTerminology: 'peróxido',
      colorTerminology: 'tinte',
      requiresAllergyTest: false,
      maxDeveloperVolume: 40,
    },
  },
  {
    code: 'CR',
    name: 'Costa Rica',
    localName: 'Costa Rica',
    flag: '🇨🇷',
    config: {
      countryCode: 'CR',
      countryName: 'Costa Rica',
      region: 'Central America',
      measurementSystem: 'metric',
      currency: 'USD',
      currencySymbol: '$',
      language: 'es',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '12h',
      decimalSeparator: ',',
      thousandsSeparator: '.',
      volumeUnit: 'ml',
      weightUnit: 'g',
      developerTerminology: 'peróxido',
      colorTerminology: 'tinte',
      requiresAllergyTest: false,
      maxDeveloperVolume: 40,
    },
  },
  {
    code: 'PA',
    name: 'Panama',
    localName: 'Panamá',
    flag: '🇵🇦',
    config: {
      countryCode: 'PA',
      countryName: 'Panamá',
      region: 'Central America',
      measurementSystem: 'metric',
      currency: 'USD',
      currencySymbol: '$',
      language: 'es',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '12h',
      decimalSeparator: '.',
      thousandsSeparator: ',',
      volumeUnit: 'ml',
      weightUnit: 'g',
      developerTerminology: 'peróxido',
      colorTerminology: 'tinte',
      requiresAllergyTest: false,
      maxDeveloperVolume: 40,
    },
  },

  // ========== CARIBBEAN ==========
  {
    code: 'DO',
    name: 'Dominican Republic',
    localName: 'República Dominicana',
    flag: '🇩🇴',
    config: {
      countryCode: 'DO',
      countryName: 'República Dominicana',
      region: 'Caribbean',
      measurementSystem: 'metric',
      currency: 'USD',
      currencySymbol: '$',
      language: 'es',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '12h',
      decimalSeparator: '.',
      thousandsSeparator: ',',
      volumeUnit: 'ml',
      weightUnit: 'g',
      developerTerminology: 'peróxido',
      colorTerminology: 'tinte',
      requiresAllergyTest: false,
      maxDeveloperVolume: 40,
    },
  },
  {
    code: 'PR',
    name: 'Puerto Rico',
    localName: 'Puerto Rico',
    flag: '🇵🇷',
    config: {
      countryCode: 'PR',
      countryName: 'Puerto Rico',
      region: 'Caribbean',
      measurementSystem: 'imperial',
      currency: 'USD',
      currencySymbol: '$',
      language: 'es',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: '12h',
      decimalSeparator: '.',
      thousandsSeparator: ',',
      volumeUnit: 'fl oz',
      weightUnit: 'oz',
      developerTerminology: 'developer',
      colorTerminology: 'tinte',
      requiresAllergyTest: false,
      maxDeveloperVolume: 40,
    },
  },

  // ========== SOUTH AMERICA ==========
  {
    code: 'AR',
    name: 'Argentina',
    localName: 'Argentina',
    flag: '🇦🇷',
    config: {
      countryCode: 'AR',
      countryName: 'Argentina',
      region: 'South America',
      measurementSystem: 'metric',
      currency: 'ARS',
      currencySymbol: '$',
      language: 'es',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '24h',
      decimalSeparator: ',',
      thousandsSeparator: '.',
      volumeUnit: 'ml',
      weightUnit: 'g',
      developerTerminology: 'oxidante',
      colorTerminology: 'tinte',
      requiresAllergyTest: false,
      maxDeveloperVolume: 40,
    },
  },
  {
    code: 'BR',
    name: 'Brazil',
    localName: 'Brasil',
    flag: '🇧🇷',
    config: {
      countryCode: 'BR',
      countryName: 'Brasil',
      region: 'South America',
      measurementSystem: 'metric',
      currency: 'BRL',
      currencySymbol: 'R$',
      language: 'pt',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '24h',
      decimalSeparator: ',',
      thousandsSeparator: '.',
      volumeUnit: 'ml',
      weightUnit: 'g',
      developerTerminology: 'oxidante',
      colorTerminology: 'coloração',
      requiresAllergyTest: true,
      maxDeveloperVolume: 30,
    },
  },
  {
    code: 'CL',
    name: 'Chile',
    localName: 'Chile',
    flag: '🇨🇱',
    config: {
      countryCode: 'CL',
      countryName: 'Chile',
      region: 'South America',
      measurementSystem: 'metric',
      currency: 'CLP',
      currencySymbol: '$',
      language: 'es',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '24h',
      decimalSeparator: ',',
      thousandsSeparator: '.',
      volumeUnit: 'ml',
      weightUnit: 'g',
      developerTerminology: 'oxidante',
      colorTerminology: 'tinte',
      requiresAllergyTest: false,
      maxDeveloperVolume: 40,
    },
  },
  {
    code: 'CO',
    name: 'Colombia',
    localName: 'Colombia',
    flag: '🇨🇴',
    config: {
      countryCode: 'CO',
      countryName: 'Colombia',
      region: 'South America',
      measurementSystem: 'metric',
      currency: 'COP',
      currencySymbol: '$',
      language: 'es',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '12h',
      decimalSeparator: ',',
      thousandsSeparator: '.',
      volumeUnit: 'ml',
      weightUnit: 'g',
      developerTerminology: 'peróxido',
      colorTerminology: 'tinte',
      requiresAllergyTest: false,
      maxDeveloperVolume: 40,
    },
  },
  {
    code: 'PE',
    name: 'Peru',
    localName: 'Perú',
    flag: '🇵🇪',
    config: {
      countryCode: 'PE',
      countryName: 'Perú',
      region: 'South America',
      measurementSystem: 'metric',
      currency: 'PEN',
      currencySymbol: 'S/',
      language: 'es',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '24h',
      decimalSeparator: ',',
      thousandsSeparator: '.',
      volumeUnit: 'ml',
      weightUnit: 'g',
      developerTerminology: 'oxidante',
      colorTerminology: 'tinte',
      requiresAllergyTest: false,
      maxDeveloperVolume: 40,
    },
  },
  {
    code: 'VE',
    name: 'Venezuela',
    localName: 'Venezuela',
    flag: '🇻🇪',
    config: {
      countryCode: 'VE',
      countryName: 'Venezuela',
      region: 'South America',
      measurementSystem: 'metric',
      currency: 'USD',
      currencySymbol: '$',
      language: 'es',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '12h',
      decimalSeparator: ',',
      thousandsSeparator: '.',
      volumeUnit: 'ml',
      weightUnit: 'g',
      developerTerminology: 'peróxido',
      colorTerminology: 'tinte',
      requiresAllergyTest: false,
      maxDeveloperVolume: 40,
    },
  },
  {
    code: 'EC',
    name: 'Ecuador',
    localName: 'Ecuador',
    flag: '🇪🇨',
    config: {
      countryCode: 'EC',
      countryName: 'Ecuador',
      region: 'South America',
      measurementSystem: 'metric',
      currency: 'USD',
      currencySymbol: '$',
      language: 'es',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '24h',
      decimalSeparator: ',',
      thousandsSeparator: '.',
      volumeUnit: 'ml',
      weightUnit: 'g',
      developerTerminology: 'peróxido',
      colorTerminology: 'tinte',
      requiresAllergyTest: false,
      maxDeveloperVolume: 40,
    },
  },
  {
    code: 'UY',
    name: 'Uruguay',
    localName: 'Uruguay',
    flag: '🇺🇾',
    config: {
      countryCode: 'UY',
      countryName: 'Uruguay',
      region: 'South America',
      measurementSystem: 'metric',
      currency: 'UYU',
      currencySymbol: '$',
      language: 'es',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '24h',
      decimalSeparator: ',',
      thousandsSeparator: '.',
      volumeUnit: 'ml',
      weightUnit: 'g',
      developerTerminology: 'oxidante',
      colorTerminology: 'tinte',
      requiresAllergyTest: false,
      maxDeveloperVolume: 40,
    },
  },
];

// Helper functions
export const getCountryByCode = (code: CountryCode): CountryInfo | undefined => {
  return countriesData.find(country => country.code === code);
};

export const getCountriesByRegion = (region: string): CountryInfo[] => {
  return countriesData.filter(country => country.config.region === region);
};

export const getCountriesByLanguage = (language: string): CountryInfo[] => {
  return countriesData.filter(country => country.config.language === language);
};

export const getCountriesByCurrency = (currency: string): CountryInfo[] => {
  return countriesData.filter(country => country.config.currency === currency);
};

// Default configurations by region
export const defaultRegionalConfigs = {
  Europe: getCountryByCode('ES')?.config,
  'North America': getCountryByCode('US')?.config,
  'Central America': getCountryByCode('MX')?.config,
  Caribbean: getCountryByCode('PR')?.config,
  'South America': getCountryByCode('AR')?.config,
};
