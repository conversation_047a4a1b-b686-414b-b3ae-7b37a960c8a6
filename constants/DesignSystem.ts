/**
 * Sistema de Diseño Salonier v2.0
 * Evolución del sistema actual con mejoras en UX/UI profesional
 * Mantiene compatibilidad con colores existentes
 */

import { Platform, Dimensions } from 'react-native';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

// Paleta de colores mejorada (mantiene colores actuales + nuevas variaciones)
export const colors = {
  // Core brand colors (preservados del sistema actual)
  primary: '#B8941F',
  primaryDark: '#8B6F1F',
  primaryLight: '#E6C757',
  secondary: '#D47A3A',

  // Nuevos colores para estados y feedback
  success: '#10B981', // Verde más vibrante
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6', // Azul más moderno

  // Grises refinados para mejor legibilidad
  neutral: {
    50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    700: '#374151',
    800: '#1F2937',
    900: '#111827',
  },

  // Superficie y fondos
  background: '#FFFFFF',
  surface: '#F9FAFB',
  card: '#FFFFFF',

  // Textos con mejor contraste
  text: {
    primary: '#111827', // Mejor contraste que el actual
    secondary: '#4B5563',
    tertiary: '#9CA3AF',
    inverse: '#FFFFFF',
  },

  // Estados interactivos
  interactive: {
    pressed: 'rgba(184, 148, 31, 0.1)',
    focused: 'rgba(184, 148, 31, 0.2)',
    disabled: '#E5E7EB',
  },
};

// Tipografía escalable con mejor jerarquía
export const typography = {
  fontFamily: {
    regular: Platform.select({
      ios: 'SF Pro Display',
      android: 'Roboto',
      default: 'System',
    }),
    mono: Platform.select({
      ios: 'SF Mono',
      android: 'Roboto Mono',
      default: 'monospace',
    }),
  },

  sizes: {
    '2xs': 10,
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
  },

  weights: {
    light: '300',
    regular: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },

  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 2.0,
  },
};

// Espaciado refinado (mantiene grid 8px)
export const spacing = {
  '0': 0,
  px: 1,
  '0.5': 2,
  '1': 4,
  '1.5': 6,
  '2': 8,
  '2.5': 10,
  '3': 12,
  '3.5': 14,
  '4': 16,
  '5': 20,
  '6': 24,
  '7': 28,
  '8': 32,
  '9': 36,
  '10': 40,
  '11': 44,
  '12': 48,
  '14': 56,
  '16': 64,
  '20': 80,
  '24': 96,
  '28': 112,
  '32': 128,
};

// Radios mejorados para mejor fluidez visual
export const radius = {
  none: 0,
  xs: 2,
  sm: 4,
  base: 6,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 20,
  '3xl': 24,
  full: 9999,
};

// Sombras más refinadas con mejor elevación
export const shadows = {
  xs: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 3,
    elevation: 2,
  },
  base: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 6,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 6,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.18,
    shadowRadius: 16,
    elevation: 8,
  },
};

// Animaciones con timing profesional
export const animations = {
  timing: {
    fast: 150,
    normal: 250,
    slow: 350,
    slower: 500,
  },

  easing: {
    easeOut: 'ease-out',
    easeIn: 'ease-in',
    easeInOut: 'ease-in-out',
  },

  spring: {
    gentle: {
      damping: 20,
      stiffness: 300,
    },
    bouncy: {
      damping: 10,
      stiffness: 400,
    },
  },
};

// Breakpoints responsive
export const breakpoints = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
};

// Utilidades responsive
export const responsive = {
  isTablet: SCREEN_WIDTH >= breakpoints.md,
  isLargeScreen: SCREEN_WIDTH >= breakpoints.lg,

  // Touch target mínimo (WCAG AA)
  minTouchTarget: 44,

  // Contenido máximo para legibilidad
  maxContentWidth: 680,
};

// Componentes específicos del sector belleza
export const components = {
  button: {
    height: {
      sm: 36,
      base: 44, // Touch target mínimo
      lg: 52,
      xl: 60,
    },
    padding: {
      sm: { horizontal: spacing['3'], vertical: spacing['2'] },
      base: { horizontal: spacing['4'], vertical: spacing['2.5'] },
      lg: { horizontal: spacing['6'], vertical: spacing['3'] },
      xl: { horizontal: spacing['8'], vertical: spacing['4'] },
    },
  },

  input: {
    height: responsive.minTouchTarget,
    borderWidth: 1.5, // Más visible
    radius: radius.md,
  },

  card: {
    padding: spacing['4'],
    radius: radius.xl,
    shadow: shadows.sm,
  },

  // Específicos para coloración
  colorSwatch: {
    size: {
      sm: 32,
      base: 40,
      lg: 48,
      xl: 56,
    },
    border: 2,
  },

  diagnosticStep: {
    padding: spacing['6'],
    radius: radius['2xl'],
    minHeight: 120,
  },
};

// Iconografía consistente
export const icons = {
  size: {
    xs: 12,
    sm: 16,
    base: 20,
    lg: 24,
    xl: 28,
    '2xl': 32,
  },
};

// Estados de UI para feedback visual
export const states = {
  loading: {
    opacity: 0.6,
    animation: 'pulse',
  },

  success: {
    color: colors.success,
    duration: animations.timing.normal,
  },

  error: {
    color: colors.error,
    shake: true,
    duration: animations.timing.fast,
  },

  disabled: {
    opacity: 0.4,
    interaction: false,
  },
};

export default {
  colors,
  typography,
  spacing,
  radius,
  shadows,
  animations,
  breakpoints,
  responsive,
  components,
  icons,
  states,
};
