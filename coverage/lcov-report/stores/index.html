
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for stores</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> stores</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">8.65% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>182/2103</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">6.15% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>87/1413</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">6.44% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>33/512</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">9.42% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>174/1847</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="active-client-store.ts"><a href="active-client-store.ts.html">active-client-store.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	</tr>

<tr>
	<td class="file medium" data-value="ai-analysis-store.ts"><a href="ai-analysis-store.ts.html">ai-analysis-store.ts</a></td>
	<td data-value="74.5" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 74%"></div><div class="cover-empty" style="width: 26%"></div></div>
	</td>
	<td data-value="74.5" class="pct medium">74.5%</td>
	<td data-value="204" class="abs medium">152/204</td>
	<td data-value="62.3" class="pct medium">62.3%</td>
	<td data-value="130" class="abs medium">81/130</td>
	<td data-value="90.9" class="pct high">90.9%</td>
	<td data-value="22" class="abs high">20/22</td>
	<td data-value="74.74" class="pct medium">74.74%</td>
	<td data-value="194" class="abs medium">145/194</td>
	</tr>

<tr>
	<td class="file low" data-value="auth-store.ts"><a href="auth-store.ts.html">auth-store.ts</a></td>
	<td data-value="1.2" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 1%"></div><div class="cover-empty" style="width: 99%"></div></div>
	</td>
	<td data-value="1.2" class="pct low">1.2%</td>
	<td data-value="249" class="abs low">3/249</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="117" class="abs low">0/117</td>
	<td data-value="4" class="pct low">4%</td>
	<td data-value="25" class="abs low">1/25</td>
	<td data-value="0.85" class="pct low">0.85%</td>
	<td data-value="235" class="abs low">2/235</td>
	</tr>

<tr>
	<td class="file low" data-value="chat-store.ts"><a href="chat-store.ts.html">chat-store.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="280" class="abs low">0/280</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="208" class="abs low">0/208</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="64" class="abs low">0/64</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="252" class="abs low">0/252</td>
	</tr>

<tr>
	<td class="file low" data-value="client-history-store.ts"><a href="client-history-store.ts.html">client-history-store.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="264" class="abs low">0/264</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="224" class="abs low">0/224</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="63" class="abs low">0/63</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="225" class="abs low">0/225</td>
	</tr>

<tr>
	<td class="file low" data-value="client-store.ts"><a href="client-store.ts.html">client-store.ts</a></td>
	<td data-value="2.83" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 2%"></div><div class="cover-empty" style="width: 98%"></div></div>
	</td>
	<td data-value="2.83" class="pct low">2.83%</td>
	<td data-value="106" class="abs low">3/106</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="90" class="abs low">0/90</td>
	<td data-value="6.89" class="pct low">6.89%</td>
	<td data-value="29" class="abs low">2/29</td>
	<td data-value="3.61" class="pct low">3.61%</td>
	<td data-value="83" class="abs low">3/83</td>
	</tr>

<tr>
	<td class="file low" data-value="dashboard-store.ts"><a href="dashboard-store.ts.html">dashboard-store.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="49" class="abs low">0/49</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="28" class="abs low">0/28</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="11" class="abs low">0/11</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="44" class="abs low">0/44</td>
	</tr>

<tr>
	<td class="file low" data-value="inventory-store.ts"><a href="inventory-store.ts.html">inventory-store.ts</a></td>
	<td data-value="1.17" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 1%"></div><div class="cover-empty" style="width: 99%"></div></div>
	</td>
	<td data-value="1.17" class="pct low">1.17%</td>
	<td data-value="512" class="abs low">6/512</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="357" class="abs low">0/357</td>
	<td data-value="1.45" class="pct low">1.45%</td>
	<td data-value="137" class="abs low">2/137</td>
	<td data-value="1.31" class="pct low">1.31%</td>
	<td data-value="457" class="abs low">6/457</td>
	</tr>

<tr>
	<td class="file low" data-value="photo-capture-store.ts"><a href="photo-capture-store.ts.html">photo-capture-store.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	</tr>

<tr>
	<td class="file low" data-value="salon-config-store.ts"><a href="salon-config-store.ts.html">salon-config-store.ts</a></td>
	<td data-value="4.91" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 4%"></div><div class="cover-empty" style="width: 96%"></div></div>
	</td>
	<td data-value="4.91" class="pct low">4.91%</td>
	<td data-value="183" class="abs low">9/183</td>
	<td data-value="3.28" class="pct low">3.28%</td>
	<td data-value="152" class="abs low">5/152</td>
	<td data-value="8.51" class="pct low">8.51%</td>
	<td data-value="47" class="abs low">4/47</td>
	<td data-value="6.08" class="pct low">6.08%</td>
	<td data-value="148" class="abs low">9/148</td>
	</tr>

<tr>
	<td class="file low" data-value="service-draft-store.ts"><a href="service-draft-store.ts.html">service-draft-store.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="41" class="abs low">0/41</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="24" class="abs low">0/24</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="22" class="abs low">0/22</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="35" class="abs low">0/35</td>
	</tr>

<tr>
	<td class="file low" data-value="sync-queue-store.ts"><a href="sync-queue-store.ts.html">sync-queue-store.ts</a></td>
	<td data-value="9.09" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 9%"></div><div class="cover-empty" style="width: 91%"></div></div>
	</td>
	<td data-value="9.09" class="pct low">9.09%</td>
	<td data-value="66" class="abs low">6/66</td>
	<td data-value="2.85" class="pct low">2.85%</td>
	<td data-value="35" class="abs low">1/35</td>
	<td data-value="7.69" class="pct low">7.69%</td>
	<td data-value="26" class="abs low">2/26</td>
	<td data-value="9.83" class="pct low">9.83%</td>
	<td data-value="61" class="abs low">6/61</td>
	</tr>

<tr>
	<td class="file low" data-value="team-store.ts"><a href="team-store.ts.html">team-store.ts</a></td>
	<td data-value="3.12" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 3%"></div><div class="cover-empty" style="width: 97%"></div></div>
	</td>
	<td data-value="3.12" class="pct low">3.12%</td>
	<td data-value="96" class="abs low">3/96</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="42" class="abs low">0/42</td>
	<td data-value="6.89" class="pct low">6.89%</td>
	<td data-value="29" class="abs low">2/29</td>
	<td data-value="4.05" class="pct low">4.05%</td>
	<td data-value="74" class="abs low">3/74</td>
	</tr>

<tr>
	<td class="file low" data-value="whimsy-store.ts"><a href="whimsy-store.ts.html">whimsy-store.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="45" class="abs low">0/45</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="31" class="abs low">0/31</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="33" class="abs low">0/33</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-17T12:24:49.108Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    